package com.game2sky.prilib.benfu.logic.integral.task;

import com.game2sky.prilib.benfu.logic.integral.IntegralGameRedis;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 天梯定时保存redis
 * <AUTHOR>
 * @version v0.1 2017年12月27日 下午7:52:28  zhoufang
 */
public class ScheduledAnsyncOperRedis extends ScheduledMessage{

	public static final long PERIOD = 5000L;
	
	public ScheduledAnsyncOperRedis() {
		super(System.currentTimeMillis());
	}

	@Override
	public void execute() {
		IntegralGameRedis.ansyncOperRedis();
	}

}
