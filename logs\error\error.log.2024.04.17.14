2024-04-17 14:15:37,062 [main] ERROR (DictSkill.java:595) - Skill 301573 buff data error
2024-04-17 14:15:37,069 [main] ERROR (DictSkill.java:595) - Skill 700677 buff data error
2024-04-17 14:15:40,369 [main] ERROR (SceneObjectCreater.java:58) - dictSceneNpc={"aIType":0,"clickEvent":"OnClickNpcOpenDialog","dir":{"x":0.43,"y":0.0,"z":-0.9},"directionX":0.43,"directionZ":-0.9,"dynamic":false,"eventArgs":"Dialogid:91027080","iconId":"0","id":5715,"isDynamic":0,"isTaskNpc":0,"modelId":1094,"moveMode":0,"name":"????","positionId":9983,"sceneId":8005,"speed":0}
java.lang.Exception: No scene is found,DictSceneNpc,{"aIType":0,"clickEvent":"OnClickNpcOpenDialog","dir":{"x":0.43,"y":0.0,"z":-0.9},"directionX":0.43,"directionZ":-0.9,"dynamic":false,"eventArgs":"Dialogid:91027080","iconId":"0","id":5715,"isDynamic":0,"isTaskNpc":0,"modelId":1094,"moveMode":0,"name":"????","positionId":9983,"sceneId":8005,"speed":0}
	at com.game2sky.prilib.core.socket.logic.scene.unit.SceneObjectCreater.randomPos(SceneObjectCreater.java:290) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.unit.SceneObjectCreater.createNpc(SceneObjectCreater.java:52) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.initNpc(AbstractCustomScene.java:75) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.init0(AbstractCustomScene.java:62) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.init(AbstractScene.java:106) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.SceneManager.createCommonScene(SceneManager.java:95) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneLineManagerV1.benfuInit(SceneLineManagerV1.java:166) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneLineManagerV1.init(SceneLineManagerV1.java:131) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.MemoryData.init(MemoryData.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initMemory(Globals.java:624) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initBenfuMemoryData(Globals.java:585) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initMemoryData(Globals.java:557) ~[mmo.core-op.jar!/:op]
	at com.game2sky.CoreBoot.boot(CoreBoot.java:637) ~[mmo.core-op.jar!/:op]
	at com.game2sky.MainOpBenfu.main(MainOpBenfu.java:93) ~[classes!/:op]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:50) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:58) ~[mmo.benfu-op.jar:op]
2024-04-17 14:17:41,895 [main] ERROR (DictSkill.java:595) - Skill 301573 buff data error
2024-04-17 14:17:41,899 [main] ERROR (DictSkill.java:595) - Skill 700677 buff data error
2024-04-17 14:17:44,567 [main] ERROR (SceneObjectCreater.java:58) - dictSceneNpc={"aIType":0,"clickEvent":"OnClickNpcOpenDialog","dir":{"x":0.43,"y":0.0,"z":-0.9},"directionX":0.43,"directionZ":-0.9,"dynamic":false,"eventArgs":"Dialogid:91027080","iconId":"0","id":5715,"isDynamic":0,"isTaskNpc":0,"modelId":1094,"moveMode":0,"name":"????","positionId":9983,"sceneId":8005,"speed":0}
java.lang.Exception: No scene is found,DictSceneNpc,{"aIType":0,"clickEvent":"OnClickNpcOpenDialog","dir":{"x":0.43,"y":0.0,"z":-0.9},"directionX":0.43,"directionZ":-0.9,"dynamic":false,"eventArgs":"Dialogid:91027080","iconId":"0","id":5715,"isDynamic":0,"isTaskNpc":0,"modelId":1094,"moveMode":0,"name":"????","positionId":9983,"sceneId":8005,"speed":0}
	at com.game2sky.prilib.core.socket.logic.scene.unit.SceneObjectCreater.randomPos(SceneObjectCreater.java:290) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.unit.SceneObjectCreater.createNpc(SceneObjectCreater.java:52) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.initNpc(AbstractCustomScene.java:75) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.init0(AbstractCustomScene.java:62) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.init(AbstractScene.java:106) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.SceneManager.createCommonScene(SceneManager.java:95) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneLineManagerV1.benfuInit(SceneLineManagerV1.java:166) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneLineManagerV1.init(SceneLineManagerV1.java:131) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.MemoryData.init(MemoryData.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initMemory(Globals.java:624) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initBenfuMemoryData(Globals.java:585) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.Globals.initMemoryData(Globals.java:557) ~[mmo.core-op.jar!/:op]
	at com.game2sky.CoreBoot.boot(CoreBoot.java:637) ~[mmo.core-op.jar!/:op]
	at com.game2sky.MainOpBenfu.main(MainOpBenfu.java:93) ~[classes!/:op]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:50) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:58) ~[mmo.benfu-op.jar:op]
2024-04-17 14:18:21,561 [LoginProcessorGroup-thread-1] ERROR (AbstractLoginService.java:1425) - req={"appVersion":"1.15.0.302225","channelId":"ANDTEST","chargeMoney":0.0,"deviceDesc":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","fightPower":0,"globalId":1,"isLogin":1,"lastLoginTime":1713338301394,"level":0,"name":"","plafformUid":"nhan2k6","playerId":************,"sdkUserId":"nhan2k6","serverId":1}, playerId=************
java.lang.NullPointerException
	at com.game2sky.publib.framework.web.base.Result.toObject(Result.java:33) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.socket.logic.login.AbstractLoginService.updateGamePlayerToMain(AbstractLoginService.java:1423) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.login.AbstractLoginService.afterMainServerCheck(AbstractLoginService.java:745) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.login.AbstractLoginService.csPlayerLogin(AbstractLoginService.java:157) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.login.AbstractLoginService.ssPlayerLogin(AbstractLoginService.java:134) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.login.LoginController.ssUserLogin(LoginController.java:34) ~[mmo.core-op.jar!/:op]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at com.game2sky.publib.framework.protostuf.ProtoBufInvokeMethod.invoke(ProtoBufInvokeMethod.java:23) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.base.BaseService.executeMsg(BaseService.java:98) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.base.BaseService.executeMsg(BaseService.java:47) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.framework.common.MessageExecuteTask.dispatcher(MessageExecuteTask.java:38) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.framework.common.MessageExecuteTask.run(MessageExecuteTask.java:29) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
2024-04-17 14:18:21,561 [LoginProcessorGroup-thread-1] ERROR (AbstractLoginService.java:1429) - playerlogin error reponse null, req={"appVersion":"1.15.0.302225","channelId":"ANDTEST","chargeMoney":0.0,"deviceDesc":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","fightPower":0,"globalId":1,"isLogin":1,"lastLoginTime":1713338301394,"level":0,"name":"","plafformUid":"nhan2k6","playerId":************,"sdkUserId":"nhan2k6","serverId":1}, playerId=************
2024-04-17 14:18:29,009 [SceneProcessorGroup-thread-5] ERROR (BaseService.java:103) - {"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"}
2024-04-17 14:18:29,011 [SceneProcessorGroup-thread-5] ERROR (AbstractScene.java:238) - sceneId:30010, lineId:1, playerId:************, ps:SSUpdateInviteInfo, {"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"}
2024-04-17 14:18:29,011 [SceneProcessorGroup-thread-5] ERROR (AbstractScene.java:240) - ************, scene:[serverFlag=1,sceneId=30010,sceneMapId=3,lineId=1,curState=STATUS_RUNNING,playerCount=1], playerState:gaming, gameFlow:com.game2sky.publib.socket.logic.flow.GamingFlow@1096b181
java.lang.reflect.InvocationTargetException
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at com.game2sky.publib.framework.protostuf.ProtoBufInvokeMethod.invoke(ProtoBufInvokeMethod.java:23) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.base.BaseService.executeMsg(BaseService.java:98) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.handlerMessage(AbstractScene.java:231) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.handlPlayerMessageQueue(AbstractCustomScene.java:167) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractSceneController.tick(AbstractSceneController.java:70) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.OpSceneController.tick(OpSceneController.java:68) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.tick(AbstractScene.java:151) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneRunner.call(SceneRunner.java:139) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneRunner.call(SceneRunner.java:29) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: com.alibaba.fastjson.JSONException: expect '[', but int, pos 9, line 1, column 10947642354
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:721) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:709) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:704) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.JSON.parseArray(JSON.java:620) ~[fastjson-1.2.70.jar!/:?]
	at com.game2sky.publib.framework.util.JsonUtils.S2Arr(JsonUtils.java:152) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.invite.InviteCodeService.updateInviteInfo(InviteCodeService.java:223) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.invite.InviteCodeController.updateInviteInfo(InviteCodeController.java:44) ~[mmo.core-op.jar!/:op]
	... 17 more
2024-04-17 14:20:19,782 [AsyncServiceImpl.bind-thread-1] ERROR (PayPrivilegeManager.java:54) - |PlayerPayPrivilegeIO: playerId : 800010028601
2024-04-17 14:20:19,784 [AsyncServiceImpl.bind-thread-1] ERROR (PlayerPayPrivilegeIO.java:40) - OPPO????????????{"bindId":800010028601}
2024-04-17 14:20:24,783 [SceneProcessorGroup-thread-3] ERROR (BaseService.java:103) - {"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"}
2024-04-17 14:20:24,783 [SceneProcessorGroup-thread-3] ERROR (AbstractScene.java:238) - sceneId:120010, lineId:1, playerId:800010028601, ps:SSUpdateInviteInfo, {"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"}
2024-04-17 14:20:24,783 [SceneProcessorGroup-thread-3] ERROR (AbstractScene.java:240) - 800010028601, scene:[serverFlag=1,sceneId=120010,sceneMapId=12,lineId=1,curState=STATUS_RUNNING,playerCount=1], playerState:gaming, gameFlow:com.game2sky.publib.socket.logic.flow.GamingFlow@1096b181
java.lang.reflect.InvocationTargetException
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at com.game2sky.publib.framework.protostuf.ProtoBufInvokeMethod.invoke(ProtoBufInvokeMethod.java:23) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.base.BaseService.executeMsg(BaseService.java:98) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.handlerMessage(AbstractScene.java:231) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene.handlPlayerMessageQueue(AbstractCustomScene.java:167) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractSceneController.tick(AbstractSceneController.java:70) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.scene.OpSceneController.tick(OpSceneController.java:68) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.AbstractScene.tick(AbstractScene.java:151) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneRunner.call(SceneRunner.java:139) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.socket.logic.scene.base.SceneRunner.call(SceneRunner.java:29) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: com.alibaba.fastjson.JSONException: expect '[', but int, pos 9, line 1, column 10947642354
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:721) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:709) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parseArray(DefaultJSONParser.java:704) ~[fastjson-1.2.70.jar!/:?]
	at com.alibaba.fastjson.JSON.parseArray(JSON.java:620) ~[fastjson-1.2.70.jar!/:?]
	at com.game2sky.publib.framework.util.JsonUtils.S2Arr(JsonUtils.java:152) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.invite.InviteCodeService.updateInviteInfo(InviteCodeService.java:223) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.invite.InviteCodeController.updateInviteInfo(InviteCodeController.java:44) ~[mmo.core-op.jar!/:op]
	... 17 more
