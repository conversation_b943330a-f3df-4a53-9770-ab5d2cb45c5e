package com.game2sky.prilib.benfu.logic.unionSeaFight;


import com.game2sky.prilib.communication.game.unionSeaFight.SSBenfuUSFActivitySchedule;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

public class ScheduleUSFActivity extends ScheduledMessage {


	private int groupId;
	
	public static final int PERIOD = 20000;
	public static final int DELAY = 1000;
	
	public ScheduleUSFActivity(int groupId) {
		super(System.currentTimeMillis());
		this.groupId = groupId;
	}
	
	@Override
	public void execute() {
		SSBenfuUSFActivitySchedule scheduleMessage = new SSBenfuUSFActivitySchedule();
		scheduleMessage.setGroup(groupId);
		Message message = Message.buildByZoneId(0L, 0, scheduleMessage, null, null, null);
		Globals.getMsgProcessDispatcher().put(message);
	}
}
