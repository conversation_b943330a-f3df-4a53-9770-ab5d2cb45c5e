2024-04-14 13:32:19,054 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [2/1/3/SSActivityTeamBossStart/0] - {"body":{"activityId":0},"head":{"len":2,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:32:19,254 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [10/1/11/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:20,268 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:32:21,241 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85608,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:32:26,852 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:32:32,603 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [8/1/9/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:33,114 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetMailList/************] - {"body":{"mails":[{"content":"?????????1?","dictId":8,"id":1500836001336001536,"itemBases":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":3000,"type":1011},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":3},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50000,"type":5}],"mailAudo":"","mailIcon":"UI_youjian_item_xinfen","mailTiming":604800000,"sendTime":1712982360053,"sender":"??????","senderPlayerId":0,"status":"MAIL_ITEMGATHERED","title":"?????"}]},"head":{"len":175,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:33,420 [LoginProcessorGroup-thread-1] INFO (BaseService.java:110) - [1/792/793/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:33,487 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:33,489 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0x7268ee48, L:/127.0.0.1:12306 - R:/127.0.0.1:17262]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"suc7B4BHRnaUyJs/v76I2g==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:33,509 [WORLD_ARENA_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [24/0/24/SSRequestEnterArena/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:33,519 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [10/0/10/SSEnterArenaSuccess/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:33,520 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [11/0/11/SSUpdateArenaPlayerRank/************] - {"body":{"highRank":1,"isWin":-1},"head":{"len":13,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:34,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:32:34,509 [LadderWarBenfu] INFO (BaseService.java:110) - [0/0/0/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:34,509 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:34,540 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [31/0/31/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:34,540 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:34,540 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [22/0/22/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:34,542 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":18.068,"y":2.966,"z":4.55},"sceneMapId":2},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:34,593 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1713076354587,"serverTime":1713076354591},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:37,093 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [25/3/28/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:37,143 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,145 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,146 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [4/0/4/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:37,147 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [5/0/5/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,157 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [4/0/4/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,159 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,161 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [17/0/17/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,166 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [18/0/18/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,169 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [18/0/18/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":1,"pushToServer":0,"type":1030},{"num":0,"pushToServer":0,"type":7},{"num":1,"pushToServer":0,"type":1031},{"num":0,"pushToServer":0,"type":8},{"num":1,"pushToServer":0,"type":1032},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":1,"pushToServer":0,"type":1038},{"num":1,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":1,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":1,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":280,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,171 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [21/0/21/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1,5001,40,22,21,12,9,6,5,4,2,144,143,142,141,140,139,138,137,136,135,134,133,132,131,130,129,128,127,126,125,124,123,122,121,120,119,118,112,110,109,108,107,106,105,103,102,100,98,97,95,94,93,92,91,89,88,85,81,80,79,78,77,76,75],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3,4,12,2]},"head":{"len":516,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500595498124313600,"itemId":212001,"itemNumber":115,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":16,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":6,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500591126443197440,"itemId":452201,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":6,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312130,"itemId":215005,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":7,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500591126443197442,"itemId":214011,"itemNumber":21,"itemType":1001,"tradeTime":0}},{"index":8,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500591137394525184,"itemId":214008,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":9,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500591137394525185,"itemId":214002,"itemNumber":10,"itemType":1001,"tradeTime":0}},{"index":10,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500591155182568448,"itemId":212007,"itemNumber":38,"itemType":1001,"tradeTime":0}},{"index":11,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500592259651863552,"itemId":451001,"itemNumber":10,"itemType":1001,"tradeTime":0}},{"index":12,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500600983342810112,"itemId":213001,"itemNumber":75,"itemType":1001,"tradeTime":0}},{"index":13,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894526770775040,"itemId":212009,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":14,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894528339444736,"itemId":212111,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":15,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894529643873280,"itemId":212112,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":16,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894530944107520,"itemId":212113,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":17,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894532244341760,"itemId":212114,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":18,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894533536187392,"itemId":212013,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":19,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894537948595200,"itemId":213009,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":20,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894539257218048,"itemId":213111,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":21,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894540553257984,"itemId":213112,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":22,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894542121927680,"itemId":213113,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":23,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894543422161920,"itemId":213114,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":24,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894544722396160,"itemId":213013,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":25,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894546282677248,"itemId":213007,"itemNumber":43,"itemType":1001,"tradeTime":0}},{"index":26,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894549151581184,"itemId":214009,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":27,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894550451815424,"itemId":214111,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":28,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894551752049664,"itemId":214112,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":29,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894553324913664,"itemId":214113,"itemNumber":33,"itemType":1001,"tradeTime":0}}],"totalSize":150}},"head":{"len":999,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894554625147904,"itemId":214114,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":31,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894555916993536,"itemId":214013,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":32,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894557489857536,"itemId":214007,"itemNumber":48,"itemType":1001,"tradeTime":0}},{"index":33,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894558790091776,"itemId":214001,"itemNumber":53,"itemType":1001,"tradeTime":0}},{"index":34,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894560086131712,"itemId":215009,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":35,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894561654801408,"itemId":215111,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":36,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894562946647040,"itemId":215112,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":37,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894564779557888,"itemId":215113,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":38,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894566356616192,"itemId":215114,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":39,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894567652656128,"itemId":215013,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":40,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894568952890368,"itemId":215007,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":41,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894570508977152,"itemId":215001,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":42,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894571817600000,"itemId":216001,"itemNumber":33,"itemType":1001,"tradeTime":0}},{"index":43,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500894890362405888,"itemId":215011,"itemNumber":7,"itemType":1001,"tradeTime":0}},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":535,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,172 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,181 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0,"item":{"bind":false,"cdTime":0,"equip":{"holes":[],"identify":0,"mainProps":[{"propId":1,"propValue":0.0}],"preciousGoogs":0,"randProps":[],"rareProps":[],"reIdentifyCount":0,"refineCards":[],"refineFailCount":0,"refineLevel":0,"score":0.0,"staticProps":[{"propId":1,"propValue":0.0}]},"expiredTime":0,"itemGuid":1500596031119688704,"itemId":460009,"itemNumber":1,"itemType":1200,"tradeTime":0}},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":205,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,181 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523939619840,"itemId":1100110,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523948008448,"itemId":1100210,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523948008449,"itemId":1100310,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523952202752,"itemId":1100410,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523952202753,"itemId":1100510,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":5,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523952202754,"itemId":1100610,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":6,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523952202755,"itemId":1100710,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":7,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523952202756,"itemId":1100810,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":8,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523960591360,"itemId":1100910,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":9,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523960591361,"itemId":1101010,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":10,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523960591362,"itemId":1101110,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":11,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523960591363,"itemId":1101210,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":12,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523960591364,"itemId":1101310,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":13,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523964785664,"itemId":1101410,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":14,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523964785665,"itemId":1101510,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":15,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523964785666,"itemId":1101610,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":16,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523964785667,"itemId":1101710,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":17,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979968,"itemId":1101810,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":18,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979969,"itemId":1101910,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":19,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979970,"itemId":1102010,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":20,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979971,"itemId":1102110,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":21,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979972,"itemId":1102210,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":22,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979973,"itemId":1102310,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":23,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500894523968979974,"itemId":1102410,"itemNumber":99,"itemType":1001,"tradeTime":0}},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":825,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,182 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913523549184,"itemId":511001,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913523549185,"itemId":511002,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913523549186,"itemId":511003,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937792,"itemId":511004,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937793,"itemId":511005,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":5,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937794,"itemId":511006,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":6,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937795,"itemId":511007,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":7,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937796,"itemId":511008,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":8,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937797,"itemId":511009,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":9,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937798,"itemId":511010,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":10,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937799,"itemId":511011,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":11,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937800,"itemId":511012,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":12,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913531937801,"itemId":511013,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":13,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913536132096,"itemId":511014,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":14,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913536132097,"itemId":511015,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":15,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913536132098,"itemId":511016,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":16,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913536132099,"itemId":511017,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":17,"item":{"bind":false,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326400,"itemId":511018,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":18,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326401,"itemId":511019,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":19,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326402,"itemId":511020,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":20,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326403,"itemId":511021,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":21,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326404,"itemId":511022,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":22,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913540326405,"itemId":511023,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":23,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715008,"itemId":511024,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":24,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715009,"itemId":511025,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":25,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715010,"itemId":512001,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":26,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715011,"itemId":512002,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":27,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715012,"itemId":512003,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":28,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715013,"itemId":512004,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":29,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913548715014,"itemId":512005,"itemNumber":999,"itemType":1001,"tradeTime":0}}],"totalSize":150}},"head":{"len":1029,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913552909312,"itemId":512006,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":31,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913552909313,"itemId":512007,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":32,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913552909314,"itemId":512008,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":33,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913552909315,"itemId":512009,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":34,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913552909316,"itemId":512010,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":35,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103616,"itemId":512011,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":36,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103617,"itemId":512012,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":37,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103618,"itemId":512013,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":38,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103619,"itemId":512014,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":39,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103620,"itemId":512015,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":40,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103621,"itemId":512016,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":41,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913557103622,"itemId":512017,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":42,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297920,"itemId":512018,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":43,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297921,"itemId":512019,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":44,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297922,"itemId":512020,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":45,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297923,"itemId":512021,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":46,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297924,"itemId":512022,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":47,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913561297925,"itemId":512023,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":48,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913565492224,"itemId":512024,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":49,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913565492225,"itemId":512025,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":50,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913565492226,"itemId":513001,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":51,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913565492227,"itemId":513002,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":52,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913569686528,"itemId":513003,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":53,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913569686529,"itemId":513004,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":54,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913569686530,"itemId":513005,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":55,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880832,"itemId":513006,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":56,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880833,"itemId":513007,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":57,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880834,"itemId":513008,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":58,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880835,"itemId":513009,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":59,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880836,"itemId":513010,"itemNumber":999,"itemType":1001,"tradeTime":0}}],"totalSize":150}},"head":{"len":1029,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880837,"itemId":513011,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":61,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880838,"itemId":513012,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":62,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880839,"itemId":513013,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":63,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880840,"itemId":513014,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":64,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913573880841,"itemId":513015,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":65,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075136,"itemId":513016,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":66,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075137,"itemId":513017,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":67,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075138,"itemId":513018,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":68,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075139,"itemId":513019,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":69,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075140,"itemId":513020,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":70,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075141,"itemId":513021,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":71,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913578075142,"itemId":513022,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":72,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913582269440,"itemId":513023,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":73,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913582269441,"itemId":513024,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":74,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913582269442,"itemId":513025,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":75,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913582269443,"itemId":514001,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":76,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913582269444,"itemId":514002,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":77,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913590658048,"itemId":514003,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":78,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913590658049,"itemId":514004,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":79,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913590658050,"itemId":514005,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":80,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913590658051,"itemId":514006,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":81,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913590658052,"itemId":514007,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":82,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913594852352,"itemId":514008,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":83,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913594852353,"itemId":514009,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":84,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913599046656,"itemId":514010,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":85,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913599046657,"itemId":514011,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":86,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913599046658,"itemId":514012,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":87,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913599046659,"itemId":514013,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":88,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913603240960,"itemId":514014,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":89,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913603240961,"itemId":514015,"itemNumber":999,"itemType":1001,"tradeTime":0}}],"totalSize":150}},"head":{"len":1029,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913603240962,"itemId":514016,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":91,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913603240963,"itemId":514017,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":92,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913607435264,"itemId":514018,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":93,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913607435265,"itemId":514019,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":94,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913607435266,"itemId":514020,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":95,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913607435267,"itemId":514021,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":96,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913611629568,"itemId":514022,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":97,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913611629569,"itemId":514023,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":98,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913611629570,"itemId":514024,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":99,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823872,"itemId":514025,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":100,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823873,"itemId":515001,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":101,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823874,"itemId":515002,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":102,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823875,"itemId":515003,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":103,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823876,"itemId":515004,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":104,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823877,"itemId":515005,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":105,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823878,"itemId":515006,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":106,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823879,"itemId":515007,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":107,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913615823880,"itemId":515008,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":108,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913620018176,"itemId":515009,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":109,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913620018177,"itemId":515010,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":110,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913624212480,"itemId":515011,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":111,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913624212481,"itemId":515012,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":112,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913624212482,"itemId":515013,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":113,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913624212483,"itemId":515014,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":114,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913624212484,"itemId":515015,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":115,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913632601088,"itemId":515016,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":116,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913632601089,"itemId":515017,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":117,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913632601090,"itemId":515018,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":118,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913636795392,"itemId":515019,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":119,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913636795393,"itemId":515020,"itemNumber":999,"itemType":1001,"tradeTime":0}}],"totalSize":150}},"head":{"len":1029,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913636795394,"itemId":515021,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":121,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913636795395,"itemId":515022,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":122,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913640989696,"itemId":515023,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":123,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913640989697,"itemId":515024,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":124,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590913640989698,"itemId":515025,"itemNumber":999,"itemType":1001,"tradeTime":0}},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":301,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,183 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500596031153243136,"itemId":620011,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590832703505408,"itemId":630002,"itemNumber":50,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500590832707699712,"itemId":630003,"itemNumber":50,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500592755770917888,"itemId":620003,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312128,"itemId":620017,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":5,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312129,"itemId":610002,"itemNumber":10,"itemType":1001,"tradeTime":0}},{"index":6,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312131,"itemId":610014,"itemNumber":20,"itemType":1001,"tradeTime":0}},{"index":7,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312132,"itemId":620002,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":8,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312133,"itemId":610001,"itemNumber":20,"itemType":1001,"tradeTime":0}},{"index":9,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312134,"itemId":610018,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":10,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312135,"itemId":610010,"itemNumber":70,"itemType":1001,"tradeTime":0}},{"index":11,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312136,"itemId":610015,"itemNumber":40,"itemType":1001,"tradeTime":0}},{"index":12,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836194475312137,"itemId":610004,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":13,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836305527899136,"itemId":620009,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":14,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836305527899137,"itemId":610003,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":15,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500836305536287744,"itemId":610008,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":16,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500837295996011520,"itemId":610011,"itemNumber":20,"itemType":1001,"tradeTime":0}},{"index":17,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500837295996011521,"itemId":610012,"itemNumber":20,"itemType":1001,"tradeTime":0}},{"index":18,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500837295996011523,"itemId":610005,"itemNumber":20,"itemType":1001,"tradeTime":0}},{"index":19,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500859240250606592,"itemId":610007,"itemNumber":10,"itemType":1001,"tradeTime":0}},{"index":20,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500859240250606593,"itemId":610013,"itemNumber":10,"itemType":1001,"tradeTime":0}},{"index":21,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500859240250606594,"itemId":620012,"itemNumber":30,"itemType":1001,"tradeTime":0}},{"index":22,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1500859579515274241,"itemId":630015,"itemNumber":50,"itemType":1001,"tradeTime":0}},{"index":23},{"index":24},{"index":25}],"totalSize":26}},"head":{"len":779,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,197 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:37,223 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":110001548,"type":3},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":3000,"type":1011},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":109999998,"type":4},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":107983048,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":99,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,223 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"http://14.225.208.214:100/noti.html?t=1713076357191&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":291,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,223 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,228 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,232 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [7/0/7/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,234 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [12/0/12/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,193,4,7,232,9,233,10,234,11,235,12,13,14,218]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,237 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [23/96/119/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:37,238 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"?????????","chapterId":3,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":214011,"num":1,"type":1001},"chapterSchedule":74},"head":{"len":50,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,246 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712991522061,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712991522061,"current":"0","param1":"10","param2":"1","paramList":["303","225"],"state":1,"targetDesc":"Defeat the Black Cat Pirates","targetId":10052,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,304,0","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????","taskId":10511,"taskName":"?????","timeControlType":0,"type":1},{"acceptTime":0,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"2,110,0,250156","giveUpType":2,"handOverType":"2,198,0,250157,1","isLoop":0,"loopId":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":1,"num":50,"type":8},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":5},{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":5,"type":1001}],"state":2,"taskDesc":"????????????????","taskId":20002,"taskName":"????","timeControlType":0,"type":2},{"acceptTime":0,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"2,248,0,250158","giveUpType":2,"handOverType":"2,212,0,250159,1","isLoop":0,"loopId":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":1,"num":50,"type":8},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":400,"type":7},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":400,"type":1101},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":5},{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":7,"type":1001}],"state":2,"taskDesc":"????????????????","taskId":20005,"taskName":"?????","timeControlType":0,"type":2},{"acceptTime":0,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"2,211,0,250198","giveUpType":2,"handOverType":"2,211,0,250199,1","isLoop":0,"loopId":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":1,"num":50,"type":8},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":400,"type":7},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":400,"type":1101},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":5},{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":7,"type":1001}],"state":2,"taskDesc":"???????????","taskId":20006,"taskName":"?????","timeControlType":0,"type":2}]},"head":{"len":766,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,250 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCTitleInfo/************] - {"body":{"curTitleId":13,"isShow":1,"titles":[{"current":44,"deadTime":-1,"isOwn":1,"titleId":13,"total":20},{"current":1,"deadTime":-1,"isOwn":1,"titleId":1,"total":1},{"current":1,"deadTime":-1,"isOwn":1,"titleId":11,"total":1},{"current":44,"deadTime":-1,"isOwn":1,"titleId":12,"total":12},{"current":44,"deadTime":-1,"isOwn":1,"titleId":14,"total":30},{"current":44,"deadTime":-1,"isOwn":1,"titleId":15,"total":40},{"current":10,"deadTime":-1,"isOwn":1,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":2,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":2,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":4200,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":4200,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":288,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":288,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":288,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1233,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,256 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [25/0/25/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,260 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [32/0/32/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":1},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":1},{"forceSwitch":1,"name":"AutoChess","state":1},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":1},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":1},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":1},{"forceSwitch":1,"name":"BloodyFight","state":1},{"forceSwitch":1,"name":"Bot","state":1},{"forceSwitch":1,"name":"BotPick","state":1},{"forceSwitch":1,"name":"BountyHunt","state":1},{"forceSwitch":1,"name":"BountyHuntBoss","state":1},{"forceSwitch":1,"name":"CatchAction","state":1},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":1},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":1},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":1},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":1},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":1},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":1},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":1},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":1},{"forceSwitch":1,"name":"GvoPlace","state":1},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":1},{"forceSwitch":1,"name":"HeroPromote","state":1},{"forceSwitch":1,"name":"HomeLand","state":1},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":1},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":1},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":1},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":1},{"forceSwitch":1,"name":"OpenTeamRobot","state":1},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":1},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":1},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":1},{"forceSwitch":1,"name":"Recruit","state":1},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":1},{"forceSwitch":1,"name":"SailLog","state":1},{"forceSwitch":1,"name":"SecretPlaceFight","state":1},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":1},{"forceSwitch":1,"name":"SpringCook","state":1},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":1},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":1},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":1},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":1},{"forceSwitch":1,"name":"WarChess","state":1},{"forceSwitch":1,"name":"WarChessAccelerate","state":1},{"forceSwitch":1,"name":"WarChessHardChapter","state":1},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":1},{"forceSwitch":1,"name":"WelfareHot","state":1},{"forceSwitch":1,"name":"WorldPersonBoss","state":1},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":2969,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,284 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [34/0/34/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5,6,3,4,18],"guideMarks":[{"key":"FuncOpen_Recruit","value":"True"}]},"head":{"len":38,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,287 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [58/0/58/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"assistFormationItems":[],"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":0,"h":1500590734489682944,"hi":113002},{"b":6,"f":0,"h":1500590736410674176,"hi":113006},{"b":4,"f":0,"h":1500590733852148736,"hi":113001}]},{"assistFormationItems":[],"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":0,"h":1500590734489682944,"hi":113002},{"b":4,"f":0,"h":1500590734917501952,"hi":113003},{"b":6,"f":0,"h":1500590839926096896,"hi":113012}]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":148,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,288 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [61/0/61/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,289 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [61/0/61/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,292 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [62/0/62/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,294 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [66/0/66/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,295 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [66/0/66/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,296 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [67/0/67/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,297 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [68/0/68/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,299 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [69/0/69/SCTriggerGiftAppear/************] - {"body":{"endTime":1713003528598,"isFirstTime":0},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,300 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [71/0/71/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,300 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [72/0/72/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,300 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [64/0/64/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1114}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,301 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [64/0/64/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":2},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:37,613 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [40/3/43/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:37,616 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:32:38,391 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5,6,3,4,18],"guideMarks":[{"key":"FuncOpen_Recruit","value":"True"}]},"head":{"len":38,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,392 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [29/0/29/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,394 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":1},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":1},{"forceSwitch":1,"name":"AutoChess","state":1},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":1},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":1},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":1},{"forceSwitch":1,"name":"BloodyFight","state":1},{"forceSwitch":1,"name":"Bot","state":1},{"forceSwitch":1,"name":"BotPick","state":1},{"forceSwitch":1,"name":"BountyHunt","state":1},{"forceSwitch":1,"name":"BountyHuntBoss","state":1},{"forceSwitch":1,"name":"CatchAction","state":1},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":1},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":1},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":1},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":1},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":1},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":1},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":1},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":1},{"forceSwitch":1,"name":"GvoPlace","state":1},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":1},{"forceSwitch":1,"name":"HeroPromote","state":1},{"forceSwitch":1,"name":"HomeLand","state":1},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":1},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":1},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":1},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":1},{"forceSwitch":1,"name":"OpenTeamRobot","state":1},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":1},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":1},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":1},{"forceSwitch":1,"name":"Recruit","state":1},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":1},{"forceSwitch":1,"name":"SailLog","state":1},{"forceSwitch":1,"name":"SecretPlaceFight","state":1},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":1},{"forceSwitch":1,"name":"SpringCook","state":1},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":1},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":1},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":1},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":1},{"forceSwitch":1,"name":"WarChess","state":1},{"forceSwitch":1,"name":"WarChessAccelerate","state":1},{"forceSwitch":1,"name":"WarChessHardChapter","state":1},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":1},{"forceSwitch":1,"name":"WelfareHot","state":1},{"forceSwitch":1,"name":"WorldPersonBoss","state":1},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":2969,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,395 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [19/2/21/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,396 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [22/0/22/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,396 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,397 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [23/0/23/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,398 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,544 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,545 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [43/0/43/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,546 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [33/0/33/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,547 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,547 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [14/0/14/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:38,548 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,564 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:32:38,564 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:40,281 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:32:41,850 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:32:47,228 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [22/0/22/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:32:47,228 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:47,687 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":9,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:49,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:32:54,684 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":10,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:32:56,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:00,040 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [40/0/40/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:33:00,243 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:33:00,654 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [50/0/50/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:33:00,654 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:04,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:11,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:13,998 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [10/0/10/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:33:13,998 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:19,292 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [51/0/51/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:19,292 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:33:19,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:20,254 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:33:26,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:27,398 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:33:27,398 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:34,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:40,269 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:33:40,789 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:33:40,789 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:41,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:49,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:33:54,177 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [6/0/6/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:33:54,177 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:33:56,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:00,035 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [34/0/34/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:34:00,289 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:34:04,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:07,600 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [30/0/30/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:34:07,600 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:11,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:19,292 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [51/0/51/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:19,292 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:34:19,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:20,249 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:34:20,991 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [27/0/27/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:34:20,991 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:26,850 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:34,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:34,380 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [32/0/32/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:34:34,380 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:40,267 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:34:41,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:47,770 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [31/0/31/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:34:47,770 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:48,996 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":9,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:49,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:34:56,000 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":10,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:34:56,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:00,043 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [42/0/42/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:35:00,246 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:35:01,158 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [19/0/19/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:35:01,158 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:04,350 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:11,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:14,544 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [17/0/17/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:35:14,544 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:19,253 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:35:19,253 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [13/0/13/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:19,350 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:20,264 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:35:26,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:27,928 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [6/0/6/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:35:27,928 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:34,350 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:40,278 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:35:41,320 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [4/0/4/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:35:41,320 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:41,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:49,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:35:54,708 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [2/0/2/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:35:54,708 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:35:56,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:00,038 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [38/0/38/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:36:00,291 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:36:04,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:08,141 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [37/0/37/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:36:08,141 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:11,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:19,244 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [2/0/2/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:19,244 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:36:19,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:20,255 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:36:21,528 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [30/0/30/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:36:21,528 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:26,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:34,350 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:34,929 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [41/0/41/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:36:34,929 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:40,272 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:36:41,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:48,309 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [19/0/19/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:36:48,309 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:49,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:50,302 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":9,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:36:56,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:36:57,304 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":10,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:00,044 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [44/0/44/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:37:00,246 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:37:01,701 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [23/0/23/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:37:01,701 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:04,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:11,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:15,104 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [38/0/38/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:37:15,104 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:19,256 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [15/0/15/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:19,256 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:37:19,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:20,270 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:37:26,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:28,492 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [28/0/28/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:37:28,492 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:34,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:40,275 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:37:41,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:41,886 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [23/0/23/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:37:41,886 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:49,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:37:55,257 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [10/0/10/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":71,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:37:55,257 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:10","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":76,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:37:56,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:00,048 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:38:00,250 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:38:04,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:08,654 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [9/0/9/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:38:08,654 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:11,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:19,267 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:38:19,267 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [26/0/26/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:19,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:20,278 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:38:22,042 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [7/0/7/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:38:22,042 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:26,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:34,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:35,476 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [46/0/46/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:38:35,476 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:9","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:40,253 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:38:41,849 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:48,866 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [47/0/47/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:38:48,866 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:49,349 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:51,618 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":9,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:38:56,848 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:38:58,619 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneObjectAction/************] - {"body":{"actionId":10,"guid":-20010000000421},"head":{"len":13,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:39:00,013 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [12/0/12/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:39:00,265 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:39:02,264 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [30/0/30/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:39:02,264 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:8","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":75,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:39:04,348 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:39:05,638 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":0,"msg":"The current server is under maintenance, please log in later","opcode":0,"type":3},"head":{"len":68,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:39:05,730 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [36/0/36/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:40:05,921 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [7/1/8/SSActivityTeamBossStart/0] - {"body":{"activityId":0},"head":{"len":2,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:40:06,166 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [40/1/41/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:40:07,131 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:40:08,123 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85608,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:40:09,025 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [14/4/18/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:40:09,567 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetMailList/************] - {"body":{"mails":[{"content":"?????????1?","dictId":8,"id":1500836001336001536,"itemBases":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":3000,"type":1011},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":3},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50000,"type":5}],"mailAudo":"","mailIcon":"UI_youjian_item_xinfen","mailTiming":604800000,"sendTime":1712982360053,"sender":"??????","senderPlayerId":0,"status":"MAIL_ITEMGATHERED","title":"?????"}]},"head":{"len":175,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:40:10,078 [LoginProcessorGroup-thread-1] INFO (BaseService.java:110) - [0/1036/1036/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:40:10,127 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:40:10,132 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [4/0/4/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0xc9d70234, L:/127.0.0.1:12306 - R:/127.0.0.1:17424]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"IVWTv6ZXRdSuOCdQLRps4w==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-14 13:40:10,158 [WORLD_ARENA_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [34/0/34/SSRequestEnterArena/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:40:10,160 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [0/0/0/CSPlayerLogin/************] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:40:10,207 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [49/0/49/SSEnterArenaSuccess/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:40:10,210 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [52/0/52/SSUpdateArenaPlayerRank/************] - {"body":{"highRank":1,"isWin":-1},"head":{"len":13,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,779 [LoginProcessorGroup-thread-2] INFO (BaseService.java:110) - [0/282579/282579/SSPlayerLoginReq/************] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":************,"serverId":1,"zoneId":1}}
2024-04-14 13:44:52,782 [IntegralMainThread] INFO (BaseService.java:110) - [1/0/1/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,783 [IntegralMainThread] INFO (BaseService.java:110) - [11/1/12/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,783 [IntegralMainThread] INFO (BaseService.java:110) - [9/0/9/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,783 [IntegralMainThread] INFO (BaseService.java:110) - [1/0/1/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,783 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,784 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,784 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,784 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/ErrorMessage/************] - {"body":{"code":0,"msg":"The network fluctuates, please log in again","opcode":0,"type":2},"head":{"len":51,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,785 [IntegralMainThread] INFO (BaseService.java:110) - [0/1/1/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,785 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,785 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [9/0/9/ErrorMessage/************] - {"body":{"code":0,"msg":"Account login in other devices","opcode":0,"type":3},"head":{"len":38,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,787 [IntegralMainThread] INFO (BaseService.java:110) - [2/0/2/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,787 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,788 [IntegralMainThread] INFO (BaseService.java:110) - [0/1/1/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,791 [IntegralMainThread] INFO (BaseService.java:110) - [2/0/2/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,791 [IntegralMainThread] INFO (BaseService.java:110) - [2/0/2/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,791 [IntegralMainThread] INFO (BaseService.java:110) - [2/0/2/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,793 [IntegralMainThread] INFO (BaseService.java:110) - [1/1/2/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,794 [IntegralMainThread] INFO (BaseService.java:110) - [2/1/3/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-14 13:44:52,807 [LoginProcessorGroup-thread-3] INFO (BaseService.java:110) - [3/2/5/SSAfterMainServerCheck/************] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"isFirstCreated":false,"playerId":************,"resCheckLoginToken":{"allServerRoleNum":1,"expireTime":1712749689,"isPass":1,"playerId":0,"serverRoleStateMap":{},"serverState":0,"subscribeData":{}}},"head":{"len":499,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [24/0/24/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,814 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [17/0/17/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,814 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [2/0/2/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [2/0/2/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [17/0/17/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [2/0/2/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [2/0/2/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [2/0/2/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,816 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:52,817 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [51/0/51/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-14 13:44:53,277 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [448/0/448/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-14 13:44:53,277 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [190/0/190/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
