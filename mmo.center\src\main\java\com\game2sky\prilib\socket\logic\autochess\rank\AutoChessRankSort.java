package com.game2sky.prilib.socket.logic.autochess.rank;

import gnu.trove.map.TLongIntMap;
import gnu.trove.map.hash.TLongIntHashMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.communication.game.autochess.AutoChessRankPlayerInfo;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.integral.sort.BarrelSort;

/**
 * 自走棋玩家的排名排序中心.
 *
 * <AUTHOR>
 * @version v0.1 2019年6月11日 下午5:22:30  Jet Lee
 */
public class AutoChessRankSort extends BarrelSort<AutoChessRankBarrel> {

	/** 
	 * 玩家缓存
	 *  */
	private Map<Long, AutoChessRankPlayerInfo> playerInfos = new HashMap<Long, AutoChessRankPlayerInfo>();

	@Override
	public AutoChessRankBarrel createInstance(Long id) {
		return new AutoChessRankBarrel(id);
	}

	/**
	 * 
	 * @param playerInfo
	 */
	public void addElement(AutoChessRankPlayerInfo playerInfo) {
		long playerId = playerInfo.getDisplayInformation().getPlayerId();
		AutoChessRankPlayerInfo tempPlayerInfo = playerInfos.get(playerId);
		if (tempPlayerInfo != null) {
			if (playerInfo.getScore() == tempPlayerInfo.getScore()) {
				// 重复数据
				playerInfos.put(playerId, playerInfo);
				return;
			} else {
				deleteElement(playerId, tempPlayerInfo.getScore());
			}
		}
		playerInfos.put(playerId, playerInfo);
		AutoChessRankBarrel barrel = addElement((long) playerInfo.getScore());
		barrel.addPlayerId(playerId);
	}

	public boolean deleteElement(long playerId, int score) {
		AutoChessRankPlayerInfo playerInfo = playerInfos.get(playerId);
		if (playerInfo != null) {
			AutoChessRankBarrel barrel = deleteElement((long) score);
			barrel.removePlayerId(playerId);
			playerInfos.remove(playerId);
			return true;
		}
		return false;
	}
	
	public AutoChessRankPlayerInfo getPlayerInfo(long playerId) {
		return playerInfos.get(playerId);
	}

	public int getRank(long playerId, int score) {
		if (score == -1) {
			// 不知道玩家的积分，从缓存里查一下。主要是查看玩家之前排名
			AutoChessRankPlayerInfo playerInfo = playerInfos.get(playerId);
			if (playerInfo != null) {
				score = playerInfo.getScore();
			}
		}
		if (score == -1) {
			return Integer.MAX_VALUE;
		}
		int before = super.getBeforeMe((long) score, true);
		AutoChessRankBarrel barrel = getBarrel((long) score);
		if (barrel == null) {
			return before + 1;
		}
		int rank = barrel.getRank(playerId);
		return before + rank;
	}

	public void initRankList(List<AutoChessRankPlayerInfo> rankList) {
		int maxRank = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_MAX_RANK);
		ArrayList<AutoChessRankBarrel> barrelList = getBarrelList();
		for (int i = barrelList.size() - 1; i >= 0; i--) {
			AutoChessRankBarrel barrel = barrelList.get(i);
			LinkedList<Long> playerIds = barrel.getPlayerIds();
			for (Iterator<Long> iterator = playerIds.iterator(); iterator.hasNext();) {
				Long playerId = (Long) iterator.next();
				AutoChessRankPlayerInfo playerInfo = playerInfos.get(playerId);
				if (playerInfo != null) {
					rankList.add(playerInfo);
				}
				// 添加数量满足排行榜最大数量了，直接返回
				if (rankList.size() >= maxRank) {
					return;
				}
			}
		}
	}
	
	/**
	 * 移除排名靠后的一些玩家
	 * @param removeNum 移除的数量
	 */
	public void removeTailPlayers(int removeNum) {
		int num = 0;
		TLongIntMap map = new TLongIntHashMap();
		for (int i = 0; i < barrelList.size(); i++) {
			if (num > removeNum) {
				break;
			}
			AutoChessRankBarrel autoChessRankBarrel = barrelList.get(i);
			LinkedList<Long> playerIds = autoChessRankBarrel.getPlayerIds();
			for (Long tmpPlayerId : playerIds) {
				num++;
				if (num > removeNum) {
					break;
				}
				map.put(tmpPlayerId, autoChessRankBarrel.getId().intValue());
			}
		}
		long[] players = map.keys();
		if (players == null || players.length < 0) {
			return;
		}
		for (int i = 0; i < players.length; i++) {
			long playerId = players[i];
			int score = map.get(playerId);
			deleteElement(playerId, score);
		}
		
	}
	
	@Override
	public void clear() {
		super.clear();
		playerInfos.clear();
	}

}
