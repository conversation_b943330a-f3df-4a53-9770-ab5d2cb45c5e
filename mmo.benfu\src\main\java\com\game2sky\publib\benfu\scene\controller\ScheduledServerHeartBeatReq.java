package com.game2sky.publib.benfu.scene.controller;

import io.netty.channel.Channel;
import io.netty.util.Attribute;

import com.game2sky.CoreBoot;
import com.game2sky.publib.communication.game.login.inner.SSServerHeartBeatReq;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.netty.support.AMAttributeKey;
import com.game2sky.publib.framework.util.StringUtils;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 服务器定时心跳消息
 *
 * <AUTHOR>
 * @version v0.1 2018年1月11日 下午7:13:01  Administrator
 */
public class ScheduledServerHeartBeatReq extends ScheduledMessage{
	
	public static final long PERIOD = 5000L;
	
	private Channel channel;

	public ScheduledServerHeartBeatReq(Channel channel) {
		super(System.currentTimeMillis());
		this.channel = channel;
	}

	@Override
	public void execute() {
		Attribute<Integer> indexAttr = channel.attr(AMAttributeKey.SS_HEART_INDEX);
		int index = indexAttr == null ? 1 : 1+(StringUtils.getInt(indexAttr.get()));
		long now = System.currentTimeMillis();
		channel.attr(AMAttributeKey.SS_HEART_INDEX).set(index);
		SSServerHeartBeatReq heart = new SSServerHeartBeatReq(index, now);
		Message message = Message.buildByZoneId(0L, 0, heart, null, null, null);
		channel.writeAndFlush(message);
		
		Attribute<String> attr = channel.attr(AMAttributeKey.SCENE_FLAG);
		if(AMLog.LOG_COMMON.isDebugEnabled()) {
			Object target = attr != null ? attr.get() : channel.remoteAddress();
			AMLog.LOG_COMMON.debug("{}--->给server:({})的心跳消息,index={}, channel:{}", CoreBoot.SELF_SERVER_TYPE, target, index, channel);			
		}
	}

}
