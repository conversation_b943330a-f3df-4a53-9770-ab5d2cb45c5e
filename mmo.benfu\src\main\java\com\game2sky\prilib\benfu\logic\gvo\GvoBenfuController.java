package com.game2sky.prilib.benfu.logic.gvo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleRes;
import com.game2sky.prilib.communication.game.gvo.SSGvoEnterBattleRes;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO 大航海
 *
 * <AUTHOR>
 * @version v0.1 2020年10月20日 下午4:56:27  guojijun
 */
@Controller
public class GvoBenfuController {
	@Autowired
	private GvoBenfuService service;

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGvoAllocBattleRes)
	public void ssGvoAllocBattleRes(SSGvoAllocBattleRes ssGvoAlloc<PERSON><PERSON>le<PERSON><PERSON>, Human human) {
		service.ssGvoAllocBattleRes(ssGvoAllocBattleRes, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGvoEnterBattleRes)
	public void ssGvoEnterBattleRes(SSGvoEnterBattleRes ssGvoEnterBattleRes, Human human) {
		service.ssGvoEnterBattleRes(ssGvoEnterBattleRes, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGvoBattleChannelInactive)
	public void ssGvoBattleChannelInactive(Human human) {
		service.ssGvoBattleChannelInactive(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSGvoEnterBattle)
	public void csGvoEnterBattle(Human human) {
		service.csGvoEnterBattle(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSGvoQuit)
	public void csGvoQuit(Human human) {
		service.csGvoQuit(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGvoBattlePlayerQuit)
	public void ssGvoBattlePlayerQuit(Human human) {
		service.ssGvoBattlePlayerQuit(human);
	}
}
