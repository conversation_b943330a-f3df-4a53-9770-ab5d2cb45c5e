package com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight;

import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSBenfuUSDFScheduleMessage;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年12月3日 下午2:01:31  daixl
 */
public class ScheduleUSDFSyncCacheData extends ScheduledMessage {

	public static final int DELAY = 0;
	public static final int PERIOD = 60000;

	private int groupId;

	private int[] zoneIds;

	public ScheduleUSDFSyncCacheData(int groupId, int[] zoneIds) {
		super(System.currentTimeMillis());
		this.groupId = groupId;
		this.zoneIds = zoneIds;
	}

	@Override
	public void execute() {

		for (int zoneId : zoneIds) {
			SSBenfuUSDFScheduleMessage msg = new SSBenfuUSDFScheduleMessage();
			msg.setZoneId(zoneId);
			msg.setGroupId(groupId);
			Message message = Message.buildByZoneId(0L, 0, msg, null, null, null);
			Globals.getMsgProcessDispatcher().put(message);
		}
	}
}
