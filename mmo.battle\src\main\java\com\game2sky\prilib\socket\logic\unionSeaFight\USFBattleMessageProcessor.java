package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.core.socket.logic.unionSeaFight.thread.AbstractUSFMessageProcessor;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.communication.MessageType;
import com.game2sky.publib.framework.thread.ExecutableMessageHandler;
import com.game2sky.publib.framework.thread.QueueMessageProcessor;

/**
 * TODO 工会海战战斗服消息处理器
 *
 * <AUTHOR>
 * @version v0.1 2018年6月22日 下午2:30:26  guojijun
 */
public class USFBattleMessageProcessor extends AbstractUSFMessageProcessor {

	@Override
	protected void init() {
		this.mainProcessor = new QueueMessageProcessor(new ExecutableMessageHandler(), "USFMain");
		USFightTickTask tickTask = new USFightTickTask(System.currentTimeMillis());
		Globals.getScheduleService().scheduleWithFixedDelay(tickTask, 5000, 5000);
	}

	@Override
	public void put(Message msg) {
		if (msg.messageType() == MessageType.UNION_SEA_FIGHT_MAIN_MSG) {
			this.mainProcessor.put(msg);
		} else {
			if (AMLog.LOG_ERROR.isErrorEnabled()) {
				AMLog.LOG_ERROR.error("USFBattleMessageProcessor收到不能处理的消息,msg={}", msg);
			}
		}
	}

}
