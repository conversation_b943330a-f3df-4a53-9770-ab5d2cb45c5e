package com.game2sky.prilib.socket.logic.centerrank.rankmodel;

import gnu.trove.set.hash.TIntHashSet;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerChange;
import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerInfo;
import com.game2sky.prilib.communication.game.entirerank.EntireRankSortData;
import com.game2sky.prilib.communication.game.entirerank.EntireRankType;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankSendSortData;
import com.game2sky.prilib.communication.game.entirerank.SSRefreshEntireRank;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.redis.RedisManager;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.entirerank.EntireRankFactory;
import com.game2sky.prilib.core.socket.logic.entirerank.rankhelper.IEntireRankHelper;
import com.game2sky.prilib.core.util.CommonConstants;
import com.game2sky.prilib.redis.RedisDao;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnect;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnectManager;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.util.TimeUtils;


/**
 * 通用全服排行榜功能中心服数据.
 *
 * <AUTHOR> Lee
 * @version v0.1 2020年2月27日 下午3:27:23  Jet Lee
 */
public class EntireRankCenterModel {
	
	private EntireRankType rankType;
	
	private EntireRankSort rankSort = new EntireRankSort();

	/** 当前排行榜缓存 */
	private List<EntireRankPlayerInfo> topRanks = null;

	/** 排行榜信息是否有变动 */
	private boolean rankChange = false;

	/** 上次刷新排行榜时间 */
	private long lastRefreshRankTime = 0;
	
	/** redis数据是否有变动 */
	private boolean saveDataChange = false;
	
	/** 上次存储数据到redis时间 */
	private long lastSaveRedisTime = 0;
	
	/** 桶排序数据是否有变动 */
	private boolean sortDataChange = false;
	
	/** 上次桶排序数据下发本服时间 */
	private long lastSendSortDataTime = 0;
	
	public EntireRankCenterModel(EntireRankType rankType) {
		this.rankType = rankType;
	}

	public int getTotalNum() {
		return rankSort.getTotal();
	}

	public void init() {
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		int topRankNum = rankHelper.getTopRankNum();
		topRanks = new ArrayList<EntireRankPlayerInfo>(topRankNum);
		try {
			RedisDao dao = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
			EntireRankCache rankCache = dao.valueGetByBlob(rankHelper.getRedisKey(), EntireRankCache.class);
			if (rankCache == null) {
				return;
			}
			List<EntireRankPlayerInfo> rankList = rankCache.getRankList();
			if (CollectionUtils.isEmpty(rankList)) {
				return;
			}
			for (int i = 0; i < rankList.size(); i++) {
				if (i >= topRankNum) {
					break;
				}
				topRanks.add(rankList.get(i));
			}
			addPlayers(rankList);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[EntireRankCenterModel] [init] [error]", e);
		}
	}
	
	public void tick() {
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		boolean needReverse = rankHelper.needReverse();
		// 检测排行桶数量，超过最大值，则移除部分
		int maxNum = rankHelper.getMaxNum();
		if (rankSort.getTotal() > maxNum) {
			maxNum = maxNum * 9 / 10;
			rankSort.removeTailPlayers(rankSort.getTotal() - maxNum, needReverse);
			sortDataChange = true;
		}
		refreshTopRank();
		saveDataToRedis();
		sendSortData();
	}

	private void sendSortData() {
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		if (!rankHelper.isSendSortData()) {
			return;
		}
		if (!sortDataChange) {
			return;
		}
		long now = Globals.getTimeService().now();
		int sendSortDataTime = BaseService.getConfigIntByDefault(PriConfigKeyName.ENTIRE_RANK_SEND_SORT_DATA_TIME);
		if (now - lastSendSortDataTime < sendSortDataTime * TimeUtils.SECOND) {
			return;
		}
		lastSendSortDataTime = now;
		ArrayList<EntireRankBarrel> barrelList = rankSort.getBarrelList();
		List<EntireRankSortData> sortDatas = new ArrayList<EntireRankSortData>(barrelList.size());
		for (EntireRankBarrel barrel : barrelList) {
			sortDatas.add(new EntireRankSortData(barrel.getId().intValue(), barrel.getHaveNumber()));
		}
		// 发送排序数据到本服
		SSEntireRankSendSortData msg = new SSEntireRankSendSortData(rankType, sortDatas);
		sendMsgToAllBenfu(msg);
	}

	private void refreshTopRank() {
		if (!rankChange) {
			return;
		}
		long now = Globals.getTimeService().now();
		int refreshTime = BaseService.getConfigIntByDefault(PriConfigKeyName.ENTIRE_RANK_REFRESH_TIME);
		if (now - lastRefreshRankTime < refreshTime * TimeUtils.SECOND) {
			return;
		}
		lastRefreshRankTime = now;
		// 重置排行
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		boolean needReverse = rankHelper.needReverse();
		topRanks.clear();
		rankSort.initRankList(topRanks, needReverse, rankHelper.getTopRankNum());
		rankChange = false;
		// 通知所有本服，排行榜发生变化
		SSRefreshEntireRank msg = new SSRefreshEntireRank(rankType, topRanks, getTotalNum());
		sendMsgToAllBenfu(msg);
	}
	
	/**
	 * 保存当前赛季排行信息
	 */
	private void saveDataToRedis() {
		if (!saveDataChange) {
			return;
		}
		long now = Globals.getTimeService().now();
		int saveRedisTime = BaseService.getConfigIntByDefault(PriConfigKeyName.ENTIRE_RANK_SAVE_REDIS_TIME);
		if (now - lastSaveRedisTime < saveRedisTime * TimeUtils.SECOND) {
			return;
		}
		lastSaveRedisTime = now;
		// 保存当前排行到redis
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		boolean needReverse = rankHelper.needReverse();
		int saveNum = rankHelper.getSaveNum();
		RedisDao redis = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
		EntireRankCache rankCache = new EntireRankCache();
		List<EntireRankPlayerInfo> rankList = new ArrayList<EntireRankPlayerInfo>(saveNum);
		rankSort.initRankList(rankList, needReverse, saveNum); // 根据桶排序初始化数据
		rankCache.setRankList(rankList);
		try {
			redis.valueSetByBlob(rankHelper.getRedisKey(), rankCache, 0);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("save rank ERROR with rankCache = " + JsonUtils.O2S(rankCache), e);
		}
		saveDataChange = false;
	}
	
	public void addPlayers(List<EntireRankPlayerInfo> players) {
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		int topRankNum = rankHelper.getTopRankNum();
		int saveNum = rankHelper.getSaveNum();
		int maxNum = rankHelper.getMaxNum();
		for (EntireRankPlayerInfo playerInfo : players) {
			PlayerDisplayInformation displayInformation = playerInfo.getDisplayInformation();
			EntireRankPlayerInfo tmpPlayerInfo = rankSort.getPlayerInfo(displayInformation.getPlayerId());
			if (tmpPlayerInfo != null && tmpPlayerInfo.getScore() == playerInfo.getScore()) {
				// 缓存里有这个玩家，并且积分一样。则重新放入缓存并跳过
				rankSort.putPlayerInfo(playerInfo);
				continue;
			}
			long playerId = displayInformation.getPlayerId();
			int oldRank = rankSort.getRank(playerId, -1, rankHelper.needReverse());
			int newRank = rankSort.getRank(playerId, playerInfo.getScore(), rankHelper.needReverse());
			if (newRank > maxNum) {
				continue;
			}
			rankSort.addElement(playerInfo);
			if (oldRank <= topRankNum || newRank <= topRankNum) {
				// 玩家在榜单里
				rankChange = true;
			}
			if (oldRank <= saveNum || newRank <= saveNum) {
				// 玩家在需要存储的数里
				saveDataChange = true;
			}
			sortDataChange = true;
		}
	}
	
	public EntireRankType getRankType() {
		return rankType;
	}
	
	private void sendMsgToAllBenfu(APSHandler msg) {
		ServerConnectManager serverCenter = NetConnectCenter.getInstance().getServerCenter();
		Set<ServerConnect> connectList = new HashSet<ServerConnect>(serverCenter.getServerIdConnects().values());
		for (ServerConnect serverConnect : connectList) {
			ServerTypeEnum clientType = serverConnect.getClientType();
			if (clientType != ServerTypeEnum.BENFU) {
				continue;
			}
			if (serverConnect.getServerIds().isEmpty()) {
				AMLog.LOG_ERROR.error("serverIds is null... Flag=" + serverConnect.getClientFlag());
				continue;
			}
			// 单进程只发送一次
			int serverId = serverConnect.getServerIds().get(0);
			CoreBoot.centerToBenfu(msg, 0, serverId);
			AMLog.LOG_COMMON.info("[EntireRankCenter] [消息名称：{}],发送给服务器组{}", msg.getClass().getSimpleName(),
				serverConnect.getServerIds());
		}
	}

	public void sendTopRankToBenfu(int serverId) {
		SSRefreshEntireRank ss = new SSRefreshEntireRank(rankType, topRanks, getTotalNum());
		CoreBoot.centerToBenfu(ss, 0, serverId);
	}

	public void sendPlayerRankToBenfu(long playerId, List<Integer> serverIds) {
		List<Long> playerIds = new ArrayList<Long>();
		List<Integer> ranks = new ArrayList<Integer>();
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		boolean needReverse = rankHelper.needReverse();
		if (playerId > 0) {
			// 查询单个玩家的排行
			int rank = rankSort.getRank(playerId, -1, needReverse);
			if (rank == Integer.MAX_VALUE) {
				rank = 0;
			}
			playerIds.add(playerId);
			ranks.add(rank);
		} else {
			TIntHashSet serverSet = new TIntHashSet();
			for (Integer serverId : serverIds) {
				serverSet.add(serverId);
			}
			int tempRank = 0;
			List<EntireRankBarrel> barrelList = rankSort.getBarrelList();
			if (needReverse) {
				for (int i = barrelList.size() - 1; i >= 0; i--) {
					EntireRankBarrel barrel = barrelList.get(i);
					LinkedList<Long> tmpPlayerIds = barrel.getPlayerIds();
					for (Iterator<Long> iterator = tmpPlayerIds.iterator(); iterator.hasNext();) {
						tempRank++;
						Long tmpPlayerId = (Long) iterator.next();
						EntireRankPlayerInfo playerInfo = rankSort.getPlayerInfo(tmpPlayerId);
						if (playerInfo != null && serverSet.contains(playerInfo.getDisplayInformation().getServerId())) {
							playerIds.add(tmpPlayerId);
							ranks.add(tempRank);
						}
					}
				}
			} else {
				for (int i = 0; i < barrelList.size(); i++) {
					EntireRankBarrel barrel = barrelList.get(i);
					LinkedList<Long> tmpPlayerIds = barrel.getPlayerIds();
					for (Iterator<Long> iterator = tmpPlayerIds.iterator(); iterator.hasNext();) {
						tempRank++;
						Long tmpPlayerId = (Long) iterator.next();
						EntireRankPlayerInfo playerInfo = rankSort.getPlayerInfo(tmpPlayerId);
						if (playerInfo != null && serverSet.contains(playerInfo.getDisplayInformation().getServerId())) {
							playerIds.add(tmpPlayerId);
							ranks.add(tempRank);
						}
					}
				}
			}
		}
		SSEntireRankPlayerRankRes res = new SSEntireRankPlayerRankRes(rankType, playerIds, ranks);
		CoreBoot.centerToBenfu(res, 0, serverIds.get(0));
	}

	public void sendScoreRankToBenfu(SSEntireRankScoreRankReq msg) {
		int score = msg.getScore();
		int serverId = msg.getServerId();
		// 查询单个玩家的排行
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
		boolean needReverse = rankHelper.needReverse();
		int rank = rankSort.getRankByScore(score, needReverse);
		SSEntireRankScoreRankRes res = new SSEntireRankScoreRankRes(rankType, score, msg.getPlayerId(), serverId, rank);
		CoreBoot.centerToBenfu(res, 0, serverId);
	}

	public void clear() {
		if (topRanks.isEmpty()) {
			// 已经清空过
			return;
		}
		topRanks.clear();
		rankSort.clear();
		// 把下边的bool值置为true，是为了利用这几个标志位，自动通知本服或做其他事情
		rankChange = true;
		saveDataChange = true;
		sortDataChange = true;
		
	}

	public void updatePlayerInfo(List<EntireRankPlayerChange> playerChanges) {
		for (EntireRankPlayerChange playerChange : playerChanges) {
			long playerId = playerChange.getPlayerId();
			EntireRankPlayerInfo playerInfo = rankSort.getPlayerInfo(playerId);
			if (playerInfo == null) {
				continue;
			}
			IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
			boolean needReverse = rankHelper.needReverse();
			int rank = rankSort.getRank(playerId, playerInfo.getScore(), needReverse);
			if (rank > rankHelper.getTopRankNum()) {
				// 没在top榜单
				continue;
			}
			// 刷新部分
            PlayerDisplayInformation displayInfo = playerInfo.getDisplayInformation();
            CommonConstants.refreshPlayerInfo(playerChange.getValues(), displayInfo);
            rankChange = true;
		}
	}


}
