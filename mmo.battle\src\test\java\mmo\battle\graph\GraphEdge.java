package mmo.battle.graph;

import com.game2sky.publib.framework.util.graph.IGraphEdge;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年6月13日 下午3:13:47  Administrator
 */
public class GraphEdge implements IGraphEdge {

	private float len;

	public GraphEdge(float len) {
		this.len = len;
	}

	@Override
	public float getLen() {
		return this.len;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}

}
