package com.game2sky.prilib.socket.logic.integral.data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentBase;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.publib.util.DateUtil;

/**
 * 天梯积分赛基本信息
 * <AUTHOR>
 * @version v0.1 2017年9月11日 下午2:16:02  zhoufang
 */
public class IntegralCenterBase implements Comparable<IntegralCenterBase> {

	/** 当前赛季 */
	private int season;
	/** 当前赛季第几周 */
	private int week;
	/** 本周开始时间 */
	private long weekStartTime;
	/** 赛季结束时间  */
	private long seasonEndTime;
	/** 上赛季的排行榜缓存 */
	private Map<Integer,IntegralAreaRankCache> lastSeasonRankCache = new HashMap<Integer,IntegralAreaRankCache>();
	
	/**
	 * 开始下一周
	 * @return 返回是否新的赛季
	 */
	public boolean nextWeek(int branch) {
		int seasonWeek = IntegralConfigCenter.getSeasonWeekByBranch(branch);
		boolean isNewSeason = false;
		if (this.season == 0) {
			// 第一次开始
			this.season = 1;
			this.seasonEndTime = DateUtil.getIntervalWeek(seasonWeek);
			isNewSeason = true;
		}
		this.week++;
		this.weekStartTime = System.currentTimeMillis();
		if (this.week > seasonWeek) {
			// 开始新赛季
			this.season++;
			this.week = 1;
			this.seasonEndTime = DateUtil.getIntervalWeek(seasonWeek);
			isNewSeason = true;
		}
		return isNewSeason;
	}
	
	/** 开始新赛季 */
	public void nextSeason(int branch){
		int seasonWeek = IntegralConfigCenter.getSeasonWeekByBranch(branch);
		this.season ++;
		this.week = 1;
		this.weekStartTime = System.currentTimeMillis();
		this.seasonEndTime = DateUtil.getIntervalWeek(seasonWeek);
		this.lastSeasonRankCache.clear();
	}

	@Override
	public int compareTo(IntegralCenterBase o) {
		if (this.season > o.season) {
			return -1;
		}
		if (this.season < o.season) {
			return 1;
		}
		if (this.week == o.week){
			return 0;
		}
		return this.week > o.week ? -1 : 1;
	}
	
	public IntegralCurrentBase toProto() {
		IntegralCurrentBase proto = new IntegralCurrentBase();
		proto.setSeason(season);
		proto.setWeek(week);
		proto.setWeekStartTime(weekStartTime);
		proto.setSeasonEndTime(seasonEndTime);
		proto.setLastRanks(new ArrayList<IntegralCurrentRank>());
		for(Integer key : lastSeasonRankCache.keySet()){
			IntegralCurrentRank rank = new IntegralCurrentRank();
			rank.setAreaType(key);
			rank.setRankList(lastSeasonRankCache.get(key).getRankList());
			proto.getLastRanks().add(rank);
		}
		return proto;
	}
	

	public int getSeason() {
		return season;
	}

	public int getWeek() {
		return week;
	}

	public long getWeekStartTime() {
		return weekStartTime;
	}

	public long getSeasonEndTime() {
		return seasonEndTime;
	}
	
	public Map<Integer, IntegralAreaRankCache> getLastSeasonRankCache() {
		return lastSeasonRankCache;
	}
	
	public void setLastSeasonRankCache(Map<Integer, IntegralAreaRankCache> lastSeasonRankCache) {
		this.lastSeasonRankCache = lastSeasonRankCache;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	public void setWeek(int week) {
		this.week = week;
	}

	public void setWeekStartTime(long weekStartTime) {
		this.weekStartTime = weekStartTime;
	}

	public void setSeasonEndTime(long seasonEndTime) {
		this.seasonEndTime = seasonEndTime;
	}

}
