package com.game2sky.prilib.socket.logic.unionSeaFight;

/**
 * TODO 工会海战场景
 *
 * <AUTHOR>
 * @version v0.1 2018年6月12日 下午2:50:37  guojijun
 */
public interface IUSFConsts {
	/**玩家可用阵容数量*/
	int PLAYER_FORMATION_COUNT = 2;
	/**阵营单位集合初始大小*/
	int USF_CAMP_UNITS_INIT_SIZE = 100;
	/**海战使用的场景ID*/
	int USF_SCENE_MAP_ID = 6001;
	/**海战BUFF加成类型之固定值*/
	int USF_BUFF_ADD_TYPE_FIXED = 0;
	/**海战BUFF加成类型之百分比*/
	int USF_BUFF_ADD_TYPE_PERCENT = 1;
	/**海战碰撞检测最大循序次数*/
	int USF_LOOP_MAX_COUNT = 100;
}
