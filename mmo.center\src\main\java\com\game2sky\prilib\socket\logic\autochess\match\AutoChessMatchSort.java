package com.game2sky.prilib.socket.logic.autochess.match;

import java.util.HashMap;
import java.util.Map;

import com.game2sky.prilib.socket.logic.autochess.data.AutoChessMatchPlayer;
import com.game2sky.prilib.socket.logic.integral.sort.BarrelSort;

/**
 * 自走棋匹配玩家的排序中心.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月8日 下午4:23:35  Jet Lee
 */
public class AutoChessMatchSort extends BarrelSort<AutoChessMatchBarrel> {
	
	/** 
	 * 玩家匹配缓存
	 *  */
	private Map<Long, AutoChessMatchPlayer> playerInfos = new HashMap<Long, AutoChessMatchPlayer>();

	@Override
	public AutoChessMatchBarrel createInstance(Long id) {
		return new AutoChessMatchBarrel(id);
	}

	/**
	 * 玩家匹配数据加入排序
	 * @param playerId
	 * @param serverId
	 * @param grade
	 * @param score
	 * @return
	 */
	public boolean addElement(long playerId, int serverId, int grade, int score) {
		AutoChessMatchPlayer playerInfo = playerInfos.get(playerId);
		if (playerInfo != null) {
			// 玩家已经在匹配中。
			return false;
		}
		playerInfos.put(playerId, new AutoChessMatchPlayer(playerId, serverId, grade, score));
		AutoChessMatchBarrel barrel = addElement((long) grade);
		barrel.addPlayerId(playerId);
		return true;
	}
	
	/**
	 * 
	 * @param matchPlayer
	 * @return
	 */
	public boolean addElement(AutoChessMatchPlayer matchPlayer) {
		long playerId = matchPlayer.getPlayerId();
		AutoChessMatchPlayer playerInfo = playerInfos.get(playerId);
		if (playerInfo != null) {
			// 玩家已经在匹配中。
			return false;
		}
		playerInfos.put(matchPlayer.getPlayerId(), matchPlayer);
		AutoChessMatchBarrel barrel = addElement((long) matchPlayer.getGrade());
		barrel.addPlayerId(playerId);
		return true;
	}

	public boolean deleteElement(long playerId, int grade) {
		AutoChessMatchPlayer matchPlayer = playerInfos.get(playerId);
		if (matchPlayer != null) {
			AutoChessMatchBarrel barrel = deleteElement((long) matchPlayer.getGrade());
			barrel.removePlayerId(playerId);
			playerInfos.remove(playerId);
			return true;
		}
		return false;
	}
	
	public AutoChessMatchPlayer getMatchPlayer(long playerId) {
		return playerInfos.get(playerId);
	}

}
