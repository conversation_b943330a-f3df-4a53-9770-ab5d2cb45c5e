package com.game2sky.prilib.socket.logic.integral.ai;

import java.util.concurrent.TimeUnit;

import com.game2sky.publib.schedule.WheelScheduledTask;

/**
 *
 * <AUTHOR>
 * @version v0.1 2020年5月6日 下午3:40:37  zhoufang
 */
public class ScheduledServerDayTask extends WheelScheduledTask {

	public ScheduledServerDayTask(long delay, TimeUnit unit) {
		super(delay, unit);
	}

	@Override
	public void execute() {
		try {
			IntegralAICenter.incrementServerDay();
		} catch (Throwable e) {
			throw e;
		} finally {
			if (this.executedCount == 0) {
				// 第一次执行完毕
				this.delay = 24 * 3600 * 1000L;
			}
		}
	}

}
