package com.game2sky.prilib.socket.logic.integral.ai;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralPlayerChange;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.dict.domain.DictIntegralMirror;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.human.DisplayInformationEnum;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterManager;
import com.game2sky.prilib.socket.logic.integral.data.IntegralArea;
import com.game2sky.prilib.socket.logic.integral.data.IntegralArena;
import com.game2sky.prilib.socket.logic.integral.match.IntegralMatchPlayer;
import com.game2sky.prilib.socket.logic.integral.sort.IntegralSort;
import com.game2sky.prilib.socket.logic.integral.sort.IntegralSort.PlayerSortInfo;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.communication.game.struct.ServerInfo;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.KV;
import com.game2sky.publib.socket.logic.server.benfu.BenfuManagerService;

/**
 * 天梯AI中心
 * <AUTHOR>
 * @version v0.1 2020年4月27日 下午3:50:31  zhoufang
 */
public class IntegralAICenter {

	/** k:serverId  v:服务器信息*/
	private static final ConcurrentHashMap<Integer, AIServer> serverInfos = new ConcurrentHashMap<Integer, AIServer>();
//	/** k:branch  v:serverList */
//	private static final ConcurrentHashMap<Integer, LinkedList<AIServer>> branchServerList = new ConcurrentHashMap<>();
	/** k:playerId v:AI信息*/
	private static final ConcurrentHashMap<Long, IntegralPlayerAIModel> playerAIModels = new ConcurrentHashMap<>();
	/** 老服务器分组 */
	private static List<Integer> oldBranchs = new ArrayList<Integer>();

	private static class AIServer {
		public int serverId;
		public int openDay;
//		public boolean havePlayer;
	}

	public static void load() {
		Collection<ServerInfo> allBenfu = BenfuManagerService.getAllBenfu();
		if (allBenfu.isEmpty()) {
			AMLog.stdout("Failure to get the list of this service server");
			AMLog.LOG_ERROR.error("The connection configuration of this service is empty, check the main service configuration");
			return;
		}
		long now = System.currentTimeMillis();
		for (ServerInfo serverInfo : allBenfu) {
			AIServer aiServer = new AIServer();
			aiServer.serverId = serverInfo.getServerId();
			aiServer.openDay = (int) ((now - serverInfo.getOpenTime()) / (24 * 3600 * 1000L));
			serverInfos.put(aiServer.serverId, aiServer);
			AMLog.LOG_COMMON.info("天梯AI: 服务器[{}]已经开服[{}]天", aiServer.serverId, aiServer.openDay);
		}
		refreshRandomBranchs();
		// 启动定时
		IntegralCommonService.setScheduleService(new IntegralCenterScheduleService());
	}

	public static void incrementServerDay() {
		for (AIServer server : serverInfos.values()) {
			server.openDay += 1;
			AMLog.LOG_COMMON.info("天梯AI: 服务器[{}]已经开服[{}]天", server.serverId, server.openDay);
		}
		refreshRandomBranchs();
	}

	private static void refreshRandomBranchs() {
		// 老服务器分组
		List<Integer> oldBranchs = new ArrayList<Integer>();
		Set<Integer> keySet = IntegralCenterManager.getInstance().getIntegralArenas().keySet();
		for (Integer branch : keySet) {
			List<Integer> serverIdList = IntegralConfigCenter.getBranchMap().get(branch).getServerIdList();
			if (CollectionUtils.isEmpty(serverIdList)) {
				continue;
			}
			if (!isOldServer(serverIdList.get(0))) {
				continue;
			}
			oldBranchs.add(branch);
		}
		IntegralAICenter.oldBranchs = oldBranchs;
		AMLog.LOG_COMMON.info("天梯AI: 当前所有的老服务器分组为:[{}]", oldBranchs);
	}

	public static boolean isOldServer(int serverId) {
		AIServer aiServer = serverInfos.get(serverId);
		if (aiServer == null) {
			return false;
		}
		int oldServerDay = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_OLD_SERVER);
		return aiServer.openDay >= oldServerDay;
	}

	/**
	 * 分配AI
	 * @param matchPlayer
	 * @return
	 */
	public static IntegralMatchPlayer assignRobot(IntegralMatchPlayer matchPlayer) {
		long time1 = System.currentTimeMillis();
		// 本次是否可以分配
		if (!canAssignRobot(matchPlayer)) {
			return null;
		}
		long time2 = System.currentTimeMillis();
		// 先分配镜像
		IntegralMatchPlayer robot = findMirror(matchPlayer);
//		IntegralMatchPlayer robot = new IntegralMatchPlayer(800100025602L, 1000, 10, true);
//		IntegralMatchPlayer robot = new IntegralMatchPlayer(800100031601L, 1000, 10, true);
		long time3 = System.currentTimeMillis();
		long robotId = 0;
		if (robot != null) {
			// TODO 替换几个英雄
			robotId = robot.getPlayerId();
		} else {
			// 镜像分配失败,分配游戏里自动生成的机器人
			robot = findDefaultRobot(matchPlayer);
		}

		long cost = time3 - time1;
		if (cost > 5) {
			AMLog.LOG_UNION.info("@MatchTaskScheduler player.{}, robotId:{}, cost1:{}, cost:{}",
				matchPlayer.getPlayerId(), robotId, (time2 - time1), cost);
		}
		return robot;
	}

	/**
	 * 本次随机机器人是否成功
	 * @param matchPlayer
	 * @return
	 */
	private static boolean canAssignRobot(IntegralMatchPlayer matchPlayer) {
		DictIntegralMirror dictMirror = matchPlayer.getDictMirror();
		if (dictMirror == null) {
			return false;
		}
		IntegralArena integralArena = IntegralCenterManager.getInstance()
			.getIntegralArenaByServerId(matchPlayer.getServerId());
		IntegralArea integralArea = integralArena.getAreas().get(matchPlayer.getAreaType());
		// 需要排行榜最小人数
		int minRankSize = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_MIN_RANKSIZE);
		int haveRankSize = integralArea.getRankSize();
		if (haveRankSize < minRankSize) {
			AMLog.LOG_COMMON.info("Ladder AI: Player [{}] In the area [{}], the robot cannot be allocated, because the current number of people in the ranking is [{}], which is lower than the request[{}]", matchPlayer.getPlayerId(),
				matchPlayer.getAreaType(), haveRankSize, minRankSize);
			return false;
		}

		List<KV<Integer, Integer>> timeRateList = dictMirror.getTimeRateList();
		long interval = System.currentTimeMillis() - matchPlayer.getJoinTime();
		for (int i = 0; i < timeRateList.size(); i++) {
			KV<Integer, Integer> kv = timeRateList.get(i);
			if (matchPlayer.getMatchSeconds().contains(kv.getK())) {
				continue;
			}
			if (interval < kv.getK() * 1000L) {
				continue;
			}
			matchPlayer.getMatchSeconds().add(kv.getK());
			int nextInt = ThreadLocalRandom.current().nextInt(100);
			if (nextInt < kv.getV()) {
				AMLog.LOG_COMMON.info("Player [{}] In the matching area [{}] matching, it has matched the time [{}], random exit weight in the second [{}] seconds [{}], meet the needs [{}], you can allocate AI AI",
					matchPlayer.getPlayerId(), matchPlayer.getAreaType(), interval, kv.getK(), nextInt, kv.getV());
				return true;
			}
			AMLog.LOG_COMMON.info("Player [{}] In the matching area [{}] matching, it has matched the time [{}], random exit weight [{}] in seconds [{}], not satisfied with the needs [{}]", matchPlayer.getPlayerId(),
				matchPlayer.getAreaType(), interval, kv.getK(), nextInt, kv.getV());
		}
		return false;
	}

	private static IntegralMatchPlayer findDefaultRobot(IntegralMatchPlayer matchPlayer) {
		return null;
	}

	/**
	 * 寻找玩家镜像
	 * @param playerId
	 * @param serverId
	 * @return
	 */
	private static IntegralMatchPlayer findMirror(IntegralMatchPlayer matchPlayer) {
		int areaType = matchPlayer.getAreaType();
		// 随机分组
		int randomBranch = randomBranchByServerId(matchPlayer.getServerId());
		if (randomBranch == -1) {
			return null;
		}
		// 可匹配的积分区域
		int minIntegral = matchPlayer.getIntegral() - matchPlayer.getDictMirror().getMinIntegral();
		minIntegral = minIntegral < 0 ? 0 : minIntegral;
		int maxIntegral = matchPlayer.getIntegral() + matchPlayer.getDictMirror().getMaxIntegral();

		// 随机分组的天梯信息
		IntegralArena integralArena = IntegralCenterManager.getInstance().getIntegralArenaByBranch(randomBranch);
		IntegralSort sort = integralArena.getAreas().get(areaType).getSort();
		// 找出积分区间段的玩家
		List<PlayerSortInfo> intervalPlayers = sort.getIntervalPlayers(minIntegral, maxIntegral);
		int intervalSize = intervalPlayers.size();
		if (intervalSize == 0) {
			AMLog.LOG_COMMON.info("天梯AI分配镜像失败.. ..玩家id=[{}]在随机的天梯分组[{}]中所属积分范围[{}]-----[{}]里无法找到玩家",
				matchPlayer.getPlayerId(), randomBranch, minIntegral, maxIntegral);
			return null;
		}
		// 本人的战力信息
		IntegralPlayerAIModel aiModel = playerAIModels.get(matchPlayer.getPlayerId());
		if (aiModel == null) {
			AMLog.LOG_ERROR.error("天梯AI: 缺少匹配者[{}]的阵容[{}]战力数据", matchPlayer.getPlayerId(), areaType);
			return null;
		}
		long myFightPower = aiModel.getFightPower(areaType);
		if (myFightPower == 0) {
			AMLog.LOG_ERROR.error("天梯AI: 缺少匹配者[{}]的阵容[{}]战力数据", matchPlayer.getPlayerId(), areaType);
			return null;
		}
		// 本人的可匹配的战力区间
		long maxPower = myFightPower + matchPlayer.getDictMirror().getMaxFight();
		long minPower = myFightPower - matchPlayer.getDictMirror().getMinFight();
		// 检测积分段玩家,战力控制
		List<PlayerSortInfo> randomList = new ArrayList<PlayerSortInfo>();
		for (PlayerSortInfo playerInfo : intervalPlayers) {
			if (playerInfo.getServerId() == matchPlayer.getServerId()) {
				// 不分配同服玩家
				continue;
			}
			long mirrorPower = getPlayerAIFightPower(playerInfo.getPlayerId(), areaType);
			if (mirrorPower == 0) {
				continue;
			}
			if (mirrorPower > matchPlayer.getDictMirror().getNormalFight()) {
				continue;
			}
			if (mirrorPower > maxPower || mirrorPower < minPower) {
				continue;
			}
			randomList.add(playerInfo);
		}
		if (randomList.isEmpty()) {
			AMLog.LOG_COMMON.info(
				"天梯AI分配镜像失败.. ..玩家id=[{}]在随机的天梯分组[{}]中所属积分范围[{}]-----[{}]里找出玩家[{}]个, 但是没有符合本人战力[{}]的玩家",
				matchPlayer.getPlayerId(), randomBranch, minIntegral, maxIntegral, intervalSize, myFightPower);
			return null;
		}
		// 随机一个玩家
		int nextInt = ThreadLocalRandom.current().nextInt(randomList.size());
		PlayerSortInfo playerSortInfo = randomList.get(nextInt);

		AMLog.LOG_COMMON.info(
			"天梯AI分配镜像成功.. ..玩家id=[{}]在随机的天梯分组[{}]中所属积分范围[{}]-----[{}]里找出玩家[{}]个, 随机分配到来自服务器[{}]下的玩家[{}]镜像,对应积分为[{}]",
			matchPlayer.getPlayerId(), randomBranch, minIntegral, maxIntegral, randomList.size(),
			playerSortInfo.getServerId(), playerSortInfo.getPlayerId(), playerSortInfo.getIntegral());
		return new IntegralMatchPlayer(playerSortInfo.getPlayerId(), playerSortInfo.getIntegral(),
			playerSortInfo.getServerId(), true);
	}

	/**
	  *  根据serverId随机寻找一个分组
	 * @param serverId
	 * @return
	 */
	private static int randomBranchByServerId(int serverId) {
		int branch = IntegralConfigCenter.getIntegralBranchByServerId(serverId);
		return randomBranch(branch);
	}

	private static int randomBranch(int branch) {
		if (!oldBranchs.contains(branch)) {
			AMLog.LOG_COMMON.info("分组[{}]为新服务器,不参与随机", branch);
			return branch;
		}
		if (oldBranchs.size() == 1) {
			AMLog.LOG_COMMON.info("分组[{}]为老服务器,可随机的分组列表为空, 无法随机", branch);
			return -1;
		}
		int randomIndex = ThreadLocalRandom.current().nextInt(oldBranchs.size() - 1);
		int randomBranch = oldBranchs.get(randomIndex);
		if (randomBranch == branch) {
			randomBranch = oldBranchs.get(oldBranchs.size() - 1);
		}
		AMLog.LOG_COMMON.info("分组[{}]为老服务器,可随机的分组列表为[{}],从中随机出分组[{}]", branch, oldBranchs,
			randomBranch);
		return randomBranch;
	}

	/**
	 * 记录玩家AI信息
	 * @param serverId
	 * @param playerInfos
	 */
	public static void addPlayerAIInfo(int serverId, List<IntegralCenterPlayerInfo> playerInfos) {
		for (IntegralCenterPlayerInfo playerInfo : playerInfos) {
			IntegralPlayerAIModel aiModel = playerAIModels.get(playerInfo.getPlayerId());
			if (aiModel == null) {
				aiModel = new IntegralPlayerAIModel(playerInfo);
				playerAIModels.put(playerInfo.getPlayerId(), aiModel);
			} else {
				aiModel.addAreaInfo(playerInfo);
			}
		}
//		serverHavePlayer(serverId);
	}

	/**
	 * 修改玩家AI信息
	 * @param player
	 */
	public static void refreshPlayerAIInfo(IntegralPlayerChange player) {
		IntegralPlayerAIModel aiModel = playerAIModels.get(player.getPlayerId());
		if (aiModel == null) {
			return;
		}
		List<DisplayInformationType> values = player.getValues();
		for (DisplayInformationType type : values) {
			if (type.getType() == DisplayInformationEnum.PRACTICE_FIGHTPOWER.getIndex()) {
				long fightPower = Long.parseLong(type.getValue());
				aiModel.refreshAreaFightPower(IntegralAreaType.PRACTICE.value(), fightPower);
			} else if (type.getType() == DisplayInformationEnum.PINNACLE_FIGHTPOWER.getIndex()) {
				long fightPower = Long.parseLong(type.getValue());
				aiModel.refreshAreaFightPower(IntegralAreaType.PINNACLE.value(), fightPower);
			}
		}
	}

	/**
	 *  获取玩家参与AI分配的阵容战力
	 * @param playerId
	 * @return
	 */
	public static long getPlayerAIFightPower(long playerId, int areaType) {
		IntegralPlayerAIModel aiModel = playerAIModels.get(playerId);
		if (aiModel == null) {
			return 0;
		}
		long playerFightPower = aiModel.getFightPower(areaType);
		if (playerFightPower == 0) {
//			AMLog.LOG_ERROR.error("天梯AI: 缺少玩家[{}]的阵容[{}]战力数据", playerId, areaType);
		}
		return playerFightPower;
	}

//	public static void serverHavePlayer(int serverId) {
//		AIServer aiServer = serverInfos.get(serverId);
//		if (aiServer == null) {
//			AMLog.LOG_ERROR.error("没有服务器{}记录", serverId);
//			return;
//		}
//		if (!aiServer.havePlayer) {
//			aiServer.havePlayer = true;
//			AMLog.LOG_COMMON.info("天梯AI: 服务器[{}]有人参与天梯, 此分组可以参与随机分配", serverId);
//		}
//	}

//	public static void recordMatchRobotRecord(IntegralMatchPlayer player, long robotId) {
//		IntegralArena integralArena = IntegralCenterManager.getInstance()
//			.getIntegralArenaByServerId(player.getServerId());
//		integralArena.recordMatchRobotRecord(player.getPlayerId(), player.getAreaType(), robotId);
//	}
//
//	public static boolean hasMatchedRobotId(IntegralMatchPlayer player, long robotId) {
//		IntegralArena integralArena = IntegralCenterManager.getInstance()
//			.getIntegralArenaByServerId(player.getServerId());
//		return integralArena.hasMatchedRobotId(player.getPlayerId(), player.getAreaType(), robotId);
//	}

}
