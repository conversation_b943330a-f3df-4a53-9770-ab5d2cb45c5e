<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD//EN" "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping package="com.game2sky.op.dbs.model">
	<!-- 竞技场开始 -->
	<query name="queryAllPlayerRank">from InstArena</query>
	
	<query name="queryCbtPayData">from CbtPay</query>

	<query name="queryPlayerStudioData">from InstPlayerStudio</query>

	<!-- 背包 -->
	<sql-query name="queryContainerDataSize">
		select count(1) from inst_container_data
	</sql-query>
	<sql-query name="queryContainerDataInfo">
		<return class="com.game2sky.publib.dbs.model.InstContainerData"></return>
		select playerId,taskBagData,storageData,itemData,itemBagData,equipBagData,gemBagData,materialBagData,tatterBagData,modifyTime from inst_container_data limit :from, :count
	</sql-query>
	
	<!-- 活动 -->
	<sql-query name="queryShopActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityShop" />
		select  id, activityId, typeItem, discount,modifyTime from activity_shop a where  activityId=:activityId
	</sql-query>
	<sql-query name="queryMonthlySignInActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityMonthlySignIn" />
		select  id, activityId, isImportant, year, month, day, reward, rewardTxt,modifyTime from activity_monthly_signIn a where  activityId=:activityId
	</sql-query>
	<sql-query name="queryActivityHolidayActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityHoliday" />
		select  id, activityId, holidayType,modifyTime from activity_holiday a where  activityId=:activityId
	</sql-query>
	<sql-query name="queryActivityLuckyCardActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityLuckyCard" />
		select  id, activityId, shopId ,awards,rewardTxt,weight,shopType,modifyTime from activity_luckyCard a where  activityId=:activityId
	</sql-query>
	<sql-query name="queryTurnTableActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityTurnTable" />
		select  id, activityId, turnTableType,modifyTime from activity_turntable a where  activityId=:activityId
	</sql-query>
	<sql-query name="getActivityAwardConfigsByActivityId">
		<return class="com.game2sky.prilib.dbs.model.InstActivityAwardConfig" />
		select  id, activityId, target, awards, description, sort, addTime, rewardTxt , receiveLimit,modifyTime,conditionType,conditionParam,awardType,conditionTxt,refreshType,numRandom,targetTxt,weight, attachActivity from inst_activity_award_config a where  activityId=:activityId
	</sql-query>
	<sql-query name="removeActivityAwardConfigsByActivityId">
		DELETE FROM inst_activity_award_config WHERE activityId=:activityId
	</sql-query>
	 <sql-query name="queryRankActivityInfoByEndTime">
       <return class="com.game2sky.prilib.dbs.model.InstRecordActivityRank" />
        select guid, activityId, serverId, activityType,rankList, showEndTime,modifyTime from inst_record_activity_rank 
        where showEndTime &gt;= :time and serverId =:serverId order by showEndTime DESC
    </sql-query>
	<!-- configCenter -->
	<sql-query name="InstServerConfigAll">
		<return class="com.game2sky.prilib.dbs.model.InstServerConfig" />
		select configKey, configValue,modifyTime from inst_server_config
	</sql-query>
	<sql-query name="queryAllInstBountyHuntOrderData">
		<return class="com.game2sky.prilib.dbs.model.InstBountyHuntOrderData" />
		select bountyHuntOrderId,createPlayerId,bountyPlayerId,createTime,reward,claimPlayerIds,finishedPlayerId,claimTime,state,finishTime,isDelete,modifyTime 
		from inst_bounty_hunt_order_data
		where isDelete = 0
	</sql-query>
	<!-- 工会 -->
	<sql-query name="queryAllUnion">
		<return class="com.game2sky.prilib.dbs.model.InstUnion" />
		SELECT unionId,unionName,unionNotice,unionDeclaration,unionType,unionPresident,unionMemberNum,unionExp,unionLevel,createTime,unionFightPower,modifyTime FROM inst_union
	</sql-query>
	<sql-query name="queryAllUnionApply">
		<return class="com.game2sky.prilib.dbs.model.InstUnionApply" />
		SELECT id,playerId,unionId,applyTime,applyState,modifyTime FROM inst_union_apply where unionId =:unionId and applyState = 0
	</sql-query>
	<!-- 
	<sql-query name="queryUnionByUnionName">
		<return class="com.game2sky.prilib.dbs.model.InstUnion" />
		SELECT unionId,unionName,unionNotice,unionType,unionPresident,unionMemberNum,unionExp,unionLevel,createTime,unionFightPower,modifyTime FROM inst_union where unionName like :unionName
	</sql-query>
	 -->
	<sql-query name="removeAllUnionApply">
		DELETE FROM inst_union_apply WHERE unionId =:unionId
	</sql-query>
	<sql-query name="queryAllUnionRedPacket">
		<return class="com.game2sky.prilib.dbs.model.InstUnionApply" />
		SELECT id,unionId,playerId,sendTime,endTime,redPacketId,getState,sendContent,redPacketInfo,modifyTime FROM inst_union_red_packet where unionId =:unionId 
	</sql-query>
	<sql-query name="removeAllUnionRedPacket">
		DELETE FROM inst_union_red_packet WHERE unionId =:unionId
	</sql-query>
	<sql-query name="queryAllUnionMember">
		<return class="com.game2sky.prilib.dbs.model.InstUnionMember" />
		SELECT playerId,unionId,roleId,contribution,joinTime,unionTaskState,usfAutoJoin,usfJoinTime,usfTrustNum,usfFightNum,usfFormationInfos,modifyTime FROM inst_union_member where unionId =:unionId
	</sql-query>
	<sql-query name="queryAllUnionBlag">
		<return class="com.game2sky.prilib.dbs.model.InstUnionBlag" />
		SELECT playerId,unionId,blagTime,blagHero,blagTotalNum,blagCurNum,givePlayer,modifyTime FROM inst_union_blag where unionId =:unionId
	</sql-query>
	<sql-query name="queryAllUnionShopLimit">
		<return class="com.game2sky.prilib.dbs.model.InstUnionShopLimitData" />
		SELECT id,unionId,goodsIndex,refreshTime,curTimes,playerInfo,modifyTime FROM inst_unionshop_limitdata where unionId =:unionId
	</sql-query>
	<sql-query name="deleteAllUnionShopLimit">
		DELETE FROM inst_unionshop_limitdata WHERE unionId =:unionId
	</sql-query>
	<!-- BI -->
	<sql-query name="addInstBiStatisticsPlayer">
		INSERT INTO inst_bi_statistics_player(playerId,addTime) VALUES(:playerId,now())
	</sql-query>
	<sql-query name="listInstBiStatisticsPlayer">
		<return class="com.game2sky.prilib.dbs.model.InstBiStatisticsPlayer" />
		SELECT  playerId,modifyTime FROM inst_bi_statistics_player a
	</sql-query>
	<sql-query name="queryAllInstExeMail">
		<return class="com.game2sky.prilib.dbs.model.InstExeMail" />
		SELECT  id, playerId, isGlobal, type, data,createTime,modifyTime,isDelete FROM inst_exe_mail a where a.isDelete = 0 and a.isGlobal = 1
	</sql-query>
	
	<sql-query name="queryOnePlayerExeMail">
		<return class="com.game2sky.prilib.dbs.model.InstExeMail" />
		SELECT  id, playerId, isGlobal, type, data,createTime,modifyTime,isDelete FROM inst_exe_mail a where a.playerId = :playerId and a.isDelete = 0
	</sql-query>
	
	<sql-query name="queryAllPrisonData">
		<return class="com.game2sky.prilib.dbs.model.InstPrisonData" />
		SELECT  playerId, prisonOutTime, inPrisonTime, guaranteedMePlayers,requestGuaranteedMePlayers,helpFightMePlayers, prisonId, killNum,wardenWinCount,logoutTime,modifyTime from inst_prison_data a
	</sql-query>
	
	<sql-query name="deleteFightReplay">
		delete from inst_fight_replay where
		createTime
		&lt; :createTime
	</sql-query>
	
	<sql-query name="deleteBountyHuntOrderData">
		delete from inst_bounty_hunt_order_data where
		createTime
		&lt; :createTime and isDelete = 1;
	</sql-query>
	
		<sql-query name="deleteExeMailData">
		delete from inst_exe_mail where
		createTime
		&lt; :createTime and isDelete = 1;
	</sql-query>
	
	<!-- BI -->
	
	<sql-query name="getValidSysAdvertisements" >
		<return class="com.game2sky.prilib.dbs.model.InstSysAdvertisement" />
		select a.id,a.type,a.url,a.pageName,a.pageArgs,a.startTime,a.endTime,a.addTime,a.modifyTime,a.channelIds,a.requiredPlayerLevel,a.showType,a.sort
		 from inst_sys_advertisement a 
	   where a.endTime &gt;= :curTime
	</sql-query>
	
	<sql-query name="queryDriftBottleConfig" >
		<return class="com.game2sky.prilib.dbs.model.ActivityDriftBottleConfig" />
		select a.guid,a.activityId,a.bottleId,a.isRare,a.refreshWeight,a.drawWeight,a.retrunRewards,a.rewards,a.modifyTime
		 from activity_drift_bottle_config a 
	   where a.activityId = :activityId
	</sql-query>
	
		<sql-query name="queryDriftBottleShare" >
		<return class="com.game2sky.prilib.dbs.model.ActivityDriftBottleShare" />
		select a.orderId,a.sharePlayerId,a.shareTime,a.activityId,a.bottleGuid,a.buyPlayerId,a.buyTime,a.hasThank,a.hasGetReturn,a.modifyTime
		 from activity_drift_bottle_share a 
	   where a.activityId = :activityId
	</sql-query>
	
		<sql-query name="queryActivityGiftProduct" >
		<return class="com.game2sky.prilib.dbs.model.ActivityGiftProductConfig" />
		select a.giftId,a.giftName,a.activityId,a.costType,a.price,a.labelType,a.discount,a.limitType,a.limitNum,a.serverLimitNum,a.refreshType,a.icon,a.icon,a.startTime,a.endTime,a.rewards,a.serverNumMap,a.rewardTxt,a.sort,a.modifyTime
		 from activity_gift_product_config a 
	   where a.activityId = :activityId
	</sql-query>
	
	<sql-query name="querySpecialRecruitConfig" >
        <return class="com.game2sky.prilib.dbs.model.ActivitySpecialRecruitConfig" />
        select a.activityAwardId,a.activityId,a.itemList,a.startTime,a.endTime, a.description, a.openExchange, a.modifyTime 
         from activity_special_recruit_config a 
       where a.activityId = :activityId
    </sql-query>
    
    <sql-query name="queryCommonFightDataByType" >
        <return class="com.game2sky.prilib.dbs.model.InstCommonFightData" />
       select a.id,a.playerId,a.formationType,a.fightData,a.modifyTime from inst_common_fight_data a
        where formationType = :formationType and playerId = :playerId
    </sql-query>
    
    <sql-query name="delSpecialRecruitConfig">
        DELETE FROM activity_special_recruit_config WHERE activityId=:activityId
    </sql-query>
	
	<sql-query name="queryHeroFashionByPlayerId">
	<return class="com.game2sky.prilib.dbs.model.InstHeroFashion" />
        select a.id,a.playerId,a.fashionId,a.heroId,a.unlockTime,a.isEquip,a.modifyTime from inst_hero_fashion a WHERE a.playerId = :playerId
    </sql-query>
    
    	<sql-query name="queryAllInstUnionSeaFightReplay">
	<return class="com.game2sky.prilib.dbs.model.InstUnionSeaFightReplay" />
        select a.replayId,a.leftUnionId,a.leftUnionName,a.leftServerId,a.leftWin,a.rightUnionId,a.rightUnionName,a.rightServerId,a.replayUrl,a.fightTime,a.modifyTime from inst_union_sea_fight_replay a
    </sql-query>
    
    <sql-query name="getActivityTimeLimitCollectionConfigsByActivityId">
		<return class="com.game2sky.prilib.dbs.model.InstActivityTimeLimitCollectionConfig" />
		select a.id,a.activityId,a.startTime,a.endTime,a.refreshInterval,a.promptContent,a.modifyTime from inst_activity_time_limit_collection_config a where activityId=:activityId
	</sql-query>
	<sql-query name="removeActivityTimeLimitCollectionConfigsByActivityId">
		DELETE FROM inst_activity_time_limit_collection_config WHERE activityId=:activityId
	</sql-query>
	<sql-query name="queryActivityUnionRepairActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityUnionRepair" />
		select  id, activityId, progress,awards,rewardTxt,modifyTime from activity_union_repair a where  activityId=:activityId
	</sql-query>
	
	<sql-query name="queryAdditionalDropConfig">
        <return class="com.game2sky.prilib.dbs.model.ActivityAdditionalDropConfig" />
        select
        a.activityAwardId,a.activityId,a.dropType,a.timeLimit,a.activityData,
        a.sort, a.refreshType,a.description, a.modifyTime
        from activity_additional_drop_config a
        where a.activityId = :activityId
    </sql-query>

    <sql-query name="delAdditionalDropConfig">
        DELETE FROM activity_additional_drop_config
        WHERE activityId=:activityId
    </sql-query>
    
    <sql-query name="queryPlayerNumByDeviceId">
		select count(playerId) from inst_player_device_info
		where deviceId = :deviceId
	</sql-query>
	
	<sql-query name="getInstActivityExtractEquipsByActivityId">
		<return class="com.game2sky.prilib.dbs.model.InstActivityExtractEquip" />
		SELECT a.id,a.activityId,a.grade,a.poolType,a.equipWeights,a.modifyTime,a.awards,a.rewardTxt FROM inst_activity_extract_equip a WHERE a.activityId=:activityId
	</sql-query>
	
	<sql-query name="removeInstActivityExtractEquipsByActivityId">
		DELETE FROM inst_activity_extract_equip WHERE activityId=:activityId
	</sql-query>
	<sql-query name="queryAllInstPlayerAutoChess">
		<return class="com.game2sky.prilib.dbs.model.InstPlayerAutoChess" />
		select playerId,serverId,grade,score,weekFightCount,seasonFightCount,historyMaxScore,weekRewardCoin,shipId,modifyTime 
		from inst_player_auto_chess
	</sql-query>
	<sql-query name="queryTreasureseaActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityTreasureSeaConfig" />
		select  id, activityId, rewardPool,modifyTime from activity_treasuresea_config a where  activityId=:activityId
	</sql-query>
	
	<sql-query name="queryAllUnionPray">
		<return class="com.game2sky.prilib.dbs.model.InstUnionPray" />
		SELECT playerId,unionId,prayTime,prayPlayerName,prayFragId,prayFragNum,getNum,givePlayer,modifyTime FROM inst_union_pray where unionId =:unionId
	</sql-query>
	
	<sql-query name="queryIdRangePlayer">
		select playerId from inst_player where playerId &gt;= :fromPlayerId and playerId &lt;= :toPlayerId and level &gt;= 25
	</sql-query>
	
	<sql-query name="queryIndexRangePlayer">
		select playerId from inst_player where isRobot = 0 and level &gt;= 25 limit :fromIndex, :count
	</sql-query>
	
	
	<sql-query name="queryActivePlayer">
		select playerId from inst_player where lastLoginTime &gt;= :activeTime
	</sql-query>
	
	<sql-query name="queryPlayerUnnalShareCount">
		select count(1) from inst_player where isRobot = 0 and level &gt;= 25
	</sql-query>
	
	<sql-query name="queryUnnalShareCount">
		select count(1) from inst_unnal_share
	</sql-query>
	
	<sql-query name="queryOptionalRecruitConfig" >
		<return class="com.game2sky.prilib.dbs.model.ActivityOptionalRecruitConfig" />
		select a.activityAwardId,a.activityId,a.heroIds, a.imageUrl, a.heroDesc, a.modifyTime 
		from activity_optional_recruit_config a 
		where a.activityId = :activityId
	</sql-query>
	
	<sql-query name="delOptionalRecruitConfig">
		DELETE FROM activity_optional_recruit_config WHERE activityId=:activityId
	</sql-query>
	
	<sql-query name="queryActivityLimitBuyEquipByActivityId">
		<return class="com.game2sky.prilib.dbs.model.ActivityLimitBuyEquip" />
		SELECT a.id,a.activityId,a.type,a.probUpEquips,a.extra,a.modifyTime FROM activity_limit_buy_equip a WHERE a.activityId=:activityId
	</sql-query>
	
	<sql-query name="removeActivityLimitBuyEquipByActivityId">
		DELETE FROM activity_limit_buy_equip WHERE activityId=:activityId
	</sql-query>
	
	<sql-query name="queryAllFightPlayer">
		<return class="com.game2sky.prilib.dbs.model.InstSecretPlaceFightPlayer" />
		select playerId, openId, score, isFight, 
		chapterGroupInfo, scoreReward,modifyTime 
		from inst_secret_place_fight_player
		WHERE openId=:openId and isFight = 1
	</sql-query>

</hibernate-mapping>