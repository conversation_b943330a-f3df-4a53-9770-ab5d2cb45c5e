package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffProInfo;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler.IUSFBuffHandler;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler.USFBuffHandlerAffectFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler.USFBuffHandlerAffectOther;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler.USFBuffHandlerNormal;
import com.game2sky.publib.framework.util.MathUtils;

/**
 * buff工厂.
 *
 * <AUTHOR>
 * @version v0.1 2018年10月22日 下午3:45:23  lidong
 */
public class USFBuffFactory {

	public static IUSFBuffHandler getBuffHandle(USFBuffType buffType) {
		switch (buffType) {
			case USF_BUFF_TYPE_NORMAL:
				return USFBuffHandlerNormal.getInstance();
			case USF_BUFF_TYPE_AFFECT_OTHER:
				return USFBuffHandlerAffectOther.getInstance();
			case USF_BUFF_TYPE_AFFECT_FORTRESS:
				return USFBuffHandlerAffectFortress.getInstance();

			default:
				break;
		}
		return null;
	}
	
	public static void addExtraProps(AbstractUSFUnit owner, List<USFClientBuffProInfo> proInfos) {
		if (CollectionUtils.isEmpty(proInfos)) {
			return;
		}
		float extraValue = owner.getSkillController().getExtraValue();
		float extraPercent = owner.getSkillController().getExtraPercent();
		if (!MathUtils.floatEquals(extraValue, 0) || !MathUtils.floatEquals(extraPercent, 0)) {
			// 有加成
			for (USFClientBuffProInfo usfClientBuffProInfo : proInfos) {
				float addValue = usfClientBuffProInfo.getAddValue();
				addValue = (addValue + extraValue) * (1 + extraPercent);
				usfClientBuffProInfo.setAddValue(addValue);
			}
		}
	}
}
