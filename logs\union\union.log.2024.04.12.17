2024-04-12 17:42:53,993 [main] INFO  (DictUnionSeaFightServerGroup.java:85) - ???????????????
2024-04-12 17:42:54,015 [main] INFO  (DictUnionSeaFightTime.java:60) - ????????????
2024-04-12 17:42:54,025 [main] INFO  (DictUnionSeaFightActivity.java:62) - ????????????
2024-04-12 17:42:58,294 [main] INFO  (AbstractUnionSeaFightService.java:30) - @UnionSeaFight Initialize naval battle activities configuration, serverIds:[1]
2024-04-12 17:42:58,295 [main] INFO  (UnionSeaFightActivityData.java:41) - @UnionSeaFight Start cleaning up the naval configuration of this issue, DictUnionSeaFightServerGroup.1, DictUSFActivity.6
2024-04-12 17:42:58,296 [main] INFO  (UnionSeaFightActivityData.java:116) - @UnionSeaFight Generate costs of naval battle activity data, DictUSFActivity.6, Activity time?{ DictActivityId: 6, subActivityId:0, ready:true, subActivityNum: 1, applyStartTime: 2024-04-12 00:00:01, applyEndTime: 2024-04-12 23:59:11, matchTime: 2024-04-13 00:00:01, playerJoinTime: 2024-04-13 09:00:01, clearTime: 2024-04-13 22:15:01, activityEndTime: 2024-04-13 22:17:01 }
2024-04-12 17:42:58,296 [main] INFO  (AbstractUnionSeaFightService.java:68) - @UnionSeaFight Initialization of naval battle activities grouping, groupId:1, serverIds:{500,499,498,497,496,495,494,493,492,491,490,489,488,487,486,485,484,483,482,481,480,479,478,477,476,475,474,473,472,471,470,469,468,467,466,465,464,463,462,461,460,459,458,457,456,455,454,453,452,451,450,449,448,447,446,445,444,443,442,441,440,439,438,437,436,435,434,433,432,431,430,429,428,427,426,425,424,423,422,421,420,419,418,417,416,415,414,413,412,411,410,409,408,407,406,405,404,403,402,401,400,399,398,397,396,395,394,393,392,391,390,389,388,387,386,385,384,383,382,381,380,379,378,377,376,375,374,373,372,371,370,369,368,367,366,365,364,363,362,361,360,359,358,357,356,355,354,353,352,351,350,349,348,347,346,345,344,343,342,341,340,339,338,337,336,335,334,333,332,331,330,329,328,327,326,325,324,323,322,321,320,319,318,317,316,315,314,313,312,311,310,309,308,307,306,305,304,303,302,301,300,299,298,297,296,295,294,293,292,291,290,289,288,287,286,285,284,283,282,281,280,279,278,277,276,275,274,273,272,271,270,269,268,267,266,265,264,263,262,261,260,259,258,257,256,255,254,253,252,251,250,249,248,247,246,245,244,243,242,241,240,239,238,237,236,235,234,233,232,231,230,229,228,227,226,225,224,223,222,221,220,219,218,217,216,215,214,213,212,211,210,209,208,207,206,205,204,203,202,201,200,199,198,197,196,195,194,193,192,191,190,189,188,187,186,185,184,183,182,181,180,179,178,177,176,175,174,173,172,171,170,169,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,146,145,144,143,142,141,140,139,138,137,136,135,134,133,132,131,130,129,128,127,126,125,124,123,122,121,120,119,118,117,116,115,114,113,112,111,110,109,108,107,106,105,104,103,102,101,100,99,98,97,96,95,94,93,92,91,90,89,88,87,86,85,84,83,82,81,80,79,78,77,76,75,74,73,72,71,70,69,68,67,66,65,64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,42,41,39,38,37,36,35,34,33,32,31,30,29,28,27,26,25,24,23,22,21,19,18,17,16,15,14,13,12,11,10,9,8,6,5,3,2,1}, usfData:{ DictActivityId: 6, subActivityId:0, ready:true, subActivityNum: 1, applyStartTime: 2024-04-12 00:00:01, applyEndTime: 2024-04-12 23:59:11, matchTime: 2024-04-13 00:00:01, playerJoinTime: 2024-04-13 09:00:01, clearTime: 2024-04-13 22:15:01, activityEndTime: 2024-04-13 22:17:01 }
2024-04-12 17:42:58,297 [main] INFO  (BenfuUnionSeaFightService.java:237) - @UnionSeaFight ???????????
2024-04-12 17:42:59,334 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:42:59,336 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:42:59,336 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:19,302 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:43:19,302 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:19,302 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:39,318 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:43:39,318 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:39,318 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:59,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:43:59,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:43:59,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:19,320 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:44:19,320 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:19,320 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:39,300 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:44:39,300 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:39,300 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:59,324 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:44:59,324 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:44:59,324 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:19,338 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:45:19,338 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:19,338 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:39,311 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:45:39,311 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:39,311 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:59,332 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:45:59,332 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:45:59,332 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:19,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:46:19,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:19,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:39,301 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:46:39,301 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:39,301 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:59,317 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:46:59,317 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:46:59,317 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:19,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:47:19,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:19,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:39,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:47:39,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:39,305 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:59,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:47:59,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:47:59,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:19,344 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:48:19,344 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:19,344 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:39,327 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:48:39,327 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:39,327 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:59,306 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:48:59,306 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:48:59,306 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:19,335 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:49:19,335 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:19,335 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:39,299 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:49:39,299 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:39,299 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:59,304 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:49:59,304 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:49:59,304 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:19,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:50:19,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:19,329 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:39,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:50:39,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:39,343 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:59,310 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:50:59,310 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:50:59,310 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:51:34,209 [main] INFO  (DictUnionSeaFightServerGroup.java:85) - ???????????????
2024-04-12 17:51:34,222 [main] INFO  (DictUnionSeaFightTime.java:60) - ????????????
2024-04-12 17:51:34,231 [main] INFO  (DictUnionSeaFightActivity.java:62) - ????????????
2024-04-12 17:51:37,854 [main] INFO  (AbstractUnionSeaFightService.java:30) - @UnionSeaFight Initialize naval battle activities configuration, serverIds:[1]
2024-04-12 17:51:37,855 [main] INFO  (UnionSeaFightActivityData.java:41) - @UnionSeaFight Start cleaning up the naval configuration of this issue, DictUnionSeaFightServerGroup.1, DictUSFActivity.6
2024-04-12 17:51:37,856 [main] INFO  (UnionSeaFightActivityData.java:116) - @UnionSeaFight Generate costs of naval battle activity data, DictUSFActivity.6, Activity time?{ DictActivityId: 6, subActivityId:0, ready:true, subActivityNum: 1, applyStartTime: 2024-04-12 00:00:01, applyEndTime: 2024-04-12 23:59:11, matchTime: 2024-04-13 00:00:01, playerJoinTime: 2024-04-13 09:00:01, clearTime: 2024-04-13 22:15:01, activityEndTime: 2024-04-13 22:17:01 }
2024-04-12 17:51:37,856 [main] INFO  (AbstractUnionSeaFightService.java:68) - @UnionSeaFight Initialization of naval battle activities grouping, groupId:1, serverIds:{500,499,498,497,496,495,494,493,492,491,490,489,488,487,486,485,484,483,482,481,480,479,478,477,476,475,474,473,472,471,470,469,468,467,466,465,464,463,462,461,460,459,458,457,456,455,454,453,452,451,450,449,448,447,446,445,444,443,442,441,440,439,438,437,436,435,434,433,432,431,430,429,428,427,426,425,424,423,422,421,420,419,418,417,416,415,414,413,412,411,410,409,408,407,406,405,404,403,402,401,400,399,398,397,396,395,394,393,392,391,390,389,388,387,386,385,384,383,382,381,380,379,378,377,376,375,374,373,372,371,370,369,368,367,366,365,364,363,362,361,360,359,358,357,356,355,354,353,352,351,350,349,348,347,346,345,344,343,342,341,340,339,338,337,336,335,334,333,332,331,330,329,328,327,326,325,324,323,322,321,320,319,318,317,316,315,314,313,312,311,310,309,308,307,306,305,304,303,302,301,300,299,298,297,296,295,294,293,292,291,290,289,288,287,286,285,284,283,282,281,280,279,278,277,276,275,274,273,272,271,270,269,268,267,266,265,264,263,262,261,260,259,258,257,256,255,254,253,252,251,250,249,248,247,246,245,244,243,242,241,240,239,238,237,236,235,234,233,232,231,230,229,228,227,226,225,224,223,222,221,220,219,218,217,216,215,214,213,212,211,210,209,208,207,206,205,204,203,202,201,200,199,198,197,196,195,194,193,192,191,190,189,188,187,186,185,184,183,182,181,180,179,178,177,176,175,174,173,172,171,170,169,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,146,145,144,143,142,141,140,139,138,137,136,135,134,133,132,131,130,129,128,127,126,125,124,123,122,121,120,119,118,117,116,115,114,113,112,111,110,109,108,107,106,105,104,103,102,101,100,99,98,97,96,95,94,93,92,91,90,89,88,87,86,85,84,83,82,81,80,79,78,77,76,75,74,73,72,71,70,69,68,67,66,65,64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,42,41,39,38,37,36,35,34,33,32,31,30,29,28,27,26,25,24,23,22,21,19,18,17,16,15,14,13,12,11,10,9,8,6,5,3,2,1}, usfData:{ DictActivityId: 6, subActivityId:0, ready:true, subActivityNum: 1, applyStartTime: 2024-04-12 00:00:01, applyEndTime: 2024-04-12 23:59:11, matchTime: 2024-04-13 00:00:01, playerJoinTime: 2024-04-13 09:00:01, clearTime: 2024-04-13 22:15:01, activityEndTime: 2024-04-13 22:17:01 }
2024-04-12 17:51:37,856 [main] INFO  (BenfuUnionSeaFightService.java:237) - @UnionSeaFight ???????????
2024-04-12 17:51:38,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:51:38,875 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:51:38,875 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:51:58,898 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:51:58,898 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:51:58,898 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:18,883 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:52:18,883 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:18,883 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:38,901 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:52:38,901 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:38,901 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:58,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:52:58,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:52:58,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:18,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:53:18,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:18,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:53:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:58,876 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:53:58,876 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:53:58,876 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:18,899 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:54:18,899 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:18,899 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:38,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:54:38,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:38,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:54:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:54:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:18,908 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:55:18,908 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:18,908 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:55:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:58,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:55:58,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:55:58,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:18,887 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:56:18,887 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:18,887 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:56:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:38,905 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:58,894 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:56:58,894 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:56:58,894 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:18,863 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:57:18,863 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:18,863 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:38,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:57:38,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:38,880 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:58,891 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:57:58,891 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:57:58,891 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:18,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:58:18,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:18,864 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:38,890 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:58:38,890 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:38,890 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:58,862 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:58:58,862 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:58:58,862 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:18,892 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:59:18,892 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:18,892 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:38,861 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:59:38,861 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:38,861 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:291) - @UnionSeaFight ????????????????serverId:1 
2024-04-12 17:59:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:478) - @UnionSeaFight ??????? ??1
2024-04-12 17:59:58,874 [WORLD_UNION_MSG-thread-1] WARN  (BenfuUnionSeaFightService.java:550) - @UnionSeaFight ??????? ??1
