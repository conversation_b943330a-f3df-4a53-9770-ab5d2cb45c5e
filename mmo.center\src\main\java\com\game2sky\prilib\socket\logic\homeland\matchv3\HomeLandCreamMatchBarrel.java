package com.game2sky.prilib.socket.logic.homeland.matchv3;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.homeland.center.HomelandBaseInfo;
import com.game2sky.prilib.socket.logic.integral.sort.Barrel;
import com.game2sky.publib.framework.util.TimeUtils;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 上午11:24:04  daixl
 */
public class HomeLandCreamMatchBarrel extends Barrel {

	// 2020-07-11 00:00:00
	private final static Long BASE_TIME = 1594396800000L;

	// 暂时使用linkedList
	private LinkedList<Long> playerIds = new LinkedList<Long>();

	public HomeLandCreamMatchBarrel(Long id) {
		this.id = id;
	}

	public void addPlayerId(long playerId) {
		this.playerIds.add(playerId);
		this.haveNumber = playerIds.size();
	}

	public void addPlayerIdFirst(long playerId) {
		this.playerIds.addFirst(playerId);
		this.haveNumber = playerIds.size();
	}

	public void addPlayerIdLast(long playerId) {
		this.playerIds.addLast(playerId);
		this.haveNumber = playerIds.size();
	}

	public void removePlayerId(Long playerId) {
		this.playerIds.remove(playerId);
		this.haveNumber = playerIds.size();
	}

	public List<Long> getPlayerIds() {
		return playerIds;
	}

	public Long randomPlayerId(Set<Long> excludeIds, boolean needCheckLastMatchTime) {
		Long result = null;
		List<Long> removeIds = new ArrayList<Long>();
		long now = System.currentTimeMillis();
		int maxBeRobTimes = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_MAX_BE_ROBED_TIMES);
		Iterator<Long> iterator = playerIds.iterator();
		while (iterator.hasNext()) {
			Long playerId = iterator.next();
			if (excludeIds.contains(playerId)) {
				continue;
			}
			HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
			if (needCheckLastMatchTime) {
				long nextMatchTime = info.getNextMatchTime();
				if (nextMatchTime > now) {
					break;
				}
			}
			int beRobTimes = info.getBeRobTimes();
			if (beRobTimes >= maxBeRobTimes) {
				boolean checkIsSameDay = checkIsSameDay(now, info.getLastUpdateTime());
				if (!checkIsSameDay) {
					info.setBeRobTimes(0);
					info.setLastUpdateTime(now);
				} else {
					iterator.remove();
					removeIds.add(playerId);
					if (removeIds.size() > 5) {
						break;
					}
					continue;
				}
			}
			result = playerId;
			excludeIds.add(playerId);
			iterator.remove();
			removeIds.add(playerId);
			break;
		}

		if (removeIds.size() > 0) {
			long configValue = BaseService.getConfigValue(PriConfigKeyName.HOMELAND_REFRESH_PROTECT_TIME);
			configValue = TimeUnit.SECONDS.toMillis(configValue);
			now += configValue;
			for (Long playerId : removeIds) {
				HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
				info.setNextMatchTime(now);
				playerIds.addLast(playerId);
			}
		}
		return result;
	}

	private static boolean checkIsSameDay(long time1, long time2) {
		long peroid1 = time1 - BASE_TIME;
		long peroid2 = time2 - BASE_TIME;

		return ((peroid1 / TimeUtils.DAY) == (peroid2 / TimeUtils.DAY));
	}
}
