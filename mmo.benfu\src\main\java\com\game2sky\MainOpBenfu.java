package com.game2sky;

import java.io.IOException;
import java.net.URL;

import org.apache.commons.httpclient.HttpException;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.embedded.EmbeddedMongoAutoConfiguration;
import org.springframework.core.env.SimpleCommandLinePropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.alibaba.fastjson.JSON;
import com.game2sky.prilib.benfu.logic.autochess.AutoChessBenfuStart;
import com.game2sky.prilib.benfu.logic.entirerank.EntireRankBenfuService;
import com.game2sky.prilib.benfu.logic.integral.IntegralGameStart;
import com.game2sky.prilib.benfu.logic.ladderwar.LadderWarBenfuStart;
import com.game2sky.prilib.benfu.logic.unionSeaFight.USFBenfuStart;
import com.game2sky.prilib.core.socket.logic.homeland.handler.HomelandBenfuStart;
import com.game2sky.prilib.core.socket.logic.partner.handler.PartnerBenfuStart;
import com.game2sky.prilib.core.socket.logic.secretplacefight.handler.SecretPlaceBenfuStart;
import com.game2sky.prilib.core.socket.logic.yearactivity.fight.boss.YearActivityBenfuStart;
import com.game2sky.publib.benfu.watch.Watcher;
import com.game2sky.publib.db.land.DbEntityPriorityManager;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.http.AMHttp;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.common.system.AMSystem;
import com.game2sky.publib.framework.constant.Errors;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.web.WebApi;

/**
 * 本服进程启动
 *
 * <AUTHOR>
 * @version v0.1 2017年3月10日 下午3:17:10 amyn
 */
@EnableScheduling
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, //
									MongoDataAutoConfiguration.class, //
									MongoRepositoriesAutoConfiguration.class, //
									EmbeddedMongoAutoConfiguration.class, //
									MongoAutoConfiguration.class //
})
public class MainOpBenfu {

	private static final String dbPriorityPath = "db_entity_priority.properties";
//	 private static Scanner s;

	static {
		// 设置启动参数
		System.setProperty("Log4jContextSelector", "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
		System.setProperty("log4j.shutdownHookEnabled", "false");
	}

	public static void main(String[] args) throws HttpException, IOException, InterruptedException,
											InstantiationException, IllegalAccessException, ClassNotFoundException {
		AMLog.stdout("init.scene.start.config...");
		try {
			SimpleCommandLinePropertySource simpleCommandLinePropertySource = new SimpleCommandLinePropertySource(args);
			String property = simpleCommandLinePropertySource.getProperty("netty.server.port");
			if (StringUtils.isBlank(property)) {
				CoreBoot.SELF_PORT = 12301;
			} else {
				CoreBoot.SELF_PORT = Integer.valueOf(property);
			}

			AMFrameWorkBoot.SELF_PORT = CoreBoot.SELF_PORT;

			CoreBoot.bootSrping(MainOpBenfu.class, args);
			CoreBoot.bootLocalConfig();
			String url = AMFrameWorkBoot.conf.getString(CoreBoot.CHANNEL_GROUP.getCenterConfigUrl()) + WebApi.GET_WWIP;
			System.out.println("-- url: " + url);

			String httpGet = AMHttp.httpGet(url);
			if (StringUtils.isEmpty(httpGet)) {
				AMLog.stdout("Obtaining the server IP failure" + url);
				throw new RuntimeException();
			}

			CoreBoot.SELF_IP = JSON.parseObject(httpGet).getString("ip");
			if(CoreBoot.SELF_IP == null || CoreBoot.SELF_IP.isEmpty() || "127.0.0.1".equals(CoreBoot.SELF_IP)) {
				CoreBoot.SELF_IP = AMSystem.getNWIP();
			}
			CoreBoot.SELF_SERVER_TYPE = ServerTypeEnum.BENFU;

			initConfig();

			CoreBoot.boot(MainOpBenfu.class, args);
			
			// 启动本服天梯
			IntegralGameStart.start();
			AutoChessBenfuStart.start();
			// 全服通用排行功能初始化
			CoreBoot.getBean(EntireRankBenfuService.class).init();
			
			// 连接中心服,此步需要在天梯启动完以后执行,因为天梯需要从redis去load数据然后推送给中心服.
			AMLog.stdout("init.center.server ...");
			CrossServerManager.init(CoreBoot.initConfig.getCenterConfig(), true);

			USFBenfuStart.start();
			// //本服需要连接跨服
			// CrossServerManager.buildConnect();
			LadderWarBenfuStart.start();
			YearActivityBenfuStart.start();
			SecretPlaceBenfuStart.start();
			HomelandBenfuStart.start();
			PartnerBenfuStart.start();
			Watcher.start();

			AMLog.stdout("init.netty.server...");
			CoreBoot.getBean(CoreBoot.class).initServer(); // 初始化与server的链接
			
			AMFrameWorkBoot.START_SUCCESS = true;
			//清理所有cache
			CoreBoot.destroyTempCache();
			AMLog.stdout("#" + CoreBoot.SELF_SERVER_TYPE + " start!");
		} catch (Throwable t) {
			AMLog.LOG_STDOUT.error(Errors.SERVER_INIT_FAIL + "#" + CoreBoot.SELF_SERVER_TYPE + ".main", t);
			AMLog.stdout("#" + CoreBoot.SELF_SERVER_TYPE + " start error. error log info logs/stdout/stdout.log!" + t.toString());
		}

	}

	private static void initConfig() throws HttpException, IOException {
//		GatewayInitConfig initConfig = wgetConfig();
//		initGatewayInitConfig(initConfig);

		initDbPrioirty();
	}

	/**
	 * 初始化db优先级
	 * 
	 * @throws IOException
	 */
	private static void initDbPrioirty() throws IOException {
		// nhan2k3
		// ClassLoader classLoader = AMFrameWorkBoot.class.getClassLoader();
		// URL resource = classLoader.getResource(dbPriorityPath);
		// DbEntityPriorityManager.getInstance().init(resource.getPath());
		DbEntityPriorityManager.getInstance().init(dbPriorityPath);
	}

//	public static void initGatewayInitConfig(GatewayInitConfig initConfig) {
//		if (initConfig.getZoneIds() != null
//				&& initConfig.getZoneIds().size() > 0) {
//			for (Map.Entry<String, String> entry : initConfig.getZoneIds()
//					.entrySet()) {
//				int zoneId = Integer.valueOf(entry.getKey());
//				String kuafuFlag = entry.getValue();
//				AMFrameWorkBoot.REL_KUAFU_SERVER_ID_MAP.put(zoneId, kuafuFlag);
//			}
//		}

	// 自动处理全服或Kaufu
//		if(initConfig.getKuafuSceneConfigs() != null && !initConfig.getKuafuSceneConfigs().isEmpty()) {
//			CrossServerManager.init(initConfig.getKuafuSceneConfigs(), false);
//		}
//		if(initConfig.getQuanfuSceneConfigs() != null && !initConfig.getQuanfuSceneConfigs().isEmpty()) {
//			CrossServerManager.init(initConfig.getQuanfuSceneConfigs(), false);
//		}
//		if(!CrossServerManager.existCrossServer(ServerTypeEnum.KUAFU) && 
//				!CrossServerManager.existCrossServer(ServerTypeEnum.QUANFU)) {
//			AMLog.LOG_STDOUT.error("未配置全服或者跨服", new RuntimeException());
//			System.exit(1);
//		}
//	}

//	public static GatewayInitConfig wgetConfig() throws HttpException,
//			IOException {
//		Map<String, String> paramMap = new HashMap<String, String>();
//		paramMap.put("inIp", AMFrameWorkBoot.SELF_IP);
//		paramMap.put("port", String.valueOf(AMFrameWorkBoot.SELF_PORT));
//		String httpPost = AMHttp.httpGet(AMFrameWorkBoot.conf
//				.getString(CoreBoot.CHANNEL_GROUP.getCenterConfigUrl()) //
//				+ WebApi.GET_GATEWAY_INIT_CONFIG //
//				+ "?outIp=" + AMFrameWorkBoot.SELF_IP //
//				+ "&port=" + AMFrameWorkBoot.SELF_PORT);
//		GatewayInitConfig initConfig = JSON.parseObject(httpPost,
//				GatewayInitConfig.class);
//		return initConfig;
//	}

}
