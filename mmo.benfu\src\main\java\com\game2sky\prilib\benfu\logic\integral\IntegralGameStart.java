package com.game2sky.prilib.benfu.logic.integral;

import com.game2sky.prilib.benfu.logic.integral.task.ScheduleaRefreshRankTask;
import com.game2sky.prilib.benfu.logic.integral.task.ScheduledAnsyncOperRedis;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.thread.IntegralThreadStart;
import com.game2sky.publib.Globals;

/**
 * 天梯游戏服启动
 * <AUTHOR>
 * @version v0.1 2018年3月27日 上午11:03:15  zhoufang
 */
public class IntegralGameStart {

	public static void start() {
		// 创建,启动天梯线程组
		IntegralThreadStart.start(new IntegralGameThreadGroup());
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		IntegralCommonService.setManager(gameManager);
		// 初始化分组信息
		gameManager.init();
//		// redis加载数据
//		IntegralGameRedis.load();
//		// 发奖
//		gameManager.start();
		// 启动reids的异步保存
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduledAnsyncOperRedis(),
			ScheduledAnsyncOperRedis.PERIOD, ScheduledAnsyncOperRedis.PERIOD);
		// 启动排行榜刷新
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleaRefreshRankTask(),
			ScheduleaRefreshRankTask.PERIOD, ScheduleaRefreshRankTask.PERIOD);
		// 启动通知定时器
//		createNewVersionNotifyDelay();
		
	}

//	private static void createNewVersionNotifyDelay() {
//		Set<Integer> groupIds = IntegralGameManager.getInstance().getGroupServerIds().keySet();
//		long now = System.currentTimeMillis();
//		for (Integer groupId : groupIds) {
//			DictIntegralServer dict = IntegralConfigCenter.getBranchMap().get(groupId);
//			long delay = dict.getNewVersionStartTime() - now;
//			if (delay <= 0) {
//				continue;
//			}
//			AMLog.LOG_COMMON.info("分组{}还未到新版本开启时间,准备{}秒后通知",groupId,delay/1000);
//			WheelScheduledService.scheduleOnce(new ScheduleStartNewVersionTask(delay, TimeUnit.MILLISECONDS,groupId));
//		}
//	}
}
