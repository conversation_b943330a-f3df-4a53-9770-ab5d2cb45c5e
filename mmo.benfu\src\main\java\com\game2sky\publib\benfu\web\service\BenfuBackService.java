package com.game2sky.publib.benfu.web.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.integral.IntegralGameManager;
import com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer;
import com.game2sky.prilib.benfu.logic.integral.IntegralGroup;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.communication.game.struct.ItemBaseType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.dbs.dao.InstCurrencyDao;
import com.game2sky.prilib.dbs.model.InstCurrency;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.web.base.Result;
import com.game2sky.publib.socket.logic.currency.CurrencyModel;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年11月26日 下午8:31:29 
 */
@Service
public class BenfuBackService {

	public Result operBenfuTest(Map<String, String> paramsMap) {
		Result jsonResult = new Result();
		String type = paramsMap.get("type");
		if ("init".equals(type)) {
			CoreBoot.getBean(com.game2sky.prilib.benfu.logic.unionSeaFight.BenfuUnionSeaFightService.class).init();
		}
		return jsonResult;
	}

	public Result queryIntegralBranchRank(Map<String, String> paramsMap) {
		Result result = new Result();
		int branch = Integer.parseInt(paramsMap.get("branch"));
		int areaType = Integer.parseInt(paramsMap.get("areaType"));
//		if (areaType != IntegralConst.AREA_AMEND && areaType != IntegralConst.AREA_NOLIMIT){
//			result.setCode(1);
//			result.setData("Error areaType=" + areaType);
//			return result;
//		}
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		IntegralGroup group = gameManager.getGroupById(branch);
		result.setCode(1);
		if (group == null) {
			result.setData("no this branch=" + branch);
			return result;
		}
		IntegralCurrentRank currentRank = group.getRankCache().get(areaType);
		if (currentRank != null) {
			List<IntegralRankItem> rankList = new ArrayList<>(currentRank.getRankList());
			List<IntegralRank> list = new ArrayList<IntegralRank>();
			for (int i = 0; i < rankList.size(); i++) {
				IntegralRankItem item = rankList.get(i);
				IntegralRank rank = new IntegralRank(Integer.toString(branch), i + 1, item);
				list.add(rank);
			}
			result.setCode(0);
			result.setData(list);
			return result;
		}
		result.setData("rankList is null");
		return result;
	}

	private class IntegralRank {
		public String branch;
		public String playerId;
		public String serverId;
		public String rank;
		public String name;
		public String integral;

		public IntegralRank(String branch, int rank, IntegralRankItem item) {
			this.branch = branch;
			this.playerId = Long.toString(item.getPlayerId());
			this.serverId = Integer.toString(item.getServerId());
			this.rank = Integer.toString(rank);
			this.name = item.getInformation().getNickName();
			this.integral = Integer.toString(item.getIntegral());
		}
	}

	public Result modifyIntegralCoin(Map<String, String> paramsMap) {
		Result result = new Result();
		int serverId = Integer.parseInt(paramsMap.get("serverId"));
		IntegralGroup group = IntegralGameManager.getInstance().getGroupByServerId(serverId);
		if (group == null) {
			result.setCode(-1);
			result.setData("Error ServerId="+serverId);
			return null;
		}
		List<Long> playerIds = null;
		int isAll = Integer.parseInt(paramsMap.get("isAll"));
		if(isAll != 1){
			// 修改部分玩家的天梯币
			String playerStr = paramsMap.get("playerIds");
			String[] split = playerStr.split(",");
			playerIds = new ArrayList<Long>(split.length);
			for(String str : split){
				playerIds.add(Long.parseLong(str));
			}
		}
		
		int zoneId = Globals.getZoneId(serverId);
		InstCurrencyDao benfuDao = Globals.getDbsDataSource().getBenfuDao(InstCurrencyDao.class, zoneId);
		List<IntegralGamePlayer> players = new ArrayList<>(group.getPlayers().values());
		for (IntegralGamePlayer player : players) {
			if (player.getServerId() != serverId) {
				continue;
			}
			if(playerIds != null && !playerIds.contains(player.getPlayerId())){
				continue;
			}
			modifyPlayerIntegralCoin(benfuDao,player.getPlayerId(), player.getServerId());
		}
		result.setData("success");
		return result;
	}

	private void modifyPlayerIntegralCoin(InstCurrencyDao benfuDao,long playerId, int serverId) {
		int coinLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_CLEAR_COIN);
		int zoneId = Globals.getZoneId(serverId);
		Human human = (Human) Globals.getHuman(zoneId, playerId);
		if (human != null) {
			return;
		}
		InstCurrency instCurrency = benfuDao.get(playerId);
		CurrencyModel model = JsonUtils.S2O(instCurrency.getCurrency(), CurrencyModel.class);
		long currencyValue = model.getCurrencyValue(ItemBaseType.INTEGRALCOIN);
		if (currencyValue > coinLimit) {
			model.subCurrencyValue(ItemBaseType.INTEGRALCOIN, currencyValue - coinLimit);
			instCurrency.setCurrency(JsonUtils.O2S(model));
			benfuDao.update(instCurrency);
		}
	}

}
