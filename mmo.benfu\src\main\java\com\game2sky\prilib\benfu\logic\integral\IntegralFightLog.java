package com.game2sky.prilib.benfu.logic.integral;

import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.Globals;
import com.game2sky.publib.log.CustomLog;
import com.game2sky.publib.log.CustomLogPrefixType;

/**
 *
 * <AUTHOR>
 * @version v0.1 2018年4月23日 下午2:09:33  zhoufang
 */
public class IntegralFightLog extends CustomLog{
	
	/** 参与的天梯赛区 */
	private int areaType;
	
	/** 总人数  */
	private int totalNumber;
	
	
	public IntegralFightLog(Human human, int areaType){
		super(CustomLogPrefixType.IntegralFightLog.getPrefix(),human);
		this.areaType = areaType;
		this.totalNumber = IntegralGameManager.getInstance().getAllPlayerSize();
	}
	
	public IntegralFightLog(Long playerId,int zoneId,int areaType){
		this.prefix = CustomLogPrefixType.IntegralFightLog.getPrefix();
		this.playerId = playerId;
		this.zoneId = zoneId;
		this.time = Globals.getTimeService().now();
		this.ip = "";
		this.deviceId = "";
		this.areaType = areaType;
		this.totalNumber = IntegralGameManager.getInstance().getAllPlayerSize();
	}

	public int getAreaType() {
		return areaType;
	}

	public void setAreaType(int areaType) {
		this.areaType = areaType;
	}

	public int getTotalNumber() {
		return totalNumber;
	}

	public void setTotalNumber(int totalNumber) {
		this.totalNumber = totalNumber;
	}
	
}
