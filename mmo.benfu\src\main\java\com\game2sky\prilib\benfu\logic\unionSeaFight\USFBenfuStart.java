package com.game2sky.prilib.benfu.logic.unionSeaFight;

import com.game2sky.publib.Globals;
import com.game2sky.publib.util.RandomUtil;

/**
 * TODO 海战本服启动
 *
 * <AUTHOR>
 * @version v0.1 2018年11月1日 下午7:05:45  guojijun
 */
public class USFBenfuStart {

	public static void start() {
		// 这里为啥要随机一个延迟时间,防止服务器同时启动同时向中心副要数据
		int delay = RandomUtil.randomInt(1000, 5000);
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleUSFBenfuSyncRanks(), delay,
			ScheduleUSFBenfuSyncRanks.PERIOD);
	}
}
