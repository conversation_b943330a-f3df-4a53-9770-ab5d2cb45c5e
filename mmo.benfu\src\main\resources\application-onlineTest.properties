banner.location=classpath:yckj.txt

info.app.name=op.benfu
info.app.version=0.0.1-SNAPSHOT

spring.http.encoding.charset = UTF-8
spring.http.encoding.enabled = true
spring.http.encoding.force = true

spring.mvc.static-path-pattern=/**
spring.resources.static-locations=

logging.config=classpath:log4j2-lan.xml

conf.properties=conf-onlineTest.properties

#superSDK.url=10.66.100.169/supersdk-web
#superSDK.appId=com.yunchang.onepiece
#superSDK.shortAppId=op
#superSDK.appSecret=pW/Z:28*~=;Invqr}+X`
##superSDK.encryptKey=fV!>P^%rj1[*|/%G+Oo/
##superSDK.paymentSecret=D!`}iE`Hx0?:yIU?Ic4F

#superSDK.encryptKey=5Czm!V@%psc6Te5jmu^Y
#superSDK.paymentSecret=NE*39r$#UteXjz?bR0K8
