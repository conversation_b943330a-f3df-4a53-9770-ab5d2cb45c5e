package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress;

import gnu.trove.iterator.TIntIterator;
import gnu.trove.iterator.TIntObjectIterator;
import gnu.trove.map.hash.TIntObjectHashMap;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUFFortressPromotionBegin;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUFFortressPromotionComplete;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitBuffChange;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitProChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientFortressInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitIntProChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitProType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFWinReason;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictUSFFortress;
import com.game2sky.prilib.core.dict.domain.DictUSFFortressDetail;
import com.game2sky.prilib.core.dict.domain.DictUSFFortressLevel;
import com.game2sky.prilib.core.dict.domain.DictUSFPromotion;
import com.game2sky.prilib.core.socket.logic.union.constants.UnionConstants;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFUtils;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.USFFightInFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFFortressBaseSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkillController;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.StringUtils;

/**
 * TODO 海战据点
 *
 * <AUTHOR>
 * @version v0.1 2018年6月7日 下午4:51:04  guojijun
 */
public class USFFortress extends AbstractUSFUnit {

	private final int dictId;
	private USFFortressType type;
	/**碰撞体积*/
	private int collisionSize;
	/**占领获得积分*/
	private int occupyGainIntegral;
	/**升级ID*/
	private int promotionId;
	/**升级时间*/
	private long promotionTime;
	/**操作升变的玩家Uid*/
	private int operateUnitId;
	/**初始详情ID*/
	private int initFortressDetailId;
	/**当前详情ID*/
	private int curFortressDetailId;
	/**等级*/
	private int level;
	/**技能列表*/
	private final ArrayList<Integer> skillIds;
	/**可达据点对应的航线(key为据点字典表ID)*/
	private final TIntObjectHashMap<USFRoute> routes;
	/**据点里的单位*/
	private final LinkedList<IUSFUnitFightable> defenders;
	/**进攻者*/
	private IUSFUnitFightable attacker;
	/**据点AI*/
	private IUSFFortressAI fortressAI;
	/**最近一次单位离开时间*/
	private long lastUnitLeaveTime;

	private USFClientFortressInfo clientInfo;

	/** 基础据点的基础技能 */
	private USFFortressBaseSkill defaultSkill;
	
	public USFFortress(int id, DictUSFFortress dictUSFFortress, DictUSFFortressDetail dictUSFFortressDetail) {
		super(id, USFUnitType.USF_UNIT_FORTRESS);
		this.dictId = dictUSFFortress.getId();
		float tmpSize = dictUSFFortressDetail.getCollisionSize();
		if (!StringUtils.isEmpty(dictUSFFortress.getModelOffset())) {
			tmpSize = 0;
		}
		this.collisionSize = USFUtils.floatToInt(tmpSize);
		this.occupyGainIntegral = dictUSFFortress.getOccupyGainIntegral();
		this.setDir(dictUSFFortress.getDir());
		this.setPos(dictUSFFortress.getPos());
		this.type = USFFortressType.valueOf(dictUSFFortress.getType());
		this.skillIds = new ArrayList<>(3);
		this.initFortressDetailId = dictUSFFortressDetail.getId();
		this.routes = new TIntObjectHashMap<>();
		this.defenders = new LinkedList<>();
		this.fsmController = new USFFortressFsmController(this);
		this.fsmController.initState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
		this.changeDetailTo(this.initFortressDetailId, false);
		this.ai = new USFFortressDefaultAI(this);
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("[id=").append(this.id);
		builder.append(",fortressDictId=").append(this.dictId);
		builder.append(",curFortressDetailId=").append(this.curFortressDetailId);
		builder.append(",curStateType=").append(this.fsmController.getCurStateType());
		builder.append(",unitCount=").append(this.defenders.size());
		builder.append("]");
		return builder.toString();
	}

	public void addUnit(USFPlayer unit) {
		if (!this.defenders.contains(unit)) {
			this.defenders.add(unit);
			if (this.fortressAI != null) {
				this.fortressAI.onAddUnit(unit);
			}
			this.triggerEvent(USFBuffTriggerType.ENTER_TOWER, this, unit);
			unit.triggerEvent(USFBuffTriggerType.ENTER_TOWER, unit, this);
			if(this.getType() == USFFortressType.USF_FORTRESS_NORMAL)
			{
				unit.triggerEvent(USFBuffTriggerType.ENTER_NORMAL_TOWER, unit, this);
			}
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战单位进入据点,fortress={},unit={}", this, unit);
			}
		}
	}

	private void removeUnit(IUSFUnitFightable unit) {
		boolean bl = this.defenders.remove(unit);
		if (bl) {
			if (unit instanceof AbstractUSFUnit) {
				AbstractUSFUnit opposite = (AbstractUSFUnit) unit;
				opposite.triggerEvent(USFBuffTriggerType.LEAVE_TOWER, opposite, this);
				this.triggerEvent(USFBuffTriggerType.LEAVE_TOWER, this, opposite);
			}
			if (this.fortressAI != null) {
				this.fortressAI.onRemoveUnit(unit);
			}
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("单位离开据点,fortress={},unit={}", this, unit);
			}
		}
	}

	/**
	 * 移除单位并且改变阵营(单位主动离开)
	 * 
	 * @param unit
	 */
	public void removeUnitAndChangeCamp(USFPlayer unit) {
		
		this.removeUnit(unit);
		if (this.defenders.size() > 0) {
			return;
		}
		if (this.type == USFFortressType.USF_FORTRESS_BASE || this.campType == USFCampType.USF_CAMP_NEUTRAL) {
			return;
		}

		if (this.curFortressDetailId != this.initFortressDetailId) {// 升级后的据点保留
			return;
		} else if (this.promotionId > 0) {// 升级中直接取消
			this.cancelPromotion();
		}

		USFCamp newCamp = this.camp.getUsFight().getCamp(USFCampType.USF_CAMP_NEUTRAL);
		if (newCamp == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战没有找到中立阵营,usFight={}", this.camp.getUsFight());
			}
			return;
		}
		USFCamp oldCamp = this.camp;
		this.camp.removeUnit(this);
		this.camp = null;
		this.joinToCamp(newCamp);
		if(buffController != null)
		{
			this.buffController.removeAllBuff();
		}
		USFClientUnitIntProChange intProChange = new USFClientUnitIntProChange(this.id,
			USFUnitProType.USF_UNIT_PRO_CAMP_TYPE, this.campType.value());
		List<USFClientUnitIntProChange> intProChanges = Arrays.asList(intProChange);
		SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
		scUSFUnitProChange.setIntProChanges(intProChanges);
		this.camp.getUsFight().broadcast(scUSFUnitProChange);
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("据点阵营改变,fortress={},oldCampType={}", this, oldCamp.getCampType());
		}
	}

	/**
	 * 移除单位并且通知单位
	 * 
	 * @param unit
	 */
	public void removeUnitAndNotify(IUSFUnitFightable unit) {
		this.removeUnit(unit);
		unit.onLeaveFortress();
	}

	/** 
	 * 添加航线
	 * 
	 * @param targetFortressId 据点字典表ID
	 * @param route
	 */
	public void addRoute(int targetFortressId, USFRoute route) {
		this.routes.put(targetFortressId, route);
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("海战据点添加可行航线,fortress={},targetFortressId={},route={}", this, targetFortressId, route);
		}
	}

	/**
	 * 获取到目标据点的航线
	 * 
	 * @param targetFortressId 据点字典表ID
	 * @return
	 */
	public USFRoute getRoute(int targetFortressId) {
		return this.routes.get(targetFortressId);
	}

	@Override
	protected void copyTo(USFClientUnitInfo clientUnitInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientFortressInfo();
		}
		this.clientInfo.setDictId(dictId);
		this.clientInfo.setBuffInfos(getShowBuffs());
		this.clientInfo.setPromotionTime(promotionTime);
		this.clientInfo.setPromotionId(promotionId);
		this.clientInfo.setFortressDetailId(this.curFortressDetailId);
		this.clientInfo.setOperateUnitId(operateUnitId);
		this.clientInfo.setLevel(level);
		clientUnitInfo.setFortressInfo(this.clientInfo);
	}

	@Override
	public USFFortress getCurFortress() {
		return this;
	}

	public void joinCamp(USFCamp camp) {
		this.joinToCamp(camp);
	}

	/**
	 * 更换阵营(被其他阵营占领)
	 * @param fightable
	 */
	public void changeCamp(IUSFUnitFightable fightable) {
		USFCamp newCamp = fightable.getCamp();
		if (this.camp == newCamp ) {
			return;
		}
		boolean canRob = false; // 是否能掠夺
		if(fightable instanceof USFPlayer) {
			USFPlayer player = (USFPlayer) fightable;
			canRob = player.canRob();
		}
		USFCamp oldCamp = this.camp;
		this.camp.removeUnit(this);
		this.camp = null;
		this.attacker = null;
		this.joinToCamp(newCamp);
		if (this.occupyGainIntegral > 0) {
			newCamp.changeIntegral(this.occupyGainIntegral);
			this.occupyGainIntegral = 0;
		}
		USFClientUnitIntProChange intProChange = new USFClientUnitIntProChange(this.id,
			USFUnitProType.USF_UNIT_PRO_CAMP_TYPE, this.campType.value());
		List<USFClientUnitIntProChange> intProChanges = Arrays.asList(intProChange);
		SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
		scUSFUnitProChange.setIntProChanges(intProChanges);
		this.camp.getUsFight().broadcast(scUSFUnitProChange);
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("据点阵营改变,fortress={},oldCampType={}", this, oldCamp.getCampType());
		}
		if(buffController != null)
		{
			this.buffController.removeAllBuff();
		}
		if (this.promotionId > 0) {// 升变中
			this.cancelPromotion();
			
		} else if (this.curFortressDetailId != this.initFortressDetailId) {
			// 被攻陷还原据点
			if (canRob) {
				// 掠夺了，不改变为初始据点。保留据点特性，只是修改等级
				this.changeDetailTo(this.curFortressDetailId, true);
			} else {
				this.changeDetailTo(this.initFortressDetailId, true);
			}
		}

		if (oldCamp.getUnionId() > 0) {
			// 大本营被攻陷,游戏结束
			if (oldCamp.getBornFortress() == this) {
				USFCampType winCampType = newCamp.getCampType();
				newCamp.getUsFight().over(winCampType, USFWinReason.WIN_REASON_BORN_FORTRESS_LOSE);
			}
		}
	}

	@Override
	public void onRes() {
	}

	public boolean hasDefender() {
		return this.defenders.size() > 0;
	}

	/**
	 * 下一个攻击者
	 */
	public void nextAttacker() {
		this.attacker = null;
	}

	/**
	 * 被攻击
	 * 
	 * @param attacker
	 */
	public void beAttacked(IUSFUnitFightable attacker) {
		USFUnitStateType curStateType = this.fsmController.getCurStateType();
		if (curStateType != USFUnitStateType.USF_UNIT_STATE_BE_ATTACKED) {
			this.attacker = attacker;
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_BE_ATTACKED);
			this.continueFight();
		}
	}

	/**
	 * 继续战斗
	 */
	public void continueFight() {
		if (this.defenders.isEmpty() || this.attacker == null) {
			this.attacker = null;
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
			return;
		}
		IUSFUnitFightable rightFightable = this.defenders.peek();
		USFFightInFortress fightInFortress = new USFFightInFortress(this, this.attacker, rightFightable);
		fightInFortress.start();
	}

	/**
	 * 重置升变
	 */
	private void resetPromotion() {
		this.promotionId = 0;
		this.promotionTime = 0;
		this.operateUnitId = 0;
	}

	/**
	 * 转变详情
	 * 
	 * @param fortressDetailId
	 * @param push 是否推送改变
	 */
	public void changeDetailTo(int fortressDetailId, boolean push) {
		this.resetPromotion();
		this.curFortressDetailId = fortressDetailId;
		this.skillIds.clear();
		int techLevel = UnionConstants.UNION_FORTRESS_INIT_LEVEL;
		if (this.camp != null) {
			techLevel = this.camp.getTechLevel(this.curFortressDetailId);
		}
		this.level = techLevel;
		DictUSFFortressLevel dictUSFFortressLevel = DictUSFFortressLevel.getByIdAndLevel(this.curFortressDetailId,
			techLevel);
		if (dictUSFFortressLevel != null) {
			if (CollectionUtils.isNotEmpty(dictUSFFortressLevel.getSkillIds())) {
				this.skillIds.addAll(dictUSFFortressLevel.getSkillIds());
			}
		}
		List<USFBuff> removeBuffers = new ArrayList<USFBuff>();
		if(buffController != null)
		{
			removeBuffers.addAll(buffController.getBuffs());
			this.buffController.removeAllBuff();
		}
		if (this.skillController == null) 
		{
			this.skillController = new USFSkillController(this);
		}
		else
		{
			removeBuffers.addAll(skillController.getAllBuffOfSkill());
		}
		this.skillController.replaceAllSkill(this.skillIds);
		this.skillController.Ready();
		
		this.type = USFFortressType.valueOf(
			DictUSFFortressDetail.getDictById(fortressDetailId).getType());
		if(defaultSkill != null)
		{
			defaultSkill.setClosed(true);
		}
		this.defaultSkill = new USFFortressBaseSkill();
		this.defaultSkill.setOwner(this);
		this.defaultSkill.triggerEvent(USFBuffTriggerType.FOREVER, this, this);
		for (IUSFUnitFightable defender : defenders) {
			AbstractUSFUnit unit = (AbstractUSFUnit) defender;
			unit.triggerEvent(USFBuffTriggerType.LEAVE_TOWER,unit, this);
			this.triggerEvent(USFBuffTriggerType.ENTER_TOWER, this, unit);
			unit.triggerEvent(USFBuffTriggerType.ENTER_TOWER, unit, this);
			if(type == USFFortressType.USF_FORTRESS_NORMAL)
			{
				unit.triggerEvent(USFBuffTriggerType.ENTER_NORMAL_TOWER, unit, this);
			}
		}

		if (push) {
			this.sendPromotionComplete();
			pushFortrestBuffs(removeBuffers);
		}
		//
	}

	public void pushFortrestBuffs(List<USFBuff> removeBuffs)
	{
		List<USFClientBuffInfo> showBuffs = getShowBuffs();
		SCUSFUnitBuffChange scUSFUnitBuffChange = new SCUSFUnitBuffChange();
		scUSFUnitBuffChange.setUnitId(getId());
		scUSFUnitBuffChange.setBuffInfos(showBuffs);
		if(removeBuffs != null)
		{
			List<Integer> removeIds = new ArrayList<Integer>();
			for(USFBuff buff : removeBuffs)
			{
				removeIds.add(buff.getClientInfo().getGuid());
			}
			scUSFUnitBuffChange.setRemoveBuffGuids(removeIds);
		}
		getCamp().getUsFight().broadcast(scUSFUnitBuffChange);
	}

	@Override
	public List<USFClientBuffInfo> getShowBuffs()
	{
		List<USFClientBuffInfo> showBuffs = new ArrayList<USFClientBuffInfo>();
		if(skillController != null)
		{
			List<USFClientBuffInfo> skillBuffs = skillController.getShowBuffs();
			if(skillBuffs != null)
			{
				showBuffs.addAll(skillBuffs);				
			}
		}
		if(buffController != null)
		{
			List<USFBuff> buffs = buffController.getBuffs();
			for(USFBuff buff : buffs)
			{
				if(!buff.isClientSide())
				{
					showBuffs.add(buff.copyClientInfoToSend());
				}
			}
		}
		return showBuffs;
	}
	
	@Override
	public void triggerEvent(	USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,
								Object... args) {
		if (this.buffController != null) {
			this.buffController.triggerEvent(triggerType, triggerUnit, oppositeUnit,args);
		}
		if (this.skillController != null) {
			this.skillController.triggerEvent(triggerType, triggerUnit, oppositeUnit, args);
		}
		if (this.defaultSkill != null && !defaultSkill.isClosed()) {
			this.defaultSkill.triggerEvent(triggerType, triggerUnit, oppositeUnit, args);
		}
	}

	/**
	 * 开始升变
	 * 
	 * @param operateUnitId 操作升变的玩家Uid
	 * @return
	 */
	public int beginPromotion(DictUSFPromotion dictUSFPromotion, int operateUnitId) {
		if (dictUSFPromotion == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点不能升级,dictUSFPromotion为空,fortress={}", this);
			}
			return ErrorCodeConstants.USF_FORTRESS_CAN_NOT_PROMOTION;
		}
		if (this.promotionId > 0) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点正在升级中,fortress={},promotionId={},promotionTime={}", this, this.promotionId,
					new Timestamp(this.promotionTime));
			}
			return ErrorCodeConstants.USF_FORTRESS_PROMOTIONING;
		}
		if (this.curFortressDetailId != this.initFortressDetailId) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点已经升级,fortress={}", this);
			}
			return ErrorCodeConstants.USF_FORTRESS_PROMOTIONED;
		}
		if (dictUSFPromotion.getSourceDetailId() != this.curFortressDetailId) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点不能升级成目标据点,fortress={},promotionId={}", this, dictUSFPromotion.getId());
			}
			return ErrorCodeConstants.USF_FORTRESS_CAN_NOT_TO_TARGET;
		}
		int code = this.camp.usePromotion(dictUSFPromotion.getId());
		if (code > 0) {
			return code;
		}
		this.promotionId = dictUSFPromotion.getId();
		this.promotionTime = getNow() + dictUSFPromotion.getCostTime();
		this.operateUnitId = operateUnitId;
		SCUFFortressPromotionBegin scUFFortressPromotionBegin = new SCUFFortressPromotionBegin();
		scUFFortressPromotionBegin.setFortressUid(this.id);
		scUFFortressPromotionBegin.setPromotionTime(this.promotionTime);
		scUFFortressPromotionBegin.setPromotionId(this.promotionId);
		scUFFortressPromotionBegin.setOperateUnitUid(operateUnitId);
		this.camp.getUsFight().broadcast(scUFFortressPromotionBegin);
		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	private void sendPromotionComplete() {
		SCUFFortressPromotionComplete scUFFortressPromotionComplete = new SCUFFortressPromotionComplete();
		scUFFortressPromotionComplete.setFortressUid(this.id);
		this.copyTo();
		scUFFortressPromotionComplete.setFortress(this.clientInfo);
		this.camp.getUsFight().broadcast(scUFFortressPromotionComplete);
	}

	/**
	 * 升变tick
	 * 
	 * @param now
	 */
	public void promotionTick(long now) {
		if (this.promotionId <= 0) {
			return;
		}
		if (this.promotionTime > now) {
			return;
		}
		DictUSFPromotion dictUSFPromotion = DictUSFPromotion.getDictUSFPromotion(this.promotionId);
		if (dictUSFPromotion != null) {
			this.changeDetailTo(dictUSFPromotion.getTargetDetailId(), true);
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战据点升变成功,fortress={}", this);
			}
		} else {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点升变失败,没有找到DictUSFPromotion,fortress={},promotionId={}", this, promotionId);
			}
			this.cancelPromotion();
		}
	}

	/**
	 * 取消升变
	 */
	public void cancelPromotion() {
		if (this.promotionId > 0) {
			this.resetPromotion();
			this.sendPromotionComplete();
		}
	}
	
	/**
	 * 获取相连的据点
	 * 
	 * @param units 存放集合
	 * @param campType 所属阵营(传null代表所有阵营)
	 * @param fortressType 据点类型(传null代表所有类型)
	 * @return
	 */
	public void selectConnectedFortresses(List<AbstractUSFUnit> units, USFCampType campType, USFFortressType fortressType) {
		if (units == null || this.routes.isEmpty()) {
			return;
		}
		for (TIntIterator iterator = this.routes.keySet().iterator(); iterator.hasNext();) {
			int fortressId = iterator.next();
			USFFortress fortress = this.camp.getUsFight().getFortress(fortressId);
			if (fortress == null) {
				continue;
			}
			if (campType != null && fortress.getCampType() != campType) {
				continue;
			}
			if (fortressType != null && fortress.getType() != fortressType) {
				continue;
			}
			units.add(fortress);
		}
	}

	/**
	 * 是否是相邻的两个据点，且容许玩家通过
	 * @param target
	 * @param player
	 * @return 0：是相邻据点且容许通过，1：不是相邻据点，2：是相邻据点，但是航线不容许通过，例如无风带
	 */
	public int isConnectForPlayer(USFFortress target,USFPlayer player)
	{
		if (this.routes.isEmpty()) {
			return 1;
		}
		for(TIntObjectIterator<USFRoute> iterator = routes.iterator(); iterator.hasNext();)
		{
			iterator.advance();
			int fortressId =  iterator.key();
			USFRoute route = iterator.value();

			USFFortress fortress = this.camp.getUsFight().getFortress(fortressId);
			if (fortress == null)
			{
				continue;
			}
			if(fortress == target)
			{
				if(route.isAllowPass(player))
				{
					return 0;
				}
				else
				{
					return  2;
				}
			}

		}
		return 1;
	}
	
	/**
	 * 选择附近航线的单位
	 * 
	 * @param units 存放结合
	 * @param campType 所属阵营(传null代表所有阵营)
	 * @param unitType 单位类型(传null代表所有类型)
	 */
	public void selectConnectedRoutesUnits(List<AbstractUSFUnit> units, USFCampType campType, USFUnitType unitType) {
		if (units == null || this.routes.isEmpty()) {
			return;
		}
		for (USFRoute route : this.routes.valueCollection()) {
			route.selectUnits(units, campType, unitType);
		}
	}
	
	@Override
	public void addBuff(USFBuff buff)
	{
		if (this.buffController != null) {
			this.buffController.addBuff(buff);
		}
		for(IUSFUnitFightable fightable : defenders)
		{
			if(defaultSkill != null)
			{
				defaultSkill.triggerEvent(USFBuffTriggerType.ENTER_TOWER, 
					this,(AbstractUSFUnit)fightable);
			}
		}
	}

	public int getDictId() {
		return dictId;
	}

	public USFFortressType getType() {
		return type;
	}

	public int getCollisionSize() {
		return collisionSize;
	}

	public void setCollisionSize(int collisionSize) {
		this.collisionSize = collisionSize;
	}

	public int getOccupyGainIntegral() {
		return occupyGainIntegral;
	}

	public void setOccupyGainIntegral(int occupyGainIntegral) {
		this.occupyGainIntegral = occupyGainIntegral;
	}

	public LinkedList<IUSFUnitFightable> getDefenders() {
		return defenders;
	}

	public IUSFFortressAI getFortressAI() {
		return fortressAI;
	}

	public TIntObjectHashMap<USFRoute> getAllRoutes() {
		return routes;
	}

	@Override
	public List<Integer> getSkillIds() {
		return this.skillIds;
	}

	public long getPromotionTime() {
		return promotionTime;
	}

	public void setPromotionTime(long promotionTime) {
		this.promotionTime = promotionTime;
	}

	public int getPromotionId() {
		return promotionId;
	}

	public void setPromotionId(int promotionId) {
		this.promotionId = promotionId;
	}

	public int getInitFortressDetailId() {
		return initFortressDetailId;
	}

	public void setInitFortressDetailId(int initFortressDetailId) {
		this.initFortressDetailId = initFortressDetailId;
	}

	public int getCurFortressDetailId() {
		return curFortressDetailId;
	}

	public void setCurFortressDetailId(int curFortressDetailId) {
		this.curFortressDetailId = curFortressDetailId;
	}

	public int getOperateUnitId() {
		return operateUnitId;
	}

	public void setOperateUnitId(int operateUnitId) {
		this.operateUnitId = operateUnitId;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public long getLastUnitLeaveTime() {
		return lastUnitLeaveTime;
	}

	public void setLastUnitLeaveTime(long lastUnitLeaveTime) {
		this.lastUnitLeaveTime = lastUnitLeaveTime;
	}

}
