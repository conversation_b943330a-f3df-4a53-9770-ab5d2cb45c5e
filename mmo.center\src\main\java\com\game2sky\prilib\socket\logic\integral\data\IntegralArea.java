package com.game2sky.prilib.socket.logic.integral.data;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;
import com.game2sky.prilib.core.util.CommonConstants;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterManager;
import com.game2sky.prilib.socket.logic.integral.sort.IntegralSort;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * 天梯积分赛卡区
 * <AUTHOR>
 * @version v0.1 2017年5月16日 下午9:21:14  zhoufang
 */
public class IntegralArea {

	/** 卡区类型 */
	private int areaType;

	private int branch;

	private IntegralAreaRankCache rankCache = new IntegralAreaRankCache();

	private int rankVersion = 1;

	private AtomicBoolean isChange = new AtomicBoolean(false);

	/** 排序中心 */
	private IntegralSort sort = new IntegralSort();

	public IntegralArea(int branch, int areaType) {
		this.branch = branch;
		this.areaType = areaType;
	}

	/**
	 * 加入排序中心
	 * @param playerId 
	 * @param serverId
	 * @param displayServerId
	 * @param integral
	 * @param integralTime
	 * @return 是否改变了排行榜
	 */
	public boolean addPlayer(long playerId, int serverId, int displayServerId, int integral, long integralTime,
								PlayerDisplayInformation information) {
		int addResult = this.sort.addElement(playerId, serverId, integral, integralTime);
		if (addResult == IntegralSort.ADD_REPEAT_NOCHANGE) {
			// 虽然是重复数据,但是玩家的基本信息可能被改变
			List<IntegralRankItem> rankList = rankCache.getRankList();
			for (IntegralRankItem rankItem : rankList) {
				if (rankItem.getPlayerId() == playerId) {
					boolean isChange = compareInformation(rankItem.getInformation(), information);
					if (isChange) {
						rankItem.setInformation(information);
						setRankChange();
					}
					return isChange;
				}
			}
			return false;
		}
		if (addResult == IntegralSort.ADD_REPEAT_CHANGE) {
			// 此玩家从排序中心删除过,可能存在于排行榜
			deletePlayerFromRankCache(playerId);
		}
		// 记录一次新排行榜算法
		this.rankCache.recordNewRank(playerId, serverId, displayServerId, integral, integralTime, information);
//		int rank = this.sort.getRank(integral, integralTime);
//		if (rank > IntegralConst.RANK_MAX) {
//			if (IntegralCenterManager.needCheckRank()) {
//				AMLog.LOG_COMMON.info("天梯分支branch={},areaType={} 的排行榜不变,玩家[id={},积分={},time={},排名{}]无法进入排行榜", branch,
//					areaType, playerId, integral, integralTime, rank);
//			}
//			return false;
//		}
//		// 更新排行榜
//		List<IntegralRankItem> rankList = rankCache.getRankList();
//		IntegralRankItem item = this.rankCache.createRankItem(playerId, serverId, displayServerId, integral,
//			integralTime, information);
//		if (rankList.size() < rank) {
//			rankList.add(item);
//		} else {
//			rankList.add(rank - 1, item);
//			if (rankList.size() > IntegralConst.RANK_MAX) {
//				rankList.remove(rankList.size() - 1);
//			}
//		}
//		if (IntegralCenterManager.needCheckRank()) {
//			AMLog.LOG_COMMON.info("天梯分支branch={},areaType={} 的排行榜更新,新加入玩家[id={},积分={},time={},排名{}]", branch, areaType,
//				playerId, integral, integralTime, rank);
//			safeCheckRankList();
//		}
//		AMLog.LOG_COMMON.info("天梯分支branch={},areaType={} 的排行榜更新, 当前排行榜为rankList={}", branch, areaType,
//			JsonUtils.O2S(rankCache.getRankList()));
		setRankChange();
		return true;
	}

	/**
	 * 比较2次的排行榜基本显示信息是否改变
	 * @param oldInformation
	 * @param newInformation
	 * @return
	 */
	private boolean compareInformation(PlayerDisplayInformation oldInformation, PlayerDisplayInformation newInformation) {
		if (oldInformation.getLevel() != newInformation.getLevel()) {
			return true;
		} else if (!oldInformation.getNickName().equals(newInformation.getNickName())) {
			return true;
		} else if (oldInformation.getGender() != newInformation.getGender()) {
			return true;
		} else if (!oldInformation.getIcon().equals(newInformation.getIcon())) {
			return true;
		} else if (!oldInformation.getUnionName().equals(newInformation.getUnionName())) {
			return true;
		}
		return false;
	}

	public void setRankChange() {
		rankVersion++;
		isChange.set(true);
	}

	/**
	 * 排行榜删除某玩家数据
	 * @param playerId
	 */
	private void deletePlayerFromRankCache(long playerId) {
		List<IntegralRankItem> rankList = rankCache.getRankList();
		java.util.Iterator<IntegralRankItem> iterator = rankList.iterator();
		while(iterator.hasNext()){
			IntegralRankItem next = iterator.next();
			if(next.getPlayerId() == playerId){
				iterator.remove();
				setRankChange();
				break;
			}
		}
//		int index = -1;
//		for (int i = 0; i < rankList.size(); i++) {
//			if (rankList.get(i).getPlayerId() == playerId) {
//				index = i;
//				break;
//			}
//		}
//		if (index != -1) {
//			rankList.remove(index);
//			setRankChange();
//		}
	}

	/**
	 * 从排序中心移除玩家
	 * @param playerId
	 * @param old
	 * @param oldTime
	 */
	public void removePlayer(long playerId, int old, long oldTime) {
		this.sort.deleteElement(playerId, old, oldTime);
		// 更新排行榜
		deletePlayerFromRankCache(playerId);
	}

	/**
	 * 本赛区排行榜是否已经改变
	 * @return
	 */
	public boolean rankIsChange() {
		return isChange.get();
	}

	public void resetChange() {
		isChange.set(false);
	}

	public IntegralAreaRankCache getRankCache() {
		return rankCache;
	}

	public List<IntegralRankItem> getRankList() {
		return rankCache.getRankList();
	}

	public void setRankCache(IntegralAreaRankCache rankCache) {
		this.rankCache = rankCache;
		List<IntegralRankItem> rankList = rankCache.getRankList();
		for (IntegralRankItem item : rankList) {
			this.sort.addElement(item.getPlayerId(), item.getServerId(), item.getIntegral(), item.getIntegralTime());
		}
		this.rankVersion = 1;
	}

	public IntegralSort getSort() {
		return sort;
	}

	public int getAreaType() {
		return areaType;
	}

	public void refreshDisplayInformation(long playerId, List<DisplayInformationType> values) {
		List<IntegralRankItem> rankList = new ArrayList<>(rankCache.getRankList());
		for (IntegralRankItem item : rankList) {
			if (item.getPlayerId() != playerId) {
				continue;
			}
			// 刷新部分
			CommonConstants.refreshPlayerInfo(values, item.getInformation());
		}
	}

	public int getRankVersion() {
		return rankVersion;
	}

	public List<IntegralRankItem> getRankList(int fromIndex, int toIndex) {
		List<IntegralRankItem> rankList = new ArrayList<>(rankCache.getRankList());
		List<IntegralRankItem> result = new ArrayList<IntegralRankItem>();
		for (int i = fromIndex; i <= toIndex; i++) {
			if (rankList.size() <= i) {
				break;
			}
			result.add(rankList.get(i));
		}
		return result;
	}

	public int getRankSize() {
		return rankCache.getRankList().size();
	}

	private void safeCheckRankList() {
		try {
			List<IntegralRankItem> rankList = new ArrayList<>(rankCache.getRankList());
			for (int i = 0; i < rankList.size(); i++) {
				if (i == rankList.size() - 1) {
					break;
				}
				IntegralRankItem current = rankList.get(i);
				IntegralRankItem next = rankList.get(i + 1);
				if (current.getIntegral() < next.getIntegral()) {
					AMLog.fatal("天梯分组[{}]的赛区[{}]排名异常.玩家[id:{},积分:{},时间:{},名次:{}] 和玩家[id:{},积分:{},时间:{},名次:{}]",
						this.branch, this.areaType, current.getPlayerId(), current.getIntegral(),
						current.getIntegralTime(), i + 1, next.getPlayerId(), next.getIntegral(),
						next.getIntegralTime(), i + 2);
					break;
				}
			}
		} catch (Throwable t) {
			AMLog.LOG_ERROR.error(t);
		}
	}

}
