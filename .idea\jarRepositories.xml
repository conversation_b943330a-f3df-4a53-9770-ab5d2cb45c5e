<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="mvnrepository" />
      <option name="name" value="mvnrepository" />
      <option name="url" value="http://www.mvnrepository.com" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://mvnrepository.com/artifact/org.hibernate.common/hibernate-commons-annotations/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jcenter" />
      <option name="name" value="jcenter" />
      <option name="url" value="https://jcenter.bintray.com/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://mvnrepository.com/artifact/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun Maven Central" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="sonatype" />
      <option name="name" value="sonatype" />
      <option name="url" value="https://mvnrepository.com/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="hutong" />
      <option name="name" value="hutong" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://repo.maven.apache.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="hutong" />
      <option name="name" value="hutong" />
      <option name="url" value="https://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun" />
      <option name="name" value="aliyun Maven Central" />
      <option name="url" value="https://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
  </component>
</project>