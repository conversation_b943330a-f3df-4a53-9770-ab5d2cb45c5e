package com.game2sky.prilib.benfu.logic.integral;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;

/**
 * 本服缓存的天梯赛基本信息
 * <AUTHOR>
 * @version v0.1 2018年3月16日 下午3:56:20  zhoufang
 */
public class IntegralBaseCache {

	/** 当前赛季 */
	private int season;
	/** 当前赛季第几周 */
	private int week;
	/** 本周开始时间 */
	private long weekStartTime;
	/** 赛季结束时间  */
	private long seasonEndTime;
	/** 是否已经发放本周奖励 */
	private boolean isSendWeekReward;
	/** 上赛季排行榜 */
	private Map<Integer, List<IntegralRankItem>> lastRankCache = new HashMap<Integer, List<IntegralRankItem>>();

	public int getSeason() {
		return season;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	public int getWeek() {
		return week;
	}

	public void setWeek(int week) {
		this.week = week;
	}

	public long getSeasonEndTime() {
		return seasonEndTime;
	}

	public void setSeasonEndTime(long seasonEndTime) {
		this.seasonEndTime = seasonEndTime;
	}

	public boolean isSendWeekReward() {
		return isSendWeekReward;
	}

	public void setSendWeekReward(boolean isSendWeekReward) {
		this.isSendWeekReward = isSendWeekReward;
	}

	public Map<Integer, List<IntegralRankItem>> getLastRankCache() {
		return lastRankCache;
	}

	public void setLastRankCache(Map<Integer, List<IntegralRankItem>> lastRankCache) {
		this.lastRankCache = lastRankCache;
	}

	public long getWeekStartTime() {
		return weekStartTime;
	}

	public void setWeekStartTime(long weekStartTime) {
		this.weekStartTime = weekStartTime;
	}

}
