package com.game2sky.prilib.socket.logic.homeland.matchv2;


/**
 *
 * <AUTHOR>
 * @version v0.1 2020年5月28日 上午9:28:44  daixl
 */
@Deprecated
public class CenterHomelandGlobalManager {
//
//	private static Map<Long, HomelandBaseInfo> centerHomeLandMap = new HashMap<Long, HomelandBaseInfo>();
//
//	private static HomeLandCreamMatchSort sort = new HomeLandCreamMatchSort();
//
//	public static void addHomeLandBaseInfo(HomelandBaseInfo info) {
//		centerHomeLandMap.put(info.getPlayerId(), info);
//	}
//
//	public static HomelandBaseInfo getByPlayerId(long playerId) {
//		return centerHomeLandMap.get(playerId);
//	}
//
//	public static HomeLandCreamMatchSort getSort() {
//		return sort;
//	}
//
//	public static int getCanGet(long playerId, int enemyLobbyLevel, int enemyWarHouseLevel) {
//		HomelandBaseInfo info = centerHomeLandMap.get(playerId);
//		if (info == null || enemyWarHouseLevel == 0) {
//			return 0;
//		}
//		int canRob = info.getCanRob();
//		int protect = info.getProtect();
//
//		canRob -= protect;
//		if (canRob <= 0) {
//			return 0;
//		}
//
//		int lobbyLevel = info.getLobbyLevel();
//		if (enemyLobbyLevel > lobbyLevel) {
//			int num = enemyLobbyLevel - lobbyLevel;
//			float percent = BaseService.getConfigFloatByDefault(PriConfigKeyName.HOMELAND_ROB_REWARD_DECREASE_PERCENT);
//			percent *= num;
//			canRob = (int) (canRob * (1 - percent));
//		}
//		if(canRob < 0){
//			return 0;
//		}
//
//		DictHomeBuildingLev dict = DictHomeBuildingLev.getByTypeAndLev(HomeBuildingType.WarHouse.value(),
//			enemyWarHouseLevel);
//		HomeBuildWarParamData paramData = (HomeBuildWarParamData)dict.getParamData();
//		int maxCanRob = paramData.getMaxCanRob();
//		if (canRob > maxCanRob) {
//			canRob = maxCanRob;
//		}
//		return canRob;
//		
//		return 0;
//	}
//
//	public static void changeBarrel(long playerId, int oldCream, int newCream) {
//		sort.deleteElement(playerId, oldCream);
//		sort.addElement(playerId, newCream);
//	}
//
//	public static Collection<HomelandBaseInfo> getAllHomeLand(){
//		return Collections.unmodifiableCollection(centerHomeLandMap.values());
//	}
}
