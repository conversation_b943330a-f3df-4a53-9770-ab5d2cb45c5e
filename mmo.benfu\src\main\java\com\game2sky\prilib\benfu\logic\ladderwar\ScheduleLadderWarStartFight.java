package com.game2sky.prilib.benfu.logic.ladderwar;

import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarCreateFight;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.struct.ServerAddress;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO 延迟开始争霸赛战斗
 *
 * <AUTHOR>
 * @version v0.1 2018年12月3日 下午8:26:48  guojijun
 */
class ScheduleLadderWarStartFight extends ScheduledMessage {

	static final int DELAY = 1000;

	private SSLadderWarCreateFight msg;
	private LadderWarBenfuService service;
	private ServerSocketAddress serverSocketAddress;
	
	ScheduleLadderWarStartFight(SSLadderWarCreateFight msg, LadderWarBenfuService service, ServerSocketAddress serverSocketAddress) {
		super(System.currentTimeMillis());
		this.msg = msg;
		this.service = service;
		this.serverSocketAddress = serverSocketAddress;
	}

	@Override
	public void execute() {
		ServerAddress serverAddress = this.msg.getCreateFightInfo().getAddress();
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(serverAddress.getFlag());
		if (connectStatus == ClientConnectStatus.CONNECTED) {
			this.service.sendCreateFightMsg(msg);
		} else {
			CrossServerManager.init(serverSocketAddress, true);
			ScheduleLadderWarStartFight scheduleLadderWarStartFight = new ScheduleLadderWarStartFight(msg,
				this.service, serverSocketAddress);
			Globals.getScheduleService().scheduleOnce(scheduleLadderWarStartFight, ScheduleLadderWarStartFight.DELAY);
		}
	}

}
