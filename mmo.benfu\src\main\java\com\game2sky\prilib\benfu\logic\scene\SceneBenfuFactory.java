package com.game2sky.prilib.benfu.logic.scene;

import org.springframework.stereotype.Component;

import com.game2sky.prilib.core.socket.logic.gvo.scene.GvoIslandSceneController;
import com.game2sky.prilib.core.socket.logic.gvo.scene.GvoSceneController;
import com.game2sky.prilib.core.socket.logic.scene.base.CommonScene;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneMapType;
import com.game2sky.prilib.core.socket.logic.scene.city.CityScene;
import com.game2sky.prilib.core.socket.logic.scene.wild.WildScene;
import com.game2sky.publib.socket.logic.scene.ISceneFactory;
import com.game2sky.publib.socket.logic.scene.base.AbstractScene;

/**
 * TODO 场景工厂
 *
 * <AUTHOR>
 * @version v0.1 2017-4-25 下午8:48:27  guojijun
 */
@Component
public class SceneBenfuFactory implements ISceneFactory {

	@Override
	public AbstractScene createScene(String serverFlag, int sceneId, int sceneMapId, int sceneType) {
		SceneMapType mapType = SceneMapType.indexOf(sceneType);
		if (mapType == null) {
			return null;
		}
		switch (mapType) {
			case CITY:
				return new CityScene(serverFlag, sceneId, sceneMapId);
			case WILD:
				return new WildScene(serverFlag, sceneId, sceneMapId);
			case GVO_ISLAND:
				return new CommonScene(serverFlag, sceneId, sceneMapId, new GvoIslandSceneController());
			case GVO:
				return new CommonScene(serverFlag, sceneId, sceneMapId, new GvoSceneController());
			default:
				return null;
		}
	}
}
