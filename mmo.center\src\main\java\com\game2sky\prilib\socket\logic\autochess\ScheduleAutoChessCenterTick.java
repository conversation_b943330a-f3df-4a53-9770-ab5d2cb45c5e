package com.game2sky.prilib.socket.logic.autochess;

import com.game2sky.prilib.communication.game.autochess.SSAutoChessCenterTick;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 中心服自走棋心跳
 *
 * <AUTHOR>
 * @version v0.1 2019年5月9日 下午3:46:01  <PERSON> Lee
 */
public class ScheduleAutoChessCenterTick extends ScheduledMessage {

    public static final int PERIOD = 1000;

    public ScheduleAutoChessCenterTick() {
        super(System.currentTimeMillis());
    }

    @Override
    public void execute() {
        // 这里只是发送消息
    	SSAutoChessCenterTick tick = new SSAutoChessCenterTick();
    	
        Message message = Message.buildByServerId(0L, 0, tick, null, null, null);
        Globals.getMsgProcessDispatcher().put(message);
    }
}
