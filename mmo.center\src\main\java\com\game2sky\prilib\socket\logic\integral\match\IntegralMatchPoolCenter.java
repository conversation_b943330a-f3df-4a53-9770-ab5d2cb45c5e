package com.game2sky.prilib.socket.logic.integral.match;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterManager;
import com.game2sky.prilib.socket.logic.integral.data.IntegralArena;
import com.game2sky.publib.match.MatchService;

/**
 * 跨服赛匹配中心
 * <AUTHOR>
 * @version v0.1 2017年5月17日 上午11:26:59  zhoufang
 */
public class IntegralMatchPoolCenter {

	private static IntegralMatchPoolCenter instance = new IntegralMatchPoolCenter();

	private IntegralMatchPoolCenter() {
	}

	public static IntegralMatchPoolCenter getInstance() {
		return instance;
	}

	/**
	 * key:branch
	 * key:跨服赛卡区Type   value:匹配池
	 */
	private Map<Integer, Map<Integer, IntegralAreaMatchPool>> pools = new HashMap<>();

	/**
	 * 初始化匹配池
	 */
	public void init() {
		Map<Integer, IntegralArena> integralArenas = IntegralCenterManager.getInstance().getIntegralArenas();
		for (Entry<Integer, IntegralArena> entry : integralArenas.entrySet()) {
			Map<Integer, IntegralAreaMatchPool> branchMap = new HashMap<>();
			this.pools.put(entry.getKey(), branchMap);
			for (Integer key : entry.getValue().getAreas().keySet()) {
				IntegralAreaMatchPool pool = new IntegralAreaMatchPool(entry.getKey(), key);
				branchMap.put(key, pool);
			}
		}
	}

	/**
	 * 处理玩家匹配状态的改变
	 * @param branch
	 * @param areaType
	 * @param serverId
	 * @param playerId
	 * @param action
	 * @param integral
	 * @return
	 */
	public void dealMatchStateChange(int branch, int areaType, int serverId, long playerId, int action, int integral) {
		if (action == IntegralConst.MATCH_STATE_DISCONNECTED) {
			// 玩家掉线,不区分areaType
			Set<Integer> keySet = IntegralConfigCenter.getAreaMap().keySet();
			for (Integer key : keySet) {
				IntegralAreaMatchPool pool = getPool(branch, key);
				if (pool != null) {
					pool.playerDisscount(playerId, serverId, key);
				}
			}
			return;
		}
		IntegralAreaMatchPool pool = getPool(branch, areaType);
		if (pool != null) {
			if (action == IntegralConst.MATCH_STATE_ENTER) {
				pool.addMatchPlayer(playerId, integral, serverId);
			} else if (action == IntegralConst.MATCH_STATE_CANCEL) {
				pool.delteMatchPlayer(playerId);
			} else if (action == IntegralConst.MATCH_STATE_CONTINUE) {
				pool.continueMatch(playerId);
			}
		}
	}

	public void kickServerMatcher(int serverId) {
		int branch = IntegralConfigCenter.getIntegralBranchByServerId(serverId);
		Map<Integer, IntegralAreaMatchPool> map = this.pools.get(branch);
		List<IntegralAreaMatchPool> copyPools = new ArrayList<IntegralAreaMatchPool>(map.values());
		for (IntegralAreaMatchPool pool : copyPools) {
			List<IntegralMatchPlayer> matchObjects = pool.getAllMatchObject();
			for (IntegralMatchPlayer matcher : matchObjects) {
				if (matcher.getServerId() == serverId) {
					pool.deleteMatchObject(matcher.getPlayerId());
				}
			}
		}
	}

	private IntegralAreaMatchPool getPool(int branch, int areaType) {
		Map<Integer, IntegralAreaMatchPool> map = this.pools.get(branch);
		if (map != null) {
			return map.get(areaType);
		}
		return null;
	}

	/**
	 * 获取所有的天梯匹配任务
	 */
	public List<IntegralAreaMatchPool> getAllIntegralAreaMatchPool() {
		List<IntegralAreaMatchPool> list = new ArrayList<>();
		if (pools == null || pools.isEmpty()) {
			return list;
		}

		for (Map<Integer, IntegralAreaMatchPool> branchMap : pools.values()) {
			if (branchMap == null || branchMap.isEmpty()) {
				continue;
			}
			list.addAll(branchMap.values());
		}
		return list;
	}

	/**
	 * 执行匹配
	 * @param branch
	 */
	public void doMatch(int branch) {
		Set<Integer> keySet = IntegralConfigCenter.getAreaMap().keySet();
		for (Integer areaType : keySet) {
			IntegralAreaMatchPool pool = getPool(branch, areaType);
			if (pool == null) {
				continue;
			}
			if (pool.needMatch()) {
				MatchService.getInstance().excuteMatchTask(pool);
			}
		}
	}

}
