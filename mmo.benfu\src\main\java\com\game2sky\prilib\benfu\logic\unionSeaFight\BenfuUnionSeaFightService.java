package com.game2sky.prilib.benfu.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.unionSeaFight.data.USFBenfuUnionRankGroup;
import com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight.BenfuRequsetUnionInfoMessage;
import com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight.ScheduleUSDFSyncCacheData;
import com.game2sky.prilib.benfu.logic.unionSeaFight.manager.IUSFBenfuRankManager;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.activity.ActivityAwardConditionType;
import com.game2sky.prilib.communication.game.growth.SCKVInfo;
import com.game2sky.prilib.communication.game.player.CommonFormationType;
import com.game2sky.prilib.communication.game.rank.USFRankInfo;
import com.game2sky.prilib.communication.game.struct.ItemBaseType;
import com.game2sky.prilib.communication.game.union.UnionBuilding;
import com.game2sky.prilib.communication.game.union.UnionBuildingInfo;
import com.game2sky.prilib.communication.game.union.inner.SSUnionAddSeaFightShopSale;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.DeclareState;
import com.game2sky.prilib.communication.game.unionSeaFight.CSLookUSFReplayList;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFGetOtherFormation;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFGetRankInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFLookFightReplay;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFSetPromotions;
import com.game2sky.prilib.communication.game.unionSeaFight.DeclareUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCLookUSFReplayList;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFActivity;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFActivityApplyInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFBattleRoundInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFGetOtherFormation;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFGetPromotionTechInfos;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFGetRankInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerJoin;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerQuit;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFSetPromotions;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnionApply;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFightSettlement;
import com.game2sky.prilib.communication.game.unionSeaFight.SSCenterUSFActivityOper;
import com.game2sky.prilib.communication.game.unionSeaFight.SSGetUSFUnionInfoRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUFLookFightReplay;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFClientBattleChannelInactive;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCreateReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFFightResult;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFGetDataOnBenfuStart;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerQuit;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncActivityData2Benfu;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksRes;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBattleRound;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBattleState;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBenfuSeasonInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientCampFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientPromotionTechInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFightAllotInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFightStage;
import com.game2sky.prilib.communication.game.unionSeaFight.USFPlayerFormation;
import com.game2sky.prilib.communication.game.unionSeaFight.USFPlayerFormationInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFPlayerInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFPlayerResult;
import com.game2sky.prilib.communication.game.unionSeaFight.USFReplayItem;
import com.game2sky.prilib.communication.game.unionSeaFight.USFResultType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionFightBatchInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionMatchInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionReplayInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.UnionSeaFightType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.data.USFFightTime;
import com.game2sky.prilib.core.dict.domain.DictUSFAwards;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightActivity;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightServerGroup;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightTime;
import com.game2sky.prilib.core.socket.logic.activity.common.OPActivityCommonTools;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.commonformation.CommonFightDataModel;
import com.game2sky.prilib.core.socket.logic.dropgroup.DropGroupAllotService;
import com.game2sky.prilib.core.socket.logic.fight.manager.CommonFightManager;
import com.game2sky.prilib.core.socket.logic.formation.event.IFormationChangeEventListener;
import com.game2sky.prilib.core.socket.logic.formation.event.IFormationChangeEventSource;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneCommonService;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.prilib.core.socket.logic.union.Union;
import com.game2sky.prilib.core.socket.logic.union.constants.USFNoticeIconType;
import com.game2sky.prilib.core.socket.logic.union.constants.UnionChatConstants;
import com.game2sky.prilib.core.socket.logic.union.constants.UnionSeaFightConstant;
import com.game2sky.prilib.core.socket.logic.union.service.AbstractUnionSeaFightService;
import com.game2sky.prilib.core.socket.logic.union.unionBase.UnionBaseModel;
import com.game2sky.prilib.core.socket.logic.union.unionMember.UnionMemberModel;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightActivityData;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightModel;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.event.IUnoinSeaFightPlayerJoinEventSource;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.event.IUnoinSeaFightResultEventSource;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.replay.IUnionSeaFightReplayManager;
import com.game2sky.prilib.core.socket.logic.union.unionShopSale.UnionShopSaleType;
import com.game2sky.prilib.core.socket.logic.union.util.CheckAuthorityUtil;
import com.game2sky.prilib.dbs.model.InstUnionSeaFightReplay;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.chat.data.ChatChannel;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.communication.game.struct.ItemBase;
import com.game2sky.publib.communication.game.struct.KeyValue;
import com.game2sky.publib.communication.game.struct.KeyValueEntry;
import com.game2sky.publib.communication.game.struct.KeyValueEnum;
import com.game2sky.publib.communication.game.struct.ServerAddress;
import com.game2sky.publib.communication.game.struct.ServerInfo;
import com.game2sky.publib.dict.domain.DictMail;
import com.game2sky.publib.event.EventSourceManager;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossBattleTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveListener;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveSource;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventLinstener;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.util.StringUtils;
import com.game2sky.publib.log.CustomLogPrefixType;
import com.game2sky.publib.log.model.unionsea.UnionSeaFightApplyLog;
import com.game2sky.publib.log.model.unionsea.UnionSeaFightLog;
import com.game2sky.publib.log.model.unionsea.UnionSeaFightMatchEmptyLog;
import com.game2sky.publib.log.model.unionsea.UnionSeaFightPlayerJoinLog;
import com.game2sky.publib.socket.logic.chat.IChatService;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.mail.MailService;
import com.game2sky.publib.socket.logic.mail.model.MailDictIdEnum;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;
import com.game2sky.publib.socket.logic.union.AbstractUnion;
import com.game2sky.publib.util.TimeUtil;
import com.google.common.collect.Lists;

import gnu.trove.map.hash.TLongObjectHashMap;
import gnu.trove.set.hash.TIntHashSet;
import io.netty.channel.Channel;

/**
 * 本服-海战-控制中心<br>
 * 关注：1、公会报名开始、报名截止   <font color="red">报名开始截止时间</font><br>
 * 	   2、 玩家参赛报名截止  <font color="red">参赛截止时间</font><br>
 * 	   3、处理排行
 * <AUTHOR>
 *
 */
@Component
@EventSubscriber(eventSources = { IClientBattleChannelInactiveSource.class, IFormationChangeEventSource.class,
									ITargetServerRestartEventSource.class })
public class BenfuUnionSeaFightService extends AbstractUnionSeaFightService
																			implements
																			IClientBattleChannelInactiveListener,
																			IFormationChangeEventListener,
																			ITargetServerRestartEventLinstener {

	@Autowired
	private IChatService chatService;

	private static final int TRUE = 1;
	private static final int FALSE = 0;

	private final static int LOAD_CENTER_FLAG_NONE = 0;
	private final static int LOAD_CENTER_FLAG_START = 1;
	private final static int LOAD_CENTER_FLAG_FINISH = 2;

	private boolean addNumByUnionLevel = false;// 按公会等级调整出战人数

	/** 下次检查活动时间 */
	public static long nextCheckUSFActivityTime;
	private Map<Integer, AtomicInteger> loadFromCenterFlagMap = new HashMap<>();

	// <groupId, [zoneId]>
	private Map<Integer, TIntHashSet> groupZoneIdArrMap = new HashMap<>();

	// 消息推送的时间保存，避免多次推送
	private TLongObjectHashMap<Map<String, Long>> humanNoticeFlagMap = new TLongObjectHashMap<>();

	public void init() {
		List<DictUnionSeaFightServerGroup> groupList = DictUnionSeaFightServerGroup.getAll();
		if (groupList != null && !groupList.isEmpty()) {
			Map<Integer, ServerInfo> serverMap = CoreBoot.getServerInfoMap();
			ServerInfo serverInfo = null;
			TIntHashSet existSet = new TIntHashSet();
			for (DictUnionSeaFightServerGroup group : groupList) {
				if (group == null || group.getServerIdArr() == null || group.getServerIdArr().length <= 0) {
					continue;
				}
				TIntHashSet zoneIdArr = new TIntHashSet(group.getServerIdArr().length);
				for (int serverId : group.getServerIdArr()) {
					serverInfo = serverMap.get(serverId);
					if (serverInfo == null) {
						continue;
					}
					if(existSet.contains(serverInfo.getZoneId())) {
						continue;
					}
					existSet.add(serverInfo.getZoneId());
					zoneIdArr.add(serverInfo.getZoneId());
				}
				if(zoneIdArr.isEmpty()) {
					continue;
				}
				groupZoneIdArrMap.put(group.getId(), zoneIdArr);
			}
		}

		List<Integer> serverIdList = CoreBoot.getAllServerId();
		if (serverIdList == null || serverIdList.isEmpty()) {
			return;
		}
		initUnionSeaFightActivityData(serverIdList);

		AMLog.LOG_UNION.info("@UnionSeaFight Start the timer to detect naval battle activities");
		// 每分钟执行1次检测
		for (Map.Entry<Integer, TIntHashSet> me : groupZoneIdArrMap.entrySet()) {
			if (me.getKey() == null || me.getValue() == null) {
				continue;
			}
			int[] array = me.getValue().toArray();

			Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleUSFActivity(me.getKey()),
				ScheduleUSFActivity.DELAY, ScheduleUSFActivity.PERIOD);

			// 约战定时
			Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleUSDFSyncCacheData(me.getKey(), array),
				ScheduleUSDFSyncCacheData.DELAY, ScheduleUSDFSyncCacheData.PERIOD);

			if (array.length > 0) {
				Globals.getScheduleService().scheduleOnce(new BenfuRequsetUnionInfoMessage(me.getKey(), array[0]),
					BenfuRequsetUnionInfoMessage.DELAY);
			}
		}

	}

	// =========================定时处理===========================================================================================

	public void doUSFActivitySchedule(int groupId) {
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("@UnionSeaFight =========Perform thread:{}", Thread.currentThread().getName());
		}
		long currTime = System.currentTimeMillis();
		DictUnionSeaFightServerGroup dictGroup = DictUnionSeaFightServerGroup.getByStrategyId(groupId);
		if (dictGroup == null) {
			return;
		}
		DictUnionSeaFightActivity dictUSFActivity = this.getDictUnionSeaFightActivityByGroup(dictGroup);
		if (dictUSFActivity == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Non -configured naval dictionary table DictUnionSeaFightActivity，  groupId:{}", groupId);
			return;
		}

		TIntHashSet zoneIdArr = groupZoneIdArrMap.get(groupId);
		if (zoneIdArr != null && !zoneIdArr.isEmpty()) {
			for (int zid : zoneIdArr.toArray()) {
				List<Integer> serverIdList = CoreBoot.getServerIdList(zid);
				if (serverIdList == null || serverIdList.isEmpty()) {
					continue;
				}
				for (Integer serverId : serverIdList) {
					AtomicInteger loadFlag = loadFromCenterFlagMap.get(serverId);
					if (loadFlag == null) {
						loadFlag = new AtomicInteger(LOAD_CENTER_FLAG_NONE);
						loadFromCenterFlagMap.put(serverId, loadFlag);
					}
					if (loadFlag.get() == LOAD_CENTER_FLAG_NONE) {
						AMLog.LOG_UNION.warn("@UnionSeaFight This service starts, you need to get data from the central service, serverid:{} ", serverId);
						// 从中心服获取下
						SSUSFGetDataOnBenfuStart message = new SSUSFGetDataOnBenfuStart();
						message.setServerId(serverId);
						message.setGroupId(dictGroup.getId());
						// 发给中心服
						CoreBoot.benfuToKuafu(message, 0, zid);
					}
				}
			}
		}

		boolean isNew = initUnionSeaFightActivityData(dictGroup, dictUSFActivity, currTime);
		if (isNew) {
			humanNoticeFlagMap.clear();
			// 清理公会
			if (zoneIdArr != null && !zoneIdArr.isEmpty()) {
				for (int zid : zoneIdArr.toArray()) {
					doUSFUnionCleanSchedule(currTime, zid, true);
				}
			}
		}
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByGroup(dictGroup);
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval war dictionary tables cannot be initialized, DictUnionSeaFightActivity：{}", dictUSFActivity.getId());
			return;
		}
		if (!dictGroup.isOpen(currTime)) {
			return;
		}
		if (!usfActivityData.isReady()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Not initialized yet, UnionSeaFightActivityData：{}", usfActivityData);
			return;
		}
		if (!usfActivityData.inActivityTime(currTime)) {
			return;
		}
		if (zoneIdArr != null && !zoneIdArr.isEmpty()) {
			for (int zid : zoneIdArr.toArray()) {
				doUSFActivitySchedule(currTime, zid, dictGroup, usfActivityData);
			}
		}
	}

	private void doUSFActivitySchedule(long currTime, int zoneId, DictUnionSeaFightServerGroup dictGroup,
										UnionSeaFightActivityData usfActivityData) {
		// 检测是否开启海战活动 按第一个serverId处理
		if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.UNION_SEA_FIGHT, zoneId)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Configuration in the function switch is configured to close the naval battle, all services in the area cannot participate, zoneId：{}", zoneId);
			return;
		}
		if (currTime >= usfActivityData.getClearTime()) {
			// 清理公会
			doUSFUnionCleanSchedule(currTime, zoneId, false);
		} else if (usfActivityData.moreThenMatchTime(currTime)) {
//			// 统计下报名的公会
//			logAllApplyUnions(usfActivityData, zoneId);

			doUSFUnionAutoFightSchedule(currTime, zoneId, usfActivityData);// READT和FIGHT都会给客户端推消息
		}
		if (usfActivityData.inApplyTimeScope(currTime)) {
			// 当前是公会报名时间
			doUSFUnionAutoApplySchedule(currTime, zoneId, usfActivityData, false);
		}
		if (usfActivityData.inPlayerJoinTime(currTime)) {
			// 处理后续自动的玩家, 改成只在凌晨1点到9点之间执行，避免频繁捞取玩家数据
			//int hour = TimeUtils.getHourTime(currTime);
			//if(hour >= 1 && hour < 9) {
				doUSFPlayerAutoJoiny(currTime, zoneId, usfActivityData, false);
			//}
		}
	}

	public void doChangeUSFServerSeason(int zoneId) {
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByZoneId(zoneId);
		if (usfActivityData == null || !usfActivityData.isReady()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight No activity, service:{}", zoneId);
			return;
		}
		doChangeUSFServerSeason(zoneId, usfActivityData);
	}

	private void doChangeUSFServerSeason(int zoneId, UnionSeaFightActivityData usfActivityData) {
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight No guild, service:{}", zoneId);
			return;
		}
		for (AbstractUnion union : unionList) {
			doChangeUSFServerSeason((Union) union, usfActivityData);
		}
	}

	private void doChangeUSFServerSeason(Union union, UnionSeaFightActivityData usfActivityData) {
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {
			return;
		}
		AMLog.LOG_UNION.info("@UnionSeaFight Guild data for changes and cleaning of the season Union.{}  usfModel：{}", union.getUnionId(), usfModel);
		usfModel.changeSeason();
		usfModel.setIntegral(usfActivityData.getDictUSFActivity().getInitIntegral());
		union.getUnionMemberManager().clearUSFPlayerJoinTime();
		union.getUnionSeaFightManager().setModified();
	}

	private void doUSFUnionAutoFightSchedule(long currTime, int zoneId, UnionSeaFightActivityData usfActivityData) {
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight I didn'tClothes:ind a guild, 服：{}", zoneId);
			return;
		}
		// 检测是否需要处理自动申请的公会
		USFUnionFightInfo unionFightInfo = null;
		UnionSeaFightModel usfModel = null;
		for (AbstractUnion absUnion : unionList) {
			if (absUnion == null || absUnion.isDeleteState())
				continue;
			Union union = ((Union) absUnion);
			usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			if (usfModel == null) {// 没有设置自动报名
				continue;
			}
			unionFightInfo = usfModel.getCurrentUnionFightInfo();
			if (unionFightInfo == null) {
				continue;
			}

			if (unionFightInfo.getBattleStage() == USFBattleState.USF_IN_FIGHT) {
				if (currTime >= unionFightInfo.getEndTime()) {
					if (usfModel.canLogSchedule(currTime)) {
						AMLog.LOG_UNION.info("@UnionSeaFight This round of battle is over,DictUSFActivity：{}, FightTimeId：{}, unionId:{}",
							usfActivityData.getDictUSFActivity().getId(), usfModel.getCurrFightTimeId(),
							union.getUnionId());
					}
				} else if (currTime < unionFightInfo.getStartTime()) {
					// 海战已进入准备状态，报名成员可以进入战场进行准备, 还有5分钟正式开始
					sendUnionReadyChat(currTime, union, usfModel);
				} else {
					if (currTime >= unionFightInfo.getStartTime()
						&& usfModel.getFightStage() == USFFightStage.USF_FIGHT_STAGE_READY) {
						usfModel.setFightStage(USFFightStage.USF_FIGHT_STAGE_FIGHT);

						AMLog.LOG_UNION.info("@UnionSeaFight Entering the battle status,DictUSFActivity：{}, FightTimeId：{}, unionId:{}",
							usfActivityData.getDictUSFActivity().getId(), usfModel.getCurrFightTimeId(),
							union.getUnionId());
						unionFightInfo.setBattleStage(USFBattleState.USF_IN_FIGHT);

//						//进入正式开战阶段时，系统会发送一条系统消息到公会频道：海战已正式开始，请报名成员积极参加战斗！
						union.unionSendChatSystem(UnionChatConstants.UNION_CHAT_USF_FIGHT);

						this.noticeIconKVInfoToUnion(union, usfModel, USFNoticeIconType.FIGHT,
							usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_ON), currTime,
							"doUSFUnionAutoFightSchedule");
					}
				}
			}
		}
	}

	private void sendUnionReadyChat(long currTime, Union union) {
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		int leftReadyNum = 5;
		usfModel.setNextChatNoticeReadyTime(currTime);
		usfModel.setChatNoticeReadyTime(leftReadyNum);
		sendUnionReadyChat(currTime, union, usfModel);
		List<Long> playerIdList = usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_ON);
		noticeIconKVInfoToUnion(union, usfModel, USFNoticeIconType.READY, playerIdList, currTime, "sendUnionReadyChat");
	}

	private void sendUnionReadyChat(long currTime, Union union, UnionSeaFightModel usfModel) {
		long nextChatReadyTime = usfModel.getNextChatNoticeReadyTime();
		int leftReadyNum = usfModel.getChatNoticeReadyTime();
		if (nextChatReadyTime >= currTime && leftReadyNum > 0) {
			AMLog.LOG_UNION.info("@UnionSeaFight Entering the preparation state,unionId:{}, The next push time:{}, Surplus{}minute", union.getUnionId(),
				nextChatReadyTime, leftReadyNum);
			union.unionSendChatSystem(UnionChatConstants.UNION_CHAT_USF_READY, leftReadyNum);

			usfModel.setChatNoticeReadyTime(leftReadyNum - 1);
			usfModel.setNextChatNoticeReadyTime(currTime + 52000);// 1分钟后开始提醒
		}
	}

	/**处理自动报名的公会*/
	private void doUSFUnionAutoApplySchedule(long currTime, int zoneId, UnionSeaFightActivityData usfActivityData,
												boolean forceApply) {
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight No guild, service:{}", zoneId);
			return;
		}
		// 检测是否需要处理自动申请的公会
		for (AbstractUnion union : unionList) {
			doUSFUnionAutoApplySchedule(currTime, usfActivityData, (Union) union, forceApply);
		}
	}

	/**处理自动报名的公会*/
	private void doUSFUnionAutoApplySchedule(long currTime, UnionSeaFightActivityData usfActivityData, Union union,
												boolean forceApply) {
		if (union == null || union.isDeleteState())
			return;
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {// 没有设置自动报名
			return;
		}
		if (usfModel.getClearTime() <= 0) {
			usfModel.setClearTime(usfActivityData.getClearTime());
		}
		if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
			// 已经报名了
			// 处理自动参赛的会员
//			doUSFPlayerAutoJoiny(usfActivityData, usfModel, union, currTime, forceApply);
			return;
		}
		if(currTime < usfModel.getNextCheckUnionAutoApplyTime()) {
			return;
		}
		long interval = Globals.getLongValueByDefault(union.getZoneId(), "usf.autoApply.interval", 600000L);
		usfModel.setNextCheckUnionAutoApplyTime(currTime+interval);//10分钟执行一次
		
		boolean needNotice = false;
		if (!forceApply && usfModel.getAutoApply() == FALSE) {// 非系统强制报名，也没有设置自动报名
			needNotice = true;
			if (usfModel.canLogSchedule(currTime)) {
				AMLog.LOG_UNION.info("@UnionSeaFight The guild has not set up automatic registration.{} ", union.getUnionId());
			}
		}
		int matchCondition = matchUnionApplyCondition(union, usfActivityData, false);
		if (matchCondition != ErrorCodeConstants.OPERATION_SUCCESS) {
			needNotice = true;
			if (usfModel.canLogSchedule(currTime)) {
				AMLog.LOG_UNION
					.info("@UnionSeaFight The guild is not met for registration conditions,Union.{}, error.{} ", union.getUnionId(), matchCondition);
			}
		}
		if (needNotice) {
			List<Long> presidentList = union.getUnionMemberManager().getUnionAllVicePresidentAndPresident();
			noticeIconKVInfoToUnion(union, usfModel, USFNoticeIconType.UNION_APPLY, presidentList, currTime,
				"doUSFUnionAutoApplySchedule1");
			//
			List<Long> allMemberList = union.getAllUnionPlayer();
			for (Long persident : presidentList) {
				allMemberList.remove(persident);
			}
			noticeIconKVInfoToUnion(union, usfModel, USFNoticeIconType.PLAYER_JOIN, allMemberList, currTime,
				"doUSFUnionAutoApplySchedule2");
			return;
		}
		union.getUnionSeaFightManager().setApply(currTime, -1);
		AMLog.LOG_BI.info(JsonUtils.O2S(new UnionSeaFightApplyLog(union, TRUE, 0L)));
		AMLog.LOG_UNION.info("@UnionSeaFight  Registration automatically::{}， result：{}", union.getUnionId(), !needNotice);
//		// 处理自动参赛的会员
//		doUSFPlayerAutoJoiny(usfActivityData, usfModel, union, currTime, forceApply);
	}

	private void doUSFPlayerAutoJoiny(long currTime, int zoneId, UnionSeaFightActivityData usfActivityData,
										boolean forceJoin) {
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight I didn't find a guild, Clothes:{}", zoneId);
			return;
		}
		// 检测是否需要处理自动申请的公会
		for (AbstractUnion union : unionList) {
			if (union == null || union.isDeleteState()) {
				continue;
			}
			doUSFPlayerAutoJoiny(usfActivityData, (Union) union, currTime, forceJoin);
		}
	}

	/**处理自动参赛的玩家*/
	private void doUSFPlayerAutoJoiny(UnionSeaFightActivityData usfActivityData, Union union, long currTime,
										boolean forceJoin) {
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {// 没有设置自动报名
			return;
		}
		if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime()) && !usfModel.existDeclare()) {// 公会还没报名海战，也没有报名宣战
			return;
		}
		if(currTime < usfModel.getNextCheckPlayerAutoJoinyTime()) {
			return;
		}
		long interval = Globals.getLongValueByDefault(union.getZoneId(), "usf.autoJoin.interval", 3600000L);
		usfModel.setNextCheckPlayerAutoJoinyTime(currTime+interval);//每小时执行一次
		doUSFPlayerAutoJoiny(usfActivityData, usfModel, union, currTime, forceJoin);
		AMLog.LOG_UNION.info("@UnionSeaFight Handling the players in the guild automatically participating in the competition, :{}, fightMap:{}", 
				union.getUnionId(), usfModel.getFightPlayerMap());
	}

	/**处理自动参赛的玩家*/
	private void doUSFPlayerAutoJoiny(UnionSeaFightActivityData usfActivityData, UnionSeaFightModel usfModel,
										Union union, long currTime, boolean forceJoin) {
		// 自动
		List<Long> playerIdList = union.getAllUnionPlayer();
		if (playerIdList == null || playerIdList.isEmpty()) {
			AMLog.LOG_UNION.debug("@UnionSeaFight There are no members in the guild, union:{}", union.getUnionId());
			return;
		}
		// 变化的玩家
		Map<Long, UnionMemberModel> addPlayerMap = new HashMap<>(playerIdList.size());
		int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
		if (maxFightNum <= 0) {
			AMLog.LOG_ERROR.error("@UnionSeaFight The largest number of players is less than 0, DictUSFActivity:{}",
				usfActivityData.getDictUSFActivityId());
			return;
		}
		int currOnNum = usfModel.getOnPlayerSize();
		boolean needLog = usfModel.canLogSchedule(currTime);
		boolean needAutoSet = false;
		Map<Long, Boolean> fightPlayerMap = usfModel.getFightPlayerMap();
		if(!fightPlayerMap.isEmpty()) {
			//剔除已经被删除的玩家
			Iterator<Long> it = fightPlayerMap.keySet().iterator();
			while(it.hasNext()) {
				Long pid = it.next();
				if(pid == null || union.getUnionMemberManager().getUnionMemberModel(pid) == null) {
					//需删除该玩家
					it.remove();
					needAutoSet = true;
				}
			}
		}
		try {
			for (Long playerId : playerIdList) { //
				UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(playerId);
				if (unionMemberModel == null) {
					continue;
				}
				if (usfActivityData.inPlayerJoinTime(unionMemberModel.getUsfJoinTime())) {
					// 已经报过名
					if (fightPlayerMap.containsKey(playerId)) {
						continue;
					}
					// 报名人不在列表中，修复下
					if (currOnNum < maxFightNum) {
						needAutoSet = true;
						++currOnNum;
						usfModel.addFightPlayer(playerId, UnionSeaFightConstant.FIGHT_PLAYER_ON);
					} else {
						usfModel.addFightPlayer(playerId, UnionSeaFightConstant.FIGHT_PLAYER_OFF);
					}
					continue;
				} else {
					// 检查未报名的人
					if (!needPlayerJoin(union.getZoneId(), unionMemberModel, forceJoin, currTime, needLog)) {
						continue;
					}
					if (!fightPlayerMap.containsKey(playerId)) {
						if (currOnNum < maxFightNum) {
							needAutoSet = true;
							++currOnNum;
							usfModel.addFightPlayer(playerId, UnionSeaFightConstant.FIGHT_PLAYER_ON);
						} else {
							usfModel.addFightPlayer(playerId, UnionSeaFightConstant.FIGHT_PLAYER_OFF);
						}
					}
					unionMemberModel.setUsfJoinTime(currTime);
					addPlayerMap.put(unionMemberModel.getPlayerId(), unionMemberModel);
				}
			}
		} catch (Exception e) {
			AMLog.LOG_UNION.error("@UnionSeaFight Players in the guild automatically participate, unionId:{}" + union.getUnionId() + ", playerId:"
									+ usfModel.getAllFightPlayerIds(), e);
		}
		if (!needAutoSet) {
			currOnNum = usfModel.getOnPlayerSize();
			if (currOnNum > maxFightNum) {
				needAutoSet = true;
			} else if (currOnNum < maxFightNum) {
				List<Long> offPlayerList = usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_OFF);
				if (offPlayerList != null && !offPlayerList.isEmpty()) {
					//
					AMLog.LOG_UNION.warn("@UnionSeaFight The number of people in the battle is less than 30, but there are people who have not played,Forced automatic deployment unionId:{}, fightPlayerMap:{}",
						union.getUnionId(), fightPlayerMap);
					needAutoSet = true;
				}
			}
		}
		// 这个公会的一起保存
		if (!addPlayerMap.isEmpty()) {
			union.getUnionMemberManager().setModified(addPlayerMap.keySet());

			sendMailOnPlayerJoin(union.getZoneId(), addPlayerMap.keySet().toArray(new Long[] {}));

			AMLog.LOG_UNION.info("@UnionSeaFight Players in the guild automatAll applicants participate, guild:{}， 新增玩家:{}, 所有报名人员:{}", union.getUnionId(),
				addPlayerMap.keySet(), fightPlayerMap);

			IUnoinSeaFightPlayerJoinEventSource eventSource = EventSourceManager.getInstance().getEventSource(
				IUnoinSeaFightPlayerJoinEventSource.class);
			if (eventSource != null) {
				long[] playerIdArr = new long[addPlayerMap.size()];
				int index = 0;
				for (Long pid : addPlayerMap.keySet()) {
					playerIdArr[index] = pid;
					++index;
				}
				eventSource.onPlayerJoin(false, union.getZoneId(), playerIdArr);
			}
		}
		if (needAutoSet) {
			// 一键部署
			union.getUnionSeaFightManager().autoSetFightList(maxFightNum, needLog);
		}
	}

	private boolean needPlayerJoin(int zoneId, UnionMemberModel memberModel, boolean forceJoin, long currTime,
									boolean needLog) {
		Human human = Globals.getHuman(zoneId, memberModel.getPlayerId(), Human.class);
		if (memberModel.getUsfAutoJoin() == TRUE || forceJoin) {
			int matchCondition = ErrorCodeConstants.OPERATION_SUCCESS;
			if(human != null) {
				matchCondition = this.matchPlayerJoinCondition(human, needLog);
			} else {
				matchCondition = this.matchPlayerJoinCondition(zoneId, memberModel, needLog);
			}
			if(matchCondition == ErrorCodeConstants.OPERATION_SUCCESS) {
				// 满足条件， 报名
				return true;
			} else if(matchCondition == ErrorCodeConstants.USF_PLAYER_JOIN_NO_FORMATION) {
				//没有阵容的，取消自动报名状态
				memberModel.setUsfAutoJoin(FALSE);
				AMLog.LOG_UNION.info("@UnionSeaFight Players who register automatically, but there is no lineup, cancel the automatic registration, playerId:{}", memberModel.getPlayerId());
			}
		}
		if (human != null && human.isOnline()) {
			noticeIconKVInfoToHuman(human, USFNoticeIconType.PLAYER_JOIN, false, currTime, "needPlayerJoin");
		}
		return false;
	}

	private void logAllApplyUnions(UnionSeaFightActivityData usfActivityData, int zoneId) {
		// 查找
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight No guild, service:{}", zoneId);
			return;
		}
		// 检测是否需要处理自动申请的公会
		int applyNum = 0;
		int matchNum = 0;
		int notMatchNum = 0;
		Union union = null;
		for (AbstractUnion absUnion : unionList) {
			if (absUnion == null)
				continue;
			union = (Union) absUnion;
			UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			if (usfModel == null) {// 没有设置自动报名
				continue;
			}

			if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
				++applyNum;
				continue;
			}
			if (matchUnionApplyCondition(union, usfActivityData, false) == ErrorCodeConstants.OPERATION_SUCCESS) {
				++matchNum;
			} else {
				++notMatchNum;
			}
		}
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("zoneId", String.valueOf(zoneId));
		paramMap.put("applyNum", String.valueOf(applyNum));
		paramMap.put("matchNum", String.valueOf(matchNum));
		paramMap.put("notMatchNum", String.valueOf(notMatchNum));
		AMLog.LOG_BI.info("_UNION_SEA_FIGHT_ApplyCount_:" + paramMap);
	}

	/**处理海战清理*/
	private void doUSFUnionCleanSchedule(long currTime, int zoneId, boolean force) {
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList == null || unionList.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight No guild, service:{}", zoneId);
			return;
		}
		// 检测是否需要处理自动申请的公会
		for (AbstractUnion union : unionList) {
			doUSFUnionCleanSchedule(currTime, (Union) union, force);
		}
	}

	private void doUSFUnionCleanSchedule(long currTime, Union union, boolean force) {
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {
			return;
		}
		if (!force) {// 非强制
			if (usfModel.getClearTime() <= 0 || currTime < usfModel.getClearTime()) {
				return;
			}
//			if (usfModel.getUnionFightInfo().isEmpty()) {
//				return;
//			}
		}
		union.getUnionMemberManager().clearUSFPlayerJoinTime();

		usfModel.clear();
		
		union.getUnionSeaFightManager().setModified();
		AMLog.LOG_UNION.info("@UnionSeaFight Clean up naval data, Union.{}", union.getUnionId());
	}

	// =========================================================================================================================
	
	
	public UnionSeaFightActivityData getUnionSeaFightActivityDataByZoneId(int zoneId) {
		List<Integer> serverIdList = CoreBoot.getServerIdList(zoneId);
		if (serverIdList == null || serverIdList.isEmpty()) {
			return null;
		}
		Integer defaultServerId = serverIdList.get(0);
		if (defaultServerId == null) {
			return null;
		}
		return this.getUnionSeaFightActivityDataByServerId(defaultServerId.intValue());
	}

	/**
	 * 最大上阵人数，跟公会等级走
	 * @param union
	 * @param dictUSFActivity
	 * @return
	 */
	private int getMaxFightPlayerNum(Union union, DictUnionSeaFightActivity dictUSFActivity) {
		if (addNumByUnionLevel) {
			UnionBaseModel unionBase = union.getUnionBaseManager().getUnionModel();
			int unionLevel = unionBase.getUnionLevel().intValue();
			if (unionLevel <= dictUSFActivity.getUnlockUnionLev()) {
				return dictUSFActivity.getMaxFightPlayerNum();
			}
			int disLevel = unionLevel - dictUSFActivity.getUnlockUnionLev();
			int maxNum = dictUSFActivity.getMaxFightPlayerNum() + disLevel;
			if (maxNum > 30) {
				return 30;
			}
			return maxNum;
		} else {
			return dictUSFActivity.getMaxFightPlayerNum();
		}
	}

	/**
	 * 中心服返回海战数据
	 * @param group
	 * @param activityStage
	 * @param matchResult
	 */
	public void syncActivityData2Benfu(SSUSFSyncActivityData2Benfu syncMessage) {
		AtomicInteger loadFromCenterFlag = loadFromCenterFlagMap.get(syncMessage.getServerId());

		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByGroup(syncMessage.getGroupId());
		if (usfActivityData == null) {
			AMLog.LOG_UNION
				.info("@UnionSeaFight Can't find the activity and cannot process the synchronous data returned by the center service, loadFromCenter:{}, serverId:{}, stage:{}, group:{}",
					loadFromCenterFlag.get(), syncMessage.getServerId(), syncMessage.getGroupId(),
					syncMessage.getGroupId());
			loadFromCenterFlag.set(LOAD_CENTER_FLAG_NONE);
			return;
		}
		loadFromCenterFlag.set(LOAD_CENTER_FLAG_FINISH);
		AMLog.LOG_UNION.info(
			"@UnionSeaFight Receive the synchronous data returned from the central service, loadFromCenter:{}, serverId:{}, group:{}, usfActivityData:{}",
			loadFromCenterFlag.get(), syncMessage.getServerId(), syncMessage.getGroupId(), usfActivityData);
		long currTime = System.currentTimeMillis();
		int zoneId = CoreBoot.getZoneId(syncMessage.getServerId());

		List<USFUnionFightBatchInfo> unionBatchInfoList = syncMessage.getUnionBatchInfo();
		if (unionBatchInfoList != null && !unionBatchInfoList.isEmpty()) {
			for (USFUnionFightBatchInfo unionFightBatchInfo : unionBatchInfoList) {
				rebuildUnionFightBatchInfo(usfActivityData, zoneId, currTime, unionFightBatchInfo);
			}
		}
	}

	private void rebuildUnionFightBatchInfo(UnionSeaFightActivityData usfActivityData, int zoneId, long currTime,
											USFUnionFightBatchInfo unionFightBatchInfo) {
		Union union = Globals.getUnion(zoneId, unionFightBatchInfo.getUnionId(), Union.class);
		if (union == null) {
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {
			return;
		}
		List<USFUnionFightInfo> fightInfoList = unionFightBatchInfo.getFightInfo();
		if (fightInfoList != null && !fightInfoList.isEmpty()) {
			int currFigthTimeId = 0;
			int waitFightTimeId = 0;
			int finishFightTimeId = 0;
			for (USFUnionFightInfo fightInfo : fightInfoList) {
				if (fightInfo == null)
					continue;
				USFFightTime usfFightTime = usfActivityData.getDictUSFActivity().getUSFFightTime(
					fightInfo.getFightTimeId());
				fightInfo.setShowTime(usfFightTime.getShowTime());
				usfModel.addBatchFightInfo(fightInfo);

				if (currTime < fightInfo.getStartTime()) {
					if (waitFightTimeId <= 0 || waitFightTimeId > fightInfo.getFightTimeId()) {
						waitFightTimeId = fightInfo.getFightTimeId();
					}
				} else if (currTime > fightInfo.getStartTime() && currTime < fightInfo.getEndTime()) {
					currFigthTimeId = fightInfo.getFightTimeId();// 当前场
				} else if (currTime >= fightInfo.getEndTime()) {
					if (finishFightTimeId < fightInfo.getFightTimeId()) {
						finishFightTimeId = fightInfo.getFightTimeId();// 最近的完成场
					}
				}
				if (fightInfo.getBattleAddress() != null) {
					ServerSocketAddress address = reConnectBattleServer(fightInfo.getBattleAddress());
					usfModel.setBattleServer(address);
				}
			}
			if (currFigthTimeId > 0) {
				usfModel.setCurrFightTimeId(currFigthTimeId);// 存在当前场，直接设置
			} else if (waitFightTimeId > 0) {
				usfModel.setCurrFightTimeId(waitFightTimeId);// 存在为准备场，直接设置
			} else {
				usfModel.setCurrFightTimeId(finishFightTimeId);// 存在为最后一场，直接设置
			}
		}

		List<DeclareUnionInfo> declareUnionInfos = unionFightBatchInfo.getDeclareUnionInfos();
		if (declareUnionInfos != null && declareUnionInfos.size() > 0) {
			for (DeclareUnionInfo info : declareUnionInfos) {
				usfModel.addDeclareUnionInfo(info);
			}
		}
	}

	private ServerSocketAddress reConnectBattleServer(ServerAddress address) {
		if (address == null) {
			return null;
		}
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(address);
		// 是否正在创建连接
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(
			serverAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.info("@UnionSeaFight dealUSFFightAllotReq:: Uniform and" + serverAddress.getFlag() + " Start creating connection");
			}
			// 未建立连接，则立即建立连接
			CrossServerManager.init(serverAddress, true);
		}
		return serverAddress;
	}

	/**
	 * 获取海战活动
	 * 
	 * @param human
	 */
	public void getUnionSeaFightActivity(Human human) {
		SCUSFActivity msg = new SCUSFActivity();
		human.push2Gateway(msg);
	}

	/**
	 * 获取海战报名信息
	 * @param human
	 */
	public void getUSFActivityApplyInfo(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFActivityApplyInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		long currTime = System.currentTimeMillis();
		DictUnionSeaFightServerGroup dictGroup = DictUnionSeaFightServerGroup.getByServerId(human.getServerId());
		if (dictGroup == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFActivityApplyInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, No guild :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFActivityApplyInfo.getCode(),
				ErrorCodeConstants.NO_UNION);
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByGroup(dictGroup);
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  zoneId.{}", human.getZoneId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFActivityApplyInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		// 公会报名状态
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		SCUSFActivityApplyInfo msg = new SCUSFActivityApplyInfo();
		msg.setUnionAutoApply(FALSE);
		msg.setUnionApplyState(FALSE);
		msg.setPlayerAutoJoin(FALSE);
		msg.setPlayerJoinState(FALSE);
		msg.setUnionAutoApply(usfModel.getAutoApply());// 公会自动报名

		if (dictGroup.isOpen(currTime)) {
			if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
				msg.setUnionApplyState(TRUE);// 玩家已经报名了
			}

			UnionMemberModel memberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
			// 玩家参赛状态
			if (memberModel != null) {
				msg.setPlayerAutoJoin(memberModel.getUsfAutoJoin());// 玩家自动参赛

				if (usfActivityData.inPlayerJoinTime(memberModel.getUsfJoinTime())) {
					msg.setPlayerJoinState(TRUE);// 玩家参赛状态 1为参赛
				}
			}
		}

		human.push2Gateway(msg);
	}

	/**会长/副会长设置自动报名*/
	public void doUnionSeaFightAutoApply(Human human, int autoApply) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			// 找不到活动
			AMLog.LOG_UNION.warn("@UnionSeaFight I can't find any naval activities at present  server.{}", human.getServerId());
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 没有公会
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, No guild :{}", human.getPlayerId(), human.getUnionId());
			human
				.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		if (!CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())) {
			// 公会等级不足
			AMLog.LOG_UNION.warn("@UnionSeaFight Only the chairman or deputy chairman can be set, and the authority is insufficient,  Human.{}, 公会 :{}", human.getPlayerId(),
				human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.UNION_ROLE_ERR);
			return;
		}
		int coditionError = matchUnionApplyCondition(union, usfActivityData, true);
		if (coditionError != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(), coditionError);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		union.getUnionSeaFightManager().setAutoApply(autoApply);

		if (autoApply == TRUE) {
			long currTime = System.currentTimeMillis();
			DictUnionSeaFightServerGroup dictGroup = DictUnionSeaFightServerGroup.getByServerId(human.getServerId());
			if (dictGroup != null && dictGroup.isOpen(currTime)) {
				// 当前是报名时间
				if (usfActivityData.inApplyTimeScope(currTime)) {
					doUnionSeaFightApply(currTime, usfActivityData, union, human, usfModel, TRUE);
				}
			}
		} else {
			// 1、取消勾选公会自动报名提示文字：公会已经取消自动报名下次海战,张帅已有提示，
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
//				ErrorCodeConstants.USF_UNION_AUTOAPPLY_CANCEL);
		}
		// 通知客户端
		sendSCUSFUnionApply(union, usfActivityData, usfModel, human);
	}

	/**会长/副会报名申请*/
	public void doUnionSeaFightApply(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare   human.{}", human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		DictUnionSeaFightServerGroup dictGroup = DictUnionSeaFightServerGroup.getByServerId(human.getServerId());
		if (dictGroup == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		if (!dictGroup.isOpen(currTime)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight Naval warfare  human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight I can't find any naval activities at present  server.{}", human.getServerId());
			return;
		}
		if (!usfActivityData.inApplyTimeScope(currTime)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.USF_NOT_APPLY_JOIN_TIME);
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight Not in the current registration time Human.{}, 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			return;
		}
//		int errorCode = matchPlayerJoinCondition(human);
//		if (errorCode != ErrorCodeConstants.OPERATION_SUCCESS) {
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(), errorCode);
//			return;
//		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, No guild, activity:{}", human.getPlayerId(),
				usfActivityData.toString());
			human
				.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		int coditionError = matchUnionApplyCondition(union, usfActivityData, true);
		if (coditionError != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(), coditionError);
			return;
		}
		if (!CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())) {
			AMLog.LOG_UNION.warn("@UnionSeaFight It must be the president or vice president to apply, and the authority is insufficient,  Human.{}, 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
				ErrorCodeConstants.UNION_ROLE_ERR);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		doUnionSeaFightApply(currTime, usfActivityData, union, human, usfModel, FALSE);
		// 通知客户端
		sendSCUSFUnionApply(union, usfActivityData, usfModel, human);
	}

	/**通知客户端，公会报名情况*/
	private void sendSCUSFUnionApply(Union union, UnionSeaFightActivityData usfActivityData,
										UnionSeaFightModel usfModel, Human human) {
		SCUSFUnionApply message = new SCUSFUnionApply();
		message.setUnionAutoApply(usfModel.getAutoApply());
		if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
			message.setUnionApplyState(TRUE);
		} else {
			message.setUnionApplyState(FALSE);
		}
		human.push2Gateway(message);
//		在界面中成功报名时飘字提示：成功报名参加海战,张帅已有提示，
//		human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFUnionApply.getCode(),
//			ErrorCodeConstants.USF_UNION_APPLY_SUCCESS);
	}

	/**
	 * 设置公会报名
	 * @param usfActivityData
	 * @param union
	 * @param human
	 * @param usfModel
	 */
	private void doUnionSeaFightApply(long currTime, UnionSeaFightActivityData usfActivityData, Union union,
										Human human, UnionSeaFightModel usfModel, int autoApply) {
		// 检测报名时间是否是本次的活动
		if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
			// 设置公会报名
			union.getUnionSeaFightManager().setApply(currTime, human.getPlayerId());

//			7.公会报名成功后，会在公会频道发送系统消息：【海战】公会已经成功报名海战！
			union.unionSendChatSystem(UnionChatConstants.UNION_CHAT_USF_APPLY);

			AMLog.LOG_BI.info(JsonUtils.O2S(new UnionSeaFightApplyLog(union, autoApply, human.getPlayerId())));
		}
		// 会长/副会长符合报名条件时，给他主动报名
		if (usfActivityData.inPlayerJoinTime(currTime)) {
			UnionMemberModel memberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
			if (this.matchPlayerJoinCondition(human, true) == ErrorCodeConstants.OPERATION_SUCCESS) {
				// 设置玩家报名
				doPlayerJoinUSFActivity(currTime, usfActivityData, union, usfModel, human, memberModel, FALSE);
			} else {
				// 未报名，则通知
				this.noticeIconKVInfoToHuman(human, USFNoticeIconType.PLAYER_JOIN, false, currTime,
					"doUnionSeaFightApply");
			}
		}

		// 处理公会其他成员的自动报名
		doUSFPlayerAutoJoiny(usfActivityData, usfModel, union, currTime, false);
	}

	/**
	 * 公会报名条件判断
	 * @param union
	 * @param currSubUSFActivity
	 * @return
	 */
	public int matchUnionApplyCondition(Union union, UnionSeaFightActivityData usfActivityData, boolean needLog) {
		int matchUnionApplyCanNotChangeCondition = matchUnionApplyCanNotChangeCondition(union, usfActivityData, needLog);
		if (matchUnionApplyCanNotChangeCondition != ErrorCodeConstants.OPERATION_SUCCESS) {
			return matchUnionApplyCanNotChangeCondition;
		}

		int matchUnionApplyCanChangeCondition = matchUnionApplyCanChangeCondition(union, usfActivityData, needLog);
		if (matchUnionApplyCanChangeCondition != ErrorCodeConstants.OPERATION_SUCCESS) {
			return matchUnionApplyCanChangeCondition;
		}

		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	/**
	 * 公会报名条件判断（可以由满足变成不满足的条件们，例如公会人数，公户资金）
	 * @param union
	 * @param currSubUSFActivity
	 * @return
	 */
	public int matchUnionApplyCanChangeCondition(Union union, UnionSeaFightActivityData usfActivityData, boolean needLog) {
		UnionBaseModel unionBase = union.getUnionBaseManager().getUnionModel();
		if (unionBase.getUnionMemberNum() < usfActivityData.getDictUSFActivity().getNeedPlayerNum()) {
			if (needLog && AMLog.LOG_UNION.isInfoEnabled())
				AMLog.LOG_UNION.info("@UnionSeaFight 公会[{}] 人数[{}]不足 , Can't participate in naval warfare:{} ", union.getUnionId(),
					unionBase.getUnionMemberNum(), usfActivityData.toString());
			return ErrorCodeConstants.USF_UNION_LESS_PLAYER;
		}
		if (union.getUnionCurrencyManager().isUnionMaintainFund()) {
			if (needLog && AMLog.LOG_UNION.isInfoEnabled())
				AMLog.LOG_UNION.info("@UnionSeaFight 公会人数[{}]维护资金已3周不足 ,不能参加海战:{} ", union.getUnionId(),
					usfActivityData.toString());
			return ErrorCodeConstants.USF_UNION_ERROR_LACK;
		}
		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	/**
	 * 公会报名条件判断（不能由满足变成不满足的条件们，例如公会等级，指挥中心等级）
	 * @param union
	 * @param currSubUSFActivity
	 * @return
	 */
	public int matchUnionApplyCanNotChangeCondition(Union union, UnionSeaFightActivityData usfActivityData,
													boolean needLog) {
		UnionBaseModel unionBase = union.getUnionBaseManager().getUnionModel();
		int unionLevel = unionBase.getUnionLevel().intValue();
		if (unionLevel < usfActivityData.getDictUSFActivity().getUnlockUnionLev()) {
			if (needLog && AMLog.LOG_UNION.isInfoEnabled())
				AMLog.LOG_UNION.info("@UnionSeaFight 公会[{}] 等级[{}]不足 , 不能参加海战:{} ", union.getUnionId(), unionLevel,
					usfActivityData.toString());
			return ErrorCodeConstants.USF_UNION_LESS_LEVEL;
		}

		// 解锁指挥中心
		UnionBuildingInfo buildingInfo = union.getUnionBuildingManager().getBuilding(
			UnionBuilding.UNION_COMMAND_CENTER.value());
		if (buildingInfo.getBuildLev() < 1) {
			if (needLog && AMLog.LOG_UNION.isInfoEnabled())
				AMLog.LOG_UNION.info("@UnionSeaFight 公会人数[{}]指挥中心未解锁 ,不能参加海战:{} ", union.getUnionId(),
					usfActivityData.toString());
			return ErrorCodeConstants.USF_UNION_APPLY_NO_CENTER;
		}
		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	/**玩家设置自动参赛*/
	public void doPlayerAutoJoinUSFActivity(Human human, int autoJoin) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战   human.{}", human.getPlayerId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 没有公会,  Human.{}, 活动:{} ", human.getPlayerId(),
				usfActivityData.toString());
			human
				.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		long currTime = System.currentTimeMillis();

		UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
		if (unionMemberModel == null) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 没有公会,  Human.{}, 活动:{} ", human.getPlayerId(),
				usfActivityData.toString());
			human
				.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		unionMemberModel.setUsfAutoJoin(autoJoin);

		int matchCode = this.matchPlayerJoinCondition(human, true);
		if (matchCode != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(), matchCode);
			return;
		}

		if (autoJoin == TRUE) {// 自动报名
			if (usfActivityData.inPlayerJoinTime(currTime)) {
				// usfActivityData.inApplyTimeScope(usfModel.getApplyTime()) &&
				// 公会已报名，且当前是报名时间内
				if (doPlayerJoinUSFActivity(currTime, usfActivityData, union, usfModel, human, unionMemberModel, TRUE)) {
					return;
				}
			}

		} else {
			// 15.2、取消勾选个人自动报名提示文字：您已取消自动报名海战,张帅已有提示，
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.CSUSFUnionAutoApply.getCode(),
//				ErrorCodeConstants.USF_PLAYER_AUTOJOIN_CANCEL);
		}
		union.getUnionMemberManager().setModified(human.getPlayerId());
		sendSCUSFPlayerJoin(usfActivityData, human, unionMemberModel);
	}

	/**玩家参赛申请*/
	public void doPlayerJoinUSFActivity(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战   human.{}", human.getPlayerId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inPlayerJoinTime(currTime)) {// 当前时间不在报名时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
				ErrorCodeConstants.USF_NOT_APPLY_JOIN_TIME);
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前不在报名时间内,  Human.{}, 活动:{} ", human.getPlayerId(),
				usfActivityData.toString());
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 公会不存在了
			AMLog.LOG_UNION.warn("@UnionSeaFight 没有公会,  Human.{}, 活动:{} ", human.getPlayerId(),
				usfActivityData.toString());
			human
				.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		// 检测公会的报名是否是本次的活动
//		if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {// 公会的报名的时间，不是本次活动
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
//				ErrorCodeConstants.USF_NOT_UNION_APPLY);
//			AMLog.LOG_UNION.warn("@UnionSeaFight 玩家报名参赛出错:公会还没报名,  Human.{}, union:{}, 活动:{} ", human.getPlayerId(),
//				union.getUnionId(), usfActivityData.toString());
//			return;
//		}
		// 玩家的报名时间是本次
		UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
		
		int matchCode = this.matchPlayerJoinCondition(human, true);
		if (matchCode != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(), matchCode);
			return;
		}
		if (doPlayerJoinUSFActivity(currTime, usfActivityData, union, usfModel, human, unionMemberModel, FALSE)) {
			return;
		}
		sendSCUSFPlayerJoin(usfActivityData, human, unionMemberModel);
	}

	protected int matchPlayerJoinCondition(int zoneId, UnionMemberModel memberModel, boolean needLog) {
		Human human = Globals.getRealHuman(zoneId, memberModel.getPlayerId());
		if (human != null) {
			return matchPlayerJoinCondition(human, needLog);
		}
		Union union = Globals.getUnion(zoneId, memberModel.getUnionId(), Union.class);
		List<USFPlayerFormationInfo> usfFormationInfos = union.getUnionSeaFightManager().getUsfPlayerFormationInfos(memberModel);
		if (usfFormationInfos == null || usfFormationInfos.isEmpty()) {
			if (needLog) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家报名参赛出错:出战阵容没有设置,  Human.{} ", memberModel.getPlayerId());
			}
			return ErrorCodeConstants.USF_PLAYER_JOIN_NO_FORMATION;
		}
		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	public int matchPlayerJoinCondition(Human human, boolean needLog) {
		List<USFPlayerFormationInfo> list = human.getCommonFormationDataManager().getUSFPlayerFormationInfos();
		if (list == null || list.isEmpty()) {
			if (needLog) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家报名参赛出错:出战阵容没有设置,  Human.{} ", human.getPlayerId());
			}
			return ErrorCodeConstants.USF_PLAYER_JOIN_NO_FORMATION;
		}
		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	/**强制个人报名*/
	public boolean forcePlayerJoin(long currTime, UnionSeaFightActivityData usfActivityData, Union union,
									UnionSeaFightModel usfModel, Human human) {
		if (matchPlayerJoinCondition(human, true) != ErrorCodeConstants.OPERATION_SUCCESS) {
			return false;
		}
		UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
		return doPlayerJoinUSFActivity(currTime, usfActivityData, union, usfModel, human, unionMemberModel, FALSE);
	}

	private boolean doPlayerJoinUSFActivity(long currTime, UnionSeaFightActivityData usfActivityData, Union union,
											UnionSeaFightModel usfModel, Human human,
											UnionMemberModel unionMemberModel, int auto) {
		if (usfActivityData.inPlayerJoinTime(unionMemberModel.getUsfJoinTime())) {// 只处理未报名的
			return false;
		}
		// 玩家的报名时间是本次
		unionMemberModel.setUsfJoinTime(currTime);

		int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
		if (usfModel.getOnPlayerSize() < maxFightNum) {
			// 超过了，设置到备战人员列表
			usfModel.addFightPlayer(human.getPlayerId(), UnionSeaFightConstant.FIGHT_PLAYER_ON);
		} else {
			usfModel.addFightPlayer(human.getPlayerId(), UnionSeaFightConstant.FIGHT_PLAYER_OFF);
		}
		union.getUnionSeaFightManager().setModified();

		sendMailOnPlayerJoin(human.getZoneId(), human.getPlayerId());

		union.getUnionMemberManager().setModified(human.getPlayerId());

		sendSCUSFPlayerJoin(usfActivityData, human, unionMemberModel);

		IUnoinSeaFightPlayerJoinEventSource eventSource = EventSourceManager.getInstance().getEventSource(
			IUnoinSeaFightPlayerJoinEventSource.class);
		if (eventSource != null) {
			eventSource.onPlayerJoin(false, human.getZoneId(), human.getPlayerId());
		}

		AMLog.LOG_BI.info(JsonUtils.O2S(new UnionSeaFightPlayerJoinLog(human, union, auto)));
		return true;
	}

	private void sendMailOnPlayerJoin(int zoneId, Long... playerIds) {
		DictMail mailKickConfig = DictMail.cache.get(MailDictIdEnum.USF_PLAYER_JOIN.getValue());
		String titleKick = mailKickConfig.getMailTitle();
		String contentKick = mailKickConfig.getMailContent();

		MailService.sendMail(MailDictIdEnum.USF_PLAYER_JOIN, titleKick, contentKick, "", 0, null, null, zoneId,
			playerIds);
	}

	private void sendSCUSFPlayerJoin(UnionSeaFightActivityData usfActivityData, Human human,
										UnionMemberModel unionMemberModel) {
		SCUSFPlayerJoin message = new SCUSFPlayerJoin();
		message.setPlayerAutoJoin(unionMemberModel.getUsfAutoJoin());
		if (usfActivityData.inPlayerJoinTime(unionMemberModel.getUsfJoinTime())) {
			message.setPlayerJoinState(TRUE);

//			在界面中成功报名时飘字提示：成功报名参加海战// 张帅已有提示
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFPlayerJoin.getCode(),
//				ErrorCodeConstants.USF_PLAYER_JOIN_SUCCESS);
		} else {
			message.setPlayerJoinState(FALSE);
		}
		human.push2Gateway(message);
	}

	/**
	 * 获取战斗场次信息
	 * @param human
	 */
	public void sendSCUSFBattleRoundInfo(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战  zoneId.{}", human.getZoneId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			// 不在活动时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在活动时间内  serverId.{}, human.{}, usfActivityData:{}",
				human.getServerId(), human.getPlayerId(), usfActivityData);
			return;
		}
		if (usfActivityData.inApplyTimeScope(currTime)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前活动状态 不可查看部署信息, Human.{}, usfActivityData:{}", human.getPlayerId(),
				usfActivityData);
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_OUT_GET_PLAYER_TIME);
			return;
		}
		if (currTime < usfActivityData.getMatchTime()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前活动状态 不可查看部署信息, Human.{}, usfActivityData:{}", human.getPlayerId(),
				usfActivityData);
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_OUT_GET_PLAYER_TIME);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 公会不存在
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, 没有公会 :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.NO_UNION);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		Map<Integer, USFUnionFightInfo> fightInfoMap = usfModel.getUnionFightInfo();
		if (fightInfoMap == null || fightInfoMap.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 公会未产生匹配结果 不可查看部署信息, Human.{}, usfActivityData:{}",
				human.getPlayerId(), usfActivityData);
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFBattleRoundInfo.getCode(),
				ErrorCodeConstants.USF_OUT_GET_PLAYER_TIME);
			return;
		}

		SCUSFBattleRoundInfo message = new SCUSFBattleRoundInfo();
		int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
		message.setMaxNum(maxFightNum);

		List<USFBattleRound> otherRounds = new ArrayList<USFBattleRound>();
		if (fightInfoMap != null && !fightInfoMap.isEmpty()) {

			for (USFUnionFightInfo fightInfo : fightInfoMap.values()) {
				DictUnionSeaFightTime dictFightTime = DictUnionSeaFightTime.getDictUnionSeaFightTime(fightInfo
					.getFightTimeId());
				USFBattleRound battleRound = toUSFBattleRound(usfModel, fightInfo, dictFightTime);
				int seaFightType = dictFightTime.getSeaFightType();
				if (seaFightType == UnionSeaFightType.UNION_SEA_FIGHT.value()) {
					if (dictFightTime.getFightBatchIndex() == usfActivityData
						.getFirstFBIndex(UnionSeaFightType.UNION_SEA_FIGHT)) {
						message.setFirstRound(battleRound);
					} else {
						message.setSecondRound(battleRound);
					}
				} else {
					otherRounds.add(battleRound);
				}
			}
		}

		message.setOnPlayerList(union.getUnionSeaFightManager().getUSFPlayerFightInfos(true));
		message.setOffPlayerList(union.getUnionSeaFightManager().getUSFPlayerFightInfos(false));
		message.setOtherRounds(otherRounds);

		human.push2Gateway(message);
	}

	private USFBattleRound toUSFBattleRound(UnionSeaFightModel usfModel, USFUnionFightInfo unionFightInfo,
											DictUnionSeaFightTime dictFightTime) {

		USFBattleRound battleRound = new USFBattleRound();
		battleRound.setFightTimeId(dictFightTime.getId());
		battleRound.setBatchIndex(dictFightTime.getFightBatchIndex());// 下一个版本去掉
		battleRound.setBattleState(unionFightInfo.getBattleStage());
		battleRound.setEnemyServerId(unionFightInfo.getEnemyServerId());
		battleRound.setEnemyUnionName(unionFightInfo.getEnemyUnionName());
		battleRound.setMapId(unionFightInfo.getMapId());
		battleRound.setStartTime(unionFightInfo.getShowTime());
		battleRound.setFightType(dictFightTime.getSeaFightType());

		if (usfModel.getUnionId() == unionFightInfo.getAttackerId()) {
			battleRound.setAdType(DeclareState.HAS_DECLARED.value());
		} else if (usfModel.getUnionId() == unionFightInfo.getDefenderId()) {
			battleRound.setAdType(DeclareState.BEEN_DECLARED.value());
		} else {
			battleRound.setAdType(DeclareState.DECLARE_NONE.value());
		}
		return battleRound;
	}

	private int checkSetFight(UnionSeaFightActivityData usfActivityData, UnionSeaFightModel usfModel) {
		// 检测玩家是否解锁了海战
		if (usfActivityData == null) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  union.{}", usfModel.getUnionId());
			return ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN;
		}
//		if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
//			// 公会未报名
//			AMLog.LOG_UNION.warn("@UnionSeaFight 需先报名,  Human.{}, 公会 :{}", human.getPlayerId(), human.getUnionId());
//			return ErrorCodeConstants.USF_OUT_SET_ON_PLAYER_TIME;
//		}
		long currTime = System.currentTimeMillis();
		if (currTime <= usfActivityData.getMatchTime()) {
			// 不在设置时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在调整时间内,  公会 :{}", usfModel.getUnionId());
			return ErrorCodeConstants.USF_OUT_SET_ON_PLAYER_TIME;
		}
		// usfModel.getBattleRoundMap();
		USFUnionFightInfo fightInfo = usfModel.getCurrentUnionFightInfo();
		if (fightInfo.getBattleStage() == USFBattleState.USF_IN_FIGHT) {
//			if (fightInfo.getEnemyServerId() != UnionSeaFightConstant.MATCH_EMPTY_EMEMY_SERVERID) {
			// 不在设置时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在调整时间内,  公会 :{}, fightInfo：{}", usfModel.getUnionId(), fightInfo);
			return ErrorCodeConstants.USF_OUT_SET_ON_PLAYER_TIME;
//			}
		}
		return 0;
	}

	/**
	 * 会长手动设置上下阵人员
	 * @param human
	 * @param addOnPlayerId 上阵的玩家[来自未出战列表]
	 * @param addOffPlayerId 下阵的玩家[来自出战列表]
	 */
	public void setFightPlayer(Human human, long addOnPlayerId, long addOffPlayerId) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战  zoneId.{}", human.getZoneId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			// 不在活动时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在活动时间内  serverId.{}, human.{}, usfActivityData:{}",
				human.getServerId(), human.getPlayerId(), usfActivityData);
			return;
		}

		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, 没有公会 :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		if (!CheckAuthorityUtil.checkPresident(union, human.getPlayerId())) {
			// 公会等级不足
			AMLog.LOG_UNION.warn("@UnionSeaFight 必须是会长才能设置，权限不足,  Human.{}, 公会 :{}", human.getPlayerId(),
				human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.UNION_ROLE_ERR);
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		if (addOnPlayerId <= 0 || addOffPlayerId <= 0 || addOnPlayerId == addOffPlayerId) {
			// 参数错误
			AMLog.LOG_UNION.warn("@UnionSeaFight 参数错误,  Human.{}, 公会 :{}", human.getPlayerId(), human.getUnionId());
//			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(), ErrorCodeConstants.USF_NOT_FIGHT_PLAYER);
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		UnionMemberModel onUnionMemberModel = union.getUnionMemberManager().getUnionMemberModel(addOnPlayerId);
		UnionMemberModel offUnionMemberModel = union.getUnionMemberManager().getUnionMemberModel(addOffPlayerId);
		if (onUnionMemberModel == null || offUnionMemberModel == null) {
			// 参数错误
			AMLog.LOG_UNION.warn("@UnionSeaFight UnionMember错误,  Human.{}, 公会 :{}", human.getPlayerId(), human.getUnionId());
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		List<USFPlayerFormationInfo> onFormationInfos = union.getUnionSeaFightManager().getUsfPlayerFormationInfos(onUnionMemberModel);
		if(onFormationInfos == null || onFormationInfos.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight onFormation错误,  Human.{}, 公会 :{}", human.getPlayerId(), human.getUnionId());
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		int checkResult = checkSetFight(usfActivityData, usfModel);
		if (checkResult != 0) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(), checkResult);
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		Map<Long, Boolean> fightPlayerMap = usfModel.getFightPlayerMap();
		if (fightPlayerMap == null || fightPlayerMap.isEmpty()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 参数错误,  Human.{}, 公会 :{}", human.getPlayerId(), human.getUnionId());
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		Boolean addOffFlag = fightPlayerMap.get(addOffPlayerId);
		if (addOffFlag == null || addOffFlag.booleanValue() != UnionSeaFightConstant.FIGHT_PLAYER_ON) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_NOT_FIGHT_PLAYER);
			AMLog.LOG_UNION.warn("@UnionSeaFight Player.{}不在出战列表中，无法移除， 公会 :{}， 参赛列表:{} ", addOffPlayerId,
				human.getUnionId(), fightPlayerMap);
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		Boolean addOnFlag = fightPlayerMap.get(addOnPlayerId);
		if (addOnFlag == null || addOnFlag.booleanValue() != UnionSeaFightConstant.FIGHT_PLAYER_OFF) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_IN_FIGHT_PLAYER);
			AMLog.LOG_UNION.warn("@UnionSeaFight Player.{}不在预备列表中，无法移除， 公会 :{}， 参赛列表:{} ", addOnPlayerId,
				human.getUnionId(), fightPlayerMap);
			union.getUnionSeaFightManager().sendSCUSFFightList(human);
			return;
		}
		fightPlayerMap.put(addOffPlayerId, UnionSeaFightConstant.FIGHT_PLAYER_OFF);
		fightPlayerMap.put(addOnPlayerId, UnionSeaFightConstant.FIGHT_PLAYER_ON);
		union.getUnionSeaFightManager().setModified();
		AMLog.LOG_UNION.warn("@UnionSeaFight 公会 :{}， Human.{} 收到设置出战， on:{}, off:{} 参赛列表:{} ", human.getUnionId(),
			human.getPlayerId(), addOnPlayerId, addOffPlayerId, fightPlayerMap);
		union.getUnionSeaFightManager().sendSCUSFFightList(human);
	}

	/**一键设置上下阵人员*/
	public void oneKeySetFightList(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战  zoneId.{}", human.getZoneId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			// 不在活动时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在活动时间内  serverId.{}, human.{}, usfActivityData:{}",
				human.getServerId(), human.getPlayerId(), usfActivityData);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 不在报名时间内
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, 没有公会 :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		if (!CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())) {
			// 公会等级不足
			AMLog.LOG_UNION.warn("@UnionSeaFight 必须是会长或副会长才能设置，权限不足,  Human.{}, 公会 :{}", human.getPlayerId(),
				human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.UNION_ROLE_ERR);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		int checkResult = checkSetFight(usfActivityData, usfModel);
		if (checkResult != 0) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(), checkResult);
			return;
		}
		long disTime = currTime - usfModel.getLastOneKeySetFightTime();
		if (disTime < 10000) {
			disTime = (10000 - disTime) / 1000;
			if (disTime == 0)
				disTime = 1;
			AMLog.LOG_UNION.warn("@UnionSeaFight 一键设置还剩{}秒,  Human.{}, 公会 :{}", disTime, human.getPlayerId(),
				human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFFightList.getCode(),
				ErrorCodeConstants.USF_AUTO_SET_FIGHT_CD, disTime);
			return;
		}
		int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
		union.getUnionSeaFightManager().autoSetFightList(maxFightNum, true);
		union.getUnionSeaFightManager().sendSCUSFFightList(human);
		usfModel.setLastOneKeySetFightTime(Globals.getTimeService().now());

		human.pushErrorMessageToClient(PrivPSEnum.SCUSFFightList.getCode(), ErrorCodeConstants.USF_AUTO_SET_FIGHT_SUCC);
	}

	/**
	 * 通知本服进度
	 * @param groupId
	 * @param stage
	 */
	public void sendBenfuStage(int groupId, int serverId, int stage, List<Long> unionIdList) {
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByGroup(groupId);
		if (usfActivityData == null) {
			return;
		}
//		int zoneId = CoreBoot.getZoneId(serverId);
//		USFActivityStage activityStage = USFActivityStage.valueOf(stage);
//		AMLog.LOG_UNION.info("@UnionSeaFight 收到中心服的进度通知, serverId:{}, stage:{}, unionIdList:{}", serverId,
//			activityStage, unionIdList);
//		if (activityStage != null) {
//			USFActivityStageTmpData tmpData = stageTmpDataMap.get(activityStage);
//			if (tmpData == null) {
//				tmpData = new USFActivityStageTmpData();
//				stageTmpDataMap.put(activityStage, tmpData);
//			}
//			tmpData.setActivityStage(activityStage);
//			tmpData.setGroupId(groupId);
//			tmpData.setZoneId(zoneId);
//			tmpData.setUnionIdList(unionIdList);
//
//			USFNoticeIconType iconType = null;
//
//			if (activityStage == USFActivityStage.READY && unionIdList != null && !unionIdList.isEmpty()) {
//				long currTime = System.currentTimeMillis();
//				Union union = null;
//				UnionSeaFightModel usfModel = null;
//				for (Long unionId : unionIdList) {
//					union = Globals.getUnion(zoneId, unionId, Union.class);
//					if (union == null || union.isDeleteState())
//						continue;
//
//					usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
//					if (usfModel == null) {
//						continue;
//					}
//					noticeIconKVInfoToUnion(union, usfModel, iconType, usfModel.getAllJoinPlayerList(), currTime,
//						"sendBenfuStage");
//				}
//			}
//		}
	}

	/**
	 * 本服给中心服发送海战的公会列表
	 */
	public void doSSGetUSFUnionInfoReq(int groupId) {
		DictUnionSeaFightServerGroup group = DictUnionSeaFightServerGroup.getByStrategyId(groupId);
		if (group == null || group.getServerIdArr() == null || group.getServerIdArr().length <= 0) {
			AMLog.LOG_UNION.error("@UnionSeaFight 获取不到DictServerGroup或ServerId数据, groupId:{}", groupId);
			return;
		}
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByGroup(group);
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  groupId.{}", groupId);
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			AMLog.LOG_UNION.error("@UnionSeaFight 不在海战的时间内，为何获取活动数据, groupId:{},  活动:{}", groupId,
				usfActivityData.toString());
			return;
		}
		TIntHashSet zoneIdSet = groupZoneIdArrMap.get(group.getId());
		if (zoneIdSet == null || zoneIdSet.isEmpty()) {
			AMLog.LOG_UNION.error("@UnionSeaFight 获取不到zoneId数据, groupId:{}", groupId);
			return;
		}
		for (int zoneId : zoneIdSet.toArray()) {
			doSSGetUSFUnionInfoReq(usfActivityData, groupId, zoneId, currTime);
		}
	}

	/**
	 * 本服给中心服发送海战的公会列表
	 */
	private void doSSGetUSFUnionInfoReq(UnionSeaFightActivityData usfActivityData, int groupId, int zoneId,
										long currTime) {
		Map<Integer, List<USFUnionMatchInfo>> serverUnionMap = new HashMap<>();
		List<Integer> serverIdList = CoreBoot.getServerIdList(zoneId);
		List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
		if (unionList != null && !unionList.isEmpty()) {
			Union union = null;
			for (AbstractUnion abstractUnion : unionList) {
				if (abstractUnion == null || abstractUnion.isDeleteState())
					continue;
				union = (Union) abstractUnion;
				UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
				if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
					// 在本次的报名时间内的
					UnionBaseModel unionBase = union.getUnionBaseManager().getUnionModel();
					int unionLV = unionBase.getUnionLevel();
					USFUnionMatchInfo usfUnionInfo = new USFUnionMatchInfo();
					usfUnionInfo.setUnionId(union.getUnionId());
					usfUnionInfo.setIntegral(usfModel.getIntegral());
					usfUnionInfo.setUnionLev(unionLV);
					usfUnionInfo.setUnionName(union.getUnionName());

					Integer serverId = Integer.valueOf(unionBase.getDisplayServerId());
					if (serverId.intValue() == 0) {
						serverId = serverIdList.get(0);
					}
					List<USFUnionMatchInfo> list = serverUnionMap.get(serverId);
					if (list == null) {
						list = new ArrayList<>();
						serverUnionMap.put(serverId, list);
					}
					list.add(usfUnionInfo);
				}
			}
		}
		for (Integer serverId : serverIdList) {
			List<USFUnionMatchInfo> umiList = serverUnionMap.get(serverId);
			doSSGetUSFUnionInfoReq(groupId, zoneId, serverId, umiList);
		}
	}

	private void doSSGetUSFUnionInfoReq(int groupId, int zoneId, int serverId, List<USFUnionMatchInfo> unionList) {
		SSGetUSFUnionInfoRes message = new SSGetUSFUnionInfoRes();
		message.setServerId(serverId);
		message.setGroup(groupId);
		int unionSize = 0;
		if (unionList != null && !unionList.isEmpty()) {
			unionSize = unionList.size();
			message.setUnionInfo(unionList);
		}
		AMLog.LOG_UNION.warn("@UnionSeaFight 给中心服推送参与海战的公会  groupId:{}, zoneId:{}, serverId.{}, unionSize:{}", groupId, zoneId, serverId,
			unionSize);
		// 发给中心服
		CoreBoot.benfuToKuafu(message, 0, zoneId);
	}

	/**收到来自中心服的匹配结果 */
	public void dealUSFUnionMatchResult(int group, int serverId, List<USFUnionFightBatchInfo> matchResult,
										List<Long> firstLeftUnionList, List<Long> secondLeftUnionList) {
		AMLog.LOG_UNION.info(
			"@UnionSeaFight 收到来自中心服的匹配结果, group:{}, matchResult:{}, firstLeftList:{}, secondLeftList:{}", group,
			matchResult, firstLeftUnionList, secondLeftUnionList);
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByGroup(group);
		int zoneId = Globals.getZoneId(serverId);
		long currTime = System.currentTimeMillis();

		if (matchResult != null && !matchResult.isEmpty()) {
			for (USFUnionFightBatchInfo result : matchResult) {
				try {
					dealUSFUnionMatchResult(usfActivityData, result, currTime, zoneId, true);
				} catch(Exception e) {
					AMLog.LOG_ERROR.error("@UnionSeaFight 处理公会匹配结果异常, result:"+result, e);
				}
			}
		}
		// 统计下报名的信息
		this.logAllApplyUnions(usfActivityData, zoneId);
	}

	private void dealUSFUnionMatchResult(UnionSeaFightActivityData usfActivityData, USFUnionFightBatchInfo result,
											long currTime, int zoneId, boolean noticeMatchSuccess) {
		Union union = Globals.getUnion(zoneId, result.getUnionId(), Union.class);
		if (union == null || union.isDeleteState()) {
			for (AbstractUnion au : Globals.getAllUnion()) {
				if(au == null) {
					continue;
				}
				if(au.getUnionId() == result.getUnionId()) {
					AMLog.LOG_UNION.warn("@UnionSeaFight 根据zoneId没找到公会，但在列表里找到了，待处理, zoneId:{}, union:{}", zoneId, result.getUnionId());
//					AMLog.LOG_ERROR.error("@UnionSeaFight  zoneId:"+zoneId+", union:"+result, new RuntimeException());
					union = (Union)au;
					break;
				}
			}
			if(union == null || union.isDeleteState()) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 处理匹配请求时，发现公会不存在, zoneId:{}, union:{}", zoneId, result.getUnionId());
				return;
			}
		}
		DictUnionSeaFightActivity dictUSFActivity = usfActivityData.getDictUSFActivity();
		int firstFightTimeId = Integer.MAX_VALUE;
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		usfModel.setFightStage(USFFightStage.USF_FIGHT_STAGE_WAIT);
		usfModel.copyJoinPlayer2LastSession();
		
		boolean existWaitFight = false;
		List<USFUnionFightInfo> fightInfoList = result.getFightInfo();
		if(fightInfoList != null && !fightInfoList.isEmpty()) {
			for (USFUnionFightInfo fightInfo : fightInfoList) {
				if (fightInfo.getShowTime() == null || fightInfo.getShowTime().isEmpty()) {
					USFFightTime usfFightTime = dictUSFActivity.getUSFFightTime(fightInfo.getFightTimeId());
					fightInfo.setShowTime(usfFightTime.getShowTime());
				}

				usfModel.addBatchFightInfo(fightInfo);
				if (firstFightTimeId > fightInfo.getFightTimeId()) {
					firstFightTimeId = fightInfo.getFightTimeId();
				}

				if (fightInfo.getEnemyServerId() == UnionSeaFightConstant.MATCH_EMPTY_EMEMY_SERVERID) {
					fightInfo.setBattleStage(USFBattleState.USF_FINISH_WIN);
					// 轮空的公会
					DictUnionSeaFightTime dictFightTime = DictUnionSeaFightTime.getDictUnionSeaFightTime(fightInfo
						.getFightTimeId());
					dealAward2MatchEmptyUnion(usfActivityData, union, usfModel, dictFightTime);
				}
				if (fightInfo.getBattleStage() == USFBattleState.USF_WAIT_START) {
					existWaitFight = true;
				}
			}
		} else {
			AMLog.LOG_UNION.warn("@UnionSeaFight 处理匹配请求时，发现公会没有匹配列表, union:{}, 宣战列表:{}", result.getUnionId(), result.getDeclareUnionInfos());
		}
		if (!existWaitFight) {// 没有待开战的， 直接设置公会状态为完成
			usfModel.setFightStage(USFFightStage.USF_FIGHT_STAGE_FINISH);
		}
		if (firstFightTimeId != Integer.MAX_VALUE) {
			usfModel.setCurrFightTimeId(firstFightTimeId);
		}
		if (noticeMatchSuccess) {
			// 通知客户端
			noticeIconKVInfoToUnion(union, usfModel, USFNoticeIconType.WAIT_FIGHT,
				usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_ON), currTime,
				"dealUSFUnionMatchResult");
		}
	}

	private void dealAward2MatchEmptyUnion(UnionSeaFightActivityData usfActivityData, Union union,
											UnionSeaFightModel usfModel, DictUnionSeaFightTime dictFightTime) {
		// TODO 需处理下只奖励一次
		if (usfModel.existRewardFightTimeId(dictFightTime.getId())) {
			return;
		}
		usfModel.setRewardFightTimeId(dictFightTime.getId());
		union.getUnionSeaFightManager().setModified();

		// 奖励配置 这个不可能为空
		DictUSFAwards dictUSFAwards = DictUSFAwards.getDictUSFAwards(USFResultType.USF_RESULT_WIN);
		if (dictUSFAwards == null) {
			AMLog.LOG_ERROR.error("@UnionSeaFight 海战奖励配置为空, union.{}", union.getUnionId());
			return;
		}
		// 加公会经验
		union.getUnionBaseManager().addUnionExp(dictUSFAwards.getUnionExpAdd());
		// 加公会货币
		union.getUnionCurrencyManager().addCurrencyValue(dictUSFAwards.getUnionAwardsList(),
			PrivPSEnum.SSUSFFightResult.getCode());
		// 拍卖行奖励
		List<ItemBase> items = addUnionSalingAward(union.getZoneId(), union.getUnionId(), dictUSFAwards,
			dictFightTime.getId(), USFCampType.USF_CAMP_BLUE.value());
		// 所有报名的玩家发奖
		Set<Long> allJoinPlayerList = usfModel.getAllFightPlayerIds();
		if (allJoinPlayerList != null && !allJoinPlayerList.isEmpty()) {
			for (Long offPid : allJoinPlayerList) {
				USFPlayerResult offResult = new USFPlayerResult(offPid, false, false, 0);
				settleOnePlayerReward(USFResultType.USF_RESULT_WIN, union.getZoneId(), dictUSFAwards, items, offResult,
					union, null);
			}
		} else {
//			Map<Long, PlayerJoinModel> playerJoinModelMap = usfModel.getPlayerJoinModelMap();
//			if(playerJoinModelMap != null && !playerJoinModelMap.isEmpty()) {
//				for (PlayerJoinModel joinModel : playerJoinModelMap.values()) {
//					if(joinModel == null)
//						continue;
//					if(usfActivityData.inPlayerJoinTime(joinModel.getJoinTime())) {
//						System.err.println();
//					}
//				}
//			}
//			System.err.println();
		}
		IUnoinSeaFightResultEventSource eventSource = EventSourceManager.getInstance().getEventSource(
			IUnoinSeaFightResultEventSource.class);
		if (eventSource != null) {
			Set<Long> playerIdSet = usfModel.getAllFightPlayerIds();
			boolean isDeclare = dictFightTime.getSeaFightType() == UnionSeaFightType.UNION_DECLARE_SEA_FIGHT.value();
			if(playerIdSet != null && !playerIdSet.isEmpty()) {
				eventSource.onEndFight(isDeclare, USFResultType.USF_RESULT_WIN, union.getZoneId(), union.getUnionId(),
					playerIdSet);
			}
		}
		// 成员奖励
		AMLog.LOG_UNION.info("@UnionSeaFight 设置公会.{} 的第{}场战斗轮空, 发奖成员:{}", union.getUnionId(), dictFightTime.getId(),
			allJoinPlayerList);

		AMLog.LOG_BI.info(JsonUtils.O2S(new UnionSeaFightMatchEmptyLog(union, dictFightTime.getFightBatchIndex())));
	}

	/**
	 * 处理来自中心服战斗分配
	 * @param serverId
	 * @param fightAllotInfoList
	 */
	public void dealUSFFightAllotReq(int serverId, int fightInfoId, List<USFFightAllotInfo> fightAllotInfoList) {
		AMLog.LOG_UNION.info("@UnionSeaFight 收到来自中心服战斗分配::  serverId:{}, fightInfoId:{}", serverId, fightInfoId);
		if (fightAllotInfoList == null || fightAllotInfoList.isEmpty()) {
			return;
		}
		for (USFFightAllotInfo fightAllotInfo : fightAllotInfoList) {
			if (fightAllotInfo == null) {
				continue;
			}
			try {
				dealUSFFightAllotReq(serverId, fightInfoId, fightAllotInfo);
			} catch(Exception e) {
				AMLog.LOG_ERROR.error("@UnionSeaFight 处理战斗服分配异常, "+fightAllotInfo, e);
			}
		}
	}

	/**
	 * 海战分配到战斗服
	 * @param serverId
	 * @param fightAllotInfo
	 */
	public void dealUSFFightAllotReq(int serverId, int fightTimeId, USFFightAllotInfo fightAllotInfo) {
		// 超时则退出
		if (isAllotReqTimeout(fightAllotInfo.getFightId())) {
			AMLog.LOG_UNION.info("@UnionSeaFight dealUSFFightAllotReq:: 连接超时了，不再分配, fightAllotInfo:{}", fightAllotInfo);
			return;
		}

		int zoneId = CoreBoot.getZoneId(serverId);
		Union union = (Union) Globals.getUnion(zoneId, fightAllotInfo.getUnionId());
		if (union == null || union.isDeleteState()) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 分配海战检测出错：公会.{} 为何没有了", fightAllotInfo.getUnionId());
			return;
		}
		// 分配成功,准备连接战斗服
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(fightAllotInfo.getAddress());

		// 是否正在创建连接
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(
			serverAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.info(
					"@UnionSeaFight dealUSFFightAllotReq:: 本服与{} 开始创建连接, fightTimeId:{} fightAllotInfo:{}",
					serverAddress.getFlag(), fightTimeId, fightAllotInfo);
			}
			// 未建立连接，则立即建立连接
			CrossServerManager.init(serverAddress, true);
		} else {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.info(
					"@UnionSeaFight dealUSFFightAllotReq:: 本服与{} 连接成功, fightTimeId:{} fightAllotInfo:{}",
					serverAddress.getFlag(), fightTimeId, fightAllotInfo);
			}
			// 检测本服是否连接上了战斗服（跨服）
			if (CrossServerManager.isCrossServerConnected(serverAddress.getServerType(), serverAddress.getFlag())) {
				// 从缓存中清理
				removeReqTimeout(fightAllotInfo.getFightId());

				// 推送改公会的玩家到战斗服
				startFight(serverId, fightTimeId, fightAllotInfo, union, serverAddress);
				return;
			}
		}
		// 定时执行
		ScheduleStartUSFFight scheduleMsg = new ScheduleStartUSFFight(serverId, fightTimeId, fightAllotInfo);
		Globals.getScheduleService().scheduleOnce(scheduleMsg, ScheduleStartUSFFight.DELAY);
	}

	private void startFight(int serverId, int fightTimeId, USFFightAllotInfo fightAllotInfo, Union union,
							ServerSocketAddress serverAddress) {
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(serverId);
		if (usfActivityData == null) {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 未配置海战字典表DictUnionSeaFightActivity， 服：{}", union.getZoneId());
			}
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION
					.error(
						"@UnionSeaFight dealUSFFightAllotReq :: serverId：{} , Union:{}, USFModel=null， 无法支持USFFightAllotInfo:{}",
						serverId, union.getUnionId(), fightAllotInfo);
			}
			return;
		}

		DictUnionSeaFightTime dictUSFFightTime = DictUnionSeaFightTime.getDictUnionSeaFightTime(fightTimeId);
		if (dictUSFFightTime == null) {
			AMLog.LOG_UNION.error("@UnionSeaFight dealUSFFightAllotReq 出错 FightInfo=null, fightTimeId:{}, Union:{}, ",
				fightTimeId, union.getUnionId());
		}

//		usfActivityData.setCurrFbIndex(fbIndex, roundIndex);
		usfModel.setCurrFightTimeId(fightTimeId);
		USFUnionFightInfo unionFightInfo = usfModel.getCurrentUnionFightInfo();
		if (unionFightInfo == null) {
			AMLog.LOG_UNION
				.error(
					"@UnionSeaFight dealUSFFightAllotReq 出错 unionFightInfo=null, fightTimeId:{}, Union:{}, UnionFightMap:{} ",
					fightTimeId, union.getUnionId(), usfModel.getUnionFightInfo());
		}
		usfModel.setFightStage(USFFightStage.USF_FIGHT_STAGE_READY);
		unionFightInfo.setBattleStage(USFBattleState.USF_IN_FIGHT);
		// 准备提醒时间
		usfModel.setChatNoticeReadyTime(usfActivityData.getDictUSFActivity().getFightReadyTime() / 60);

		usfModel.setBattleServer(serverAddress);

		USFUnionInfo unionInfo = new USFUnionInfo();
		unionInfo.setCampType(fightAllotInfo.getCampType());
		unionInfo.setServerId(serverId);
		unionInfo.setUnionId(union.getUnionId());
		unionInfo.setUnionName(union.getUnionName());
		unionInfo.setIntegral(usfModel.getIntegral());
		unionInfo.setTechInfos(union.getUnionSeaFightManager().getUnionTechInfos());
		unionInfo.setPromotionIds(union.getUnionSeaFightManager().getPromotions());

		int readyTime = usfActivityData.getDictUSFActivity().getFightReadyTime();
		int fightTime = usfActivityData.getDictUSFActivity().getFightTime();

		long currTime = System.currentTimeMillis();
		if (currTime >= unionFightInfo.getStartTime()) {
			readyTime = 20;// 过了准备时间后，给20秒的时间
			int fightDisTime = (int) (currTime - unionFightInfo.getEndTime()) / 1000;
			if (fightDisTime < fightTime) {
				fightTime = fightDisTime;
			}
		} else {
			int readyDisTime = (int) (unionFightInfo.getStartTime() - currTime) / 1000;
			if (readyDisTime < readyTime) {
				readyTime = readyDisTime;
			}
		}
		if (fightTime <= 0) {
			fightTime = 600;// 默认打10分钟
		}

		SSUSFCreateReq createReq = new SSUSFCreateReq(unionInfo, fightAllotInfo.getMapId(),
			fightAllotInfo.getFightId(), readyTime, fightTime);
//		CoreBoot.dispatch2Server(createReq, 0L, serverId, serverAddress.getFlag());
		
		List<Long> playerIdList = usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_ON);

		AMLog.LOG_UNION.info("@UnionSeaFight 开始分配战斗服, Union:{},fightTimeId:{}, serverAddress:{} , playerIdList:{}", union.getUnionId(),
			fightTimeId, serverAddress, playerIdList);
		// 异步加载战斗数据再推送到战斗服
		USFAsyncCreateIo usfAsyncCreateIo = new USFAsyncCreateIo();
		usfAsyncCreateIo.setBenfuUnionSeaFightService(this);
		usfAsyncCreateIo.setFightPlayerIdList(playerIdList);
		usfAsyncCreateIo.setServerFlag(serverAddress.getFlag());
		usfAsyncCreateIo.setSsUSFCreateReq(createReq);
		usfAsyncCreateIo.setUnion(union);
		Globals.getAsyncService().createOperationAndExecuteAtOnce(usfAsyncCreateIo);
	}

	void dealUSFFightAllotReq(SSUSFCreateReq ssUSFCreateReq, String serverFlag, Collection<Long> fightPlayerIdList,
								Union union) {
		USFUnionInfo unionInfo = ssUSFCreateReq.getUnionInfo();

		List<USFPlayerInfo> usfPlayerList = new ArrayList<>();
		unionInfo.setPlayerInfos(usfPlayerList);
		if (fightPlayerIdList != null && !fightPlayerIdList.isEmpty()) {
			for (Long fightPlayerId : fightPlayerIdList) {
				try {
					if (fightPlayerId == null)
						continue;

					USFPlayerInfo playerInfo = toUSFPlayerInfo(fightPlayerId, union);
					if (playerInfo == null)
						continue;
					usfPlayerList.add(playerInfo);
				} catch (Throwable e) {
					AMLog.LOG_ERROR.error("@UnionSeaFight startUSF load one Player Error {}", fightPlayerId, e);
				}
			}
		}
		boolean falg = CoreBoot.dispatch2Server(ssUSFCreateReq, 0L, unionInfo.getServerId(), serverFlag);
		AMLog.LOG_UNION.info("@UnionSeaFight 将公会及玩家数据发送至战斗服, falg:{} Union:{},battleServer:{}, fightPlayerIdList:{} ",
			falg, union.getUnionId(), serverFlag, fightPlayerIdList);
	}

	public USFPlayerInfo toUSFPlayerInfo(long fightPlayerId, Union union) {
		PlayerDisplayInformation displayInfo = Globals.getPlayerDisplayInformation(fightPlayerId, union.getZoneId());
		if (displayInfo == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight海战开启没有找到PlayerDisplayInformation,fightPlayerId={}", fightPlayerId);
			return null;
		}
		UnionMemberModel memberModel = union.getUnionMemberManager().getUnionMemberModel(fightPlayerId);
		if (memberModel == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight海战开启没有找到memberModel,fightPlayerId={}", fightPlayerId);
			return null;
		}
		final ArrayList<USFPlayerFormation> formations = new ArrayList<>(2);
		CommonFightDataModel commonFightDataModel = CommonFightManager.getFFightSideByType(fightPlayerId,
			union.getZoneId(), CommonFormationType.SEA_FIGHT_TYPE_1);
		if(commonFightDataModel == null || commonFightDataModel.getFightData() == null || commonFightDataModel.getShipInfo() == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight海战开启 玩家没有设置第一场阵容,fightPlayerId={}", fightPlayerId);
			return null;
		}
		if(com.game2sky.prilib.core.dict.domain.DictShip.getByShipId(commonFightDataModel.getShipInfo().getShipId()) == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight海战开启 玩家没有设置第一场阵容的船,fightPlayerId={}, shipId:{}", fightPlayerId, commonFightDataModel.getShipInfo().getShipId());
			return null;
		}
		USFPlayerFormation formation1 = new USFPlayerFormation(commonFightDataModel.getFightData(),
			commonFightDataModel.getShipInfo(), commonFightDataModel.getFightPower());
		formations.add(formation1);
		
		CommonFightDataModel commonFightDataModel2 = CommonFightManager.getFFightSideByType(fightPlayerId,
			union.getZoneId(), CommonFormationType.SEA_FIGHT_TYPE_2);
		if (commonFightDataModel2 != null && commonFightDataModel2.getFightData() != null && commonFightDataModel2.getShipInfo() != null) {
			if(com.game2sky.prilib.core.dict.domain.DictShip.getByShipId(commonFightDataModel2.getShipInfo().getShipId()) != null) {
				USFPlayerFormation formation2 = new USFPlayerFormation(commonFightDataModel2.getFightData(),
					commonFightDataModel2.getShipInfo(), commonFightDataModel2.getFightPower());
				formations.add(formation2);
			}
		}
		USFPlayerInfo playerInfo = new USFPlayerInfo();
		playerInfo.setDisplayInfo(displayInfo);
		playerInfo.setFormations(formations);
		playerInfo.setUnionRoleId(memberModel.getRoleId());
		return playerInfo;
	}

	/**
	 * 处理战斗创建结果
	 */
	public void dealUSFCreateRes(int serverId, long unionId, long fightId, boolean result) {
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(serverId);
		if (usfActivityData == null) {
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 战斗创建失败， 未配置海战字典表DictUnionSeaFightActivity， 服：{}", serverId);
			}
			return;
		}
		AMLog.LOG_UNION.warn("@UnionSeaFight 战斗创建:{}， Union.{}, fightId:{}", result, unionId, fightId);
		if (!result) {
			return;
		}
		int zoneId = CoreBoot.getZoneId(serverId);
		Union union = Globals.getUnion(zoneId, unionId, Union.class);
		if (union != null) {
			// 通知公会，准备按钮
			sendUnionReadyChat(System.currentTimeMillis(), union);
		}
	}

	public boolean isApplyUnionSeaFight(Union union) {
		// 检测是否开启海战活动 按第一个serverId处理
		if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.UNION_SEA_FIGHT, union.getZoneId())) {
			return false;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByZoneId(union.getZoneId());
		if (usfActivityData == null) {
			return false;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			return false;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		// 检测报名时间是否是本次的活动
		if (usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
			return true;
		}
		// 检测是否宣战或被宣了
		Map<Integer, USFUnionFightInfo> fightInfoMap = usfModel.getUnionFightInfo();
		if (!fightInfoMap.isEmpty()) {
//			for (USFUnionFightInfo fightInfo : fightInfoMap.values()) {
//				if (currTime < fightInfo.getEndTime()) {
//					return true;
//				}
//			}
			return true;
		}
		return false;
	}

	public boolean isPlayerJoinUnionSeaFight(Union union, long playerId) {
		if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.UNION_SEA_FIGHT, union.getZoneId())) {
			return false;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByZoneId(union.getZoneId());
		if (usfActivityData == null) {
			return false;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {
			// 公会未报名
			if (usfModel.getUnionFightInfo().isEmpty()) {
				// 也没有宣战
				return false;
			}
		}
		UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(playerId);
		if (unionMemberModel != null) {
			// 检测玩家参赛时间是否是本次的活动
			if (usfActivityData.inPlayerJoinTime(unionMemberModel.getUsfJoinTime())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 超时记录Map, <fightId, time>
	 */
	protected static ConcurrentHashMap<Long, Long> fightUnionMap = new ConcurrentHashMap<>(16);
	private static final long USF_FIGHT_ALLOT_TIMEOUT = 600000;

	private boolean isAllotReqTimeout(long fightId) {
		long currTime = System.currentTimeMillis();
		Long key = Long.valueOf(fightId);
		Long logTime = fightUnionMap.get(key);
		if (logTime == null) {
			logTime = Long.valueOf(currTime);
			fightUnionMap.put(key, logTime);
			return false;
		}
		long disTime = currTime - logTime.longValue();
		return disTime > USF_FIGHT_ALLOT_TIMEOUT;
	}

	private void removeReqTimeout(long fightId) {
		fightUnionMap.remove(Long.valueOf(fightId));
	}

	/**
	 * 只为GM的操作
	 * @param key
	 * @param valueList
	 * @param human
	 */
	public void operUSFByGM(int groupId, List<KeyValue> paramList) {
		String key = getUSFByGMParamValue(paramList, "key");
		int serverId = StringUtils.getInt(getUSFByGMParamValue(paramList, "serverId"));// 非空
		long unionId = StringUtils.getLong(getUSFByGMParamValue(paramList, "unionId"));// 可空参数
		long playerId = StringUtils.getLong(getUSFByGMParamValue(paramList, "playerId"));// 可空
		if (key == null || key.isEmpty() || serverId <= 0) {
			return;
		}
		int zoneId = CoreBoot.getZoneId(serverId);
		if (zoneId <= 0) {
			return;
		}
		DictUnionSeaFightServerGroup group = DictUnionSeaFightServerGroup.getByServerId(serverId);
		if (group == null) {
			group = new DictUnionSeaFightServerGroup();
			group.setId(100000 + serverId);
			group.setServerIds(String.valueOf(serverId));
			group.setActivityId(2);
			DictUnionSeaFightServerGroup.getCache().put(group.getId(), group);
		}
		groupId = group.getId();
		long currTime = System.currentTimeMillis();
		UnionSeaFightActivityData usfActivityData = this.getUnionSeaFightActivityDataByServerId(serverId);
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 未配置海战字典表DictUnionSeaFightActivity， 服：{}", zoneId);
			return;
		}
		if ("apply.all".equalsIgnoreCase(key)) {
			if (!usfActivityData.inApplyTimeScope(currTime)) {
				// 当前不是分配时间
				return;
			}
			// 检测是否需要处理自动申请的公会
			doUSFUnionAutoApplySchedule(currTime, zoneId, usfActivityData, true);

		} else if ("union.apply.clear".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			union.getUnionSeaFightManager().setApply(0L, -1);
		} else if ("player.join.clear".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(playerId);
			if (unionMemberModel != null) {
				unionMemberModel.setUsfJoinTime(0L);
				union.getUnionMemberManager().setModified(playerId);
			}
		} else if ("player.join.all".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			doUSFPlayerAutoJoiny(usfActivityData, usfModel, union, currTime, false);
		} else if ("match".equalsIgnoreCase(key)) {
			if (currTime >= usfActivityData.getPlayerJoinTime()) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 已经开始战斗了，不能再次执行匹配， usfActivityData : {}", usfActivityData);
				return;
			}
			// 通知中心服匹配
			SSCenterUSFActivityOper message = new SSCenterUSFActivityOper(groupId, paramList);
			CoreBoot.benfuToKuafu(message, playerId, zoneId);
		} else if ("clearBattleState".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			if (usfModel != null) {
				usfModel.clearUnionFightInfo();
				union.getUnionSeaFightManager().updateUnionSeaFightModel();
			}
		} else if ("clearFightPlayer".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			if (usfModel != null) {
				usfModel.getFightPlayerMap().clear();
				union.getUnionSeaFightManager().updateUnionSeaFightModel();
			}
		} else if ("resetIntegral".equalsIgnoreCase(key)) {
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null || union.isDeleteState()) {
				return;
			}
			UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			if (usfModel != null) {
				usfModel.setIntegral(usfActivityData.getDictUSFActivity().getInitIntegral());
				union.getUnionSeaFightManager().updateUnionSeaFightModel();
			}
		} else if ("changeSeason".equalsIgnoreCase(key)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  收到赛季变更的通知， serverId.{}", serverId);
			doChangeUSFServerSeason(zoneId, usfActivityData);
		} else if ("autoSetMaxPlayer".equalsIgnoreCase(key)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  处理超出的在线列表， zoneId.{}", zoneId);
			List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
			if (unionList != null && !unionList.isEmpty()) {
				for (AbstractUnion absUnion : unionList) {
					if (absUnion == null)
						continue;
					Union union = (Union) absUnion;
					int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
					UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
					if (usfModel == null)
						continue;
					if (usfModel.getOnPlayerSize() > maxFightNum) {
						union.getUnionSeaFightManager().autoSetFightList(maxFightNum, true);
					}
				}
			}
		} else if ("clearDelPlayer".equalsIgnoreCase(key)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  处理参战列表中不存在的玩家， zoneId.{}", zoneId);
			List<AbstractUnion> unionList = Globals.getAllUnion(zoneId);
			if (unionList != null && !unionList.isEmpty()) {
				Set<Long> memberList = new java.util.HashSet<>();
				for (AbstractUnion absUnion : unionList) {
					if (absUnion == null)
						continue;
					Union union = (Union) absUnion;
					UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
					if (usfModel == null)
						continue;
					memberList.clear();
					memberList.addAll(union.getUnionMemberManager().getUnionMemberId());
					Map<Long, Boolean> fightPlayerMap = usfModel.getFightPlayerMap();
					if (fightPlayerMap != null && !fightPlayerMap.isEmpty()) {
						boolean needReset = false;
						java.util.Iterator<Map.Entry<Long, Boolean>> it = fightPlayerMap.entrySet().iterator();
						while (it.hasNext()) {
							Map.Entry<Long, Boolean> me = it.next();
							if (me == null || me.getKey() == null) {
								it.remove();
								continue;
							}
							Long memberId = me.getKey();
							if (!memberList.contains(memberId)) {
								AMLog.LOG_UNION.warn("@UnionSeaFight 公会发现不存在玩家， union.{}, memberId:{}",
									union.getUnionId(), memberId);
								needReset = true;
								it.remove();
								continue;
							}
						}
						if (needReset) {
							int maxFightNum = getMaxFightPlayerNum(union, usfActivityData.getDictUSFActivity());
							union.getUnionSeaFightManager().autoSetFightList(maxFightNum, true);
						}
					}
				}
			}
		}
	}

	/**
	 * 玩家进入海战(benfu获取玩家工会ID)
	 * 
	 * @param human
	 */
	@Override
	public void doUSFPlayerEnter(Human human, boolean isGm) {
		if (human.getSceneObject().getTeamId() > 0) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  组队状态下不能进入海战， Human.{}", human.getPlayerId());
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(),
				ErrorCodeConstants.USF_PLAYER_ENTER_HAS_TEAM);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  不在公会中， Human.{}, Union.{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), ErrorCodeConstants.NO_UNION);
			return;
		}
		boolean bl = SceneCommonService.canBeginRaid(PrivPSEnum.SCUSFPlayerEnter, human);
		if (!bl) {
			return;
		}
		ServerSocketAddress serverAddress = union.getUnionSeaFightManager().getFightServer();
		if (serverAddress == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight  战斗未开始， Human.{}, Union:{}, USFModel:{}", human.getPlayerId(),
				human.getUnionId(), union.getUnionSeaFightManager().getUnionSeaFightModel());
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), ErrorCodeConstants.USF_NOT_EXISTS);
			return;
		}
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(
			serverAddress.getFlag());
		if (connectStatus != ClientConnectStatus.CONNECTED) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 和战斗服的连接断开,开始重新建立,playerId={},serverFlag={}", human.getPlayerId(),
				serverAddress.getFlag());
			CrossServerManager.init(serverAddress, true);
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), ErrorCodeConstants.USF_NOT_EXISTS);
			return;
		}
		// CSUSFPlayerEnter

		boolean succ = human.enterCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT, serverAddress);
		if (!succ) {
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), ErrorCodeConstants.USF_STATE_ERROR);
			return;
		}
		SSUSFPlayerEnterReq ssusfPlayerEnterReq = new SSUSFPlayerEnterReq();
		ssusfPlayerEnterReq.setPlayerId(human.getPlayerId());
		ssusfPlayerEnterReq.setServerId(human.getServerId());
		ssusfPlayerEnterReq.setUnionId(human.getUnionId());
		succ = CoreBoot.dispatch2Server(ssusfPlayerEnterReq, human.getPlayerId(), human.getServerId(),
			serverAddress.getFlag());
		if (!succ) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 发送SSUSFPlayerEnterReq失败,playerId={}, Union.{},serverFlag={}",
				human.getPlayerId(), human.getUnionId(), serverAddress.getFlag());
			human.exitCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
			human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), ErrorCodeConstants.USF_NOT_EXISTS);
		}
	}

	/**
	 * 战斗服响应玩家进入海战
	 * 
	 * @param human
	 * @param ssUSFPlayerEnterRes
	 */
	public void doUSFPlayerEnterRes(Human human, SSUSFPlayerEnterRes ssUSFPlayerEnterRes) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		if (bl) {
			int errorCode = ssUSFPlayerEnterRes.getErrorCode();
			if (errorCode > 0) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家进入战斗服失败,playerId={}, errorCode={}", human.getPlayerId(),
					errorCode);
				human.exitCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
				human.pushErrorMessageToClient(PrivPSEnum.SCUSFPlayerEnter.getCode(), errorCode);
			} else {
				String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
				AMLog.LOG_UNION.info("@UnionSeaFight 玩家进入海战,屏蔽当前场景消息,playerId={},unionId={},serverFlag={}",
					human.getPlayerId(), human.getUnionId(), serverFlag);
				human.getSceneObject().stopReceiveSceneMsg();
				this.chatService.registerHuman(ChatChannel.CHAT_CHANNEL_USF_CAMP, human);
			}
		} else {
			AMLog.LOG_UNION
				.info("@UnionSeaFight 玩家收到doUSFPlayerEnterRes消息,但是已经不再海战状态,playerId={}", human.getPlayerId());
		}
	}

	/**
	 * 玩家退出海战 <br> 改执行在场景线程中
	 * 
	 * @param human
	 * @param tips 是否提示
	 */
	@Override
	public void doUSFPlayerQuit(Human human, boolean tips) {
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		if (isCrossBattle) {
			String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
			if (!StringUtils.isEmpty(serverFlag)) {
				SSUSFPlayerQuit ssUSFPlayerQuit = new SSUSFPlayerQuit(human.getUnionId(), human.getServerId(),
					human.getPlayerId());
				CoreBoot.dispatch2Server(ssUSFPlayerQuit, human.getPlayerId(), human.getServerId(), serverFlag);
			} else {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家退出海战没有找到serverFlag,playerId={}", human.getPlayerId());
			}
			human.exitCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		} else {
			if (tips) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家并没有进入海战,playerId={}", human.getPlayerId());
			}
		}
		if (tips) {
			human.push2Gateway(new SCUSFPlayerQuit());
		}
		this.chatService.unregisterHuman(ChatChannel.CHAT_CHANNEL_USF_CAMP, human);
	}

	public void doUSFClientBattleChannelInactive(Human human) {
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		if (!isCrossBattle) {
			return;
		}
		AMLog.LOG_UNION.warn("@UnionSeaFight 海战中和战斗服断开连接,强制踢掉玩家,playerId={}", human.getPlayerId());
		human.exitCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
			"@Minc.close.usf.client.battle.channel.inactive");
	}

	/**
	 * 海战结束
	 * @param ssUSFFightResult
	 */
	public void doUSFFightResult(SSUSFFightResult ssUSFFightResult) {
		int zoneId = Globals.getZoneId(ssUSFFightResult.getServerId());
		long unionId = ssUSFFightResult.getUnionId();
		Union union = (Union) Globals.getUnion(zoneId, unionId);
		if (union == null) {
			AMLog.LOG_ERROR.error("@UnionSeaFight 海战结束处理，发现公会不存在 {}", ssUSFFightResult);
			return;
		}
		// 保存回放记录
		addReplayRecord(ssUSFFightResult);
		AMLog.LOG_UNION.info("@UnionSeaFight 海战结束处理 Union.{}", unionId);
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel != null) {// 清理战斗服引用
			usfModel.setBattleServer(null);
		}
		USFUnionFightInfo fightInfo = usfModel.getUnionFightInfo(ssUSFFightResult.getFightId());
		if (fightInfo == null) {
			AMLog.LOG_ERROR.error(
				"@UnionSeaFight USFUnionFightInfo=null, union.{}, fightId={}, fightInfoMap:{}, usfModel={}", unionId,
				ssUSFFightResult.getFightId(), usfModel.getUnionFightInfo(), usfModel);
		}
		int fightTimeId = fightInfo == null ? usfModel.getCurrFightTimeId() : fightInfo.getFightTimeId();
		DictUnionSeaFightTime dictFightTime = DictUnionSeaFightTime.getDictUnionSeaFightTime(fightTimeId);
		// 更新公会信息
		updateUnionData(ssUSFFightResult, union, usfModel, fightInfo, dictFightTime);
		// 奖励配置 这个不可能为空
		DictUSFAwards dictUSFAwards = DictUSFAwards.getDictUSFAwards(ssUSFFightResult.getResultType());
		if (dictUSFAwards == null) {
			AMLog.LOG_ERROR.error("@UnionSeaFight 海战奖励配置为null{}", ssUSFFightResult);
			return;
		}
		// 加公会经验
		union.getUnionBaseManager().addUnionExp(dictUSFAwards.getUnionExpAdd());
		// 加公会货币
		union.getUnionCurrencyManager().addCurrencyValue(dictUSFAwards.getUnionAwardsList(),
			PrivPSEnum.SSUSFFightResult.getCode());
		// 拍卖行奖励
		List<ItemBase> items = addUnionSalingAward(zoneId, unionId, dictUSFAwards, fightTimeId,
			ssUSFFightResult.getMyCampType());
		// 参战玩家发奖
		List<USFPlayerResult> playerResults = ssUSFFightResult.getPlayerResults();
		if (playerResults != null) {
			for (USFPlayerResult playerResult : playerResults) {
				settleOnePlayerReward(ssUSFFightResult.getResultType(), zoneId, dictUSFAwards, items, playerResult,
					union, ssUSFFightResult);
			}
		}
		// 未参赛的玩家发奖
		List<Long> offPlayerList = usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_OFF);
		for (Long offPid : offPlayerList) {
			USFPlayerResult offResult = new USFPlayerResult(offPid, false, false, 0);
			settleOnePlayerReward(ssUSFFightResult.getResultType(), zoneId, dictUSFAwards, items, offResult, union,
				ssUSFFightResult);
		}
		// 给观战的人发消息
		List<Long> viewers = ssUSFFightResult.getViewers();
		if (viewers != null && !viewers.isEmpty()) {
			SCUSFightSettlement scUSFightSettlement = new SCUSFightSettlement();
			scUSFightSettlement.setResultType(ssUSFFightResult.getResultType());
			scUSFightSettlement.setCampFightInfos(ssUSFFightResult.getCampFightInfos());
			scUSFightSettlement.setChangeIntegral(ssUSFFightResult.getChangeIntegral());
			scUSFightSettlement.setUnionAwards(dictUSFAwards.getUnionAwardsList());
			scUSFightSettlement.setAuctionAwards(items);
			scUSFightSettlement.setWinReason(ssUSFFightResult.getWinReason());
			for (Long viewer : viewers) {
				AbstractHuman human = Globals.getHuman(zoneId, viewer);
				if (human != null && human.isOnline()) {
					human.push2Gateway(scUSFightSettlement);
				}
			}
		}
		IUnoinSeaFightResultEventSource eventSource = EventSourceManager.getInstance().getEventSource(
			IUnoinSeaFightResultEventSource.class);
		if (eventSource != null) {
			Set<Long> playerIdSet = usfModel.getAllFightPlayerIds();
			if(playerIdSet != null && !playerIdSet.isEmpty()) {
				boolean isDeclare = dictFightTime.getSeaFightType() == UnionSeaFightType.UNION_DECLARE_SEA_FIGHT.value();
				eventSource.onEndFight(isDeclare, ssUSFFightResult.getResultType(), zoneId, unionId, playerIdSet);
			}
		}

		// 打印bi日志
		AMLog.LOG_BI.info(JsonUtils.O2S(new UnionSeaFightLog(CustomLogPrefixType.UnionSeaFightResult, union,
			ssUSFFightResult)));
	}

	private void addReplayRecord(SSUSFFightResult ssUSFFightResult) {
		try {
			List<USFClientCampFightInfo> campFightInfos = ssUSFFightResult.getCampFightInfos();
			USFClientCampFightInfo left = campFightInfos.get(0);
			USFClientCampFightInfo right = campFightInfos.get(1);
			InstUnionSeaFightReplay replay = new InstUnionSeaFightReplay();
			replay.setReplayId(ssUSFFightResult.getFightId());
			replay.setFightTime(System.currentTimeMillis());// TDDO:确定是开始时间还是结束时间
			replay.setReplayUrl(ssUSFFightResult.getDownloadUrl());
			USFResultType resultType = ssUSFFightResult.getResultType();
			boolean mainUnionWin = resultType == USFResultType.USF_RESULT_WIN
									|| resultType == USFResultType.USF_RESULT_BIG_WIN;
			boolean leftWin = (ssUSFFightResult.getUnionId() == left.getUnionId()) == mainUnionWin;
			replay.setLeftWin(leftWin ? 1 : 0);
			for (USFClientCampFightInfo usfClientCampFightInfo : campFightInfos) {
				// 蓝方算左边 红方算右边
				if (usfClientCampFightInfo.getCampType() == USFCampType.USF_CAMP_BLUE) {
					replay.setLeftServerId(left.getServerId());
					replay.setLeftUnionId(left.getUnionId());
					replay.setLeftUnionName(left.getUnionName());
				} else {
					replay.setRightServerId(right.getServerId());
					replay.setRightUnionId(right.getUnionId());
					replay.setRightUnionName(right.getUnionName());
				}
			}
			int zoneId = Globals.getZoneId(ssUSFFightResult.getServerId());
			IUnionSeaFightReplayManager unionSeaFightReplayManager = Globals.getMemoryData(zoneId).getMemoryDataInit()
				.getUnionSeaFightReplayManager();
			unionSeaFightReplayManager.addInstUnionSeaFightReplay(replay);
		} catch (Exception e) {
		}
	}

	private void updateUnionData(SSUSFFightResult ssUSFFightResult, Union union, UnionSeaFightModel usfModel,
									USFUnionFightInfo fightInfo, DictUnionSeaFightTime dictFightTime) {
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(ssUSFFightResult
			.getServerId());
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}", ssUSFFightResult.getServerId());
			return;
		}
		AMLog.LOG_UNION.info("@UnionSeaFight 更新公会海战数据，  Union.{}的积分由{}变成{}", usfModel.getUnionId(),
			usfModel.getIntegral(), ssUSFFightResult.getIntegral());

		USFNoticeIconType iconType = null;
//		if (dictFightTime.getFightBatchIndex() == usfActivityData.getFirstFBIndex(UnionSeaFightType.UNION_SEA_FIGHT)) {
//			iconType = USFNoticeIconType.FIRST_FINISH;
//		} else {
//			iconType = USFNoticeIconType.SECOND_FINISH;
//		}

		if (usfModel.existUnionFightInfo(USFBattleState.USF_WAIT_START)) {
			iconType = USFNoticeIconType.WAIT_FIGHT;
		} else {
			iconType = USFNoticeIconType.SECOND_FINISH;
		}

		usfModel.setIntegral(ssUSFFightResult.getIntegral());

		long currTime = System.currentTimeMillis();
		this.noticeIconKVInfoToUnion(union, usfModel, iconType,
			usfModel.getAllFightPlayerIds(UnionSeaFightConstant.FIGHT_PLAYER_ON), currTime, "updateUnionData");

		USFBattleState state = null;
		// 保存次数
		switch (ssUSFFightResult.getResultType()) {
			case USF_RESULT_DOGFALL:
				usfModel.addTieNum(1);
				state = USFBattleState.USF_FINISH_TIE;
				break;
			case USF_RESULT_WIN:
			case USF_RESULT_BIG_WIN:
				usfModel.addSuccessNum(1);
				state = USFBattleState.USF_FINISH_WIN;
				break;
			case USF_RESULT_FAILED:
				usfModel.addFailNum(1);
				state = USFBattleState.USF_FINISH_FAIL;
				break;
		}
		if (fightInfo != null) {
			fightInfo.setBattleStage(state);
		}
		usfModel.setFightStage(USFFightStage.USF_FIGHT_STAGE_FINISH);
		usfModel.setRewardFightTimeId(dictFightTime.getId());
		union.getUnionSeaFightManager().updateUnionSeaFightModel();
	}

	/**
	 * 给个人发奖和结算
	 * @param ssUSFFightResult
	 * @param zoneId
	 * @param dictUSFAwards
	 * @param items
	 * @param playerResult
	 * @param union
	 */
	private void settleOnePlayerReward(USFResultType result, int zoneId, DictUSFAwards dictUSFAwards,
										List<ItemBase> items, USFPlayerResult playerResult, Union union,
										SSUSFFightResult ssUSFFightResult) {
		long playerId = playerResult.getPlayerId();
		try {
			UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(playerId);
			if(unionMemberModel == null) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家.{}不在公会.{}中，不能被发奖", playerId, union.getUnionId());
				return;
			}
			if (playerResult.getIsBot()) {
				unionMemberModel.setUsfTrustNum(unionMemberModel.getUsfTrustNum()+1);
			}
			unionMemberModel.setUsfFightNum(unionMemberModel.getUsfFightNum()+1);
			union.getUnionMemberManager().setModified(playerId);
			
			// 贡献
			int playerContribution = playerResult.getIsBot() ? dictUSFAwards.getBotPlayerContribution() : dictUSFAwards
				.getPlayerContribution();
			int contribution = dictUSFAwards.getContributionByKill(playerResult.getContinuityKill()) + playerContribution;
			// 检测双倍活动 公会海战
			double doubleActivityRateWithoutHuman = OPActivityCommonTools.getDoubleActivityRateWithoutHuman(zoneId,
				ActivityAwardConditionType.CARNIVA_UNION_SEAFIGHT_REWARD_DOUBLE);
			contribution = (int) (contribution * (1 + doubleActivityRateWithoutHuman));
			ArrayList<ItemBase> awardsList = Lists.newArrayList(new ItemBase(0, ItemBaseType.UNIONCOIN.value(),
				contribution, 0, false));
			
			String dropGroups = playerResult.getIsBot() ? dictUSFAwards.getBotPlayerDropGroups() : dictUSFAwards.getPlayerDropGroups();
			if (!StringUtils.isEmpty(dropGroups)) {
				List<ItemBase> list = DropGroupAllotService.getInstance().drop(dropGroups);
				if (CollectionUtils.isNotEmpty(list)) {
					awardsList.addAll(list);
				}
			}
			
			// 给还在海战中的玩家发结算界面
			boolean isOnFight = playerResult.getIsOnFight();
			if (isOnFight && ssUSFFightResult != null) {
				AbstractHuman human = Globals.getHuman(zoneId, playerId);
				if (human != null) {
					SCUSFightSettlement scUSFightSettlement = new SCUSFightSettlement();
					scUSFightSettlement.setResultType(ssUSFFightResult.getResultType());
					scUSFightSettlement.setCampFightInfos(ssUSFFightResult.getCampFightInfos());
					scUSFightSettlement.setChangeIntegral(ssUSFFightResult.getChangeIntegral());
					scUSFightSettlement.setUnionAwards(dictUSFAwards.getUnionAwardsList());
					scUSFightSettlement.setPlayerAwards(awardsList);
					scUSFightSettlement.setAuctionAwards(items);
					scUSFightSettlement.setWinReason(ssUSFFightResult.getWinReason());
					human.push2Gateway(scUSFightSettlement);
				}
			}
			// 发邮件
			MailDictIdEnum mailDict = null;
			switch (result) {

				case USF_RESULT_WIN:
				case USF_RESULT_BIG_WIN:
					mailDict = playerResult.getIsBot() ? MailDictIdEnum.USFWinPlayerBotAward
						: MailDictIdEnum.USFWinPlayerAward;
					break;
				default:
					mailDict = playerResult.getIsBot() ? MailDictIdEnum.USFLosePlayerBotAward
						: MailDictIdEnum.USFLosePlayerAward;

			}
			MailService.sendSystemMailWithParams(mailDict, awardsList, zoneId, playerId);
		} catch(Throwable t) {
			AMLog.LOG_ERROR.error("个人发奖和结束异常，Human."+playerId, t);
		}
	}

	/**
	 * 发拍卖行奖励
	 * 
	 * @return
	 */
	private List<ItemBase> addUnionSalingAward(int zoneId, long unionId, DictUSFAwards dictUSFAwards, int fightTimeId,
												int campType) {
		int type = 2;
		long saleEndTime = TimeUtil.getTomorrowDayZero();
		long salingEndTime = saleEndTime + TimeUnit.HOURS.toMillis(12);
		int minAdd = BaseService.getConfigIntByDefault(PriConfigKeyName.UNION_FIGHT_SALE_MINADD);
		DictUnionSeaFightTime dictUnionSeaFightTime = DictUnionSeaFightTime.getDictUnionSeaFightTime(fightTimeId);
		if (dictUnionSeaFightTime.getSeaFightType() == UnionSeaFightType.UNION_SEA_FIGHT.value()) {
			type = dictUnionSeaFightTime.getFightBatchIndex() + 1;
		} else {
			if (campType == USFCampType.USF_CAMP_BLUE.value()) {
				type = UnionShopSaleType.UnionDeclareFightDefender.value();
			} else {
				type = UnionShopSaleType.UnionDeclareFightAttacher.value();
			}
		}
		String dropGroups = dictUSFAwards.getDropGroups();
		List<ItemBase> randomRwards = DropGroupAllotService.getInstance().drop(dropGroups);
		if (randomRwards == null || randomRwards.size() == 0) {
			return randomRwards;
		}
		// 拍卖行写死绑定
		for (ItemBase itemBase : randomRwards) {
			itemBase.setBind(true);
		}
		SSUnionAddSeaFightShopSale ss = new SSUnionAddSeaFightShopSale();
		ss.setCurrSalePrice(0);
		ss.setCurrSaleShopInfo(randomRwards);
		ss.setMinAdd(minAdd);
		ss.setSaleEndTime(saleEndTime);
		ss.setSalingEndTime(salingEndTime);
		ss.setStartPrice(0);
		ss.setType(type);// 中午的传2 晚上的传3
		ss.setUnionId(unionId);
		Message msg = Message.buildByZoneId(0L, zoneId, ss, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);

		return randomRwards;
	}

	@Override
	public void onClientBattleChannelInactive(AbstractHuman abstractHuman) {
		Human human = (Human) abstractHuman;
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		if (!isCrossBattle) {
			return;
		}
		Message msg = Message.buildByZoneId(abstractHuman.getPlayerId(), abstractHuman.getZoneId(),
			new SSUSFClientBattleChannelInactive(), null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
	}

	@Override
	public void setPlayerOnline(Union union, long playerId, boolean online) {
		Human human = Globals.getHuman(union.getZoneId(), playerId, Human.class);
		if (human == null) {
			return;
		}
		// 检测是否开启海战活动 按第一个serverId处理
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 功能开关中配置关闭海战，玩家不能参与， zoneId：{}", human.getPlayerId());
			return;
		}
		if (online) {
			long currTime = System.currentTimeMillis();
			UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
			if (usfActivityData == null) {
				AMLog.LOG_UNION.warn("@UnionSeaFight 未配置海战字典表DictUnionSeaFightActivity， 服：{}", human.getServerId());
				return;
			}
			if (!usfActivityData.inActivityTime(currTime)) {
				return;
			}
			noticeKVInfoOnLogin(currTime, usfActivityData, union, human);
		}
	}

	/***
	 * 1、报名日： 公会未报名 --> 会长/副会长推送工会报名图标
	 * 2、玩家可报名时间内： 任何会员未报名， 推送个人报名图标
	 * 3、战斗日：
	 */
	private void noticeKVInfoOnLogin(long currTime, UnionSeaFightActivityData usfActivityData, Union union, Human human) {
		boolean forcePush = true;// 强制推送
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfActivityData.inApplyTimeScope(currTime)) {// 当前时间在报名时间内
			if (!usfActivityData.inApplyTimeScope(usfModel.getApplyTime())) {// 公会未报名
				// 是会长或副会长
				if (CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())
					&& usfModel.getAutoApply() == FALSE) {
					noticeIconKVInfoToHuman(human, USFNoticeIconType.UNION_APPLY, forcePush, currTime,
						"noticeKVInfoOnLogin");
					return;
				}
			}
		}
		UnionMemberModel unionMemberModel = union.getUnionMemberManager().getUnionMemberModel(human.getPlayerId());
		if (unionMemberModel == null || !usfActivityData.inPlayerJoinTime(unionMemberModel.getUsfJoinTime())) {
			// 玩家未报名的
			if (usfActivityData.inPlayerJoinTime(currTime)) {// 在报名时间内， 强制推送
				noticeIconKVInfoToHuman(human, USFNoticeIconType.PLAYER_JOIN, forcePush, currTime,
					"noticeKVInfoOnLogin");
				return;
			}
		} else if (usfActivityData.moreThenMatchTime(currTime) && currTime <= usfActivityData.getClearTime()) {
			// 玩家已报名的
			USFUnionFightInfo fightInfo = usfModel.getCurrentUnionFightInfo();
			if (fightInfo == null) {
//				noticeIconKVInfoToHuman(human, USFNoticeIconType.WAIT_FIGHT, forcePush, currTime, "noticeKVInfoOnLogin");
				return;
			} else {
				if (fightInfo.getBattleStage() == null) {
					return;
				}
				switch (fightInfo.getBattleStage()) {
					case USF_WAIT_START:
						noticeIconKVInfoToHuman(human, USFNoticeIconType.WAIT_FIGHT, forcePush, currTime,
							"noticeKVInfoOnLogin");
						break;
					case USF_IN_FIGHT:
						if (currTime >= fightInfo.getStartTime()) {
							noticeIconKVInfoToHuman(human, USFNoticeIconType.FIGHT, forcePush, currTime,
								"noticeKVInfoOnLogin");
						} else {
							noticeIconKVInfoToHuman(human, USFNoticeIconType.READY, forcePush, currTime,
								"noticeKVInfoOnLogin");
						}
						break;
					case USF_FINISH_WIN:
					case USF_FINISH_FAIL:
					case USF_FINISH_TIE:// 本场打完了
						// 还有未完成的战斗
						if (usfModel.existUnionFightInfo(USFBattleState.USF_WAIT_START)) {
							noticeIconKVInfoToHuman(human, USFNoticeIconType.WAIT_FIGHT, forcePush, currTime,
								"noticeKVInfoOnLogin");
						} else {
							noticeIconKVInfoToHuman(human, USFNoticeIconType.SECOND_FINISH, forcePush, currTime,
								"noticeKVInfoOnLogin");
						}
						break;
				}
			}
		}
	}

	private void noticeIconKVInfoToUnion(Union union, UnionSeaFightModel usfModel, USFNoticeIconType iconType,
											List<Long> playerIdList, long currTime, String reason) {
		if (playerIdList == null || playerIdList.isEmpty()) {
			return;
		}
		boolean needCheckKuafuFight = false;
		switch (iconType) {
			case READY:
			case FIGHT:
			case FIRST_FINISH:
			case SECOND_FINISH:// 只给非跨服状态的玩家推送
				needCheckKuafuFight = true;
				break;
			default:
				break;
		}
		AbstractHuman human = null;
		for (Long playerId : playerIdList) {
			if (playerId == null || playerId.longValue() <= 0)
				continue;
			human = Globals.getHuman(union.getZoneId(), playerId);
			if (human == null || human.isOffline()) {
				continue;
			}
			if (needCheckKuafuFight && human.isOnKuafuFight()) {
				continue;
			}
			UnionMemberModel memberModel = union.getUnionMemberManager().getUnionMemberModel(playerId);
			if (memberModel == null) {
				continue;
			}
			noticeIconKVInfoToHuman(human, iconType, false, currTime, reason);
		}
	}

	// playerId, String
	private void noticeIconKVInfoToHuman(AbstractHuman human, USFNoticeIconType iconType, boolean forcePush,
											long currTime, String reason) {
		Map<String, Long> noticeMap = humanNoticeFlagMap.get(human.getPlayerId());
		if (noticeMap == null) {
			noticeMap = new HashMap<>();
			humanNoticeFlagMap.put(human.getPlayerId(), noticeMap);
		}
		if (!forcePush) {
			Long lastSendTime = noticeMap.get(iconType.getName());
			if (lastSendTime != null && lastSendTime.longValue() > 0) {
				long disTime = currTime - lastSendTime.longValue();
				if (disTime < iconType.getSendDelayTime()) {
					return;
				}
			}
			noticeMap.put(iconType.getName(), Long.valueOf(currTime));
		}

		AMLog.LOG_UNION.info("@UnionSeaFight 给客户端推送进度. Human.{} , stage:{}, reason:{}", human.getPlayerId(), iconType,
			reason);
		// 给前端 推送
		List<KeyValueEntry> kvList = new ArrayList<>();
		KeyValueEntry monthCard = new KeyValueEntry(KeyValueEnum.USFActivityStageFlag.value(), iconType.getName());
		kvList.add(monthCard);
		SCKVInfo scKVInfo = new SCKVInfo();
		scKVInfo.setEntrys(kvList);
		human.push2Gateway(scKVInfo);
	}

	@Override
	public void onFormationChange(Human human) {
//		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
//		if (!isCrossBattle) {
//			return;
//		}
//		String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
//		if (!StringUtils.isEmpty(serverFlag)) {
//			FFightSide fightSide = CommonFightManager.getAttFFightSide(human, EnumDefine.FightCampEnum_Left);
//			byte[] bs = SerializationUtil.serialize(fightSide);
//			List<byte[]> fightSides = Arrays.asList(bs);
//			SSUSFSyncPlayerFormation ssUSFSyncPlayerFormation = new SSUSFSyncPlayerFormation(fightSides);
//			boolean succ = CoreBoot.dispatch2Server(ssUSFSyncPlayerFormation, human.getPlayerId(), human.getServerId(),
//				serverFlag);
//			if (succ) {
//				return;
//			}
//		}
//		AMLog.LOG_UNION.warn("同步海战阵容失败,没有找到绑定战斗服,serverId={},playerId={}", human.getServerId(), human.getPlayerId());
	}

	/**
	 * 同步排行榜响应
	 * 
	 * @param ssUSFSyncRanksRes
	 */
	public void ssUSFSyncRanksRes(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		IUSFBenfuRankManager.MANAGER.syncRanks(ssUSFSyncRanksRes);
	}

	/**
	 * 查看海战录像
	 * 
	 * @param cs
	 * @param human
	 */
	public void csUSFLookFightReplay(CSUSFLookFightReplay cs, Human human) {
		// 校验是否在海战中
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.UNION_SEA_FIGHT);
		if (isCrossBattle) {
			String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
			if (!StringUtils.isEmpty(serverFlag)) {
				SSUFLookFightReplay msg = new SSUFLookFightReplay(cs.getFightId());
				CoreBoot.dispatch2Server(msg, human.getPlayerId(), human.getServerId(), serverFlag);
			} else {
				AMLog.LOG_UNION.warn("@UnionSeaFight 玩家查看海战录像没有找到serverFlag,playerId={}", human.getPlayerId());
			}
		}
	}

	public void csUSFGetRankInfo(CSUSFGetRankInfo csUSFGetRankInfo, Human human) {
		int serverId = human.getServerId();
		SCUSFGetRankInfo ret = new SCUSFGetRankInfo();
		Integer groupId = DictUnionSeaFightServerGroup.getGroupId(serverId);
		Calendar calendar = Calendar.getInstance();
		if (groupId == null) {
			ret.setMonth(calendar.get(Calendar.MONTH));
			human.push2AllGateway(ret);
			return;
		}
		long unionId = human.getUnionId();
		int maxNum = BaseService.getConfigIntByDefault(PriConfigKeyName.USF_RANK_SHOW_NUM);

		// 本届
		USFBenfuUnionRankGroup unionRankGroup = IUSFBenfuRankManager.MANAGER.getUnionRankGroup(groupId);
		if (csUSFGetRankInfo.getType() == 0) {
			if (unionRankGroup != null) {
				fullRankInfo(ret, unionId, maxNum, unionRankGroup.getCurSeasonInfo());
				ret.setTime(unionRankGroup.getResetTime());
			}
		} else if (csUSFGetRankInfo.getType() == 1) {
			if (unionRankGroup != null) {
				fullRankInfo(ret, unionId, maxNum, unionRankGroup.getLastSeasonInfo());
				calendar.add(Calendar.MONTH, -1);
				ret.setTime(unionRankGroup.getResetTime());
			}
		}
		ret.setMonth(calendar.get(Calendar.MONTH) + 1);
		// 修复排行榜显示
		List<USFRankInfo> ranks = ret.getRanks();
		if (CollectionUtils.isNotEmpty(ranks)) {
			for (USFRankInfo usfRankInfo : ranks) {
				usfRankInfo.setDisplayServerId(usfRankInfo.getServerId() % 1000);
			}
		}
		human.push2AllGateway(ret);
	}

	private void fullRankInfo(SCUSFGetRankInfo ret, long unionId, int maxNum, USFBenfuSeasonInfo benfuSeasonInfo) {
		if (benfuSeasonInfo == null || benfuSeasonInfo.getRanks() == null) {
			return;
		}
		ret.setRanks(new ArrayList<>(benfuSeasonInfo.getRanks()));
		USFRankInfo myRank = null;
		if (CollectionUtils.isNotEmpty(benfuSeasonInfo.getServerRanks())) {
			for (USFRankInfo usfRankInfo : benfuSeasonInfo.getServerRanks()) {
				if (unionId == usfRankInfo.getUnionId()) {
					myRank = usfRankInfo;
					break;
				}
			}
		}
		ret.setMyRank(myRank);
	}

	public List<USFPlayerFormationInfo> getUSFPlayerFormationInfo(Human realHuman) {
		return realHuman.getCommonFormationDataManager().getUSFPlayerFormationInfos();
	}

	public void csUSFGetOtherFormation(CSUSFGetOtherFormation csUSFGetOtherFormation, final Human human) {
		final long playerId = csUSFGetOtherFormation.getPlayerId();
		final int zoneId = human.getZoneId();
		long unionId = human.getUnionId();
		Union union = (Union) Globals.getUnion(zoneId, unionId);
		if (union == null || union.isDelete() || union.isDeleteState()) {
			// 玩家没有公会
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetOtherFormation.getCode(),
				ErrorCodeConstants.NO_UNION);
			AMLog.LOG_UNION.warn("@UnionSeaFight 没有公会  human.{}", human.getPlayerId());
			return;
		}
		UnionMemberModel otherUnionMember = union.getUnionMemberManager().getUnionMemberModel(playerId);
		if(otherUnionMember == null) {
			// 非同一个公会的
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetOtherFormation.getCode(),
				ErrorCodeConstants.UNION_NOT_SAME_UNION);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在同一个公会  human.{}, 对方:{}", human.getPlayerId(), playerId);
			return;
		}
		// 校验是不是调整阵容阶段
		final SCUSFGetOtherFormation ret = new SCUSFGetOtherFormation();
		ret.setInfos(new ArrayList<USFPlayerFormationInfo>());
		
		List<USFPlayerFormationInfo> usfPlayerFormationInfos = union.getUnionSeaFightManager().getUsfPlayerFormationInfos(otherUnionMember);
		if(usfPlayerFormationInfos != null && !usfPlayerFormationInfos.isEmpty()) {
			ret.getInfos().addAll(usfPlayerFormationInfos);
		}
		human.push2Gateway(ret);
	}

	public void doUSFGetPromotionTechInfos(Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetPromotionTechInfos.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战  human.{}", human.getPlayerId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetPromotionTechInfos.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			// 不在活动时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetPromotionTechInfos.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在活动时间内  serverId.{}, human.{}, usfActivityData:{}",
				human.getServerId(), human.getPlayerId(), usfActivityData);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 公会不存在
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, 没有公会 :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFGetPromotionTechInfos.getCode(),
				ErrorCodeConstants.NO_UNION);
			return;
		}
		List<USFClientPromotionTechInfo> promotionTechInfos = union.getUnionSeaFightManager().getPromotionTechInfos();
		SCUSFGetPromotionTechInfos scUSFGetPromotionTechInfos = new SCUSFGetPromotionTechInfos(promotionTechInfos);
		human.push2Gateway(scUSFGetPromotionTechInfos);
	}

	public void doUSFSetPromotions(CSUSFSetPromotions csUSFSetPromotions, Human human) {
		// 检测是否开启海战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_FIGHT, human)) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 未开启海战  Human.{}", human.getPlayerId());
			return;
		}
		UnionSeaFightActivityData usfActivityData = getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 当前找不到任何海战活动  serverId.{}, human.{}", human.getServerId(),
				human.getPlayerId());
			return;
		}
		long currTime = System.currentTimeMillis();
		if (!usfActivityData.inActivityTime(currTime)) {
			// 不在活动时间内
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(),
				ErrorCodeConstants.USF_ACTIVITY_NOT_OPEN);
			AMLog.LOG_UNION.warn("@UnionSeaFight 不在活动时间内  serverId.{}, human.{}, usfActivityData:{}",
				human.getServerId(), human.getPlayerId(), usfActivityData);
			return;
		}
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			// 公会不存在
			AMLog.LOG_UNION.warn("@UnionSeaFight  Human.{}, 没有公会 :{}", human.getPlayerId(), human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(),
				ErrorCodeConstants.NO_UNION);
			return;
		}
		if (!CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 必须是会长才能设置，权限不足,  Human.{}, 公会 :{}", human.getPlayerId(),
				human.getUnionId());
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(),
				ErrorCodeConstants.UNION_ROLE_ERR);
			return;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		int checkResult = checkSetFight(usfActivityData, usfModel);
		if (checkResult != 0) {
			if (checkResult == ErrorCodeConstants.USF_OUT_SET_ON_PLAYER_TIME) {
				checkResult = ErrorCodeConstants.USF_OUT_SET_PROMOTION_TIME;
			}
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(), checkResult);
			return;
		}

		int code = union.getUnionSeaFightManager().selectPromotion(csUSFSetPromotions.getPromotionId(),
			csUSFSetPromotions.getSelected());
		if (code > 0) {
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCUSFSetPromotions.getCode(), code);
			return;
		}
		SCUSFSetPromotions scUSFSetPromotions = new SCUSFSetPromotions(csUSFSetPromotions.getPromotionId(),
			csUSFSetPromotions.getSelected());
		human.push2Gateway(scUSFSetPromotions);
	}

	/**
	 * 此方法非公会线程调用
	 */
	@Override
	public void onTargetServerRestart(ServerTypeEnum serverType, Channel channel, int restartType) {
		AMLog.LOG_UNION.info("@UnionSeaFight 海战处理 服务器[{}]重启", serverType);
		if (serverType == ServerTypeEnum.BATTLE) {
			// 战斗服重启了
		} else if (serverType == ServerTypeEnum.QUANFU) {

		}
		switch (serverType) {
			case BATTLE:// 战斗服重启了

				break;
			case KUAFU:
			case QUANFU: // 中心服重启了

				break;
			default:
				break;
		}

	}

	/**公会的是否处于海战中*/
	public static boolean isUnionOnUSFFight(Union union) {
		if (union == null) {
			return false;
		}
		UnionSeaFightModel usfModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (usfModel == null) {
			return false;
		}
		if (usfModel.getBattleServer() != null) {
			return false;
		}
		return true;
	}

	public void csLookUSFReplayList(CSLookUSFReplayList csLookUSFReplayList, Human human) {
		SCLookUSFReplayList ret = new SCLookUSFReplayList();
		long unionId = human.getUnionId();
		int zoneId = human.getZoneId();
		IUnionSeaFightReplayManager unionSeaFightReplayManager = Globals.getMemoryData(zoneId).getMemoryDataInit()
			.getUnionSeaFightReplayManager();
		List<InstUnionSeaFightReplay> list = unionSeaFightReplayManager.findByUnionId(unionId);
		Collections.sort(list, new Comparator<InstUnionSeaFightReplay>() {
			@Override
			public int compare(InstUnionSeaFightReplay o1, InstUnionSeaFightReplay o2) {
				if (o1.getFightTime() > o2.getFightTime()) {
					return 1;
				}
				if (o1.getFightTime() < o2.getFightTime()) {
					return -1;
				}
				return 0;
			}
		});
		long fromTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7);
		List<USFReplayItem> items = new ArrayList<>();
		for (InstUnionSeaFightReplay inst : list) {
			if (fromTime > inst.getFightTime()) {
				continue;
			}
			USFReplayItem item = new USFReplayItem();
			item.setReplayId(inst.getReplayId());
			item.setReplayUrl(inst.getReplayUrl());
			item.setFightTime(inst.getFightTime());
			boolean leftWin = inst.getLeftWin() == 1;
			USFUnionReplayInfo left = new USFUnionReplayInfo(inst.getLeftUnionId(), inst.getLeftUnionName(), leftWin,
				CoreBoot.getServerDisplayId(inst.getLeftServerId()), USFCampType.USF_CAMP_BLUE.value());
			USFUnionReplayInfo right = new USFUnionReplayInfo(inst.getRightUnionId(), inst.getRightUnionName(),
				!leftWin, CoreBoot.getServerDisplayId(inst.getRightServerId()), USFCampType.USF_CAMP_RED.value());
			List<USFUnionReplayInfo> unions = new ArrayList<>();
			unions.add(left);
			unions.add(right);
			item.setUnions(unions);
			items.add(item);
		}
		ret.setItems(items);
		human.push2Gateway(ret);
	}

	@Override
	public void syncSeasonInfo(Union union) {
		int serverId = union.getUnionBaseManager().getUnionModel().getDisplayServerId();
		DictUnionSeaFightServerGroup serverGroup = DictUnionSeaFightServerGroup.getByServerId(serverId);
		if (serverGroup == null) {
			AMLog.LOG_UNION.error("@syncSeasonInfo 没有找到DictUnionSeaFightServerGroup,serverId={},unionId={}", serverId,
				union.getUnionId());
			return;
		}
		USFBenfuUnionRankGroup unionRankGroup = IUSFBenfuRankManager.MANAGER.getUnionRankGroup(serverGroup.getId());
		if (unionRankGroup == null) {
			AMLog.LOG_UNION.error("@syncSeasonInfo 没有找到USFBenfuUnionRankGroup,serverId={},unionId={}", serverId,
				union.getUnionId());
			return;
		}
		USFBenfuSeasonInfo lastSeasonInfo = unionRankGroup.getLastSeasonInfo();
		if (lastSeasonInfo == null) {
			return;
		}
		int lastSeasonId = lastSeasonInfo.getId();
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		if (unionSeaFightModel.getResetIntegralSeasonId() < lastSeasonId) {
			unionSeaFightModel.setResetIntegralSeasonId(lastSeasonId);
		}
		if (unionSeaFightModel.getSendRewardSeasonId() < lastSeasonId) {
			unionSeaFightModel.setSendRewardSeasonId(lastSeasonId);
		}
		AMLog.LOG_UNION.info("@syncSeasonInfo 同步赛季数据,serverId={},unionId={},lastSeasonId={}", serverId,
			union.getUnionId(), lastSeasonId);
	}

}
