package com.game2sky.prilib.socket.logic.centerrank.rankmodel;

import gnu.trove.map.TLongIntMap;
import gnu.trove.map.hash.TLongIntHashMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerInfo;
import com.game2sky.prilib.socket.logic.integral.sort.BarrelSort;

/**
 * 通用全服排行榜功能玩家排名排序中心..
 *
 * <AUTHOR>
 * @version v0.1 2020年2月27日 下午3:28:27  Jet Lee
 */
public class EntireRankSort extends BarrelSort<EntireRankBarrel> {

	/** 
	 * 玩家缓存
	 *  */
	private Map<Long, EntireRankPlayerInfo> playerInfos = new HashMap<Long, EntireRankPlayerInfo>();

	@Override
	public EntireRankBarrel createInstance(Long id) {
		return new EntireRankBarrel(id);
	}

	/**
	 * 
	 * @param playerInfo
	 */
	public void addElement(EntireRankPlayerInfo playerInfo) {
		long playerId = playerInfo.getDisplayInformation().getPlayerId();
		EntireRankPlayerInfo tempPlayerInfo = playerInfos.get(playerId);
		if (tempPlayerInfo != null) {
			if (playerInfo.getScore() == tempPlayerInfo.getScore()) {
				// 重复数据
				playerInfos.put(playerId, playerInfo);
				return;
			} else {
				deleteElement(playerId, tempPlayerInfo.getScore());
			}
		}
		playerInfos.put(playerId, playerInfo);
		EntireRankBarrel barrel = addElement((long) playerInfo.getScore());
		barrel.addPlayerId(playerId);
	}

	public boolean deleteElement(long playerId, int score) {
		EntireRankPlayerInfo playerInfo = playerInfos.get(playerId);
		if (playerInfo != null) {
			EntireRankBarrel barrel = deleteElement((long) score);
			barrel.removePlayerId(playerId);
			playerInfos.remove(playerId);
			return true;
		}
		return false;
	}
	
	public EntireRankPlayerInfo getPlayerInfo(long playerId) {
		return playerInfos.get(playerId);
	}
	
	public void putPlayerInfo(EntireRankPlayerInfo playerInfo) {
		playerInfos.put(playerInfo.getDisplayInformation().getPlayerId(), playerInfo);
	}

	/**
	 * 
	 * @param playerId
	 * @param score
	 * @param needReverse 是否倒序排名。 分越高，排名越高，就是倒序。反之则是正序。
	 * @return
	 */
	public int getRank(long playerId, int score, boolean needReverse) {
		if (score == -1) {
			// 不知道玩家的积分，从缓存里查一下。主要是查看玩家之前排名
			EntireRankPlayerInfo playerInfo = playerInfos.get(playerId);
			if (playerInfo != null) {
				score = playerInfo.getScore();
			}
		}
		if (score == -1) {
			return Integer.MAX_VALUE;
		}
		int before = super.getBeforeMe((long) score, needReverse);
		EntireRankBarrel barrel = getBarrel((long) score);
		if (barrel == null) {
			return before + 1;
		}
		int rank = barrel.getRank(playerId);
		return before + rank;
	}
	
	/**
	 * 根据积分获取排名
	 * @param score
	 * @param needReverse
	 * @return
	 */
	public int getRankByScore(int score, boolean needReverse) {
		int before = super.getBeforeMe((long) score, needReverse);
		EntireRankBarrel barrel = getBarrel((long) score);
		if (barrel == null) {
			return before + 1;
		}
		return before + barrel.getHaveNumber() + 1;
	}

	/**
	 * 
	 * @param rankList
	 * @param needReverse 是否倒序排名。 分越高，排名越高，就是倒序。反之则是正序。
	 * @param rankNum
	 */
	public void initRankList(List<EntireRankPlayerInfo> rankList, boolean needReverse, int rankNum) {
		ArrayList<EntireRankBarrel> barrelList = getBarrelList();
		if (needReverse) {
			for (int i = barrelList.size() - 1; i >= 0; i--) {
				EntireRankBarrel barrel = barrelList.get(i);
				LinkedList<Long> playerIds = barrel.getPlayerIds();
				for (Iterator<Long> iterator = playerIds.iterator(); iterator.hasNext();) {
					Long playerId = (Long) iterator.next();
					EntireRankPlayerInfo playerInfo = playerInfos.get(playerId);
					if (playerInfo != null) {
						rankList.add(playerInfo);
					}
					// 添加数量满足排行榜最大数量了，直接返回
					if (rankList.size() >= rankNum) {
						return;
					}
				}
			}
		} else {
			for (int i = 0; i < barrelList.size(); i++) {
				EntireRankBarrel barrel = barrelList.get(i);
				LinkedList<Long> playerIds = barrel.getPlayerIds();
				for (Iterator<Long> iterator = playerIds.iterator(); iterator.hasNext();) {
					Long playerId = (Long) iterator.next();
					EntireRankPlayerInfo playerInfo = playerInfos.get(playerId);
					if (playerInfo != null) {
						rankList.add(playerInfo);
					}
					// 添加数量满足排行榜最大数量了，直接返回
					if (rankList.size() >= rankNum) {
						return;
					}
				}
			}
		}
	}
	
	/**
	 * 移除排名靠后的一些玩家
	 * @param removeNum 移除的数量
	 * @param needReverse 倒序
	 */
	public void removeTailPlayers(int removeNum, boolean needReverse) {
		int num = 0;
		TLongIntMap map = new TLongIntHashMap();
		if (needReverse) {
			// 倒序。说明列表后边的是排名靠前的，列表前边的是排名靠后。这里要找排名后边的。
			for (int i = 0; i < barrelList.size(); i++) {
				if (num > removeNum) {
					break;
				}
				EntireRankBarrel autoChessRankBarrel = barrelList.get(i);
				LinkedList<Long> playerIds = autoChessRankBarrel.getPlayerIds();
				for (Long tmpPlayerId : playerIds) {
					num++;
					if (num > removeNum) {
						break;
					}
					map.put(tmpPlayerId, autoChessRankBarrel.getId().intValue());
				}
			}
		} else {
			for (int i = barrelList.size() - 1; i >= 0; i--) {
				if (num > removeNum) {
					break;
				}
				EntireRankBarrel autoChessRankBarrel = barrelList.get(i);
				LinkedList<Long> playerIds = autoChessRankBarrel.getPlayerIds();
				for (Long tmpPlayerId : playerIds) {
					num++;
					if (num > removeNum) {
						break;
					}
					map.put(tmpPlayerId, autoChessRankBarrel.getId().intValue());
				}
			}
		}
		long[] players = map.keys();
		if (players == null || players.length < 0) {
			return;
		}
		for (int i = 0; i < players.length; i++) {
			long playerId = players[i];
			int score = map.get(playerId);
			deleteElement(playerId, score);
		}
		
	}
	
	@Override
	public void clear() {
		super.clear();
		playerInfos.clear();
	}

}
