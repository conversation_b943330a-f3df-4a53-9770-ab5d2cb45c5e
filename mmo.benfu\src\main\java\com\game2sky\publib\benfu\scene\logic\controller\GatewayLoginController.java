//package com.game2sky.publib.benfu.scene.logic.controller;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//
//import com.game2sky.publib.communication.PSEnum;
//import com.game2sky.publib.communication.game.login.SCPlayerLogin;
//import com.game2sky.publib.framework.communication.Message;
//import com.game2sky.publib.framework.netty.support.AMAttributeKey;
//import com.game2sky.publib.framework.netty.support.handler.ClientManager;
//import com.game2sky.publib.framework.netty.support.handler.ClientSession;
//import com.game2sky.publib.framework.netty.support.handler.SceneClientCenter;
//import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
//
//import io.netty.channel.Channel;
//import io.netty.util.Attribute;
//
//@Controller
//public class GatewayLoginController {
//
//	@Autowired
//	private SceneClientCenter sceneClientCenter;
//
//	@ProtoStuffMapping(PSEnum.SCPlayerLogin)
//	public void SCPlayerLogin(Message message, SCPlayerLogin sc) {
//		String channelId = sc.getChannelKey();
//		Channel channel = ClientManager.removeActiveChannel(channelId);
//		long playerId = sc.getPlayerId();
//		if (channel != null) {
//			ClientSession session = new ClientSession();
//			session.setPlayerId(playerId);
//			session.setZoneId(message.getHead().getSid());
//			session.setSessionKey(sc.getSessionKey());
//			ClientManager.addClient(session, channel);
//			Attribute<Long> playerIdAttr = channel.attr(AMAttributeKey.PLAYER_ID);
//			playerIdAttr.set(playerId);
//			sceneClientCenter.push2Client(message, channel);
//			channel.attr(AMAttributeKey.MARK).get().registC = System.currentTimeMillis();
//		}
//	}
//
//}
