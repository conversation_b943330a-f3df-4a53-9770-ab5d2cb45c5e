package com.game2sky.prilib.benfu.logic.ladderwar;

import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.communication.MessageType;
import com.game2sky.publib.framework.thread.ExecutableMessageHandler;
import com.game2sky.publib.framework.thread.QueueMessageProcessor;
import com.game2sky.publib.framework.thread.message.IMessageProcessor;

/**
 * TODO 争霸赛消息处理器
 *
 * <AUTHOR>
 * @version v0.1 2018年11月23日 下午3:27:47  Administrator
 */
class LadderWarBenfuMessageProcessor implements IMessageProcessor<Message> {

	/**主线程*/
	private QueueMessageProcessor mainProcessor;

	LadderWarBenfuMessageProcessor() {
		this.mainProcessor = new QueueMessageProcessor(new ExecutableMessageHandler(), "LadderWarBenfu");
		this.mainProcessor.start();
		Globals.getMsgProcessDispatcher().setLadderWarMsgProcessor(this);
	}

	@Override
	public void start() {
	}

	@Override
	public void stop() {
		this.mainProcessor.stop();
	}

	@Override
	public void put(Message msg) {
		if (msg.messageType() == MessageType.LADDER_WAR) {
			this.mainProcessor.put(msg);
		} else {
			if (AMLog.LOG_ERROR.isErrorEnabled()) {
				AMLog.LOG_ERROR.error("LadderWarBenfuMessageProcessor收到不能处理的消息,msg={}", msg);
			}
		}
	}

	@Override
	public boolean isFull() {
		return this.mainProcessor.isFull();
	}

	@Override
	public int messageQueueSize() {
		int queueSize = this.mainProcessor.messageQueueSize();
		return queueSize;
	}

	@Override
	public void printMessageQueue() {
		this.mainProcessor.printMessageQueue();
	}

}
