package main;

import mmo.sensitiveword.com.game2sky.inter.SensitiveWordService;

import org.springframework.context.support.ClassPathXmlApplicationContext;

public class ConsumerMain {
	public static void main(String[] args) {
		ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(
				new String[] { "applicationConsumer.xml" });
		context.start();
		SensitiveWordService service = (SensitiveWordService) context.getBean("sensitiveService");
		for (int i = 0; i < 886; i++) {
			System.out.println("..."+service.containSensitiveName("习 近4平" + i));
		}
		
		context.close();
	}
}
