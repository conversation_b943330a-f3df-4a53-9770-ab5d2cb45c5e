package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;

/**
 * 公会战战场触发器
 *
 * <AUTHOR>
 * @version v0.1 2018年10月23日 上午2:52:36  lidong
 */
public interface IBattleTriggerEvent
{
	void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,Object... args);
}
