package com.game2sky.prilib.benfu;

import org.springframework.stereotype.Controller;

import com.game2sky.publib.framework.web.base.BaseController;

/**
 * Redis测试.
 *
 * <AUTHOR>
 * @version v0.1 2017年7月8日 下午4:11:44  <PERSON>
 */
@Controller
public class RedisTestController extends BaseController {
	
//	@RequestMapping(path = "/testRedis")
//	public ModelAndView testRedis(HttpServletRequest request) {
//		RedisDao dao = new RedisDao();
//		dao.initRedisTemplate("172.16.1.51", 6379, "op123456A", 0);
//		long playerId = 756978145751468032l;
//		long begin = System.currentTimeMillis();
//		String newKey = "playerInformation:p" + playerId;
//		for (int i = 0; i < 1000; i++) {
//			try {
//				PlayerInformation valueGetByBlob = dao.valueGetByBlob(new<PERSON>ey, PlayerInformation.class);
////				System.out.println(valueGetByBlob);
//				dao.valueSetByBlob(newKey, valueGetByBlob, 600000);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//		long end = System.currentTimeMillis();
//		long cost = end - begin;
//		System.out.println(cost);
//		return returnString("[redisTest] [cost: " + cost);
//	}
//	
//	@RequestMapping(path = "/testRedis1")
//	public ModelAndView testRedis1(HttpServletRequest request) {
//		RedisDao dao = new RedisDao();
//		dao.initRedisTemplate("172.16.1.51", 6379, "op123456A", 0);
//		long playerId = 766347734209790976l;
//		long begin = System.currentTimeMillis();
//		String newKey = "playerFormation:s1";
//		for (int i = 0; i < 1000; i++) {
//			try {
//				PlayerInformation value = dao.hashGet(newKey, Long.toString(playerId), PlayerInformation.class);
////				System.out.println(value);
//				dao.hashSet(newKey, Long.toString(playerId), value);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//		long end = System.currentTimeMillis();
//		long cost = end - begin;
//		System.out.println(cost);
//		return returnString("[redisTest] [cost: " + cost);
//	}
	
//	public static String KEY = "RedisTest";
//
//	@RequestMapping(path = "/createAllPlayerByRedis/{threadNum},{minPlayerId},{maxPlayerId}")
//	public ModelAndView createAllPlayerByRedis(@PathVariable int threadNum, @PathVariable int minPlayerId,
//												@PathVariable int maxPlayerId, HttpServletRequest request) {
//		AMLog.LOG_COMMON.info("[redisTest] [createAllHumans] [begin]");
//		ExecutorService _ioPool = Executors.newFixedThreadPool(threadNum);
//		List<FutureTask<Integer>> futureTasks = new ArrayList<FutureTask<Integer>>();
//		long begin = System.currentTimeMillis();
//		for (final Integer zoneId : Globals.getAllZoneId()) {
//			for (int i = minPlayerId; i <= maxPlayerId; i++) {
//				final long playerId = i;
//				FutureTask<Integer> future = new FutureTask<Integer>(new Callable<Integer>() {
//					@Override
//					public Integer call() throws Exception {
//						return runCreate(playerId);
//					}
//				});
//				_ioPool.submit(future);
//				futureTasks.add(future);
//			}
//		}
//
//		Iterator<FutureTask<Integer>> iterator = futureTasks.iterator();
//		while (iterator.hasNext()) {
//			FutureTask<Integer> next = iterator.next();
//			try {
//				if (next.get() == 1) {
//					iterator.remove();
//				}
//			} catch (InterruptedException | ExecutionException e) {
//			}
//		}
//
////		for (Integer zoneId : Globals.getAllZoneId()) {
////			for (int i = 10001; i <= 20000; i++) {
////				runCreate(zoneId, i);
////			}
////		}
//		AMLog.LOG_COMMON.info("[redisTest] [createAllHumans] [end] [cost: " + (System.currentTimeMillis() - begin)
//								+ "]");
//		return returnString("[redisTest] [createAllHumans] [success]");
//	}
//
//	private Integer runCreate(long playerId) {
//		long beginTime = System.currentTimeMillis();
//		try {
//			InstPlayer instPlayer = createPlayer(0, playerId, 1, ("名称" + playerId), 0, (playerId + ""));
//			instPlayer.setChannelId("tencent");
//			RedisDao redisDao = RedisManager.getRedisDao();
//			String temp = "adfsafadfadffasdfadfafsd";
//			redisDao.hashSet(KEY, "InstPlayer" + playerId, instPlayer);
//			redisDao.hashSet(KEY, "InstShopLimitData" + playerId, new InstShopLimitData(playerId, temp, 0, 0l));
//			redisDao.hashSet(KEY, "InstPlayerGiftCode" + playerId, new InstPlayerGiftCode(playerId, temp, temp));
//			redisDao.hashSet(KEY, "InstRedPoint" + playerId, new InstRedPoint(playerId, temp));
//			redisDao.hashSet(KEY, "InstHeroSkill" + playerId, new InstHeroSkill(playerId, temp));
//			redisDao.hashSet(KEY, "InstHero" + playerId, new InstHero());
//			redisDao.hashSet(KEY, "InstContainerData" + playerId, new InstContainerData(playerId, temp, temp, temp,
//				temp, temp));
//			redisDao.hashSet(KEY, "InstFormation" + playerId, new InstFormation());
//			redisDao.hashSet(KEY, "InstCurrency" + playerId, new InstCurrency(playerId, temp));
//			redisDao.hashSet(KEY, "InstRaidData" + playerId, new InstRaidData());
//			redisDao.hashSet(KEY, "InstPlayerRecruit" + playerId, new InstPlayerRecruit());
//			redisDao.hashSet(KEY, "InstEquipmentData" + playerId, new InstEquipmentData(playerId, temp, temp, temp));
//			redisDao.hashSet(KEY, "InstTask" + playerId, new InstTask(playerId, temp, temp, temp));
//			redisDao.hashSet(KEY, "InstPlayerExtraProp" + playerId, new InstPlayerExtraProp(playerId, temp));
//			redisDao.hashSet(KEY, "InstAchievement" + playerId, new InstAchievement(playerId, temp));
//			redisDao.hashSet(KEY, "InstRaidSailLog" + playerId, new InstRaidSailLog(playerId, temp, 0l,0));
//			redisDao.hashSet(KEY, "InstLife" + playerId, new InstLife(playerId, temp));
//			redisDao.hashSet(KEY, "InstExpeditionData" + playerId, new InstExpeditionData());
//			redisDao.hashSet(KEY, "InstSystemSettings" + playerId, new InstSystemSettings(playerId, temp));
//			redisDao.hashSet(KEY, "InstPlayerArena" + playerId, new InstPlayerArena());
//			redisDao.hashSet(KEY, "InstActivityRaid" + playerId, new InstActivityRaid(playerId, temp));
//			redisDao.hashSet(KEY, "InstDailyTask" + playerId, new InstDailyTask());
//			redisDao.hashSet(KEY, "InstTitle" + playerId, new InstTitle(playerId, -1, -1, temp));
//			redisDao.hashSet(KEY, "InstMSeaData" + playerId, new InstMSeaData());
//			redisDao.hashSet(KEY, "InstFightRecord" + playerId, new InstFightRecord(playerId, temp, temp, temp, temp));
//			redisDao.hashSet(KEY, "InstPlotData" + playerId, new InstPlotData(playerId, temp));
//			redisDao.hashSet(KEY, "InstPlayerFight" + playerId, new InstPlayerFight(playerId, temp, -1l));
//			redisDao.hashSet(KEY, "InstPlayerSceneBuff" + playerId, new InstPlayerSceneBuff(playerId, temp));
//			redisDao.hashSet(KEY, "InstActivityMonthlySignIn" + playerId, new InstActivityMonthlySignIn());
//			redisDao.hashSet(KEY, "InstActivityLevelReward" + playerId, new InstActivityLevelReward(playerId, temp));
//			redisDao.hashSet(KEY, "InstActivityRead" + playerId, new InstActivityRead(playerId, temp));
//			redisDao.hashSet(KEY, "InstPlayerActivityLogin" + playerId, new InstPlayerActivityLogin(playerId, temp));
//
//			AMLog.LOG_COMMON.info("[redisTest] [human_create] [playerId: " + playerId + "] [playerName "
//									+ instPlayer.getNickName() + "] [cost: " + (System.currentTimeMillis() - beginTime)
//									+ "ms]");
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return 1;
//	}
//
//	private static InstPlayer createPlayer(int isRobot, long playerId, int zoneId, String name, int gender,
//											String platformUid) {
//		InstPlayer instPlayer = new InstPlayer();
//		instPlayer.setPlayerId(playerId);
//		instPlayer.setZoneId(zoneId);
//		instPlayer.setNickName(name);
//		instPlayer.setGender(gender);
//		instPlayer.setIsRobot(isRobot);
//		instPlayer.setDiamond(0L);
//		instPlayer.setFreeDiamond(0L);
//		instPlayer.setPayDiamond(0L);
//		instPlayer.setLevel(1);
//		instPlayer.setExp(0L);
//		instPlayer.setHeadIcon("1");
//		instPlayer.setLastLoginTime(System.currentTimeMillis());
//		instPlayer.setClientVersion("");
//		instPlayer.setPlatformUid(platformUid);
//		return instPlayer;
//	}
//
//	@RequestMapping(path = "/loadAllPlayerFromRedis/{threadNum},{minPlayerId},{maxPlayerId}")
//	public ModelAndView loadAllPlayerFromRedis(@PathVariable int threadNum, @PathVariable int minPlayerId,
//												@PathVariable int maxPlayerId, HttpServletRequest request) {
//		ExecutorService _ioPool = Executors.newFixedThreadPool(threadNum);
//		List<FutureTask<Integer>> futureTasks = new ArrayList<FutureTask<Integer>>();
//		long begin = System.currentTimeMillis();
//		for (final Integer zoneId : Globals.getAllZoneId()) {
//			for (int i = minPlayerId; i <= maxPlayerId; i++) {
//				final long playerId = i;
//				FutureTask<Integer> future = new FutureTask<Integer>(new Callable<Integer>() {
//					@Override
//					public Integer call() throws Exception {
//						return runLoad(playerId);
//					}
//				});
//				_ioPool.submit(future);
//				futureTasks.add(future);
//			}
//		}
//
//		Iterator<FutureTask<Integer>> iterator = futureTasks.iterator();
//		while (iterator.hasNext()) {
//			FutureTask<Integer> next = iterator.next();
//			try {
//				if (next.get() == 1) {
//					iterator.remove();
//				}
//			} catch (InterruptedException | ExecutionException e) {
//			}
//		}
//
//		AMLog.LOG_COMMON.info("[redisTest] [loadAllHuman] [end] [cost: " + (System.currentTimeMillis() - begin) + "]");
//		return returnString("[redisTest] [loadAllHumanFromDBS] [success][threadNum: " + threadNum + "] [cost: "
//							+ (System.currentTimeMillis() - begin) + "]");
//	}
//
//	private Integer runLoad(final long playerId) throws Exception {
//		long beginTime = System.currentTimeMillis();
//		RedisDao redisDao = RedisManager.getRedisDao();
//		InstPlayer instPlayer = redisDao.hashGet(KEY, "InstPlayer" + playerId, InstPlayer.class);
//		redisDao.hashGet(KEY, "InstShopLimitData" + playerId, InstShopLimitData.class);
//		redisDao.hashGet(KEY, "InstPlayerGiftCode" + playerId, InstPlayerGiftCode.class);
//		redisDao.hashGet(KEY, "InstRedPoint" + playerId, InstRedPoint.class);
//		redisDao.hashGet(KEY, "InstHeroSkill" + playerId, InstHeroSkill.class);
//		redisDao.hashGet(KEY, "InstHero" + playerId, InstHero.class);
//		redisDao.hashGet(KEY, "InstContainerData" + playerId, InstContainerData.class);
//		redisDao.hashGet(KEY, "InstFormation" + playerId, InstFormation.class);
//		redisDao.hashGet(KEY, "InstCurrency" + playerId, InstCurrency.class);
//		redisDao.hashGet(KEY, "InstRaidData" + playerId, InstRaidData.class);
//		redisDao.hashGet(KEY, "InstPlayerRecruit" + playerId, InstPlayerRecruit.class);
//		redisDao.hashGet(KEY, "InstEquipmentData" + playerId, InstEquipmentData.class);
//		redisDao.hashGet(KEY, "InstTask" + playerId, InstTask.class);
//		redisDao.hashGet(KEY, "InstPlayerExtraProp" + playerId, InstPlayerExtraProp.class);
//		redisDao.hashGet(KEY, "InstAchievement" + playerId, InstAchievement.class);
//		redisDao.hashGet(KEY, "InstRaidSailLog" + playerId, InstRaidSailLog.class);
//		redisDao.hashGet(KEY, "InstLife" + playerId, InstLife.class);
//		redisDao.hashGet(KEY, "InstExpeditionData" + playerId, InstExpeditionData.class);
//		redisDao.hashGet(KEY, "InstSystemSettings" + playerId, InstSystemSettings.class);
//		redisDao.hashGet(KEY, "InstPlayerArena" + playerId, InstPlayerArena.class);
//		redisDao.hashGet(KEY, "InstActivityRaid" + playerId, InstActivityRaid.class);
//		redisDao.hashGet(KEY, "InstDailyTask" + playerId, InstDailyTask.class);
//		redisDao.hashGet(KEY, "InstTitle" + playerId, InstTitle.class);
//		redisDao.hashGet(KEY, "InstMSeaData" + playerId, InstMSeaData.class);
//		redisDao.hashGet(KEY, "InstFightRecord" + playerId, InstFightRecord.class);
//		redisDao.hashGet(KEY, "InstPlotData" + playerId, InstPlotData.class);
//		redisDao.hashGet(KEY, "InstPlayerFight" + playerId, InstPlayerFight.class);
//		redisDao.hashGet(KEY, "InstPlayerSceneBuff" + playerId, InstPlayerSceneBuff.class);
//		redisDao.hashGet(KEY, "InstActivityMonthlySignIn" + playerId, InstActivityMonthlySignIn.class);
//		redisDao.hashGet(KEY, "InstActivityLevelReward" + playerId, InstActivityLevelReward.class);
//		redisDao.hashGet(KEY, "InstActivityRead" + playerId, InstActivityRead.class);
//		InstPlayerActivityLogin login = redisDao.hashGet(KEY, "InstPlayerActivityLogin" + playerId,
//			InstPlayerActivityLogin.class);
//
//		AMLog.LOG_COMMON.info("[redisTest] [human_load] [playerId: " + playerId + "] [playerName "
//								+ instPlayer.getNickName() + "] [login " + login.getGotRecords() + "] [cost: "
//								+ (System.currentTimeMillis() - beginTime) + "ms]");
//
//		return 1;
//	}
}
