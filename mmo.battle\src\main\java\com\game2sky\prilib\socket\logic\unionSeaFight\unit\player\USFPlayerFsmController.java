package com.game2sky.prilib.socket.logic.unionSeaFight.unit.player;

import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateBattling;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateDeath;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateGarrison;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateGhost;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateMoving;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateNormal;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateWaiting;

/**
 * TODO 海战玩家状态机
 *
 * <AUTHOR>
 * @version v0.1 2018年6月9日 下午4:26:15  guojijun
 */
public class USFPlayerFsmController extends AbstractUSFUnitFsmController {

	public USFPlayerFsmController(USFPlayer player) {
		super(player);
		this.init(player);
	}

	private void init(USFPlayer player) {
		this.addFsmState(new USFUnitFsmStateNormal(this));
		this.addFsmState(new USFUnitFsmStateGarrison(this));
		this.addFsmState(new USFUnitFsmStateMoving(this, player));
		this.addFsmState(new USFUnitFsmStateBattling(this, player));
		this.addFsmState(new USFUnitFsmStateDeath(this));
		this.addFsmState(new USFUnitFsmStateWaiting(this, player));
		this.addFsmState(new USFUnitFsmStateGhost(this));
		this.addFsmState(new USFUnitFsmStateReorganize(this));
	}

}
