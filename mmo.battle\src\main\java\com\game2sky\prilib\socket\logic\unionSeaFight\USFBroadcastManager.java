package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.concurrent.LinkedBlockingQueue;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.BattleCoreBoot;
import com.game2sky.prilib.core.socket.logic.base.CommonMsgRecorder;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.common.CommonMsg;
import com.game2sky.publib.communication.game.chat.SCPushChannelChat;
import com.game2sky.publib.communication.game.chat.data.ChatChannel;
import com.game2sky.publib.communication.game.chat.data.ChatContentType;
import com.game2sky.publib.communication.game.chat.data.ChatFrom;
import com.game2sky.publib.communication.game.chat.data.ContacterChatInfo;
import com.game2sky.publib.communication.game.chat.inner.SSPushChannelChatToBenfu;
import com.game2sky.publib.communication.game.chat.inner.SSPushChannelChatToKuafu;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.protostuf.OpDefine;

/**
 * TODO 海战广播管理
 *
 * <AUTHOR>
 * @version v0.1 2018年9月3日 下午2:10:11  guojijun
 */
public class USFBroadcastManager implements IUSFTickable {

	private final USFight usFight;
	/**需要广播的玩家(key:服务器ID)*/
	private final ArrayList<USFBroadcast> broadcasts;
	/**频道消息*/
	private final LinkedBlockingQueue<SSPushChannelChatToKuafu> chats;
	/**发送消息*/
	private final LinkedList<SSPushChannelChatToKuafu> pushChats;
	
	private CommonMsgRecorder commonMsgRecorder;

	public USFBroadcastManager(USFight usFight) {
		this.usFight = usFight;
		this.broadcasts = new ArrayList<>(2);
		this.chats = new LinkedBlockingQueue<>();
		this.pushChats = new LinkedList<>();
		this.commonMsgRecorder = new CommonMsgRecorder(usFight.createCosKey());
	}

	/**
	 * 广播消息
	 * 
	 * @param msg
	 */
	public void broadcast(APSHandler msg) {
		com.game2sky.prilib.communication.game.unionSeaFight.USFStateType stateType = this.usFight.getFsmController()
			.getCurStateType();
		if (stateType != com.game2sky.prilib.communication.game.unionSeaFight.USFStateType.USF_STATE_PREPARE
			&& stateType != com.game2sky.prilib.communication.game.unionSeaFight.USFStateType.USF_STATE_NONE) {
			commonMsgRecorder.addReplayMsg(msg);
		}
		if (this.broadcasts.isEmpty()) {
			return;
		}
		for (USFBroadcast broadcast : this.broadcasts) {
			broadcast.pushMsg(msg);
		}
//		commonMsgRecorder.addReplayMsg(msg);
	}

	/**
	 * 获取指定serverId的广播
	 * 
	 * @param serverId
	 * @return
	 */
	USFBroadcast getBroadcast(int serverId) {
		if (this.broadcasts.isEmpty()) {
			return null;
		}
		for (USFBroadcast broadcast : this.broadcasts) {
			if (broadcast.getServerId() == serverId) {
				return broadcast;
			}
		}
		return null;
	}

	/**
	 * 当玩家进入
	 * 
	 * @param serverId
	 * @param playerId
	 */
	void onPlayerEnter(int serverId, long playerId) {
		USFBroadcast broadcast = this.getBroadcast(serverId);
		if (broadcast == null) {
			broadcast = new USFBroadcast(serverId, this.usFight);
			this.broadcasts.add(broadcast);
		}
		boolean bl = broadcast.addPlayer(playerId);
		if (bl) {
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战玩家进入,usFight={},serverId={},playerId={}", this, serverId, playerId);
			}
		}
	}

	/**
	 * 当玩家退出
	 * 
	 * @param serverId
	 * @param playerId
	 */
	void onPlayerQuit(int serverId, long playerId) {
		USFBroadcast broadcast = this.getBroadcast(serverId);
		if (broadcast != null) {
			boolean bl = broadcast.removePlayer(playerId);
			if (bl) {
				if (AMLog.LOG_UNION.isDebugEnabled()) {
					AMLog.LOG_UNION.debug("海战玩家离开,usFight={},serverId={},playerId={}", this, serverId, playerId);
				}
			}
		}
	}

	/**
	 * 添加聊天信息
	 * 
	 * @param chat
	 */
	public void addChat(SSPushChannelChatToKuafu chat) {
		this.chats.add(chat);
	}
	
	public void PushSystemBattleChat(String content){
//		SSPushChannelChatToKuafu msg = new SSPushChannelChatToKuafu();
//		msg.setToChatChannel(ChatChannel.CHAT_CHANNEL_USF_SYSTEM);
//		msg.setChatFrom(ChatFrom.SYSTEM);
//		ContacterChatInfo chatInfo = new ContacterChatInfo();
//		chatInfo.setIsHorn(false);
//		chatInfo.setContent(content);
//		chatInfo.setSendTime(Globals.getTimeService().now());
//		chatInfo.setContentType(ChatContentType.TEXT);
//		msg.setMsg(chatInfo);
//		addChat(msg);
		
		SCPushChannelChat scPushChannelChat = new SCPushChannelChat();
		ContacterChatInfo chatInfo = new ContacterChatInfo();
		chatInfo.setIsHorn(false);
		chatInfo.setContent(content);
		chatInfo.setSendTime(Globals.getTimeService().now());
		chatInfo.setContentType(ChatContentType.TEXT);
		scPushChannelChat.setMsg(chatInfo);
		scPushChannelChat.setFromId(0);
		scPushChannelChat.setToChatChannel(ChatChannel.CHAT_CHANNEL_USF_SYSTEM);
		scPushChannelChat.setChatFrom(ChatFrom.SYSTEM);
		this.broadcast(scPushChannelChat);
	}

	@Override
	public void tick(long now) {
		if (this.broadcasts.isEmpty()) {
			return;
		}
		this.pushChatToBenfu();
		for (USFBroadcast broadcast : this.broadcasts) {
			broadcast.tick(now);
		}
		
	}

	/**
	 * 推送聊天信息到本服
	 */
	private void pushChatToBenfu() {
		if (this.chats.isEmpty()) {
			return;
		}
		this.chats.drainTo(this.pushChats);
		try {
			for (SSPushChannelChatToKuafu chat : this.pushChats) {
				if(chat.getFromId() != 0)
				{
					USFPlayer player = this.usFight.getPlayer(chat.getFromId());
					if (player == null || !player.isInGame()) {
						continue;
					}
				}
				
				for (USFBroadcast broadcast : this.broadcasts) {
					Collection<Long> playerIds = broadcast.getPlayerIds();
					if (CollectionUtils.isEmpty(playerIds)) {
						continue;
					}
					ArrayList<Long> toIds = new ArrayList<>(playerIds);
					SSPushChannelChatToBenfu ssPushChannelChatToBenfu = new SSPushChannelChatToBenfu(chat.getMsg(),
						chat.getFromId(), chat.getFromServerId(), toIds, broadcast.getServerId(),
						chat.getToChatChannel(), chat.getChatFrom());
					BattleCoreBoot.pushToBenfu(broadcast.getChannel(), ssPushChannelChatToBenfu,
						broadcast.getServerId());
				}
			}
		} finally {	
			this.pushChats.clear();
		}
	}
	
	public CommonMsgRecorder getCommonMsgRecorder() {
		return commonMsgRecorder;
	}

}
