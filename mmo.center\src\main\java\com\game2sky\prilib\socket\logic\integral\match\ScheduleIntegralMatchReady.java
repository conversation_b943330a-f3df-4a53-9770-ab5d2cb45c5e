package com.game2sky.prilib.socket.logic.integral.match;

import com.game2sky.prilib.communication.game.integralArena.SSCheckIntegralMatchReady;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 扫描天梯等待准备队列
 * <AUTHOR>
 * @version v0.1 2017年10月26日 下午9:14:45  zhoufang
 */
public class ScheduleIntegralMatchReady extends ScheduledMessage {

	public static final long PERIOD = 1000L;

	public ScheduleIntegralMatchReady() {
		super(System.currentTimeMillis());
	}

	@Override
	public void execute() {
		Message msg = Message.buildByZoneId(0L, 0, new SSCheckIntegralMatchReady(), null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
	}

}
