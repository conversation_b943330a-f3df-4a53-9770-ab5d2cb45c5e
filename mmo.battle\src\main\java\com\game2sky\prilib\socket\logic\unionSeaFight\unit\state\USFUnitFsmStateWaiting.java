package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateWaitingInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitMoveable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;

/**
 * TODO 等待状态(在航线上)
 *
 * <AUTHOR>
 * @version v0.1 2018年6月15日 下午6:29:14  guojijun
 */
public class USFUnitFsmStateWaiting extends AbstractUSFUnitFsmState {

	private USFClientUnitStateWaitingInfo clientInfo;

	private final IUSFUnitMoveable moveable;
	/**等待超时时间*/
	private long timeout;

	public USFUnitFsmStateWaiting(AbstractUSFUnitFsmController controller, IUSFUnitMoveable moveable) {
		super(USFUnitStateType.USF_UNIT_STATE_WAITING, controller);
		this.moveable = moveable;
	}

	/**
	 * 重置等待超时时间
	 * 
	 * @param time(毫秒)
	 */
	public void resetTimeout(int time) {
		this.timeout = System.currentTimeMillis() + time;
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
		this.timeout = 0;
	}

	@Override
	public void tick(long now) {
		if (this.timeout > 0 && now < this.timeout) {
			return;
		}
		this.moveable.tryMoveOn();
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateWaitingInfo();
		}
		USFRoute route = this.moveable.getRoute();
		this.clientInfo.setRouteId(route.getDictId());
		float progressPercent = route.getProgressPercent(this.moveable);
		this.clientInfo.setProgress(progressPercent);
		USFFortress targetFortress = moveable.getTargetFortress();
		this.clientInfo.setTargetFortressUid(targetFortress.getId());
		clientUnitStateInfo.setWaitingInfo(this.clientInfo);
	}

}
