package com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareEnemyListInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarConfirm;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarListInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarUpdate;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSBenfuUSDFScheduleMessage;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSendEnemyUnionListToBenfu;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncChangeUnionInfoToBenfu;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncFightUnionInfoToBenfu;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年12月3日 下午8:40:27  daixl
 */
@Controller
public class BenfuUSDFController {

	@Autowired
	private BenfuUSDFService benfuUSDFService;

	/**
	 * 中心服同步公会信息到本服
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncChangeUnionInfoToBenfu)
	public void ssSyncChangeUnionInfoToBenfu(SSSyncChangeUnionInfoToBenfu msg) {
		this.benfuUSDFService.ssSyncChangeUnionInfoToBenfu(msg);
	}

	/**
	 * 约战定时
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSBenfuUSDFScheduleMessage)
	public void ssBenfuUSDFScheduleMessage(SSBenfuUSDFScheduleMessage msg) {
		this.benfuUSDFService.ssBenfuUSDFScheduleMessage(msg);
	}

//	/**
//	 * 约战结果返回
//	 * 
//	 * @param msg
//	 * @param human
//	 */
//	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncDeclareMessageToBenfu)
//	public void ssSyncDeclareMessageToBenfu(SSSyncDeclareMessageToBenfu msg, Human human) {
//		this.benfuUSDFService.ssSyncDeclareMessageToBenfu(msg, human);
//	}

	/**
	 * 约战信息
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncFightUnionInfoToBenfu)
	public void ssSyncFightUnionInfoToBenfu(SSSyncFightUnionInfoToBenfu msg) {
		this.benfuUSDFService.ssSyncFightUnionInfoToBenfu(msg);
	}

	/**
	 * 约战信息
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSendEnemyUnionListToBenfu)
	public void ssSendEnemyUnionListToBenfu(SSSendEnemyUnionListToBenfu msg) {
		this.benfuUSDFService.ssSendEnemyUnionListToBenfu(msg);
	}

	/**
	 * 打开宣战界面
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSDeclareWarInfo)
	public void getDeclareWarInfo(CSDeclareWarInfo msg, Human human) {
		this.benfuUSDFService.getDeclareWarInfo(msg, human);
	}

	/**
	 * 获取可宣战的列表
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSDeclareWarListInfo)
	public void getDeclareWarList(CSDeclareWarListInfo msg, Human human) {
		this.benfuUSDFService.getDeclareWarList(human, true, msg.getTimeId());
	}

	/**
	 * 刷新可宣战列表
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSDeclareWarUpdate)
	public void updateDeclareWarList(CSDeclareWarUpdate msg, Human human) {
		this.benfuUSDFService.getDeclareWarList(human, false, msg.getTimeId());
	}

	/**
	 * 宣战
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSDeclareWarConfirm)
	public void declareWarConfirm(CSDeclareWarConfirm msg, Human human) {
		this.benfuUSDFService.declareWarConfirm(msg, human);
	}

	/**
	 * 获取敌人列表
	 * 
	 * @param msg
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSDeclareEnemyListInfo)
	public void getDeclareEnemyListInfo(CSDeclareEnemyListInfo msg, Human human) {
		this.benfuUSDFService.getDeclareEnemyListInfo(msg, human);
	}

//	@RequestMapping("/manager/getBenfuInfo")
//	public ModelAndView getBenfuInfo(HttpServletRequest request) {
//		return new ModelAndView(new FastJsonView(), AView.RESULT, BenfuUSDFUnionMemberManager.map);
//	}

}
