package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress;

import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateNormal;

/**
 * TODO 海战据点状态机
 *
 * <AUTHOR>
 * @version v0.1 2018年6月14日 下午9:51:56  guojijun
 */
public class USFFortressFsmController extends AbstractUSFUnitFsmController {

	public USFFortressFsmController(USFFortress fortress) {
		super(fortress);
		this.init(fortress);
	}

	private void init(USFFortress fortress) {
		this.addFsmState(new USFUnitFsmStateNormal(this));
		this.addFsmState(new USFFortressFsmStateBeAttacked(this));
	}
}
