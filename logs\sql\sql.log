2024-04-17 18:03:36,050 [DbLandService-woker-thread-7] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:03:36,050 [DbLandService-woker-thread-7] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:03:46,050 [DbLandService-woker-thread-8] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:03:46,050 [DbLandService-woker-thread-8] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:04:06,059 [DbLandService-woker-thread-1] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:04:06,059 [DbLandService-woker-thread-1] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:04:20,766 [DbLandService-woker-thread-2] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:04:20,766 [DbLandService-woker-thread-2] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713338600138,"playerId":800010028601,"splitCount":0,"splitPlayerId":800010028601,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010028601}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:04:36,070 [DbLandService-woker-thread-3] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:04:36,070 [DbLandService-woker-thread-3] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:05:16,082 [DbLandService-woker-thread-4] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:05:16,082 [DbLandService-woker-thread-4] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:06:06,087 [DbLandService-woker-thread-5] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:06:06,088 [DbLandService-woker-thread-5] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:07:06,094 [DbLandService-woker-thread-6] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:07:06,094 [DbLandService-woker-thread-6] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:08:16,104 [DbLandService-woker-thread-7] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:08:16,105 [DbLandService-woker-thread-7] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:09:36,114 [DbLandService-woker-thread-8] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:09:36,115 [DbLandService-woker-thread-8] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:11:06,125 [DbLandService-woker-thread-1] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:11:06,125 [DbLandService-woker-thread-1] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:12:46,133 [DbLandService-woker-thread-2] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:12:46,134 [DbLandService-woker-thread-2] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:13:00,770 [DbLandService-woker-thread-3] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:13:00,771 [DbLandService-woker-thread-3] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713338600138,"playerId":800010028601,"splitCount":0,"splitPlayerId":800010028601,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010028601}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:14:36,138 [DbLandService-woker-thread-4] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:14:36,138 [DbLandService-woker-thread-4] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:16:36,139 [DbLandService-woker-thread-5] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:16:36,140 [DbLandService-woker-thread-5] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:18:46,148 [DbLandService-woker-thread-6] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:18:46,148 [DbLandService-woker-thread-6] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:21:06,155 [DbLandService-woker-thread-7] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:21:06,165 [DbLandService-woker-thread-7] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:21:50,776 [DbLandService-woker-thread-8] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:21:50,777 [DbLandService-woker-thread-8] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713338600138,"playerId":800010028601,"splitCount":0,"splitPlayerId":800010028601,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010028601}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-17 18:23:36,152 [DbLandService-woker-thread-1] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-17 18:23:36,153 [DbLandService-woker-thread-1] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"dBNameSuffix":"player","enitityClassName":"InstPlayerMonitor","entityPriority":247,"fromServerLocalHostId":0,"integralAreaMonitorData":"{\"areaMonitors\":{}}","integralRiskLevel":0,"modified":false,"modifyTime":1713351815849,"playerId":800010036001,"splitCount":0,"splitPlayerId":800010036001,"splitSuffix":"","tableName":"inst_player_monitor","uniqDBSId":800010036001}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:85) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:80) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager.updateEntity(PlayerMonitorManager.java:56) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.ConstraintViolationException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:71) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.SQLIntegrityConstraintViolationException: Column 'integralBannedEndTime' cannot be null
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
