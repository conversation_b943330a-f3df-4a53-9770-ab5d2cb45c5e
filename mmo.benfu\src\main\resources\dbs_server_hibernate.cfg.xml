<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-configuration PUBLIC "-//Hibernate/Hibernate Configuration DTD//EN" "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
	<session-factory>
<!--		<property name="hibernate.dialect">org.hibernate.dialect.MySQL5InnoDBDialect</property>-->
		<property name="hibernate.dialect">com.game2sky.publib.CustomMySQLDialect</property>
		<property name="hibernate.physical_naming_strategy">com.game2sky.publib.CustomNamingStrategy</property>

		<!--		<property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver </property>-->
		<property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
		<property name="hibernate.transaction.factory_class">org.hibernate.transaction.JDBCTransactionFactory</property>
		<property name="hibernate.cache.provider_class">org.hibernate.cache.NoCacheProvider</property>
		<property name="hibernate.cache.use_query_cache">false</property>
<!--		<property name="hibernate.connection.provider_class">org.hibernate.connection.C3P0ConnectionProvider</property>-->
<!--		<property name="hibernate.connection.provider_class">com.zaxxer.hikari.hibernate.HikariConnectionProvider</property>-->
		<property name="hibernate.connection.provider_class">org.hibernate.connection.DriverManagerConnectionProvider</property>

		<!-- <property name="hibernate.connection.url">*************************************************************************************************************************</property> -->
		<!-- <property name="hibernate.connection.username">root</property> -->
		<!-- <property name="hibernate.connection.password"></property> -->

		<property name="show_sql">false</property>
		<property name="hbm2ddl.auto">none</property>
		<property name="hibernate.c3p0.max_size">50</property>  
		<property name="hibernate.c3p0.min_size">8</property>   
		<property name="hibernate.c3p0.acquire_increment">1</property>
		<property name="hibernate.c3p0.timeout">1000</property>    
		<property name="hibernate.c3p0.max_statements">100</property> 
		<property name="hibernate.c3p0.acquireRetryAttempts">10</property>
		<property name="hibernate.c3p0.acquireRetryDelay">2000</property>
        
        <property name="hibernate.c3p0.testConnectionOnCheckout">true</property>
		<property name="hibernate.c3p0.idle_test_period">300</property>
		<property name="hibernate.c3p0.preferredTestQuery">select 1</property>

        <property name="hibernate.c3p0.breakAfterAcquireFailure">false</property>  
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerChat" />
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerExtraProp" />
		<mapping class="com.game2sky.publib.dbs.model.InstPlayer" />
		<mapping class="com.game2sky.prilib.dbs.model.InstHero" />
		<mapping class="com.game2sky.prilib.dbs.model.InstIdCounter" />
		<mapping class="com.game2sky.prilib.dbs.model.InstServerConfig" />
		<mapping class="com.game2sky.publib.dbs.model.InstContainerData" />
		<mapping class="com.game2sky.prilib.dbs.model.InstFormation" />
		<mapping class="com.game2sky.prilib.dbs.model.InstHeroSkill"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstCurrency"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerRecruit"/>
		<mapping class="com.game2sky.publib.dbs.model.InstContacterRelation"/>
		<mapping class="com.game2sky.publib.dbs.model.InstContacterChat"/>
		<mapping class="com.game2sky.publib.dbs.model.InstContacterSetting"/>
		<mapping class="com.game2sky.publib.dbs.model.InstChatGroup"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstRaidData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstEquipmentData"/>
		<mapping class="com.game2sky.publib.dbs.model.InstTask"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSceneData"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerMail"/>
		<mapping class="com.game2sky.publib.dbs.model.InstServerMail"/>
		<mapping class="com.game2sky.publib.dbs.model.InstServerMailReceivedRecord"/>
		<mapping class="com.game2sky.publib.dbs.model.InstShopLimitData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstAchievement"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstRaidSailLog"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstLife"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstArena"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerArena"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerFight"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstFightReplay"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstExpeditionData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSystemSettings"/>
		<mapping class="com.game2sky.publib.dbs.model.InstSystemMarketItem"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityRaid"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstDailyTask"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstTitle"/>
		<mapping class="com.game2sky.publib.dbs.model.InstFaceToFaceDealing"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerShop"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerShopCommodity"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerShopDealing"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstMSeaData"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerGiftCode"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstFightRecord"/>
		<mapping class= "com.game2sky.prilib.dbs.model.InstPlotData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerSceneBuff"/>
		<mapping class="com.game2sky.publib.dbs.model.InstAwardPlayer"/>
		<mapping class="com.game2sky.publib.dbs.model.ActivityConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityShop"/>
		<mapping class="com.game2sky.publib.dbs.model.InstRedPoint"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityMonthlySignIn"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityMonthlySignIn"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityLevelReward"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityRead"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityAwardConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityLogin"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstRecordPlayerActivity"/>  
        <mapping class="com.game2sky.prilib.dbs.model.InstRecordActivityRank"/>  
		<mapping class="com.game2sky.publib.dbs.model.InstIdentity"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityTimeLimitRecruit"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityTimeLimitRecruit"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerGuide"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstBountyHuntPlayerData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstBountyHuntOrderData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnion"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionPlayer"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionMember"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionApply"/>
		<mapping class="com.game2sky.publib.dbs.model.InstSysDbVersion"/>
		<mapping class="com.game2sky.publib.dbs.model.InstBlock"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstBiConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstBiStatisticsPlayer"/>
		<mapping class="com.game2sky.publib.dbs.model.InstContacterKuafuRelation"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionBuilding"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionCurrency"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerCommonTimes"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstExeMail"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerExeMail"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionBlag"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionBattery"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPrisonData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerPrisonData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionTrainRoom"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerRecharge"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPay"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionAttornPresident"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerBloodyFight"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionCommonTimes"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerMorale"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionBanquet"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionState"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionDonate"/>
		<mapping class="com.game2sky.publib.dbs.model.InstPlayerDealConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerDeviceInfo"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstInviteCode"/>
		<mapping class="com.game2sky.publib.dbs.model.InstGlobalShopLimitData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionShopLimitData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSysAdvertisement"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionShopAuctionData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionShopRefresh"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerShopRefresh"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerShopBuy"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstShipData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityGift"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityGiftProductConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstOnlineTime"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstReputation"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityDriftBottleConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityDriftBottleShare"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstDriftBottlePlayerData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionControlCenter"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionSeaFight"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityDailyLogin"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerShipTrade"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstOpenCelebration"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstDailyActive"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivitySpecialRecruitConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSpecialRecruitRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstYybRewardRecord"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstCommonRewardRecord"/>
        <mapping class="com.game2sky.prilib.dbs.model.CbtPay"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstReport"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstCommonFightData"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstCommonFormationData"/>
        <mapping class="com.game2sky.publib.dbs.model.InstPlayerTimelimitActivity"/>
       	<mapping class="com.game2sky.prilib.dbs.model.ActivityHoliday"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityLuckyCard"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstUnionShopSaleData"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstPlayerContinuousPay"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstPlayerDiamondCost"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstPlayerExtraDrop"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstPlayerDailyLogin"/>
       	<mapping class="com.game2sky.prilib.dbs.model.InstHeroFashion"/>
     	<mapping class="com.game2sky.publib.dbs.model.InstPlayerMonitor"/>
     	<mapping class="com.game2sky.prilib.dbs.model.InstUnionSeaFightReplay"/>
     	<mapping class="com.game2sky.prilib.dbs.model.InstPlayerReturn"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstPlayerTurnTable"/>
        <mapping class="com.game2sky.prilib.dbs.model.ActivityTurnTable"/>
        <mapping class="com.game2sky.prilib.dbs.model.InstActivityTimeLimitCollectionConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerLuckyCard"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityUnionRepair"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityAdditionalDropConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerAdditionalDropRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityUnionRepair"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionActivityUnionRepair"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerTimeLimitCommon"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstFormationLev"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerPayPrivilege"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerIntegral"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstActivityExtractEquip"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityExtractEquip"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerAutoChess"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstResourceBack"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstTreasureSea"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityTreasureSeaConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerWarChess"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerYearActivity"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHumanAutochess"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionPray"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHideTreasure"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnnalShare"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstNavigationPass"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityOptionalRecruitConfig"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstOptionalRecruitRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityLimitBuyEquip"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityLimitBuyEquip"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerGvo"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerGvoPlace"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstArenaServer"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerSpring"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstUnionRedPacket"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstCatchAction"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSecretPlaceFightPlayer"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSecretPlaceFightHighScore"/>
		<mapping class="com.game2sky.prilib.dbs.model.ActivityCommonExtra"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerStudio"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerResourceCostRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityInvest"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstSuperiorRecruitRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPromoteRecruitRecord"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerCollection"/>
		
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLand"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandGridArea"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandHero"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandDorm"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandProduce"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandCream"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandRelationship"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandWarHouse"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandFightData"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstHomeLandTask"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerExplore"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityPuzzle"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPlayerActivityBless"/>


		<mapping class="com.game2sky.prilib.dbs.model.InstPartner"/>
		<mapping class="com.game2sky.prilib.dbs.model.InstPartnerPlayer"/>
	</session-factory>
</hibernate-configuration>