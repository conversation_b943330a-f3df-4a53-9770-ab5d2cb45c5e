package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateMovingInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitMoveable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;

/**
 * TODO 单位移动中(肯定在航线上)
 *
 * <AUTHOR>
 * @version v0.1 2018年6月9日 下午4:39:49  guojijun
 */
public class USFUnitFsmStateMoving extends AbstractUSFUnitFsmState {

	private final IUSFUnitMoveable moveable;
	private USFClientUnitStateMovingInfo clientInfo;

	public USFUnitFsmStateMoving(AbstractUSFUnitFsmController controller, IUSFUnitMoveable moveable) {
		super(USFUnitStateType.USF_UNIT_STATE_MOVING, controller);
		this.moveable = moveable;
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
		this.moveable.refreshProgress(now);
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateMovingInfo();
		}
		USFRoute route = this.moveable.getRoute();
		this.clientInfo.setRouteId(route.getDictId());
		float progressPercent = route.getProgressPercent(this.moveable);
		this.clientInfo.setProgress(progressPercent);
		USFFortress targetFortress = this.moveable.getTargetFortress();
		this.clientInfo.setTargetFortressUid(targetFortress.getId());
		this.clientInfo.setDiveState(moveable.getDiveState());
		clientUnitStateInfo.setMovingInfo(clientInfo);
	}

}
