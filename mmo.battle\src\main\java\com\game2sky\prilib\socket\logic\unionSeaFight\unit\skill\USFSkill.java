package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUFFortressAttack;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffSelectType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFSkillType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.prilib.core.dict.domain.DictUsfSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IBattleTriggerEvent;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuffFactory;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * 海战技能
 *
 * <AUTHOR>
 * @version v0.1 2018年10月23日 上午2:06:33  lidong
 */
public class USFSkill implements IUSFConsts, IBattleTriggerEvent
{

	private static int SKILL_ID = 1;
	private int guid;

	protected int skillId;

	protected AbstractUSFUnit owner;

	/**
	 * 触发类型
	 */
	protected USFBuffTriggerType triggerType;

	/**
	 * 关闭时机
	 */
	protected USFBuffTriggerType closeType;

	/**
	 * 所产生buff的卸载类型
	 */
	protected USFBuffTriggerType unloadType;

	/**
	 * 选择策略
	 */
	protected USFBuffSelectType selectType;

	/**
	 * 选择条件
	 */
	private TriggerCondition triggerCondition;

	private String selectCondition;

	// private int selectCondition;

	protected List<USFBuff> usfBuffers;

	private String stateParticle;

	protected String extraInfo;

	// private String effectParticle;
	/** 技能是否关闭，角色死亡以后技能应该关闭 */
	protected boolean closed = false;

	/**周期 */
	protected int duration;

	public static final String RESET_SAME_IN_TOWER ="reset_sameskill_intower";

	public USFSkill()
	{
		guid = SKILL_ID++;
	}

	public void setData(DictUsfSkill dictUsfSkill)
	{
		skillId = dictUsfSkill.getSkillId();
		this.setTriggerType(USFBuffTriggerType.valueOf(dictUsfSkill.getTriggerType()));
		this.setSelectType(USFBuffSelectType.valueOf(dictUsfSkill.getSelectType()));
		this.setCloseType(USFBuffTriggerType.valueOf(dictUsfSkill.getCloseType()));
		setUnloadType(USFBuffTriggerType.valueOf(dictUsfSkill.getUnloadType()));
		selectCondition = dictUsfSkill.getSelectCondition();
		triggerCondition = new TriggerCondition(dictUsfSkill.getTriggerCondition());
		stateParticle = dictUsfSkill.getStateParticle();
		this.extraInfo = dictUsfSkill.getExtroInfo();
		// effectParticle = dictUsfSkill.getEffectParticle();
		this.duration = dictUsfSkill.getDuration();

		setUsfBuffers(USFBuff.createBuff(dictUsfSkill.getUsfBuffers()));
	}

	public USFSkillType getSkillType()
	{
		return USFSkillType.NORMAL_USF_SKILL;
	}

	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,
								Object... args)
	{
		if (triggerUnit == null)
		{
			return;
		}
		if (this.triggerType == triggerType
			&& triggerCondition.meetCondition(triggerType, triggerUnit, oppositeUnit, args))
		{
			this.sourceUnit = triggerUnit;
			this.targetUnit = oppositeUnit;
			this.extraArgs = args;
//			if(triggerType == USFBuffTriggerType.BE_KILLED)
//			{
//				System.out.println("被杀了" + skillId);
//			}
//			if(sourceUnit instanceof USFPlayer)
//			{
//				USFPlayer player = (USFPlayer)sourceUnit;
//				if(player.getMoveController().getShipId() == 130001)
//				{
//					System.out.println("黄金梅里号" + player.getDisplayInfo().getNickName());
//				}
//			}
			doEffect(owner.getNow());
		}
	}

	protected AbstractUSFUnit sourceUnit;
	protected AbstractUSFUnit targetUnit;
	protected Object[] extraArgs;
	protected long lastDurationTime;

	public void doDurationEffect(long now)
	{
		lastDurationTime = now;
		return;
	}

	public void doEffect(long now)
	{
		if (getUsfBuffers() == null || getUsfBuffers().size() == 0)
		{
			return;
		}

		List<AbstractUSFUnit> selectTargets = selectTarget(selectType, sourceUnit, targetUnit, extraArgs);
		if (selectTargets == null || selectTargets.size() == 0)
		{
			return;
		}
		lastDurationTime = now;
		for (AbstractUSFUnit target : selectTargets)
		{
			if (target == null)
			{
				continue;
			}
			int duration = 1;
			for (USFBuff buff : getUsfBuffers())
			{
				if(selectType == USFBuffSelectType.OPPOSITE_WITH_SOURCE && buff.getSourceUnit() == target)
				{
					continue;
				}
				USFBuff copyBuff = buff.copy();
				copyBuff.setUnloadType(unloadType);
				copyBuff.setSourceUnit(owner);
				copyBuff.setOwnerUnit(target);
				copyBuff.setSourceSkill(this);
				USFBuffFactory.addExtraProps(owner, copyBuff.getClientInfo().getProInfos());
				target.addBuff(copyBuff);
				duration = copyBuff.getClientInfo().getDuration();
			}
			if (selectType == USFBuffSelectType.CONNECT_ROUTE_PLAYER_RANDOM)
			{
				SCUFFortressAttack scAttact = new SCUFFortressAttack();
				if(sourceUnit instanceof USFPlayer)
                {
                	USFFortress fortress = sourceUnit.getCurFortress();
                	if(fortress != null)
					{
						scAttact.setFortressUid(fortress.getId());
					}
                    scAttact.setAttactPlayerUId(sourceUnit.getId());
                }
                else
                {
                    scAttact.setFortressUid(sourceUnit.getId());
                }

				scAttact.setPlayerUid(target.getId());

				scAttact.setDelayDuration(duration);
				this.sourceUnit.getCamp().getUsFight().broadcast(scAttact);
			}
		}
		doExtra();
	}

	public void doExtra()
    {
        if(extraInfo != null && !extraInfo.equals(""))
        {
            if(RESET_SAME_IN_TOWER.equals(extraInfo))
            {
                if(owner instanceof USFPlayer)
                {
                    USFPlayer ownerPlayer = (USFPlayer)owner;
                    USFFortress fortress = ownerPlayer.getCurFortress();
                    if(fortress != null)
                    {
                        for(IUSFUnitFightable uint: fortress.getDefenders())
                        {
                            USFPlayer player = (USFPlayer)uint;
                            if(player != null)
                            {
                                for(USFSkill skill : player.getSkillController().getSkills())
                                {
                                    if(skill.getSkillId() == skillId)
                                    {
                                        skill.lastDurationTime = this.lastDurationTime;
                                    }
                                }
                            }
                        }
                    }

                }
            }
            else
            {

            }

        }
    }
	
	public boolean IsArriveDuration(long now)
	{
		return false;
	}

	public void doDuration()
	{

	}

	public static List<AbstractUSFUnit> selectTarget(USFBuffSelectType selectType, AbstractUSFUnit sourceUnit,
														AbstractUSFUnit targetUnit, Object... args)
	{
		List<AbstractUSFUnit> selectTargets = new ArrayList<AbstractUSFUnit>();
		switch (selectType)
		{
			case SELF:
				if (sourceUnit != null)
				{
					selectTargets.add(sourceUnit);
				}
				break;
			case FIGHT_BOTH:
				if (sourceUnit != null)
				{
					selectTargets.add(sourceUnit);
				}
				if (targetUnit != null)
				{
					selectTargets.add(targetUnit);
				}
				break;
			case PALYER_IN_SAME_TOWER:
				if (sourceUnit != null && sourceUnit instanceof USFFortress)
				{
					USFFortress sourceFortress = (USFFortress) sourceUnit;
					// 由据点发起的
					LinkedList<IUSFUnitFightable> defenders = sourceFortress.getDefenders();
					for (IUSFUnitFightable defender : defenders)
					{
						selectTargets.add((AbstractUSFUnit) defender);
					}
				} else if (sourceUnit != null && sourceUnit instanceof USFPlayer && targetUnit instanceof USFFortress)
				{
					USFFortress targetFortress = (USFFortress) targetUnit;
					// 由据点发起的
					LinkedList<IUSFUnitFightable> defenders = targetFortress.getDefenders();
					for (IUSFUnitFightable defender : defenders)
					{
						selectTargets.add((AbstractUSFUnit) defender);
					}
				} else
				{
					AMLog.LOG_ERROR.info("buffer select error PALYER_IN_SAME_TOWER");
				}
				break;
			case PALYER_IN_SAME_TOWER_WITHOUT_SELF:
				if (sourceUnit != null && sourceUnit instanceof USFPlayer && targetUnit instanceof USFFortress)
				{
					USFFortress targetFortress = (USFFortress) targetUnit;
					// 由据点发起的
					LinkedList<IUSFUnitFightable> defenders = targetFortress.getDefenders();
					for (IUSFUnitFightable defender : defenders)
					{
						if (defender.getId() != sourceUnit.getId())
						{
							selectTargets.add((AbstractUSFUnit) defender);
						}
					}
				} else
				{
					AMLog.LOG_ERROR.info("buffer select error PALYER_IN_SAME_TOWER");
				}
				break;
			case ALL_SELF:
				if (sourceUnit != null && sourceUnit instanceof USFPlayer)
				{
					Collection<IUSFUnit> units = sourceUnit.getCamp().getUnits();
					for (IUSFUnit unit : units)
					{
						if (unit instanceof USFPlayer)
						{
							selectTargets.add((AbstractUSFUnit) unit);
						}
					}
				}
				break;
			case ALL_RIVAL:
				if (sourceUnit != null && sourceUnit instanceof USFPlayer)
				{
					USFCampType campType = sourceUnit.getCampType() == USFCampType.USF_CAMP_BLUE ? USFCampType.USF_CAMP_RED
						: USFCampType.USF_CAMP_RED;

					USFCamp targetCamp = sourceUnit.getCamp().getUsFight().getCamp(campType);
					for (IUSFUnit unit : targetCamp.getUnits())
					{
						if (unit instanceof USFPlayer)
						{
							selectTargets.add((AbstractUSFUnit) unit);
						}
					}
				}

				break;
			case ALL:
				if (sourceUnit != null && sourceUnit instanceof USFPlayer)
				{
					for (IUSFUnit unit : sourceUnit.getCamp().getUnits())
					{
						if (unit instanceof USFPlayer)
						{
							selectTargets.add((AbstractUSFUnit) unit);
						}
					}

					USFCampType campType = sourceUnit.getCampType() == USFCampType.USF_CAMP_BLUE ? USFCampType.USF_CAMP_RED
						: USFCampType.USF_CAMP_RED;
					USFCamp targetCamp = sourceUnit.getCamp().getUsFight().getCamp(campType);
					for (IUSFUnit unit : targetCamp.getUnits())
					{
						if (unit instanceof USFPlayer)
						{
							selectTargets.add((AbstractUSFUnit) unit);
						}
					}
				}
				break;
			case OPPOSITE:
				if (targetUnit != null)
				{
					selectTargets.add(targetUnit);
				}
				break;
			case CONNECT_NORMAL_TOWER:
				if (sourceUnit != null && sourceUnit instanceof USFFortress)
				{
					USFFortress fortress = (USFFortress) sourceUnit;
					fortress.selectConnectedFortresses(selectTargets, fortress.getCampType(),
						USFFortressType.USF_FORTRESS_NORMAL);
//					TIntObjectHashMap<USFRoute> allRoutes = fortress.getAllRoutes();
//					TIntObjectIterator<USFRoute> iterator = allRoutes.iterator();
//					while (iterator.hasNext())
//					{
//						iterator.advance();
//						
//						USFFortress fortress1 = iterator.value().getHeadVertex().getFortress();
//						if(fortress1 == fortress)
//						{
//							fortress1 = iterator.value().getTailVertex().getFortress();
//						}
//						if (fortress1 != null && fortress1.getType() == USFFortressType.USF_FORTRESS_BASE)
//						{
//							selectTargets.add(fortress1);
//						}
//					}
				}
				break;
			case CONNECT_ROUTE_PLAYER_RANDOM:
				if (sourceUnit != null)
				{
                    USFFortress fortress = null;
                    if(sourceUnit instanceof USFFortress)
                    {
                        fortress = (USFFortress) sourceUnit;
                    }
                    else
                    {
                        fortress = sourceUnit.getCurFortress();
                    }
                    if(fortress == null)
                    {
                        break;
                    }
					List<AbstractUSFUnit> players = new ArrayList<AbstractUSFUnit>();
					USFCampType campType = (fortress.getCampType() == USFCampType.USF_CAMP_BLUE ? USFCampType.USF_CAMP_RED
						: USFCampType.USF_CAMP_BLUE);
					fortress.selectConnectedRoutesUnits(players, campType, USFUnitType.USF_UNIT_PLAYER);
					if (players.size() > 0)
					{	
						// 移除潜行船
						for (Iterator<AbstractUSFUnit> iterator = players.iterator(); iterator.hasNext();) {
							AbstractUSFUnit unit = (AbstractUSFUnit) iterator.next();
							USFPlayer player = (USFPlayer) unit;
							if (player.isInDiveState()) {
								// 潜行的船，不能被选取
								iterator.remove();
							}
						}
						if (players.size() > 0) {
							ThreadLocalRandom random = ThreadLocalRandom.current();
							selectTargets.add(players.get(random.nextInt(players.size())));
						}
					}

//					TIntObjectHashMap<USFRoute> allRoutes = fortress.getAllRoutes();
//					TIntObjectIterator<USFRoute> iterator = allRoutes.iterator();
//					List<USFPlayer> players = new ArrayList<USFPlayer>();
//					while (iterator.hasNext())
//					{
//						iterator.advance();
//						USFRoute route = iterator.value();
//						IUSFRouteNode nextNode = route.getHeadVertex().getNextNode();
//								//对方的人
//						while(nextNode instanceof USFPlayer)
//						{
//							USFPlayer player = (USFPlayer) nextNode;
//							if(player.getCampType() != fortress.getCampType())
//							{
//								players.add(player);
//							}
//						}
//					}
//					if(players.size() > 0)
//					{
//						ThreadLocalRandom random = ThreadLocalRandom.current(); 
//						selectTargets.add(players.get(random.nextInt(players.size())));
//					}
				}
				break;
			case OPPOSITE_WITH_SOURCE:
				if (sourceUnit != null)
				{
					selectTargets.add(targetUnit);
				}
				break;
			default:
				break;
		}
		return selectTargets;
	}

	public int getSkillId()
	{
		return skillId;
	}

	public void setSkillId(int skillId)
	{
		this.skillId = skillId;
	}

	public String getSelectCondition()
	{
		return selectCondition;
	}

	public void setSelectCondition(String selectCondition)
	{
		this.selectCondition = selectCondition;
	}

	public String getStateParticle()
	{
		return stateParticle;
	}

	public void setStateParticle(String stateParticle)
	{
		this.stateParticle = stateParticle;
	}

//	public String getEffectParticle()
//	{
//		return effectParticle;
//	}
//
//	public void setEffectParticle(String effectParticle)
//	{
//		this.effectParticle = effectParticle;
//	}

	public USFBuffTriggerType getTriggerType()
	{
		return triggerType;
	}

	public void setTriggerType(USFBuffTriggerType triggerType)
	{
		this.triggerType = triggerType;
	}

	public USFBuffSelectType getSelectType()
	{
		return selectType;
	}

	public void setSelectType(USFBuffSelectType selectType)
	{
		this.selectType = selectType;
	}

	public int getGuid()
	{
		return guid;
	}

	public void setGuid(int guid)
	{
		this.guid = guid;
	}

	public List<USFBuff> getUsfBuffers()
	{
		return usfBuffers;
	}

	public void setUsfBuffers(List<USFBuff> usfBuffers)
	{
		this.usfBuffers = usfBuffers;
		if (usfBuffers != null)
		{
			for (USFBuff buff : usfBuffers)
			{
				buff.setUnloadType(unloadType);
			}
		}
	}

	public AbstractUSFUnit getOwner()
	{
		return owner;
	}

	public void setOwner(AbstractUSFUnit owner)
	{
		this.owner = owner;
	}

	public boolean isClosed()
	{
		return closed;
	}

	public void setClosed(boolean closed)
	{
		this.closed = closed;
	}

	public USFBuffTriggerType getCloseType()
	{
		return closeType;
	}

	public void setCloseType(USFBuffTriggerType closeType)
	{
		this.closeType = closeType;
	}

	/**
	 * 关闭技能，在角色死亡的时候
	 */
	public void closeSkill()
	{
		this.closed = true;
	}

	public USFBuffTriggerType getUnloadType()
	{
		return unloadType;
	}

	public void setUnloadType(USFBuffTriggerType unloadType)
	{
		this.unloadType = unloadType;
	}

	public TriggerCondition getTriggerCondition()
	{
		return triggerCondition;
	}

	public void setTriggerCondition(TriggerCondition triggerCondition)
	{
		this.triggerCondition = triggerCondition;
	}

	public String getExtraInfo() {
		return extraInfo;
	}

	public void setExtraInfo(String extraInfo) {
		this.extraInfo = extraInfo;
	}
}
