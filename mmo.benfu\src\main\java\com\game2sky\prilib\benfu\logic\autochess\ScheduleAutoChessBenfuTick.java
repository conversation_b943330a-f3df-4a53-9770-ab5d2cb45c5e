package com.game2sky.prilib.benfu.logic.autochess;

import com.game2sky.prilib.communication.game.autochess.SSAutoChessBenfuTick;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 本服自走棋心跳.
 *
 * <AUTHOR>
 * @version v0.1 2019年8月19日 下午2:27:29  Jet Lee
 */
public class ScheduleAutoChessBenfuTick extends ScheduledMessage {

	public static final int DELAY = 10000;
	
    public static final int PERIOD = 1000;
    

    public ScheduleAutoChessBenfuTick() {
        super(System.currentTimeMillis());
    }

    @Override
    public void execute() {
        // 这里只是发送消息
    	SSAutoChessBenfuTick tick = new SSAutoChessBenfuTick();
    	
        Message message = Message.buildByServerId(0L, 0, tick, null, null, null);
        Globals.getMsgProcessDispatcher().put(message);
    }
}
