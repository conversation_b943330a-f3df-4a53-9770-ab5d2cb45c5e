package com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSBenfuRequestUnionInfoMessage;
import com.game2sky.publib.Globals;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年12月7日 下午7:21:23  daixl
 */
public class BenfuRequsetUnionInfoMessage extends ScheduledMessage {

	public static final int DELAY = 2000;

	private int groupId;

	private int zoneId;

	public BenfuRequsetUnionInfoMessage(int groupId, int zoneId) {
		super(System.currentTimeMillis());
		this.groupId = groupId;
		this.zoneId = zoneId;
	}

	@Override
	public void execute() {
		SSBenfuRequestUnionInfoMessage message = new SSBenfuRequestUnionInfoMessage();
		message.setGroupId(groupId);
		message.setServerId(Globals.getServerIdList(zoneId).get(0));
		CoreBoot.benfuToKuafu(message, 0, zoneId);
	}

}
