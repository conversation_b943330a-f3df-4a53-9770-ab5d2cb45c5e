package com.game2sky.prilib.benfu.logic.integral;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.core.redis.RedisManager;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.redis.RedisDao;
import com.game2sky.publib.constants.RedisKeyConstantsPub;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.JsonUtils;

/**
 * 本服的天梯redis
 * <AUTHOR>
 * @version v0.1 2018年3月6日 下午2:42:43  zhoufang
 */
public class IntegralGameRedis {

	// 存储本进程所有玩家的天梯数据
	// 存储一份赛季基本信息和上赛季排行榜

//	// 玩家信息(hash): IntegralGame:player:server(serverId)
//	private static final String PLAYER_CACHE_KEY = "IntegralGame:player:server";
//
//	private static final String BASE_CACHE_KEY = "IntegralGame:baseCache:process";

	private static boolean ANSYNC_SAVE_REDIS = true;
	private static ConcurrentHashMap<Long, IntegralGamePlayer> ANSYNC_REDIS_MAP = new ConcurrentHashMap<>();
	private static List<IntegralGamePlayer> tmpList = new LinkedList<>();

	/**
	 * 20190716更新后的旧key
	 * @return
	 */
	private static String getBaseCacheOldkey() {
		StringBuilder sb = new StringBuilder();
		sb.append(RedisKeyConstantsPub.INTEGRAL_GAME_BASE_CACHE_OLD);
		sb.append(CoreBoot.sceneProcessId);
		return sb.toString();
	}

	private static String getBaseCacheGroupkey(int groupId) {
		StringBuilder sb = new StringBuilder();
		sb.append(RedisKeyConstantsPub.INTEGRAL_GAME_BASE_CACHE_GROUP);
		sb.append(groupId);
		return sb.toString();
	}

	private static String getPlayerkey(int serverId) {
		StringBuilder sb = new StringBuilder();
		sb.append(RedisKeyConstantsPub.INTEGRAL_GAME_PLAYER_CACHE);
		sb.append(serverId);
		return sb.toString();
	}
	
	private static String getTempPlayerkey(int serverId) {
		StringBuilder sb = new StringBuilder();
		sb.append(RedisKeyConstantsPub.INTEGRAL_GAME_TEMP_PLAYER_CACHE);
		sb.append(serverId);
		return sb.toString();
	}
	

	public static void load() {
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		int zoneId = CoreBoot.getAllZoneId().get(0);
		RedisDao redis = RedisManager.getRedisDao(zoneId);
		// loadBaseCache
		try {
			Set<Integer> groupIds = gameManager.getGroupServerIds().keySet();
			for (Integer groupId : groupIds) {
				// 先取新key
				String baseCachekey = getBaseCacheGroupkey(groupId);
				IntegralBaseCache baseCache = redis.valueGet(baseCachekey, IntegralBaseCache.class);
				if (baseCache == null) {
					// 取旧key
					String oldKey = getBaseCacheOldkey();
					baseCache = redis.valueGet(oldKey, IntegralBaseCache.class);
					if (baseCache != null) {
						// 物理合服之前没有创建新key转移数据
						//TODO 正式服上线新代码以前,如果工具统一修改所有redis的key, 就注掉下面的2行代码
						AMLog.LOG_ERROR.error("缺少天梯分组[{}]的redis缓存信息,并且存在旧数据[oldkey={}],直接将旧数据转移至新key,忽略物理合服前的未操作可能性.", groupId, oldKey);
						redis.valueSet(baseCachekey, baseCache);
					}
				}
				if (baseCache != null) {
					gameManager.changeGroupBaseCache(groupId, baseCache);
				}
			}
//			IntegralBaseCache baseCache = redis.valueGet(getBaseCachekey(), IntegralBaseCache.class);
//			if (baseCache != null) {
//				gameManager.setBaseCache(baseCache);
//			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("本服加载天梯缓存信息失败", e);
			AMLog.stdout("本服加载天梯缓存信息失败" + e);
			e.printStackTrace();
			System.exit(1);
		}

		// loadPlayers
		for (Integer serverId : CoreBoot.getAllServerId()) {
			String playerkey = getPlayerkey(serverId);
			try {
				List<IntegralGamePlayer> list = redis.hashValues(playerkey, IntegralGamePlayer.class);
				for (IntegralGamePlayer player : list) {
					IntegralGroup group = gameManager.getGroupByServerId(player.getServerId());
					if(group == null){
						AMLog.LOG_ERROR.error("玩家ID={},serverId={}不属于本机的天梯分组",player.getPlayerId(),player.getServerId());
						continue;
					}
					group.addPlayer(player);
				}
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("本服加载天梯玩家信息失败", e);
				AMLog.stdout("本服加载天梯玩家信息失败" + e);
				e.printStackTrace();
				System.exit(1);
			}
		}
	}

	public static void saveBaseCache(int groupId) {
		IntegralGroup group = IntegralGameManager.getInstance().getGroupById(groupId);
		RedisDao redis = RedisManager.getRedisDao(CoreBoot.getAllZoneId().get(0));
		// loadBaseCache
		try {
			String baseCachekey = getBaseCacheGroupkey(groupId);
			redis.valueSet(baseCachekey, group.getBaseCache());
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("本服保存天梯缓存信息失败", e);
		}
	}

	public static void savePlayer(IntegralGamePlayer player) {
		if (ANSYNC_SAVE_REDIS) {
			ANSYNC_REDIS_MAP.put(player.getPlayerId(), player);
			return;
		}
		int zoneId = CoreBoot.getZoneId(player.getServerId());
		RedisDao redis = RedisManager.getRedisDao(zoneId);
		String playerKey = getPlayerkey(player.getServerId());
		String hashKey = Long.toString(player.getPlayerId());
		try {
			redis.hashSet(playerKey, hashKey, player);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("SaveIntegralPlayer Error,player={}" + JsonUtils.O2S(player));
		}
	}

	/**
	 * 异步保存到Redis
	 */
	public static void ansyncOperRedis() {
		if (!ANSYNC_SAVE_REDIS) {
			return;
		}
		if (ANSYNC_REDIS_MAP == null || ANSYNC_REDIS_MAP.isEmpty()) {
			return;
		}
		tmpList.addAll(ANSYNC_REDIS_MAP.values());
		ANSYNC_REDIS_MAP.clear();
		Map<String, Map<String, String>> map = new HashMap<>();
		for (IntegralGamePlayer player : tmpList) {
			String playerKey = getPlayerkey(player.getServerId());
			String hashKey = Long.toString(player.getPlayerId());

			Map<String, String> hashMap = map.get(playerKey);
			if (hashMap == null) {
				hashMap = new HashMap<>();
				map.put(playerKey, hashMap);
			}
			hashMap.put(hashKey, JsonUtils.O2S(player));
		}
		tmpList.clear();

		if (!map.isEmpty()) {
			RedisDao redis = RedisManager.getRedisDao();
			for (Map.Entry<String, Map<String, String>> me : map.entrySet()) {
				try {
					redis.hashSet(me.getKey(), me.getValue());
				} catch (Exception e) {
					AMLog.LOG_ERROR.error("save IntegralGamePlayer ERROR with PLAYERINFOS = " + me.getValue(), e);
				}
			}
		}
	}

	/**
	 * 删除某个分组下的所有玩家天梯数据
	 * @param groupId
	 */
	public static void deleteAllPlayer(int groupId) {
		// 此分组下的所有服务器
		List<Integer> listByGroupId = IntegralConfigCenter.getServerIdListByGroupId(groupId);
		// 本进程下的所有服务器
		List<Integer> allServerId = CoreBoot.getAllServerId();
		for (int serverId : listByGroupId) {
			if (!allServerId.contains(serverId)) {
				continue;
			}
			String playerkey = getPlayerkey(serverId);
			int zoneId = CoreBoot.getZoneId(serverId);
			RedisDao redis = RedisManager.getRedisDao(zoneId);
			try {
				redis.del(playerkey);
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("Delete IntegralPlayers ERROR!!! serverId=" + serverId);
			}
		}
	}

	
	public static void clearGroupPlayer(int groupId) {
		// 不再直接删除本赛季数据, 改为临时保存14天.
		Map<Integer, LinkedList<Integer>> groupServerIds = IntegralGameManager.getInstance().getGroupServerIds();
		LinkedList<Integer> serverIdList = groupServerIds.get(groupId);
		for (Integer serverId : serverIdList) {
			String playerkey = getPlayerkey(serverId);
			String tempPlayerkey = getTempPlayerkey(serverId);
			int zoneId = CoreBoot.getZoneId(serverId);
			RedisDao redis = RedisManager.getRedisDao(zoneId);
			try {
				if(redis.exist(tempPlayerkey)){
					// 先删除上次的临时数据
					redis.del(tempPlayerkey);
				}
				if(redis.exist(playerkey)){
					// 把本赛季的数据临时保存两周
					redis.renameKey(playerkey, tempPlayerkey);
					redis.expire(tempPlayerkey, 14, TimeUnit.DAYS);
					AMLog.LOG_COMMON.info("清除分组[{}],服务器[{}]的玩家数据成功, 临时保存一份本赛季数据到新key=[{}]",groupId,serverId, tempPlayerkey);
				}else{
					AMLog.LOG_COMMON.info("清除分组[{}],服务器[{}]的玩家数据成功, 此服没有玩家参加天梯",groupId,serverId);
				}
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("清除分组[{}], 服务器[{}]的玩家数据时异常",groupId,serverId,e);
			}
		}
	}

}
