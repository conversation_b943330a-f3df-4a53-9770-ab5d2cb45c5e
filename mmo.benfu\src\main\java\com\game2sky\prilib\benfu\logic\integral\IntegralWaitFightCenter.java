package com.game2sky.prilib.benfu.logic.integral;

import java.util.concurrent.ConcurrentHashMap;

import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;

/**
 *
 * <AUTHOR>
 * @version v0.1 2017年12月20日 下午7:59:08  zhoufang
 */
public class IntegralWaitFightCenter {

	private static ConcurrentHashMap<Long, Long> fights = new ConcurrentHashMap<Long, Long>();

	public static boolean isTimeout(long fightId) {
		long now = System.currentTimeMillis();
		Long time = fights.get(fightId);
		if (time == null) {
			fights.put(fightId, now + IntegralConst.PLAYER_LOAD_FIGHT_TIME);
			return false;
		}
		if (now >= time) {
			fights.remove(fightId);
			return true;
		}
		return false;
	}
	
	public static void delete(Long fightId){
		fights.remove(fightId);
	}

}
