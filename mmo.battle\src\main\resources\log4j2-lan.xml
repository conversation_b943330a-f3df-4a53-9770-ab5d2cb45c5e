<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="1">

	<Appenders>
		<RollingRandomAccessFile name="stdoutLog"
			fileName="logs/stdout/stdout.log" filePattern="logs/stdout/stdout.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/stdout" maxDepth="1">
					<IfFileName glob="stdout.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="commonLog"
			fileName="logs/common/common.log" filePattern="logs/common/common.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/common" maxDepth="1">
					<IfFileName glob="common.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="errorLog"
			fileName="logs/error/error.log" filePattern="logs/error/error.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/error" maxDepth="1">
					<IfFileName glob="error.log.*">
						<IfLastModified age="504H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="httpLog"
			fileName="logs/http/http.log" filePattern="logs/http/http.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/http" maxDepth="1">
					<IfFileName glob="http.log.*">
						<IfLastModified age="504H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="interfaceLog"
			fileName="logs/interface/interface.log" filePattern="logs/interface/interface.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/interface" maxDepth="1">
					<IfFileName glob="interface.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="interfaceSlowLog"
			fileName="logs/interfaceSlow/interfaceSlow.log" filePattern="logs/interfaceSlow/interfaceSlow.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/interfaceSlow" maxDepth="1">
					<IfFileName glob="interfaceSlow.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>
		
		<RollingRandomAccessFile name="fatalLog"
			fileName="logs/fatal/fatal.log" filePattern="logs/fatal/fatal.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/fatal" maxDepth="1">
					<IfFileName glob="fatal.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>
		
		<RollingRandomAccessFile name="biLog"
			fileName="logs/bi/bi.log" filePattern="logs/bi/bi.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout charset="UTF-8"
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/bi" maxDepth="1">
					<IfFileName glob="bi.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="monitorLog"
			fileName="logs/monitor/monitor.log" filePattern="logs/monitor/monitor.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/monitor" maxDepth="1">
					<IfFileName glob="monitor.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="unionLog"
			fileName="logs/union/union.log" filePattern="logs/union/union.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/union" maxDepth="1">
					<IfFileName glob="union.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>
		
	</Appenders>

	<Loggers>
		<Root level="info" additivity="false" includeLocation="true">
			<AppenderRef ref="stdoutLog" />
		</Root>
		<Logger name="stdoutLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="stdoutLog" />
		</Logger>
		<Logger name="commonLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="commonLog" />
		</Logger>
		<Logger name="errorLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="errorLog" />
		</Logger>
		<Logger name="httpLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="httpLog" />
		</Logger>
		<Logger name="interfaceLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="interfaceLog" />
		</Logger>
		<Logger name="monitorLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="monitorLog" />
		</Logger>
		<Logger name="interfaceSlowLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="interfaceSlowLog" />
		</Logger>
		<Logger name="fatalLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="fatalLog" />
		</Logger>
		<Logger name="biLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="biLog" />
		</Logger>
		<Logger name="unionLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="unionLog" />
		</Logger>
	</Loggers>

</Configuration>