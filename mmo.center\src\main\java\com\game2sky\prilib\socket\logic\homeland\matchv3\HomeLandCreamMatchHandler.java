package com.game2sky.prilib.socket.logic.homeland.matchv3;

import gnu.trove.map.hash.TLongLongHashMap;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.game2sky.prilib.communication.game.homeland.inner.SSRequestCanRobList;
import com.game2sky.prilib.communication.game.homeland.inner.SSResponseCanRobList;
import com.game2sky.publib.Globals;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 下午2:11:09  daixl
 */
public class HomeLandCreamMatchHandler {

	private static TLongLongHashMap matchPlayers = new TLongLongHashMap();

	public static boolean checkCanMatch(long playerId, long now) {
		long value = matchPlayers.get(playerId);
		if (value > now) {
			return false;
		}
		return true;
	}

	public static Map<Long, Integer> match(SSRequestCanRobList msg, SSResponseCanRobList robList) {
		// 将玩家加入正在匹配列表，防止玩家多次匹配，会在匹配结束以后，删除这个记录
		long now = Globals.getTimeService().now();
		long playerId = msg.getPlayerId();
		matchPlayers.put(playerId, now + 10 * 1000);

		Map<Long, Integer> result = new HashMap<Long, Integer>();
		Set<Long> excludeIds = new HashSet<Long>();
		List<Long> enemyIds = msg.getEnemyIds();
		if (enemyIds != null) {
			excludeIds.addAll(enemyIds);
		}
		List<Long> friendIds = msg.getFriendIds();
		if (friendIds != null) {
			excludeIds.addAll(friendIds);
		}
		excludeIds.add(playerId);

		List<Integer> creams = msg.getCreams();

		randomPlayerIds(creams, excludeIds, result, true);

		if (result.size() < creams.size()) {
			robList.setNeedClean(true);
			if(enemyIds != null){
				excludeIds.removeAll(enemyIds);
			}
			randomPlayerIds(creams, excludeIds, result, false);
		}

		matchPlayers.remove(playerId);
		return result;
	}

	private static void randomPlayerIds(List<Integer> creams, Set<Long> excludeIds, Map<Long, Integer> result, boolean needCheckLastMatchTime) {
		HomeLandCreamMatchSort sort = CenterHomelandGlobalManager.getSort();
		int maxSize = sort.getMaxSize();
		int index = -1;
		for (int cream : creams) {
			if (result.size() >= creams.size()) {
				break;
			}
			int limit = 100;
			index = sort.getBarrelIndex(cream);
			int tmp = index;
			boolean gotten = false;
			while (tmp < maxSize) {
				if (limit <= 0) {
					break;
				}
				HomeLandCreamMatchBarrel barrel = sort.randomBarrel(tmp);
				Long randomPlayerId = barrel.randomPlayerId(excludeIds, needCheckLastMatchTime);
				if (randomPlayerId != null) {
					excludeIds.add(randomPlayerId);
					result.put(randomPlayerId, cream);
					gotten = true;
					break;
				}
				++tmp;
				--limit;
			}
			if (!gotten) {
				--index;
				limit = 100;
				while (index >= 0) {
					if (limit <= 0) {
						break;
					}
					HomeLandCreamMatchBarrel barrel = sort.randomBarrel(index);
					Long randomPlayerId = barrel.randomPlayerId(excludeIds, needCheckLastMatchTime);
					if (randomPlayerId != null) {
						excludeIds.add(randomPlayerId);
						result.put(randomPlayerId, cream);
						gotten = true;
						break;
					}
					--index;
					--limit;
				}
			}
		}
	}
}
