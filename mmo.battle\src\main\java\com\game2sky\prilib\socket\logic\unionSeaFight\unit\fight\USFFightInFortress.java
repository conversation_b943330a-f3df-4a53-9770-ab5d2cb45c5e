package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.core.socket.logic.battle.fdefine.EnumDefine;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * TODO 据点内的战斗
 *
 * <AUTHOR>
 * @version v0.1 2018年6月17日 下午6:31:31  guojijun
 */
public class USFFightInFortress extends AbstractUSFFight {

	private final USFFortress fortress;

	public USFFightInFortress(USFFortress fortress, IUSFUnitFightable leftFightable, IUSFUnitFightable rightFightable) {
		super(leftFightable, rightFightable);
		this.fortress = fortress;
	}

	@Override
	protected void onStart() {
		if(leftFightable instanceof AbstractUSFUnit && rightFightable instanceof AbstractUSFUnit)
		{
			AbstractUSFUnit leftUnit = (AbstractUSFUnit)leftFightable;
			AbstractUSFUnit rightUnit = (AbstractUSFUnit)rightFightable;
			leftUnit.triggerEvent(USFBuffTriggerType.FIGHT_TOWER, leftUnit, rightUnit,fortress);
			rightUnit.triggerEvent(USFBuffTriggerType.DEFENDER_TOWER, rightUnit, leftUnit,fortress);
			leftUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_TOWER_BEGIN, leftUnit, rightUnit,fortress);
			rightUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_TOWER_BEGIN, rightUnit, leftUnit,fortress);
		}
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("海战据点内战斗开始,fortress={},left={},right={}", this.fortress, this.leftFightable,
				this.rightFightable);
		}
	}

	@Override
	protected void settleFightEnd(int winCamp, IUSFUnitFightable winner,IUSFUnitFightable loser)
	{
		//这里的调用顺序不能变
		if (winCamp == EnumDefine.FightCampEnum_Left) {
			this.fortress.removeUnitAndNotify(this.rightFightable);
			if (!fortress.hasDefender()) {
				this.fortress.changeCamp(this.leftFightable);
			}
		} else {
			this.fortress.nextAttacker();
		}
		winner.onFightWin(this);
		loser.onFightFailed(this);
		if(leftFightable instanceof AbstractUSFUnit && rightFightable instanceof AbstractUSFUnit)
		{
			AbstractUSFUnit leftUnit = (AbstractUSFUnit)leftFightable;
			AbstractUSFUnit rightUnit = (AbstractUSFUnit)rightFightable;
			leftUnit.triggerEvent(USFBuffTriggerType.FIGHT_TOWER_OVER, leftUnit,rightUnit, fortress);
			rightUnit.triggerEvent(USFBuffTriggerType.DEFENDER_TOWER_OVER, rightUnit,leftUnit, fortress);
		}
		this.fortress.continueFight();
	}

	@Override
	public USFFightTypeEnum geFightType() {
		return USFFightTypeEnum.FIGHT_IN_FORTRESS;
	}

	@Override
	public USFFortress getFortress() {
		return this.fortress;
	}

	@Override
	public int getTargetId(IUSFUnitFightable fightable) {
		return this.fortress.getId();
	}

}
