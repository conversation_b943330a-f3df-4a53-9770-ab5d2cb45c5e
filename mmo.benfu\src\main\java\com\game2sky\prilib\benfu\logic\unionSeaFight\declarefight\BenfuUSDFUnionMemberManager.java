package com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.unionSeaFight.BenfuUnionSeaFightService;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.BrifeUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncChangeUnionInfoToCenter;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightServerGroup;
import com.game2sky.prilib.core.socket.logic.union.Union;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.IUnionSeaFightManager;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightActivityData;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.union.AbstractUnion;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年11月28日 上午11:29:06  daixl
 */
public class BenfuUSDFUnionMemberManager {

	/** <groupId : <unionId : BrifeUnionInfo>> */
	public static Map<Integer, Map<Long, BrifeUnionInfo>> map = new HashMap<Integer, Map<Long, BrifeUnionInfo>>();

	public static List<BrifeUnionInfo> getAllMatchCondition(long unionId, int zoneId, int serverId) {

		Integer groupId = DictUnionSeaFightServerGroup.getGroupId(serverId);
		if (groupId == null) {
			return null;
		}
		Map<Long, BrifeUnionInfo> mapTmp = map.get(groupId);
		if (mapTmp == null) {
			AMLog.LOG_ERROR.error("Benfu group:{}, union is null!", groupId);
			return null;
		}

		List<BrifeUnionInfo> result = new ArrayList<BrifeUnionInfo>();
		Union union = Globals.getUnion(zoneId, unionId, Union.class);
		if (union == null) {
			return null;
		}
		int unionLevel = union.getUnionBaseManager().getUnionModel().getUnionLevel();
		IUnionSeaFightManager unionSeaFightManager = union.getUnionSeaFightManager();
		int unionIntegral = unionSeaFightManager.getIntegral();

		List<BrifeUnionInfo> list = new ArrayList<BrifeUnionInfo>(mapTmp.values());
		for (BrifeUnionInfo info : list) {
			if (unionId == info.getUid()) {
				continue;
			}
			int level = info.getLv();
			int integral = info.getScore();
			if (level <= (unionLevel + 3) && level >= (unionLevel - 3) && integral <= (unionIntegral + 200)
				&& integral >= (unionIntegral - 200)) {
				result.add(info);
			}
		}

		return result;
	}

	public static void refreshAll(int groupId, List<BrifeUnionInfo> list) {
		Map<Long, BrifeUnionInfo> mapTmp = new HashMap<>();

		if (list != null && list.size() > 0) {
			for (BrifeUnionInfo info : list) {
				//暂时这么处理
				info.setSid(CoreBoot.getServerDisplayId(info.getSid()));
				mapTmp.put(info.getUid(), info);
			}
		}
		BenfuUSDFUnionMemberManager.map.put(groupId, mapTmp);
	}

	public static void benfuUSDFUnionSeaFightSchedule(int groupId, int zoneId) {
		BenfuUnionSeaFightService bean = CoreBoot.run.getBean(BenfuUnionSeaFightService.class);
		UnionSeaFightActivityData activityData = bean.getUnionSeaFightActivityDataByZoneId(zoneId);
		if(activityData == null) {
			AMLog.LOG_ERROR.error("activityData==null, groupId={}, zoneId={}", groupId, zoneId);
			return;
		}
		List<AbstractUnion> allUnion = Globals.getAllUnion(zoneId);

		List<BrifeUnionInfo> matchConditionUnions = new ArrayList<BrifeUnionInfo>();
		List<BrifeUnionInfo> notMatchConditionUnions = new ArrayList<BrifeUnionInfo>();
		for (AbstractUnion abstractUnion : allUnion) {
			Union union = (Union) abstractUnion;
			if (union.isDeleteState()) {
				notMatchConditionUnions.add(union.convert2BrifeUnionInfo());
				continue;
			}
			int matchUnionApplyCanNotChangeCondition = bean.matchUnionApplyCanNotChangeCondition(union, activityData,
				false);
			if (matchUnionApplyCanNotChangeCondition != ErrorCodeConstants.OPERATION_SUCCESS) {
				continue;
			}
			matchConditionUnions.add(union.convert2BrifeUnionInfo());
		}

		SSSyncChangeUnionInfoToCenter msg = new SSSyncChangeUnionInfoToCenter(groupId, matchConditionUnions,
			notMatchConditionUnions);
		CoreBoot.benfuToKuafu(msg, 0, zoneId);
	}

	public static BrifeUnionInfo getByGroupIdAndUnionId(int groupId, long unionId) {

		Map<Long, BrifeUnionInfo> tmp = map.get(groupId);
		if (tmp == null) {
			return null;
		}

		return tmp.get(unionId);

	}

	public static void deleteUnion(int groupId, long unionId) {
		Map<Long, BrifeUnionInfo> tmp = map.get(groupId);
		if (tmp != null) {
			tmp.remove(unionId);
		}
	}
}
