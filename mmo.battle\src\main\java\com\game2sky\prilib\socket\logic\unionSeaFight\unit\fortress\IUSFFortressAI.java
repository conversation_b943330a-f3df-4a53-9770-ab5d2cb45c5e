package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress;

import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitAI;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;

/**
 * TODO 海战据点AI
 *
 * <AUTHOR>
 * @version v0.1 2018年10月17日 下午2:49:23  guojijun
 */
public interface IUSFFortressAI extends IUSFUnitAI {

	/**
	 * 当添加单位
	 * 
	 * @param unit
	 */
	void onAddUnit(IUSFUnitFightable unit);

	/**
	 * 当移除单位
	 * 
	 * @param unit
	 */
	void onRemoveUnit(IUSFUnitFightable unit);
}
