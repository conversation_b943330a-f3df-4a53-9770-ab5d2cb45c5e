package com.game2sky.prilib.benfu.logic.integral;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer.IntegralAreaInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.core.socket.logic.awardSend.AwardSendType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig;
import com.game2sky.prilib.core.socket.logic.title.TitleService;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.awardSend.IAwardSendService;

/**
 * 天梯新版本发奖任务
 * <AUTHOR>
 * @version v0.1 2020年4月22日 下午2:56:08  zhoufang
 */
public class IntegralNewVersionRewardTask implements IntegralSendRewardTask {

	private IntegralGroup group;

	public IntegralNewVersionRewardTask(IntegralGroup group) {
		this.group = group;
	}

	@Override
	public void sendWeekEndReward() {
		// Publish awards on weekends, practice competitions and peak competitions to publish prizes
		List<IntegralGamePlayer> playerList = new ArrayList<IntegralGamePlayer>(group.getPlayers().values());
		for (IntegralGamePlayer player : playerList) {
			sendIntegralReward(AwardSendType.IntegralPracticeWeekReward, player, IntegralAreaType.PRACTICE);
			sendIntegralReward(AwardSendType.IntegralPinnacleWeekReward, player, IntegralAreaType.PINNACLE);
		}
	}

	private void sendIntegralReward(AwardSendType sendType, IntegralGamePlayer player, IntegralAreaType areaType) {
		IntegralAreaInfo areaInfo = player.getAreaInfos().get(areaType.value());
		// 此赛区的积分和参赛次数
		int integral = areaInfo == null ? 0 : areaInfo.getIntegral();
		int weekCount = areaInfo == null ? 0 : areaInfo.getWeekFightCount();
		// 在此赛区可获取的奖励配置
		IntegralRewardConfig config = player.getIntegralRewardConfig(areaType.value());
		if (config == null) {
			AMLog.LOG_COMMON.info("天梯新玩法每周积分奖励----->玩家[{}]无法发放[{}]的奖励, 此赛区本周积分[{}],战斗场次[{}]", player.getPlayerId(),
				areaType.getExplain(), integral, weekCount);
			return;
		}
		IAwardSendService awardSendService = CoreBoot.getBean(IAwardSendService.class);
		TitleService titleService = CoreBoot.getBean(TitleService.class);
		int zoneId = Globals.getZoneId(player.getServerId());
		awardSendService.addAward(sendType, zoneId, player.getPlayerId(), config.items, new Object[] {},
			new Object[] { integral });
		if (config.title != null) {
			titleService.addTitle(zoneId, player.getPlayerId(), Integer.parseInt(config.title));
		}
		AMLog.LOG_COMMON.info("天梯新玩法每周积分奖励----->玩家[{}]在[{}]的奖励发送成功,本周积分{},战斗场次{}", player.getPlayerId(),
			areaType.getExplain(), integral, weekCount);
	}

	@Override
	public void sendSeasonEndReward() {
		// 赛季结束发奖,练习赛和巅峰赛各自发奖
		Map<Integer, IntegralCurrentRank> rankCache = group.getRankCache();

		// 练习赛排名数据和奖励配置
		List<IntegralRankItem> practiceRankList = new ArrayList<IntegralRankItem>();
		if (rankCache.containsKey(IntegralAreaType.PRACTICE.value())) {
			practiceRankList.addAll(rankCache.get(IntegralAreaType.PRACTICE.value()).getRankList());
		}
		List<IntegralRewardConfig> practiceRewardConfigs = IntegralConfigCenter.getIntegralRankRewards().get(
			IntegralAreaType.PRACTICE.value());
		if (practiceRewardConfigs == null) {
			AMLog.LOG_ERROR.error("天梯赛季结束,练习赛区缺少赛季奖励,当前排行榜人数:{}", practiceRankList.size());
		} else {
			AMLog.LOG_COMMON.info("天梯赛季结束,开始发送练习赛赛季奖励,当前排行榜人数:{}", practiceRankList.size());
			sendRankReward(AwardSendType.IntegralPracticeSeasonReward, practiceRankList, practiceRewardConfigs);
		}

		// 巅峰赛排名数据和奖励配置
		List<IntegralRankItem> pinnacleRankList = new ArrayList<IntegralRankItem>();
		if (rankCache.containsKey(IntegralAreaType.PINNACLE.value())) {
			pinnacleRankList.addAll(rankCache.get(IntegralAreaType.PINNACLE.value()).getRankList());
		}
		List<IntegralRewardConfig> pinnacleRewardConfigs = IntegralConfigCenter.getIntegralRankRewards().get(
			IntegralAreaType.PINNACLE.value());
		if (pinnacleRewardConfigs == null) {
			AMLog.LOG_ERROR.error("天梯赛季结束,巅峰赛区缺少赛季奖励,当前排行榜人数:{}", pinnacleRankList.size());
		} else {
			AMLog.LOG_COMMON.info("天梯赛季结束,开始发送巅峰赛奖励,当前排行榜人数:{}", pinnacleRankList.size());
			sendRankReward(AwardSendType.IntegralPinnacleSeasonReward, pinnacleRankList, pinnacleRewardConfigs);
		}
	}

	private void sendRankReward(AwardSendType sendType, List<IntegralRankItem> rankList,
								List<IntegralRewardConfig> rewardConfigs) {
		IAwardSendService awardSendService = CoreBoot.getBean(IAwardSendService.class);
		TitleService titleService = CoreBoot.getBean(TitleService.class);
		for (IntegralRewardConfig config : rewardConfigs) {
			for (int i = config.before; i <= config.after; i++) {
				if (rankList.size() < i) {
					break;
				}
				IntegralRankItem rankItem = rankList.get(i - 1);
				if (!CoreBoot.getAllServerId().contains(rankItem.getServerId())) {
					continue;
				}
				// 发送奖励
				int zoneId = Globals.getZoneId(rankItem.getServerId());
				awardSendService.addAward(sendType, zoneId, rankItem.getPlayerId(), config.items, new Object[] {},
					new Object[] { i });
				if (config.title != null) {
					titleService.addTitle(zoneId, rankItem.getPlayerId(), Integer.parseInt(config.title));
				}
				AMLog.LOG_COMMON.info("发送天梯新版玩法排名奖励,sendType={}, playerId={},rank={}, 积分={}", sendType.getIndex(),
					rankItem.getPlayerId(), i, rankItem.getIntegral());
			}
		}
	}

}
