package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * TODO 航线上的战斗
 *
 * <AUTHOR>
 * @version v0.1 2018年6月17日 下午1:02:15  guojijun
 */
public class USFFightOnRoute extends AbstractUSFFight {

	private final USFRoute route;

	public USFFightOnRoute(USFRoute route, IUSFUnitFightable leftFightable, IUSFUnitFightable rightFightable) {
		super(leftFightable, rightFightable);
		this.route = route;
	}

	@Override
	protected void onStart() {
		if(leftFightable instanceof AbstractUSFUnit && rightFightable instanceof AbstractUSFUnit)
		{
			AbstractUSFUnit leftUnit = (AbstractUSFUnit)leftFightable;
			AbstractUSFUnit rightUnit = (AbstractUSFUnit)rightFightable;
			leftUnit.triggerEvent(USFBuffTriggerType.TOUTE_FIGHT_BEGIN, leftUnit, rightUnit,route);
			rightUnit.triggerEvent(USFBuffTriggerType.TOUTE_FIGHT_BEGIN, rightUnit, leftUnit,route);
		}
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("海战航线上战斗开始,route={},left={},right={}", this.route, this.leftFightable,
				this.rightFightable);
		}
	}

//	@Override
//	protected void settleCurrentFight(int winCamp) {
//		IUSFUnitFightable winner;
//		IUSFUnitFightable loser;
//		if (winCamp == EnumDefine.FightCampEnum_Left) {
//			winner = this.leftFightable;
//			loser = this.rightFightable;
//		} else {
//			winner = this.rightFightable;
//			loser = this.leftFightable;
//		}
//		winner.onFightWin(this);
//		loser.onFightFailed(this);
//	}

	@Override
	protected void settleFightEnd(int winCamp,IUSFUnitFightable winner,IUSFUnitFightable loser)
	{
		winner.onFightWin(this);
		loser.onFightFailed(this);
	}
	
	@Override
	public USFFightTypeEnum geFightType() {
		return USFFightTypeEnum.FIGHT_ON_ROUTE;
	}

	@Override
	public USFFortress getFortress() {
		return null;
	}

	@Override
	public int getTargetId(IUSFUnitFightable fightable) {
		int uid = (fightable == this.leftFightable ? this.rightFightable.getId() : this.leftFightable.getId());
		return uid;
	}

}
