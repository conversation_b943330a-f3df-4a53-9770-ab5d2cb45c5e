package com.game2sky.prilib.socket.logic.integral.match;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.publib.util.DateUtil;

/**
 *
 * <AUTHOR>
 * @version v0.1 2019年8月24日 下午4:41:07  zhoufang
 */
public class MatcherModel {
	
	private long playerId;
	
	/** 战斗力 */
	private long fightPower;
	
	/** 今天已经匹配的机器人Id */
	private Map<Integer,Set<Long>> matchedRobotIds = new ConcurrentHashMap<Integer,Set<Long>>();
	
	/** 下次清空机器人时间 */
	private long clearRobotTime;
	
	
	public MatcherModel(long playerId){
		this.playerId = playerId;
		this.clearRobotTime = DateUtil.getTomorrowTime(0, 0);
		Set<Integer> keySet = IntegralConfigCenter.getAreaMap().keySet();
		for(Integer areaType : keySet){
			matchedRobotIds.put(areaType, new HashSet<Long>());
		}
	}
	
	public long getPlayerId() {
		return playerId;
	}

	public long getFightPower() {
		return fightPower;
	}

	public void setFightPower(long fightPower) {
		this.fightPower = fightPower;
	}

	public Map<Integer, Set<Long>> getMatchedRobotIds() {
		return matchedRobotIds;
	}

	public void setMatchedRobotIds(Map<Integer, Set<Long>> matchedRobotIds) {
		this.matchedRobotIds = matchedRobotIds;
	}

	public long getClearRobotTime() {
		return clearRobotTime;
	}

	public void setClearRobotTime(long clearRobotTime) {
		this.clearRobotTime = clearRobotTime;
	}
	
	
}
