package com.game2sky.prilib.socket.logic.autochess.data;


/**
 * 自走棋玩家匹配信息.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月8日 下午4:28:50  Jet Lee
 */
public class AutoChessMatchPlayer {

	private int serverId;

	private long playerId;

	/** 玩家自走棋积分 */
	private int score;
	
	/** 玩家自走棋段位 */
	private int grade;

	private long beginMatchTime;

	public AutoChessMatchPlayer(long playerId, int serverId, int grade, int score) {
		this.playerId = playerId;
		this.serverId = serverId;
		this.grade = grade;
		this.score = score;
//		this.beginMatchTime = Globals.getTimeService().now(); TODO
		this.beginMatchTime = System.currentTimeMillis();
	}
	
	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public long getPlayerId() {
		return playerId;
	}

	public void setPlayerId(long playerId) {
		this.playerId = playerId;
	}

	public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}
	
	public int getGrade() {
		return grade;
	}
	
	public void setGrade(int grade) {
		this.grade = grade;
	}

	public long getBeginMatchTime() {
		return beginMatchTime;
	}

	public void setBeginMatchTime(long beginMatchTime) {
		this.beginMatchTime = beginMatchTime;
	}

}
