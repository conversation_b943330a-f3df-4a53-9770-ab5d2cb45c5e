package com.game2sky.prilib.socket.logic.homeland.matchv2;


/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 上午11:24:58  daixl
 */
@Deprecated
public class HomeLandCreamMatchSort {
// extends BarrelSort<HomeLandCreamMatchBarrel>
//	@Override
//	public HomeLandCreamMatchBarrel createInstance(Long id) {
//		return new HomeLandCreamMatchBarrel(id);
//	}
//
//	public void addElement(long playerId, int cream) {
//		DictHomeCreamGroup group = DictHomeCreamGroup.getGroup(cream);
//		if (group == null) {
//			return;
//		}
//		HomeLandCreamMatchBarrel addElement = addElement((long) group.getGroupId());
//		addElement.addPlayerId(playerId);
//	}
//
//	public void deleteElement(long playerId, int cream) {
//		DictHomeCreamGroup group = DictHomeCreamGroup.getGroup(cream);
//		if (group == null) {
//			return;
//		}
//		HomeLandCreamMatchBarrel barrel = getBarrel((long) group.getGroupId());
//		if (barrel == null) {
//			return;
//		}
//		barrel.removePlayerId(playerId);
//	}
//
//	public HomeLandCreamMatchBarrel getBarrel(int groupId) {
//		HomeLandCreamMatchBarrel barrel = getBarrel((long) groupId);
//		return barrel;
//	}
}
