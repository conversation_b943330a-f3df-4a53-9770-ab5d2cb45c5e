package com.game2sky.prilib.socket.logic.autochess;

import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.communication.MessageType;
import com.game2sky.publib.framework.thread.ExecutableMessageHandler;
import com.game2sky.publib.framework.thread.QueueMessageProcessor;
import com.game2sky.publib.framework.thread.message.IMessageProcessor;

/**
 * 自走棋本服消息处理器.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月8日 下午10:54:25  Jet Lee
 */
class AutoChessCenterMessageProcessor implements IMessageProcessor<Message> {

	/**主线程*/
	private QueueMessageProcessor mainProcessor;

	AutoChessCenterMessageProcessor() {
		this.mainProcessor = new QueueMessageProcessor(new ExecutableMessageHandler(), "AutoChessCenter");
		this.mainProcessor.start();
		Globals.getMsgProcessDispatcher().setAutoChessMsgProcessor(this);
	}

	@Override
	public void start() {
	}

	@Override
	public void stop() {
		this.mainProcessor.stop();
	}

	@Override
	public void put(Message msg) {
		if (msg.messageType() == MessageType.AUTO_CHESS) {
			this.mainProcessor.put(msg);
		} else {
			if (AMLog.LOG_ERROR.isErrorEnabled()) {
				AMLog.LOG_ERROR.error("AutoChessCenterMessageProcessor收到不能处理的消息,msg={}", msg);
			}
		}
	}

	@Override
	public boolean isFull() {
		return this.mainProcessor.isFull();
	}

	@Override
	public int messageQueueSize() {
		int queueSize = this.mainProcessor.messageQueueSize();
		return queueSize;
	}

	@Override
	public void printMessageQueue() {
		this.mainProcessor.printMessageQueue();
	}

}
