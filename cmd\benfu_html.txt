#热更, 可选参数[?subPath=]
curl http://127.0.0.1:22302/manager/reloadClass

#刷新本服字典表
curl http://127.0.0.1:22302/manager/refreshDictData

#刷新本服inst_server_config表，可选参数[?serverId=$serverId]
curl http://127.0.0.1:22302/manager/refreshConfigCenter

#字典表数据检测
curl http://127.0.0.1:22302/bi/dataCheck

#强踢玩家下线  (Human不会进入离线缓存)
curl -d 'serverId=9997&playerId=199970033602' http://127.0.0.1:22302/bi/kickOffPlayer

#强踢所有玩家下线  (Human不会进入离线缓存)
curl -d 'yckj_op_123456=1' http://127.0.0.1:22302/bi/kickOffPlayer

#Maintain kicking people, do not log in again
curl  http://127.0.0.1:22302/manager/forceOffline

#After maintaining the kick, let go and log in again
curl http://127.0.0.1:22302/manager/allowClientEnter

#封号10秒
curl -d 'serverId=9997&playerId=199970033602&blockDesc=test&fenghaoReason=test&fenghaoTime=1' http://127.0.0.1:22302/bi/updatePlayerFenghao


#查看内存
curl -d "playerId=110020027292,110020027291" http://127.0.0.1:22302/manager/showHumanMemory

#清理离线缓存的数据，清理某个玩家的数据 (yckj_op_123456)
curl -d "playerId=199970033602" http://127.0.0.1:22302/manager/clearOfflineHuman


#重新加载排队配置
curl http://127.0.0.1:22302/manager/reloadQueueConfig


#查看 玩家数量， 包括 在线、离线Human 、各种channelSize 各种排队队列数量
curl http://127.0.0.1:22302/manager/showPlayerSize


#Open the challenge of the strong
curl http://127.0.0.1:22302/bi/openPersonBoss?timeType=1
#Open the reward boss
curl http://127.0.0.1:22302/bi/openBountyHuntBoss?bossId=1


#Gamura Points
curl -d 'playerIds=999970022401&type=2&integral=1500&areaType=4' http://127.0.0.1:22302/bi/modifyIntegral


#When logical combined, send a rename card to a guild of the same name
curl http://127.0.0.1:22302/manager/dealAllSameNameUnion


#When changing the activity time, you must re -refresh it
curl -d "verifyKey=game2Sky_Acitvity_Reload" http://127.0.0.1:22302/activity/reloadActivity


#########################################

Raiders:
Blood war opening: inst_server_config   bloodyFightCheckTime 设置为 0

#在中心服查询战斗服情况
curl http://127.0.0.1:22308/bi/battleServerInfo
#在中心服更改战斗服的状态(status:1上架,2下架)
curl http://localhost:22308/bi/battleServerStatus?battleServerId=?&status=?

#关闭和战斗服的连接
curl http://127.0.0.1:22302/manager/closeServerChannel?battleIp=

#中心服开启争霸赛
curl http://127.0.0.1:22308/bi/topMatchBegin?branchId=1
	说明：branchId参数见dict_integral_server；内网测试缩短比赛时间，请配置config表的ladderWarTestTime=1，并设置ladderWarPlayerMinCount，ladderWarBattleContinueTime，ladderWarBattleIntervalTime

#开关Fatal报警(open=false关闭，alarmType参见AlarmType.name, 不传为屏蔽所有报警类型 )
curl http://127.0.0.1:22302/self/openAlarm?open=true[&alarmType=reconnect]



#设置开启新客服系统的渠道  参数:type=0为新增，type=1为修改，当inst_server_config里存在use_new_vip_service_channel时，需用type=1
curl -d "serverId=9996&key=use_new_vip_service_channel&value=IOS0019&type=0" http://127.0.0.1:22302/bi/updateServerConfig

#中心服调用周重置
curl http://127.0.0.1:22308/bi/autoChessCenterWeekReset
#本服设置自走棋玩家分数
curl http://127.0.0.1:22302/bi/autoChessChangeScore?playerId=1&serverId=1&changeScore=10
#本服自己调用周重置(慎用)
curl http://127.0.0.1:22302/bi/autoChessBenfuWeekReset?season=2&week=3&isWeekEnd=false

#开启关闭消息统计
http://127.0.0.1:22302/manager/openPSMonitor?isPSMonitorOpen=true&isHumanPSMonitorOpen=true
#统计消息
grep '@PSMonitor=' monitor.log|awk -F 'PSMonitor=' '{print $2}'|awk -F '-' '{a[$1]+=$2;b[$1]+=$3}END{for(i in a)print i,a[i],b[i]}'|sort -k 3nr|head -n 20
#统计玩家消息
grep '@HumanPSMonitor' monitor.log|awk -F '@HumanPSMonitor=' '{print $2}'|sort -t - -k  2nr