package com.game2sky.prilib.socket.logic.unionSeaFight.unit.player;

import java.util.*;

import com.game2sky.BattleCoreBoot;
import com.game2sky.prilib.communication.game.unionSeaFight.*;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFMsgSender;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.IUSFRouteNode;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitMoveable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.USFFightController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.USFMoveController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.AbstractUSFFight;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.USFFightOnRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.USFFightTypeEnum;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkillController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateBattling;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateDeath;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.state.USFUnitFsmStateWaiting;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.IPSEnum;
import com.game2sky.publib.communication.common.ErrorMessage;
import com.game2sky.publib.communication.common.SystemMessage;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.communication.game.struct.SendMessageType;
import com.game2sky.publib.communication.game.struct.SystemMessageType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.util.RandomUtil;

import io.netty.channel.Channel;

/**
 * TODO 海战玩家
 *
 * <AUTHOR>
 * @version v0.1 2018年6月7日 下午5:02:52  guojijun
 */
public class USFPlayer extends AbstractUSFUnit
												implements IUSFConsts, IUSFUnitMoveable, IUSFUnitFightable,
												IUSFMsgSender {

	private Channel channel;
	/**基本数据*/
	private final PlayerDisplayInformation displayInfo;
	/**所在据点*/
	private USFFortress curFortress;
	/**是否托管*/
	private boolean isBot;
	/**开始托管的时间*/
	private long botBeginTime;
	/**托管总时间*/
	private long botTotalTime;
	/**是否在游戏中*/
	private boolean isInGame;
	/**进入海战的时间*/
	private long enterGameTime;
	/**在海战里的时间*/
	private long inGameTotalTime;
	/**剩余生命条数*/
	private int remainLifeCount;
	/**能否开启语音*/
	private boolean canOpenMic;
	/**工会角色ID*/
	private int unionRoleId;
	/**当前阵容索引*/
	private int curFormationIndex;
	/**是否手动操作过*/
	private boolean manualed = false;
	/**阵容数据*/
	private final ArrayList<USFPlayerFormation> formations;
	/**移动控制器*/
	private final USFMoveController moveController;
	/**战斗控制器*/
	private final USFFightController fightController;

	private USFClientPlayerInfo clientInfo;
	private USFClientMyPrivateInfo privateClientInfo;
	private USFClientPlayerFightInfo clientPlayerFightInfo;

	private Map<USFUnitProType, Integer> extraProperties;

	public USFPlayer(int id, USFPlayerInfo playerInfo) {
		super(id, USFUnitType.USF_UNIT_PLAYER);
		this.displayInfo = playerInfo.getDisplayInfo();
		this.unionRoleId = playerInfo.getUnionRoleId();
		this.canOpenMic = true;
		this.formations = new ArrayList<>(PLAYER_FORMATION_COUNT);
		this.moveController = new USFMoveController(this);
		this.fightController = new USFFightController(this);
		this.fsmController = new USFPlayerFsmController(this);
		this.fsmController.initState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
		this.fillFormations(playerInfo.getFormations());
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("[id=").append(this.id);
		builder.append(",serverId=").append(this.displayInfo.getServerId());
		builder.append(",playerId=").append(this.displayInfo.getPlayerId());
		builder.append(",nickname=").append(this.displayInfo.getNickName());
		builder.append(",curStateType=").append(this.fsmController.getCurStateType());
		if (this.curFortress != null) {
			builder.append(",curFortressId=").append(this.curFortress.getDictId());
		}
		if (this.moveController.getRoute() != null) {
			builder.append(",curRouteId=").append(this.moveController.getRoute().getDictId());
		}
		builder.append("]");
		return builder.toString();
	}

	/**
	 * 获取可以潜行的次数.如果没有潜行功能，默认-1
	 * @return
	 */
	public int getCouldDiveNumber() {
		if (extraProperties != null) {
			if (extraProperties.containsKey(USFUnitProType.USF_UNIT_PRO_DIVE_NUMBER)) {
				return extraProperties.get(USFUnitProType.USF_UNIT_PRO_DIVE_NUMBER);
			}
		}
		return -1;
	}

	/**
	 * 增加可以使用的潜行次数
	 * @param number
	 */
	public void addDiveNumber(int number) {
		if (extraProperties == null) {
			extraProperties = new HashMap<>();
		}
		Integer diveNumber = extraProperties.get(USFUnitProType.USF_UNIT_PRO_DIVE_NUMBER);
		if (diveNumber != null) {
			number += diveNumber;
		}
		extraProperties.put(USFUnitProType.USF_UNIT_PRO_DIVE_NUMBER, number);
		// 推送剩余潜行次数
		SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
		USFClientUnitIntProChange intProChange = new USFClientUnitIntProChange(id,
			USFUnitProType.USF_UNIT_PRO_DIVE_NUMBER, number);
		List<USFClientUnitIntProChange> intProChanges = Arrays.asList(intProChange);
		scUSFUnitProChange.setIntProChanges(intProChanges);
		pushMsg(scUSFUnitProChange);
	}

	/**
	 * 获取潜行状态
	 * @return
	 */
	@Override
	public int getDiveState() {
		if (extraProperties != null) {
			if (extraProperties.containsKey(USFUnitProType.USF_UNIT_PRO_DIVE_STATE)) {
				return extraProperties.get(USFUnitProType.USF_UNIT_PRO_DIVE_STATE);
			}
		}
		return 0;
	}

	/**
	 * 是否在潜行状态
	 * @return
	 */
	public boolean isInDiveState() {
		return getDiveState() != 0;
	}

	/**
	 * 设置潜行状态
	 * @param diveState
	 */
	public void setDiveState(int diveState) {
		if (extraProperties == null) {
			extraProperties = new HashMap<>();
		}
		extraProperties.put(USFUnitProType.USF_UNIT_PRO_DIVE_STATE, diveState);

	}

	public void setRobState(int robState) {
		if (extraProperties == null) {
			extraProperties = new HashMap<>();
		}
		extraProperties.put(USFUnitProType.USF_UNIT_PRO_ROB_STATE, robState);
	}
	
	public void setRobProb(int robProb) {
		if (extraProperties == null) {
			extraProperties = new HashMap<>();
		}
		extraProperties.put(USFUnitProType.USF_UNIT_PRO_ROB_PROB, robProb);
	}

	/**
	 * 是否能掠夺（艾斯船）
	 * @return
	 */
	public boolean canRob() {
		if (extraProperties == null) {
			return false;
		}
		Integer robState = extraProperties.get(USFUnitProType.USF_UNIT_PRO_ROB_STATE);
		if (robState == null || robState <= 0) {
			return false;
		}
		Integer robProb = extraProperties.get(USFUnitProType.USF_UNIT_PRO_ROB_PROB);
		if (robProb == null) {
			return false;
		}
		return robProb > RandomUtil.randomInt(100);
	}

//	public SCUSFUnitProChange getExtraInfo()
//	{
//		if(extraProperties == null || extraProperties.size() <= 0)
//		{
//			return null;
//		}
//		ArrayList<USFClientUnitIntProChange> intProChanges = new ArrayList<>(2);
//		for (Map.Entry<USFUnitProType,Integer> entry: extraProperties.entrySet())
//		{
//			USFClientUnitIntProChange pro = new USFClientUnitIntProChange(this.getId(),
//					entry.getKey(), entry.getValue());
//			intProChanges.add(pro);
//		}
//		if(intProChanges.size() > 0)
//		{
//			SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
//			scUSFUnitProChange.setIntProChanges(intProChanges);
//			return scUSFUnitProChange;
//		}
//		return null;
//	}

	@Override
	public USFFortress getCurFortress() {
		return this.curFortress;
	}

	@Override
	protected void copyTo(USFClientUnitInfo clientUnitInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientPlayerInfo();
		}
		this.clientInfo.setDisplayInfo(displayInfo);
		this.clientInfo.setShipInfo(this.moveController.getShipInfo());
		this.clientInfo.setHp(this.fightController.getHp());
		this.clientInfo.setKillCount(this.fightController.getKillCount());
		clientUnitInfo.setPlayerInfo(this.clientInfo);
	}

	private USFClientMyPrivateInfo copyToPrivate() {
		if (this.privateClientInfo == null) {
			this.privateClientInfo = new USFClientMyPrivateInfo();
		}
		this.privateClientInfo.setIsBot(this.isBot);
		this.privateClientInfo.setFormationIndex(this.curFormationIndex + 1);
		this.privateClientInfo.setFinalTargetFortressUid(this.moveController.getFinalTargetFortressUid());
		this.privateClientInfo.setCanOpenMic(this.canOpenMic);
		this.privateClientInfo.setUnionRoleId(this.unionRoleId);
		ArrayList<USFClientPlayerFormation> clientFormations = new ArrayList<>(this.formations.size());
		this.privateClientInfo.setFormations(clientFormations);
		for (USFPlayerFormation playerFormation : this.formations) {
			clientFormations.add(new USFClientPlayerFormation(playerFormation.getShipInfo().getShipId()));
		}
		this.privateClientInfo.setPathFortressUids(this.moveController.getPathFortressUids());
		this.privateClientInfo.setDiveNumber(getCouldDiveNumber());
		return this.privateClientInfo;
	}

	public USFClientMyInfo copyToMy() {
		USFClientMyInfo clientMyInfo = new USFClientMyInfo();
		clientMyInfo.setUnitInfo(this.copyTo());
		clientMyInfo.setPrivateInfo(this.copyToPrivate());
		return clientMyInfo;
	}

	/**
	 * 获取玩家战斗数据
	 * 
	 * @param isSettlement 是否结算
	 * @return
	 */
	public USFClientPlayerFightInfo copyToFightInfo(boolean isSettlement) {
		if (this.clientPlayerFightInfo == null) {
			this.clientPlayerFightInfo = new USFClientPlayerFightInfo();
		}
		this.clientPlayerFightInfo.setPlayerId(this.getPlayerId());
		this.clientPlayerFightInfo.setKillCountTotal(fightController.getKillCountTotal());
		this.clientPlayerFightInfo.setDeathCount(fightController.getDeathCount());
		this.clientPlayerFightInfo.setAssistCountTotal(fightController.getAssistCountTotal());
		if (isSettlement) {
			this.clientPlayerFightInfo.setIsBot(this.isRealBot());
		} else {
			this.clientPlayerFightInfo.setIsBot(this.isBot);
			this.clientPlayerFightInfo.setCanOpenMic(this.canOpenMic);
		}
		return this.clientPlayerFightInfo;
	}

	/**
	 * 获取战斗结果(推送到本服结算的)
	 * 
	 * @return
	 */
	public USFPlayerResult copyToResult() {
		USFPlayerResult playerResult = new USFPlayerResult();
		playerResult.setIsBot(this.isRealBot());
		playerResult.setPlayerId(this.getPlayerId());
		int maxKill = this.getFightController().getMaxKillCount();
		playerResult.setContinuityKill(maxKill);
		playerResult.setIsOnFight(this.isInGame);
		return playerResult;
	}

	/**
	 * 同步私有数据
	 */
	public void syncData() {
		SCUSFPlayerSyncData scUSFPlayerSyncData = new SCUSFPlayerSyncData();
		scUSFPlayerSyncData.setPrivateInfo(this.copyToPrivate());
		this.pushMsg(scUSFPlayerSyncData);
	}

	/**
	 * 加入阵营
	 * 
	 * @param camp
	 */
	public void joinCamp(USFCamp camp) {
		this.joinToCamp(camp);
		USFFortress bornFortress = this.camp.getBornFortress();
		this.enterFortress(bornFortress);
	}

	@Override
	public void onRes() {
		this.curFormationIndex++;
		if (this.curFormationIndex >= this.formations.size()) {
			this.curFormationIndex = 0;
		}
		this.switchFormation(this.curFormationIndex, true);
		this.syncData();
		USFFortress bornFortress = this.camp.getBornFortress();
		if (bornFortress.getCampType() == this.campType) {
			this.enterFortress(bornFortress);
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战玩家复活,player={}", this);
			}
		} else {
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_GHOST);
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战玩家复活失败,大本营被攻陷,player={}", this);
			}
		}
	}

	// 转成战斗记录对象
	public USFFightRecordPerson toUSFFightRecordPerson() {
		return new USFFightRecordPerson(displayInfo.getPlayerId(), fightController.getHp(), false, curFormationIndex);
	}

	@Override
	public int getCollisionSize() {
		return this.moveController.getCollisionSize();
	}

	@Override
	public IUSFRouteNode getNextNode() {
		return this.moveController.getNextNode();
	}

	@Override
	public IUSFRouteNode getPrevNode() {
		return this.moveController.getPrevNode();
	}

	@Override
	public void changeNextNode(IUSFRouteNode nextNode) {
		this.moveController.setNextNode(nextNode);
	}

	@Override
	public void changePrevNode(IUSFRouteNode prevNode) {
		this.moveController.setPrevNode(prevNode);
	}

	@Override
	public USFRoute getRoute() {
		return this.moveController.getRoute();
	}

	@Override
	public boolean enterRoute(USFRoute route) {
		return this.moveController.enterRoute(route, this.moveController.getTargetFortress());
	}

	@Override
	public void leaveRoute() {
		this.moveController.leaveRoute();
	}

	@Override
	public void enterFortress(USFFortress targetFortress) {
		if (this.curFortress != null) {
			if (this.curFortress.getId() == targetFortress.getId()) {
				return;
			} else {
				this.leaveFortress();
			}
		}
		this.curFortress = targetFortress;
		this.curFortress.addUnit(this);
		this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_GARRISON);
	}

	@Override
	public void leaveFortress() {
		if (this.curFortress != null) {
			this.curFortress.removeUnitAndChangeCamp(this);
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
			this.curFortress = null;
		}
	}

	/**
	 * 当离开据点
	 */
	@Override
	public void onLeaveFortress() {
		this.curFortress = null;
		this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
	}

	@Override
	public void onStreamChange() {
	}

	@Override
	public boolean onCollide(IUSFUnitMoveable sourceNode) {
		if (!(sourceNode instanceof IUSFUnitFightable)) {
			return false;
		}
		USFPlayer movePlayer = sourceNode instanceof USFPlayer ? (USFPlayer) sourceNode : null;
		if (movePlayer != null && (movePlayer.isInDiveState() || isInDiveState())) {
			return false;
		}
		IUSFUnitFightable sourceUnit = (IUSFUnitFightable) sourceNode;
		USFUnitStateType curStateType = this.fsmController.getCurStateType();
		if (curStateType == USFUnitStateType.USF_UNIT_STATE_BATTLING) {
			sourceUnit.getFsmController().switchState(USFUnitStateType.USF_UNIT_STATE_WAITING);
			return true;
		}
		if (sourceUnit.getCampType() == this.getCampType()) {
			if (curStateType == USFUnitStateType.USF_UNIT_STATE_WAITING
				&& sourceNode.getTargetFortress() == this.getTargetFortress()) {
				sourceUnit.getFsmController().switchState(USFUnitStateType.USF_UNIT_STATE_WAITING);
				return true;
			}
			return false;
		} else {
			USFFightOnRoute usfFightOnRoute = new USFFightOnRoute(this.getRoute(), this, sourceUnit);
			usfFightOnRoute.start();
			return true;
		}
	}

//	@Override
//	public void changePos(FPoint3 pos) {
//		this.setPos(pos);
//	}

	@Override
	public int getProgress() {
		return this.moveController.getProgress();
	}

	@Override
	public int readyToMove(int targetFortressId) {
		USFUnitStateType curStateType = this.fsmController.getCurStateType();
		if (curStateType == USFUnitStateType.USF_UNIT_STATE_GARRISON) {
			if (this.curFortress.getOperateUnitId() == this.id) {
				return ErrorCodeConstants.USF_PLAYER_MOVE_FORTRESS_PROMOTION;
			}
		}
		return this.moveController.readyToMove(targetFortressId);
	}

	@Override
	public boolean canMove() {
		boolean bl = this.fightController.isOnFight();
		return !bl;
	}

	@Override
	public void tryMove() {
		if (this.moveController.getTargetFortress() == null) {
			if (!this.isBot) {
				return;
			}
			int openBot = BaseService.getConfigValue(PriConfigKeyName.USF_OPEN_PLAYER_BOT);
			if (!this.isInGame && openBot == 0) {
				return;
			}

			if (this.curFortress == null) {
				return;
			}
			if (this.curFortress.getOperateUnitId() == this.id) {
				return;
			}
			int[] fortressIds = this.curFortress.getAllRoutes().keys();
			if (fortressIds == null || fortressIds.length == 0) {
				return;
			}
			int targetFortressId = fortressIds[RandomUtil.randomInt(fortressIds.length)];
			USFFortress targetFortress = this.camp.getUsFight().getFortress(targetFortressId);
			if (targetFortress == null
				|| (targetFortress.getType() == USFFortressType.USF_FORTRESS_BASE && targetFortress.getCampType() != this.campType)) {
				return;
			}
			int code = this.readyToMove(targetFortressId);
			if (code == 0) {
				this.syncData();
			}
		} else {
			this.moveController.tryMove();
		}
	}

	@Override
	public void tryMoveOn() {
		this.moveController.tryMoveOn();
	}

//	@Override
//	public void refreshPos(long now) {
//		this.moveController.refreshPos(now);
//	}

	@Override
	public void refreshProgress(long now) {
		this.moveController.refreshProgress(now);
	}

	@Override
	public USFFortress getTargetFortress() {
		return this.moveController.getTargetFortress();
	}

	@Override
	public void nextTargetFortress() {
		this.moveController.nextTargetFortress();
	}

	@Override
	public void incrMoveDis(int addMoveDis) {
		this.moveController.incrMoveDis(addMoveDis);
	}

	@Override
	public FFightSide getCurFFightSide(int fightCamp) {
		return this.fightController.getCurFFightSide(fightCamp);
	}

	@Override
	public void onStartFight(AbstractUSFFight fight) {
		USFUnitStateType curStateType = this.fsmController.getCurStateType();
		if (curStateType == USFUnitStateType.USF_UNIT_STATE_GARRISON) {
			// 发送据点内进入战斗的数据
		} else {
			USFUnitFsmStateBattling fsmStateBattling = this.fsmController
				.getState(USFUnitStateType.USF_UNIT_STATE_BATTLING);
			fsmStateBattling.setTargetUid(fight.getTargetId(this));
			fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_BATTLING);
		}

		this.fightController.onStartFight();
	}

	@Override
	public void onFightWin(AbstractUSFFight fight) {
		USFFightTypeEnum fightType = fight.geFightType();
		if (fightType == USFFightTypeEnum.FIGHT_ON_ROUTE) {// 航线上战斗胜利
			// 胜利后进入指定时间的等待状态
			int timeout = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_WIN_WAITING_TIMEOUT);
			USFUnitFsmStateWaiting fsmStateWaiting = this.fsmController
				.getState(USFUnitStateType.USF_UNIT_STATE_WAITING);
			fsmStateWaiting.resetTimeout(timeout);
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_WAITING);
		} else if (fightType == USFFightTypeEnum.FIGHT_IN_FORTRESS) {// 据点内战斗胜利
			if (this.getRoute() != null) {// 航线上的玩家
				USFFortress fortress = fight.getFortress();
				if (!fortress.hasDefender()) {
					this.leaveRoute();
					this.enterFortress(fortress);
				}
			}
		}
		this.fightController.onWin(fight);
	}

	@Override
	public void onFightFailed(AbstractUSFFight fight) {
		USFFightTypeEnum fightType = fight.geFightType();
		if (fightType == USFFightTypeEnum.FIGHT_ON_ROUTE) {// 航线上战斗失败
			this.leaveRoute();
		} else if (fightType == USFFightTypeEnum.FIGHT_IN_FORTRESS) {// 据点内战斗失败
			if (this.getRoute() != null) {// 航线上的玩家
				this.leaveRoute();
			}
		}

		this.moveController.reset();
		this.fightController.onFailed(fight);
		if (skillController != null) {
			this.skillController.closeAllSkill();
		}
		buffController.removeAllBuff();
		this.remainLifeCount--;
		this.syncData();
		this.camp.onPlayerDie(this);

		if (this.remainLifeCount > 0) {
			if (this.curFormationIndex == this.formations.size() - 1) {
				this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_REORGANIZE);
			} else {
				USFUnitFsmStateDeath stateDeath = fsmController.getState(USFUnitStateType.USF_UNIT_STATE_DEATH);
				int delay = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_RES_DELAY);
				stateDeath.die(delay);
			}
		} else {
			this.fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_GHOST);
		}
	}

	@Override
	public void onFightInterrupt(AbstractUSFFight fight) {
		this.fightController.onInterrupt();
		this.moveController.reset();
		this.syncData();
		USFRoute route = this.getRoute();
		if (route != null) {// 船在航线上战斗出问题先丢回老家
			this.leaveRoute();
			this.enterFortress(this.getCamp().getBornFortress());
		}
	}

	@Override
	public void onFightAssist(IUSFUnitFightable loser) {
		this.fightController.onFightAssist(loser);
	}

	@Override
	public PlayerDisplayInformation toPlayerDisplayInformation() {
		return this.displayInfo;
	}

	private boolean checkChannel() {
		if (channel == null || !channel.isActive()) {
			channel = BattleCoreBoot.getBenfuChannel(this.getServerId());
			if (channel == null) {
				return false;
			}
		}
		return true;
	}

	@Override
	public void pushMsg(APSHandler data) {
		if (!this.isInGame) {
			return;
		}
		final int serverId = this.getServerId();
		final long playerId = this.getPlayerId();
		boolean succ = this.checkChannel();
		if (!succ) {
			AMLog.LOG_COMMON.warn("没有找到channel,serverId={},playerId={},data={}", serverId, playerId, data);
			return;
		}
		Message message = Message.buildByServerId(playerId, serverId, data, null, null, null);
		this.channel.writeAndFlush(message);
	}

	@Override
	public void pushPrompt(String prompt) {
		SystemMessage resp = new SystemMessage();
		resp.setMsg(prompt);
		resp.setType(SystemMessageType.CENTER);
		resp.setMsgType(SendMessageType.SYSTEM_MESSAGE);
		this.pushMsg(resp);
	}

	public void pushErrorMsg(IPSEnum psEnum, int errorCode, Object... arguments) {
		if (!this.isInGame) {
			return;
		}
		final int serverId = this.getServerId();
		final long playerId = this.getPlayerId();
		boolean succ = this.checkChannel();
		if (!succ) {
			AMLog.LOG_COMMON.warn("没有找到channel,serverId={},playerId={},errorCode={}", serverId, playerId, errorCode);
			return;
		}
		ErrorMessage data = BaseService.getErrorMessage(psEnum.getCode(), errorCode, arguments);
		Message message = Message.buildByServerId(playerId, serverId, data, null, null, null);
		this.channel.writeAndFlush(message);
	}

	/**
	 * 当玩家进入海战
	 */
	public void onEnter() {
		final long now = System.currentTimeMillis();
		if (!this.isInGame) {
			this.isInGame = true;
			this.enterGameTime = now;
		}
		if (!this.camp.getUsFight().isRunning()) {
			this.finishBot(now);
		}
	}

	/**
	 * 当玩家退出海战
	 */
	public void onQuit() {
		final long now = System.currentTimeMillis();
		if (this.isInGame) {
			this.isInGame = false;
			long time = now - this.enterGameTime;
			if (time > 0) {
				this.inGameTotalTime = this.inGameTotalTime + time;
			}
			this.enterGameTime = 0;
		}
		this.startBot(now);
	}

	/**
	 * 当海战开始运行
	 * 
	 * @param now
	 */
	public void onFightStartRunning(long now) {
		if (!this.isInGame) {
			this.startBot(now);
		}
		AbstractUSFUnitFsmState unitFsmState = this.fsmController.getCurState();
		unitFsmState.onFightEnterRunning(now);
	}

	/**
	 * 当海战结束运行
	 * 
	 * @param now
	 */
	public void onFightFinishRunning(long now) {
		this.finishBot(now);
	}

	/**
	 * 开始托管
	 * 
	 * @param now
	 */
	public void startBot(long now) {
		if (!this.isBot) {
			this.isBot = true;
			this.botBeginTime = now;
		}
	}

	/**
	 * 结束托管
	 * 
	 * @param now
	 */
	public void finishBot(long now) {
		if (this.isBot) {
			this.isBot = false;
			long time = now - this.botBeginTime;
			if (time > 0) {
				this.botTotalTime = this.botTotalTime + time;
			}
			this.botBeginTime = 0;
		}
	}

	/**
	 * 是否真实托管(托管超过策划配置的时间被记为真实托管,会计入托管率)
	 * 
	 * @return
	 */
	public boolean isRealBot() {
//		if (this.botTotalTime <= 0) {
//			return false;
//		}
//		int percent = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_BOT_TIME_PERCENT);
//		long time = percent * this.inGameTotalTime / 100;
//		boolean bl = this.botTotalTime > time;
//		return bl;
		return !manualed;
	}

	public void fillFormations(List<USFPlayerFormation> list) {
		int lifeCount = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_LIFE_COUNT);
		this.remainLifeCount = list.size() * lifeCount;
		this.curFormationIndex = 0;
		this.formations.addAll(list);
		this.switchFormation(this.curFormationIndex, false);
	}

	public void switchFormation(int index, boolean push) {
		USFPlayerFormation formation = this.formations.get(this.curFormationIndex);
		this.moveController.updateShipInfo(formation.getShipInfo());
		this.fightController.updateFightData(formation.getFightSide());
		this.displayInfo.setFightPower(formation.getFightPower());
		if (skillController == null) {
			skillController = new USFSkillController(this);
		}
		skillController.replaceAllSkill(moveController.getSkillIds());
		skillController.Ready();
		if (extraProperties != null) {
			extraProperties.clear();
		}
		if (push) {
			SCUSFPlayerSwitchFormation scUSFPlayerSwitchFormation = new SCUSFPlayerSwitchFormation();
			scUSFPlayerSwitchFormation.setUnitId(this.id);
			this.copyTo();
			scUSFPlayerSwitchFormation.setPlayerInfo(this.clientInfo);
			this.camp.getUsFight().broadcast(scUSFPlayerSwitchFormation);
		}

	}

	/**
	 * 当手动
	 *
	 */
	public void onManual(boolean cancelMove) {
		this.manualed = true;
		if (this.isBot()) {
			long now = System.currentTimeMillis();
			this.finishBot(now);
		}
		if (cancelMove) {
			this.moveController.cancelMove();
		}
		this.syncData();
	}

	public PlayerDisplayInformation getDisplayInfo() {
		return displayInfo;
	}

	public boolean isBot() {
		return isBot;
	}

	public long getPlayerId() {
		return this.displayInfo.getPlayerId();
	}

	public int getServerId() {
		return this.displayInfo.getServerId();
	}

	public USFMoveController getMoveController() {
		return moveController;
	}

	public USFFightController getFightController() {
		return fightController;
	}

	public int getRemainLifeCount() {
		return remainLifeCount;
	}

	public void setRemainLifeCount(int remainLifeCount) {
		this.remainLifeCount = remainLifeCount;
	}

	public boolean isInGame() {
		return isInGame;
	}

	public boolean isCanOpenMic() {
		return canOpenMic;
	}

	public void setCanOpenMic(boolean canOpenMic) {
		this.canOpenMic = canOpenMic;
	}

	public int getUnionRoleId() {
		return unionRoleId;
	}

	public void setUnionRoleId(int unionRoleId) {
		this.unionRoleId = unionRoleId;
	}

	public List<USFPlayerFormation> getFormations() {
		return formations;
	}

	public boolean isManualed() {
		return manualed;
	}

	public void setManualed(boolean manualed) {
		this.manualed = manualed;
	}

	@Override
	public List<Integer> getSkillIds() {
		return moveController.getSkillIds();
	}

	@Override
	public List<USFClientBuffInfo> getShowBuffs() {
		if (buffController != null) {
			return buffController.getShowBuffs();
		}
		return null;
	}

}
