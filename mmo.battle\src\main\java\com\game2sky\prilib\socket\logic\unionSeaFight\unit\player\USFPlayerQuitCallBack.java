package com.game2sky.prilib.socket.logic.unionSeaFight.unit.player;

import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerQuit;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFightBattleService;
import com.game2sky.publib.communication.common.ISysCallBack;

/**
 * TODO 玩家退出海战回调
 *
 * <AUTHOR>
 * @version v0.1 2018年7月30日 下午8:08:47  guojijun
 */
public class USFPlayerQuitCallBack implements ISysCallBack {

	private USFightBattleService service;
	private long uqId;
	private SSUSFPlayerQuit ssUSFPlayerQuit;

	@Override
	public void apply() {
		this.service.playerQuit(uqId, ssUSFPlayerQuit);
	}

	public USFightBattleService getService() {
		return service;
	}

	public void setService(USFightBattleService service) {
		this.service = service;
	}

	public long getUqId() {
		return uqId;
	}

	public void setUqId(long uqId) {
		this.uqId = uqId;
	}

	public SSUSFPlayerQuit getSsUSFPlayerQuit() {
		return ssUSFPlayerQuit;
	}

	public void setSsUSFPlayerQuit(SSUSFPlayerQuit ssUSFPlayerQuit) {
		this.ssUSFPlayerQuit = ssUSFPlayerQuit;
	}

}
