package com.game2sky.prilib.socket.logic.autochess.rank;

import java.util.LinkedList;

import com.game2sky.prilib.socket.logic.integral.sort.Barrel;

/**
 * 自走棋积分桶.
 *
 * <AUTHOR>
 * @version v0.1 2019年6月11日 下午5:22:44  <PERSON>
 */
public class AutoChessRankBarrel extends Barrel {

	/** 同一积分的玩家Ids */
	private LinkedList<Long> playerIds = new LinkedList<Long>();

	public AutoChessRankBarrel(Long id) {
		this.id = id;
	}

	public void addPlayerId(Long playerId) {
		this.playerIds.addLast(playerId);
		this.haveNumber = playerIds.size();
	}

	public void removePlayerId(Long playerId) {
		this.playerIds.remove(playerId);
		this.haveNumber = playerIds.size();
	}

	public LinkedList<Long> getPlayerIds() {
		return playerIds;
	}

	/**
	 * 在链表的位置越靠前，排名越靠前
	 * @param playerId
	 * @return
	 */
	public int getRank(long playerId) {
		int indexOf = playerIds.indexOf(playerId);
		return indexOf < 0 ? playerIds.size() + 1 : indexOf + 1;
	}
}
