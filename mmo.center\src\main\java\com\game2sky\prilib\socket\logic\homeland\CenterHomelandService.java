package com.game2sky.prilib.socket.logic.homeland;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.homeland.EnemyChangeInfo;
import com.game2sky.prilib.communication.game.homeland.HomeLandBrifeInfo;
import com.game2sky.prilib.communication.game.homeland.RobPlayerInfo;
import com.game2sky.prilib.communication.game.homeland.inner.SSRequestCanRobList;
import com.game2sky.prilib.communication.game.homeland.inner.SSResponseCanRobList;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncEnemyInfoFromCenter;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncHomeLandInfoToCenter;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncHomeLandInfoToCenterWhenConnect;
import com.game2sky.prilib.communication.game.homeland.inner.SSUpdateEnemyInfoFromCenter;
import com.game2sky.prilib.core.socket.logic.homeland.center.HomelandBaseInfo;
import com.game2sky.prilib.core.socket.logic.homeland.constant.HomeLandConstants;
import com.game2sky.prilib.socket.logic.homeland.matchv3.CenterHomelandGlobalManager;
import com.game2sky.prilib.socket.logic.homeland.matchv3.HomeLandCreamMatchBarrel;
import com.game2sky.prilib.socket.logic.homeland.matchv3.HomeLandCreamMatchHandler;
import com.game2sky.prilib.socket.logic.homeland.matchv3.HomeLandCreamMatchSort;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年4月2日 下午5:45:44  daixl
 */

@Service
public class CenterHomelandService {

	public void syncHomeLandInfoToCenter(SSSyncHomeLandInfoToCenter msg) {
		HomeLandBrifeInfo info = msg.getInfo();
		updateHomeLandCache(info, false);
	}

	public void requestCanRobList(SSRequestCanRobList msg) {
		long playerId = msg.getPlayerId();

		SSResponseCanRobList robList = new SSResponseCanRobList();
		robList.setNeedClean(false);
		robList.setIsRefresh(msg.getIsRefresh());

		Map<Long, Integer> match = HomeLandCreamMatchHandler.match(msg, robList);
		if (!match.isEmpty()) {
			List<RobPlayerInfo> list = new ArrayList<RobPlayerInfo>(match.size());
			robList.setList(list);

			HomelandBaseInfo enemy = null;
			Iterator<Entry<Long, Integer>> iterator = match.entrySet().iterator();
			while (iterator.hasNext()) {
				Entry<Long, Integer> next = iterator.next();
				Long enemyId = next.getKey();
				int canGet = next.getValue();
				enemy = CenterHomelandGlobalManager.getByPlayerId(enemyId);
				RobPlayerInfo robPlayerInfo = enemy.toRobPlayerInfo();
				AMLog.LOG_COMMON.info(
					"_ROB_BUFF_TEST_ : playerId : {}, primary can be robbed : {}, finally can be robbed : {}", enemyId,
					canGet, canGet - enemy.getProtect());
				canGet -= enemy.getProtect();
				if (canGet < 0) {
					canGet = 0;
				}
				robPlayerInfo.setCg(canGet);
				int robWarValue = HomeLandConstants.getRobWarValue(msg.getWarValue(), enemy.getWarValue());
				robPlayerInfo.setGwv(robWarValue);
				list.add(robPlayerInfo);
			}
		}
		CoreBoot.centerToBenfu(robList, playerId, msg.getServerId());
	}

	@SuppressWarnings("unchecked")
	public void syncEnemyInfoFromCenter(SSSyncEnemyInfoFromCenter msg) {
		long playerId = msg.getPlayerId();
		int serverId = msg.getServerId();
		List<Long> enemyIds = msg.getEnemyIds();

		List<EnemyChangeInfo> infos = null;
		if (enemyIds != null && !enemyIds.isEmpty()) {
			infos = new ArrayList<EnemyChangeInfo>(enemyIds.size());
			HomelandBaseInfo homelandBaseInfo = null;
			for (long enemyId : enemyIds) {
				homelandBaseInfo = CenterHomelandGlobalManager.getByPlayerId(enemyId);
				if (homelandBaseInfo == null) {
					continue;
				}
				EnemyChangeInfo info = new EnemyChangeInfo();
				info.setPid(enemyId);
				CenterHomelandGlobalManager.getCanGet(info);
				info.setWv(homelandBaseInfo.getWarValue());
				info.setLobbyLevel(homelandBaseInfo.getLobbyLevel());
				info.setProsperity(homelandBaseInfo.getProsperity());
				info.setWarHouseLevel(homelandBaseInfo.getWarHouseLevel());
				infos.add(info);
			}
		} else {
			infos = Collections.EMPTY_LIST;
		}

		SSUpdateEnemyInfoFromCenter result = new SSUpdateEnemyInfoFromCenter(playerId, infos);
		CoreBoot.centerToBenfu(result, playerId, serverId);
	}

	public void syncHomeLandInfoToCenterWhenConnect(SSSyncHomeLandInfoToCenterWhenConnect msg) {

		List<HomeLandBrifeInfo> infos = msg.getInfos();
		if (CollectionUtils.isEmpty(infos)) {
			return;
		}

		for (HomeLandBrifeInfo info : infos) {
			updateHomeLandCache(info, true);
		}
	}

	private void updateHomeLandCache(HomeLandBrifeInfo info, boolean isStart) {
		int oldCanRob = 0;
		int oldBeRobTimes = 0;
		long playerId = info.getPlayerId();
		HomelandBaseInfo homelandBaseInfo = CenterHomelandGlobalManager.getByPlayerId(playerId);
		if (homelandBaseInfo == null) {
			homelandBaseInfo = new HomelandBaseInfo();
			homelandBaseInfo.setPlayerId(playerId);
			homelandBaseInfo.setServerId(info.getServerId());
			CenterHomelandGlobalManager.addHomeLandBaseInfo(homelandBaseInfo);
		} else {
			oldCanRob = homelandBaseInfo.getCanRob();
			oldBeRobTimes = homelandBaseInfo.getBeRobTimes();
			if (isStart) {
				homelandBaseInfo.setNextMatchTime(0);
			}
		}
		homelandBaseInfo.setName(info.getNickName());
		homelandBaseInfo.setLevel(info.getPlayerLevel());
		homelandBaseInfo.setWarValue(info.getWarValue());
		homelandBaseInfo.setLobbyLevel(info.getLobbyLevel());
		homelandBaseInfo.setWarHouseLevel(info.getWarHouseLevel());
		int canRob = info.getCanRob();
		homelandBaseInfo.setCanRob(info.getCanRob());
		homelandBaseInfo.setProtect(info.getProtect());
		homelandBaseInfo.setDefenseNum(info.getDefenseNum());
		homelandBaseInfo.setProsperity(info.getProsperity());
		int beRobTimes = info.getBeRobTimes();
		homelandBaseInfo.setBeRobTimes(beRobTimes);
		if (beRobTimes != oldBeRobTimes) {
			homelandBaseInfo.setLastUpdateTime(Globals.getTimeService().now());
		}

		if (oldCanRob != canRob) {
			int oldBarrelId = oldCanRob / 10;
			int newBarrelId = canRob / 10;
			if (oldBarrelId != newBarrelId) {
				CenterHomelandGlobalManager.changeBarrel(playerId, oldCanRob, canRob, isStart);
			}
		}
	}

	// 定时，每一个小时删除所有桶中无用的桶
	public void cleanHomeBarrel() {
		HomeLandCreamMatchSort sort = CenterHomelandGlobalManager.getSort();
		ArrayList<HomeLandCreamMatchBarrel> barrelList = sort.getBarrelList();
		HashMap<Long, HomeLandCreamMatchBarrel> barrelMap = sort.getBarrelMap();
		if (!CollectionUtils.isEmpty(barrelList)) {
			Iterator<HomeLandCreamMatchBarrel> iterator = barrelList.iterator();
			while (iterator.hasNext()) {
				HomeLandCreamMatchBarrel next = iterator.next();
				if (next.getHaveNumber() == 0 && CollectionUtils.isEmpty(next.getPlayerIds())) {
					iterator.remove();
					barrelMap.remove(next.getId());
				}
			}
		}
	}
}
