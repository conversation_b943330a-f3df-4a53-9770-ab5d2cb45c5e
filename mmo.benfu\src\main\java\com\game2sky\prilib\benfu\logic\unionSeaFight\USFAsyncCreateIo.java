package com.game2sky.prilib.benfu.logic.unionSeaFight;

import java.util.Collection;
import java.util.Set;

import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCreateReq;
import com.game2sky.prilib.core.socket.logic.union.Union;
import com.game2sky.publib.async.IIoOperation;

/**
 * TODO 异步开始海战
 *
 * <AUTHOR>
 * @version v0.1 2018年11月3日 下午9:22:13  guojijun
 */
public class USFAsyncCreateIo implements IIoOperation {

	private SSUSFCreateReq ssUSFCreateReq;
	private String serverFlag;
	private Collection<Long> fightPlayerIdList;
	private Union union;
	private BenfuUnionSeaFightService benfuUnionSeaFightService;

	@Override
	public int doStart() {
		return IIoOperation.STAGE_START_DONE;
	}

	@Override
	public int doIo() {
		this.benfuUnionSeaFightService.dealUSFFightAllotReq(ssUSFCreateReq, serverFlag, fightPlayerIdList, union);
		return IIoOperation.STAGE_IO_DONE;
	}

	@Override
	public int doStop() {
		return IIoOperation.STAGE_STOP_DONE;
	}

	public SSUSFCreateReq getSsUSFCreateReq() {
		return ssUSFCreateReq;
	}

	public void setSsUSFCreateReq(SSUSFCreateReq ssUSFCreateReq) {
		this.ssUSFCreateReq = ssUSFCreateReq;
	}

	public String getServerFlag() {
		return serverFlag;
	}

	public void setServerFlag(String serverFlag) {
		this.serverFlag = serverFlag;
	}

	public Collection<Long> getFightPlayerIdList() {
		return fightPlayerIdList;
	}

	public void setFightPlayerIdList(Collection<Long> fightPlayerIdList) {
		this.fightPlayerIdList = fightPlayerIdList;
	}

	public BenfuUnionSeaFightService getBenfuUnionSeaFightService() {
		return benfuUnionSeaFightService;
	}

	public void setBenfuUnionSeaFightService(BenfuUnionSeaFightService benfuUnionSeaFightService) {
		this.benfuUnionSeaFightService = benfuUnionSeaFightService;
	}

	public Union getUnion() {
		return union;
	}

	public void setUnion(Union union) {
		this.union = union;
	}

}
