package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateBattlingInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.IUSFRouteNode;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitMoveable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;

/**
 * TODO 单位战斗中(肯定在航线上)
 *
 * <AUTHOR>
 * @version v0.1 2018年6月9日 下午5:00:44  guojijun
 */
public class USFUnitFsmStateBattling extends AbstractUSFUnitFsmState {

	private USFClientUnitStateBattlingInfo clientInfo;
	private final IUSFRouteNode node;
	private int targetUid;

	public USFUnitFsmStateBattling(AbstractUSFUnitFsmController controller, IUSFRouteNode node) {
		super(USFUnitStateType.USF_UNIT_STATE_BATTLING, controller);
		this.node = node;
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
		this.targetUid = 0;
	}

	@Override
	public void tick(long now) {
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateBattlingInfo();
		}
		USFRoute route = this.node.getRoute();
		this.clientInfo.setRouteId(route.getDictId());
		float progressPercent = route.getProgressPercent(this.node);
		this.clientInfo.setProgress(progressPercent);
		this.clientInfo.setTargetFortressUid(0);
		if (this.node instanceof IUSFUnitMoveable) {
			IUSFUnitMoveable moveable = (IUSFUnitMoveable) this.node;
			USFFortress targetFortress = moveable.getTargetFortress();
			if (targetFortress != null) {
				this.clientInfo.setTargetFortressUid(targetFortress.getId());
			}
		}
		this.clientInfo.setTargetUid(this.targetUid);
		clientUnitStateInfo.setBattlingInfo(this.clientInfo);
	}

	public int getTargetUid() {
		return targetUid;
	}

	public void setTargetUid(int targetUid) {
		this.targetUid = targetUid;
	}

}
