package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IBattleTriggerEvent;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IUSFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.AbstractUSFFight;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;

/**
 * TODO 可战斗的
 *
 * <AUTHOR>
 * @version v0.1 2018年6月15日 下午7:15:49  guojijun
 */
public interface IUSFUnitFightable extends IUSFBuff ,IBattleTriggerEvent{

	int getId();

	USFCamp getCamp();

	USFCampType getCampType();

	/**
	 * 获取状态机
	 * 
	 * @return
	 */
	<T extends AbstractUSFUnitFsmController> T getFsmController();

	/**
	 * 当离开据点
	 */
	void onLeaveFortress();

	/**
	 * 获取当前战斗数据
	 * 
	 * @param 战斗内阵营
	 * @return
	 */
	FFightSide getCurFFightSide(int fightCamp);

	/**
	 * 当开始战斗
	 * 
	 * @param fight
	 */
	void onStartFight(AbstractUSFFight fight);

	/**
	 * 当战斗胜利
	 * 
	 * @param fight
	 */
	void onFightWin(AbstractUSFFight fight);

	/**
	 * 当战斗失败
	 * 
	 * @param fight
	 */
	void onFightFailed(AbstractUSFFight fight);

	/**
	 * 当战斗中断
	 * 
	 * @param fight
	 */
	void onFightInterrupt(AbstractUSFFight fight);

	/**
	 * 当助攻
	 * 
	 * @param loser
	 */
	void onFightAssist(IUSFUnitFightable loser);
	
	/**
	 * 获取展示数据
	 * 
	 * @return
	 */
	PlayerDisplayInformation toPlayerDisplayInformation();
}
