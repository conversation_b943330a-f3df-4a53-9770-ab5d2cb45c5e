package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.publib.framework.util.fsm.AbstractFsmState;

/**
 * TODO 海战单位状态基类
 *
 * <AUTHOR>
 * @version v0.1 2018年6月14日 下午2:38:34  guojijun
 */
public abstract class AbstractUSFUnitFsmState extends AbstractFsmState<USFUnitStateType> {

	public AbstractUSFUnitFsmState(USFUnitStateType stateType, AbstractUSFUnitFsmController controller) {
		super(stateType, controller);
	}

	@SuppressWarnings("unchecked")
	public <T extends IUSFUnit> T getUnit() {
		AbstractUSFUnitFsmController fsmController = this.getController();
		return (T) fsmController.getUnit();
	}

	protected abstract void copyTo(USFClientUnitStateInfo clientUnitStateInfo);

	/**
	 * 当战斗进入运行状态
	 */
	public void onFightEnterRunning(long now) {
	}

}
