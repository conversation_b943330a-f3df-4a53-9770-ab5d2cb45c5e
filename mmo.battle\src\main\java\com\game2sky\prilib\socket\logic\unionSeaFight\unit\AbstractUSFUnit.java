package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import java.util.Collections;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFScene;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IBattleTriggerEvent;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuffController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkillController;
import com.game2sky.publib.communication.game.struct.FPoint3;
import com.game2sky.publib.socket.logic.scene.SceneUtils;

/**
 * TODO 海战单位基类
 *
 * <AUTHOR>
 * @version v0.1 2018年6月7日 下午5:01:27  guojijun
 */
public abstract class AbstractUSFUnit implements IUSFUnit,IBattleTriggerEvent{

	protected final int id;
	protected final USFUnitType unitType;
	/**当前位置*/
	private FPoint3 pos;
	/**朝向*/
	private FPoint3 dir;
	/**所属阵营*/
	protected USFCamp camp;
	/**所属阵营类型*/
	protected USFCampType campType;
	/**AI*/
	protected IUSFUnitAI ai;
	/**状态机*/
	protected AbstractUSFUnitFsmController fsmController;
	/**BUFF控制器*/
	protected USFBuffController buffController;
	/**Skill控制器 */
	protected USFSkillController skillController;

	private USFClientUnitInfo tmpClientUnitInfo;
	
	private long now ;



	public AbstractUSFUnit(int id, USFUnitType unitType) {
		this.id = id;
		this.unitType = unitType;
		this.dir = new FPoint3();
		this.buffController = new USFBuffController(this);
//		skillController = new USFSkillController(this);
	}

	@Override
	public final int hashCode() {
		return this.id;
	}

	@Override
	public final boolean equals(Object obj) {
		if (obj instanceof AbstractUSFUnit) {
			return this.id == ((AbstractUSFUnit) obj).id;
		}
		return false;
	}

	@Override
	public final void tick(long now) {
		this.now = now;
		if (this.ai != null) {
			this.ai.tick(now);
		}
		if (this.buffController != null) {
			this.buffController.tick(now);
		}
		if (this.skillController != null) {
			this.skillController.tick(now);
		}
		this.fsmController.updateState(now);
	}

	protected void joinToCamp(USFCamp targetCamp) {
		if (this.camp != null) {
			if (this.camp == targetCamp) {
				return;
			} else {
				this.camp.removeUnit(this);
			}
		}
		this.camp = targetCamp;
		this.campType = camp.getCampType();
		this.camp.addUnit(this);
	}

	@Override
	public List<USFBuff> getBuffs() {
		if (this.buffController != null) {
			return this.buffController.getBuffs();
		}
		return Collections.emptyList();
	}


	@Override
	public void addBuff(USFBuff buff)
	{
		if (this.buffController != null) {
			this.buffController.addBuff(buff);
		}
	}
	
	@Override
	public void removeBuff(USFBuff removeBuff) {
		if (this.buffController != null) {
			this.buffController.removeBuff(removeBuff);
		}
	}

	@Override
	public void refreshProps(FFightSide fightSide) {
		if (this.buffController != null) {
			this.buffController.refreshProps(fightSide);
		}
	}

	public USFScene getScene() {
		return this.camp.getScene();
	}

	@Override
	public int getId() {
		return this.id;
	}

	@Override
	public USFUnitType getUnitType() {
		return this.unitType;
	}

	@Override
	public FPoint3 getPos() {
		return pos;
	}

	@Override
	public FPoint3 getDir() {
		return dir;
	}

	@Override
	public USFCamp getCamp() {
		return camp;
	}

	@Override
	public USFCampType getCampType() {
		return this.campType;
	}

	@Override
	public USFClientUnitInfo copyTo() {
		if (this.tmpClientUnitInfo == null) {
			this.tmpClientUnitInfo = new USFClientUnitInfo();
		}
		this.tmpClientUnitInfo.setId(id);
		this.tmpClientUnitInfo.setPos(pos);
		this.tmpClientUnitInfo.setDir(dir);
		this.tmpClientUnitInfo.setCampType(campType);
		this.tmpClientUnitInfo.setUnitType(unitType);
		USFClientUnitStateInfo clientUnitStateInfo = this.fsmController.copyTo();
		this.tmpClientUnitInfo.setStateInfo(clientUnitStateInfo);
		if (buffController != null) {
			this.tmpClientUnitInfo.setBuffInfos(this.buffController.getShowBuffs());
		}
		this.copyTo(this.tmpClientUnitInfo);
		return this.tmpClientUnitInfo;
	}

	protected abstract void copyTo(USFClientUnitInfo clientUnitInfo);

	public void setPos(FPoint3 tmpPos) {
		this.pos = SceneUtils.copy(tmpPos, this.pos);
	}

	public void setDir(FPoint3 tmpDir) {
		this.dir = SceneUtils.copy(tmpDir, this.dir);
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T extends AbstractUSFUnitFsmController> T getFsmController() {
		return (T) this.fsmController;
	}

	public IUSFUnitAI getAi() {
		return ai;
	}

	public USFBuffController getBuffController() {
		return buffController;
	}
	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,Object... args){
		if (this.buffController != null) {
			this.buffController.triggerEvent(triggerType, triggerUnit, oppositeUnit,args);
		}
		if (this.skillController != null) {
			this.skillController.triggerEvent(triggerType, triggerUnit, oppositeUnit,args);
		}
	}
	public long getNow()
	{
		return now;
	}

	public void setNow(long now)
	{
		this.now = now;
	}

	public USFSkillController getSkillController()
	{
		return skillController;
	}

	public void setSkillController(USFSkillController skillController)
	{
		this.skillController = skillController;
	}

	public void setBuffController(USFBuffController buffController)
	{
		this.buffController = buffController;
	}
	
	public abstract List<USFClientBuffInfo> getShowBuffs();
}
