package com.game2sky.prilib.socket.logic.autochess;

import org.springframework.stereotype.Service;

import com.game2sky.prilib.communication.game.autochess.SSAutoChessBaseReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessMatchStateChange;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerToCenter;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessWeekReset;
import com.game2sky.prilib.core.socket.logic.autochess.AbstractAutoChessService;
import com.game2sky.prilib.core.socket.logic.autochess.AutoChessConstans;
import com.game2sky.prilib.socket.logic.autochess.match.AutoChessMatchSort;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;

/**
 * 自走棋中心服.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月9日 下午4:33:34  Jet Lee
 */
@Service
public class AutoChessCenterService extends AbstractAutoChessService{

	public void tick() {
		AutoChessCenterManager.getInstance().tick();

	}

	public void ssAutoChessMatchStateChange(SSAutoChessMatchStateChange msg) {
		long playerId = msg.getPlayerId();
		int serverId = msg.getServerId();
		int grade = msg.getGrade();
		int score = msg.getScore();
		int action = msg.getAction();
		AutoChessMatchSort matchSort = AutoChessCenterManager.getInstance().getAutoChessMatchSort();
		if (action == AutoChessConstans.MATCH_STATE_ENTER) {
			// 进入匹配池
			matchSort.addElement(playerId, serverId, grade, score);
		} else {
			// 取消匹配
			boolean success = matchSort.deleteElement(playerId, grade);
			if (!success) {
				AMLog.LOG_ERROR.error("[autochess] [cancelMatch fail] [playerId:{}] [serverId:{}]", playerId, serverId);
			}
			// 不需要通知本服取消匹配了，因为本服已经提前取消了。
//			SSAutoChessCancelMatch ssMsg = new SSAutoChessCancelMatch(playerId, serverId,
//				success ? CommonConstants.TRUE : CommonConstants.FALSE);
//			CoreBoot.centerToBenfu(ssMsg, playerId, serverId);
		}
	}

	public void ssAutoChessWeekReset(SSAutoChessWeekReset msg) {
		AutoChessCenterManager.getInstance().weekReset();
	}

	public void ssAutoChessBaseReq(SSAutoChessBaseReq msg, Head head) {
		AutoChessCenterManager.getInstance().sendBaseInfoToBenfu(head.getServerId() ,false);
	}

	public void ssAutoChessPlayerToCenter(SSAutoChessPlayerToCenter msg, Head head) {
		AutoChessCenterManager.getInstance().addPlayers(msg.getPlayers());
	}

	public void ssAutoChessPlayerRankReq(SSAutoChessPlayerRankReq msg, Head head) {
		AutoChessCenterManager.getInstance().sendPlayerRankToBenfu(msg);
	}

}
