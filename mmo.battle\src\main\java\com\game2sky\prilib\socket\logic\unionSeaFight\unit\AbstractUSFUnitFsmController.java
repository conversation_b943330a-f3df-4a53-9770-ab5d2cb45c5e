package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import java.util.LinkedList;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitStateChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFight;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.fsm.AbstractFsmController;
import com.game2sky.publib.framework.util.fsm.IFsmState;

/**
 * TODO 海战单位状态机基类
 *
 * <AUTHOR>
 * @version v0.1 2018年6月14日 下午2:28:57  guojijun
 */
public abstract class AbstractUSFUnitFsmController extends AbstractFsmController<USFUnitStateType> {

	private final IUSFUnit unit;
	private USFClientUnitStateInfo clientInfo;

	public AbstractUSFUnitFsmController(IUSFUnit unit) {
		this.unit = unit;
	}

	/**
	 * 进入初始状态
	 * 
	 * @param stateType
	 */
	public void initState(USFUnitStateType stateType) {
		if (this.curState == null) {
			IFsmState<USFUnitStateType> fsmState = this.allStates.get(stateType);
			if (fsmState != null) {
				fsmState.enter();
				this.curState = fsmState;
				if (AMLog.LOG_UNION.isDebugEnabled()) {
					AMLog.LOG_UNION.debug("单位进入初始状态,unit={},stateType={}", this.unit, stateType);
				}
			}
		}
	}

	@Override
	protected void onSwitchState(USFUnitStateType oldStateType, USFUnitStateType curStateType) {
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("单位状态切换,unit={},oldStateType={},curStateType={}", this.unit, oldStateType,
				curStateType);
		}

		USFClientUnitStateChange stateChange = new USFClientUnitStateChange();
		stateChange.setStateInfo(this.copyTo());
		stateChange.setUnitId(this.unit.getId());
		stateChange.setUnitType(this.unit.getUnitType());

		LinkedList<USFClientUnitStateChange> stateChanges = new LinkedList<>();
		stateChanges.add(stateChange);

		SCUSFUnitStateChange unitStateChange = new SCUSFUnitStateChange();
		unitStateChange.setStateChanges(stateChanges);
		USFight usFight = this.unit.getCamp().getUsFight();
		usFight.broadcast(unitStateChange);
	}

	@SuppressWarnings("unchecked")
	public <T extends IUSFUnit> T getUnit() {
		return (T) unit;
	}

	public USFClientUnitStateInfo copyTo() {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateInfo();
		} else {
			this.clientInfo.setNormalInfo(null);
			this.clientInfo.setGarrisonInfo(null);
			this.clientInfo.setMovingInfo(null);
			this.clientInfo.setBattlingInfo(null);
			this.clientInfo.setDeathInfo(null);
			this.clientInfo.setWaitingInfo(null);
			this.clientInfo.setBeAttackedInfo(null);
			this.clientInfo.setGhostInfo(null);
			this.clientInfo.setReorganizeInfo(null);
		}
		this.clientInfo.setStateType(this.getCurStateType());
		AbstractUSFUnitFsmState unitFsmState = this.getCurState();
		unitFsmState.copyTo(clientInfo);
		return this.clientInfo;
	}
}
