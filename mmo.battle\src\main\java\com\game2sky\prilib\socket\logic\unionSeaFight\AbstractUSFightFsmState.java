package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.publib.framework.util.fsm.AbstractFsmState;

/**
 * TODO 海战状态基类
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 上午11:37:12  guojijun
 */
public abstract class AbstractUSFightFsmState extends AbstractFsmState<USFStateType> {

	public AbstractUSFightFsmState(USFStateType stateType, USFightFsmController controller) {
		super(stateType, controller);
	}

	protected abstract void copyTo(USFClientStateInfo clientStateInfo);
}
