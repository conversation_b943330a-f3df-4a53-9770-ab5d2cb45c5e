package com.game2sky.prilib.benfu.logic;

import com.game2sky.publib.Globals;
import com.game2sky.publib.async.IIoOperation;
import com.game2sky.publib.communication.game.contact.CSApplyContacter;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.socket.logic.human.AbstractHuman;

import java.util.List;

/**
 * 异步好友操作
 *
 * <AUTHOR>
 * @version v0.1 2017年07月04日 13:31 wuyidi
 */
public class FriendTestControllerIO implements IIoOperation {

	private List<AbstractHuman> humanList;

	public FriendTestControllerIO(List<AbstractHuman> humanList) {
		this.humanList = humanList;
	}

	@Override
	public int doStart() {
		return IIoOperation.STAGE_START_DONE;
	}

	@Override
	public int doIo() {
		try {
			List<AbstractHuman> allHuman = this.humanList;
			for (AbstractHuman currentHuman : allHuman) {
				AMLog.LOG_COMMON.info("Async adding friend job for player: " + currentHuman.getPlayerId());
				for (AbstractHuman insideHuman : allHuman) {
					if (currentHuman.getPlayerId() != insideHuman.getPlayerId()) {
						CSApplyContacter request = new CSApplyContacter(insideHuman.getPlayerId(),
							insideHuman.getZoneId());
						Message build = Message.buildByZoneId(currentHuman.getPlayerId(), currentHuman.getZoneId(), request,
							null, null, null);
						Globals.getMsgProcessDispatcher().put(build);
					}
					Thread.sleep(5L);
				}
				// 线程睡1秒
				Thread.sleep(1000L);
			}
		} catch (Exception e) {

		}
		return IIoOperation.STAGE_IO_DONE;
	}

	@Override
	public int doStop() {
		return IIoOperation.STAGE_STOP_DONE;
	}
}
