package com.game2sky.prilib.socket.logic.unionSeaFight.unit.player;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateReorganizeInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;
import com.game2sky.publib.Globals;

/**
 * TODO 整编状态
 *
 * <AUTHOR>
 * @version v0.1 2018年11月3日 下午3:33:25  guojijun
 */
public class USFUnitFsmStateReorganize extends AbstractUSFUnitFsmState {

	/**超时时间*/
	private long timeout;
	private USFClientUnitStateReorganizeInfo clientInfo;

	public USFUnitFsmStateReorganize(USFPlayerFsmController controller) {
		super(USFUnitStateType.USF_UNIT_STATE_REORGANIZE, controller);
	}

	@Override
	public void enter() {
		int delay = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_REORGANIZE_DELAY);
		this.timeout = Globals.getTimeService().now() + delay;
	}

	@Override
	public void leave() {
		this.timeout = 0;
	}

	@Override
	public void tick(long now) {
		if (this.timeout <= now) {
			IUSFUnit unit = this.getUnit();
			unit.onRes();
		}
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateReorganizeInfo();
		}
		this.clientInfo.setEndTime(this.timeout);
		clientUnitStateInfo.setReorganizeInfo(this.clientInfo);
	}

}
