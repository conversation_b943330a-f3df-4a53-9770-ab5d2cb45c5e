<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="mmo.communication" />
        <module name="mmo.benfu" />
        <module name="mmo.core" />
        <module name="mmo.battle" />
        <module name="mmo.kuafu" />
        <module name="mmo.center" />
        <module name="mmo.db.log" />
        <module name="mmo.reload" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="mmo.tools" target="1.7" />
    </bytecodeTargetLevel>
  </component>
</project>