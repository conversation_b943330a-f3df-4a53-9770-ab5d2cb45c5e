package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.communication.game.union.UnionPlayerType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;

/**
 * TODO 海战工具类
 *
 * <AUTHOR>
 * @version v0.1 2018年10月24日 下午6:50:04  guojijun
 */
public class USFUtils {

	private static final int RATIO = 1000;

	public static int floatToInt(float len) {
		float floatValue = len * RATIO;
		int intValue = Math.round(floatValue);
		return intValue;
	}

	/**
	 * 获取出据点的配置体积半径
	 * 
	 * @return
	 */
	public static int getLeaveFortressCollisionSize() {
		float commonCollisionSize = BaseService.getConfigValue(PriConfigKeyName.USF_LEAVE_FORTRESS_COLLISION_SIZE);
		int realSize = USFUtils.floatToInt(commonCollisionSize);
		return realSize;
	}

	/**
	 * 是否管理者
	 * 
	 * @param player
	 * @return
	 */
	public static boolean isManager(USFPlayer player) {
		return player.getUnionRoleId() == UnionPlayerType.UNION_PRESIDENT.value()
				|| player.getUnionRoleId() == UnionPlayerType.UNION_VICEPRESIDENT.value();
	}
}
