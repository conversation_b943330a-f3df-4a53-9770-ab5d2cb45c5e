<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD//EN" "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<hibernate-mapping package="com.game2sky.op.dbs.model">
	<query name="queryChatByPlayerId">from InstPlayerChat where playerId = :playerId
	</query>
	<query name="updateChatInfo">
		update InstPlayerChat m set
		m.worldSendTime=:worldSendTime where
		m.playerId=:playerId
	</query>
	<query name="updatePlayerForbidDealing">update InstPlayer set forbidDealing=:forbidDealing where playerId=:playerId</query>
	<query name="queryHeroByPlayerId">from InstHero where playerId = :playerId</query>
	<query name="queryByZoneIdAndType">from InstIdCounter where zoneId = :zoneId and
		type = :type</query>
	<!--根据platformUid 和 zoneId查询player -->
	<sql-query name="queryByPuidSid">
		<return class="com.game2sky.publib.dbs.model.InstPlayer" />
		select * from inst_player where platformUid = :platformUid and
		serverId = :serverId
	</sql-query>
	<!--根据platformUid 和 zoneId查询player -->
	<sql-query name="queryBlockByLoginIp">
		<return class="com.game2sky.publib.dbs.model.InstPlayer" />		
		select *  from inst_player 
			where `level` &lt;= :level and chargeMoney &lt;= :chargeMoney and chargeDiamond &lt;= :chargeDiamond and lastLoginIp = :lastLoginIp
			order by `level` desc limit 1000
	</sql-query>

	<!-- 联系人开始 -->
	<sql-query name="queryContacterRelationByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstContacterRelation" />
		select
		relationId,playerId,contacterId,doubleRelation,groupIds,friendlyDegree,addTime,sendGifts,modifyTime
		from
		inst_contacter_relation where playerId = :playerId
	</sql-query>
	<sql-query name="queryContacterRelationByPlayerIdAndContacterId">
		<return class="com.game2sky.publib.dbs.model.InstContacterRelation" />
		select
		relationId,playerId,contacterId,doubleRelation,groupIds,friendlyDegree,addTime,sendGifts,modifyTime
		from
		inst_contacter_relation where playerId = :playerId and contacterId
		=:contacterId
	</sql-query>
	<!-- 联系人结束 -->
	<!-- 跨服联系人开始 -->
	<sql-query name="queryContacterKuafuRelationByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstContacterKuafuRelation" />
		select
		relationId,playerId,friendId,friendServerId,friendName,friendLevel,friendHeadIcon,addTime,friendRole,lastLoginTime,fightPower,modifyTime
		from
		inst_contacter_kuafu_relation where playerId = :playerId
	</sql-query>
	<sql-query name="queryContacterKuafuRelationByPlayerIdAndContacterId">
		<return class="com.game2sky.publib.dbs.model.InstContacterKuafuRelation" />
		select
		relationId,playerId,friendId,friendZoneId,friendName,friendLevel,friendHeadIcon,addTime,friendRole,lastLoginTime,fightPower,modifyTime
		from
		inst_contacter_kuafu_relation where playerId = :playerId and
		friendId
		=:friendId
	</sql-query>
	<!-- 跨服联系人结束 -->
	<!-- 聊天开始 -->
	<sql-query name="queryContacterChatByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstContacterChat" />
		select
		chatId,playerId,fromId,toId,contentType,content,addTime,modifyTime from
		inst_contacter_chat where playerId = :playerId and fromId=:fromId
		order by addTime ASC
	</sql-query>
	<sql-query name="queryContacterChatConutByPlayerId">
		select count(chatId) from inst_contacter_chat
		where playerId =
		:playerId
	</sql-query>
	<sql-query name="queryTopContacterChatByPlayerIdAndMaxNum">
		<return class="com.game2sky.publib.dbs.model.InstContacterChat" />
		select chatId,playerId,fromId,toId,contentType,content,addTime,modifyTime from
		inst_contacter_chat where playerId = :playerId order by addTime DESC
		limit :maxNum,:maxNum+1
	</sql-query>
	<sql-query name="deleteTopContacterChatByAddTime">
		delete from inst_contacter_chat where
		addTime
		&lt; :addTime
	</sql-query>
	<sql-query name="getChatGroups">
		<return class="com.game2sky.publib.dbs.model.InstChatGroup" />
		select
		chatGroupId,masterId,name,notice,members,onlyMasterInvite,addTime,modifyTime from
		inst_chat_group order by addTime DESC
	</sql-query>
	<!-- 聊天结束 -->
	<sql-query name="queryPlayerMailByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstPlayerMail" />
		select playerId,mailId,mailDictId,title,content,sender,itemBases,items,mailStatus,sendTime,senderPlayerId,modifyTime 
		from inst_player_mail where playerId = :playerId and mailStatus != 3
	</sql-query>
	<sql-query name="querySystemMarket">
		<return class="com.game2sky.publib.dbs.model.InstSystemMarketItem" />
		select itemDictId,defaultPrice,modifyTime
		from inst_systemmarket_item
	</sql-query>
	<sql-query name="queryUnGainDealingList">
		<return class="com.game2sky.publib.dbs.model.InstFaceToFaceDealing" />
		select
		orderId,sellerId,sellerName,buyerId,buyerName,dealingTime,itemId,price,state,gainMoneyTime,lockTime,unlockTime,itemInfo,gainMoney,middlePrice,defaultMaxPrice,itemLevel,suspiciousRate,itemName,needCheck,modifyTime
		from inst_facetoface_dealings where sellerId = :playerId and state BETWEEN 2 and 3
	</sql-query>
	<sql-query name="queryAllPlayerShops">
		<return class="com.game2sky.publib.dbs.model.InstPlayerShop" />
		select
		playerId,shopName,funds,checkingFunds,createTime,sellMax,lastMaintainTime,modifyTime
		from inst_player_shop
	</sql-query>
	<sql-query name="queryAllPlayerShopCommodities">
		<return class="com.game2sky.publib.dbs.model.InstPlayerShopCommodity" />
		select
		commodityId,playerId,itemId,price,initialCanSellCount,curCount,itemInfo,sellStartTime,state,isPrecious,needCheck,modifyTime
		from inst_player_shop_commodity where state BETWEEN 1 and 3 
	</sql-query>
	<sql-query name="queryPlayerShopDealings">
		<return class="com.game2sky.publib.dbs.model.InstPlayerShopDealing" />
		select
		orderId,commodityId,buyCount,buyTime,orderState,lockTime,unlockTime,unlockReason,tax,sellerId,buyerId,reason,price,itemId,isPrecious,middlePrice,defaultMaxPrice,itemLevel,suspiciousRate,itemName,serverId,modifyTime
		from inst_player_shop_dealing where sellerId = :playerId and
		orderState != 1
	</sql-query>
	
	<sql-query name="getAwardPlayersByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstAwardPlayer" />
		select  id, playerId, type, checkTime, state, awards, addTime, sendTime, mailTitleArgs, mailContentArgs,modifyTime
		  from inst_award_player a where  playerId=:playerId and  state=0 LIMIT :count
	</sql-query>
	
	<sql-query name="getAwardPlayerCountByTypeAndCheckTime">
		select count(1) from inst_award_player a where  playerId=:playerId and  type=:type and  checkTime=:checkTime
	</sql-query>
	
	<sql-query name="deleteAwardPlayers">
		delete from inst_award_player where type=:type and state=:state and addTime &lt;=:time
	</sql-query>
	
	<sql-query name="queryTypeAndCurTimeModel">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   activityType=:type and  startTime &lt;=  :curTime  and  endTime &gt;=  :curTime and  onOff=1 
	</sql-query>
	<sql-query name="queryLastTypeAndCurTimeModel">
        <return class="com.game2sky.publib.dbs.model.ActivityConfig" />
        select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   activityType=:type and  startTime &lt;=  :curTime  and  onOff=1  order by startTime DESC
    </sql-query>
	<sql-query name="queryAllActivityByCurTime">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where  startTime &lt;=  :curTime  and  endTime &gt;=  :curTime and  onOff=1 
	</sql-query>
	<sql-query name="queryAllActivityTaskNotDone">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   taskState != 111 and  onOff=1 
	</sql-query>
	<sql-query name="queryAllActivityNotOpenByCurTime">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   activityType=:type and  startTime &gt;  :curTime and  onOff=1 
	</sql-query>
	<sql-query name="queryLastActivityHasEnded">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   activityType=:type and  endTime &lt;  :curTime
	</sql-query>
	<sql-query name="queryAllActivityByType">
		<return class="com.game2sky.publib.dbs.model.ActivityConfig" />
		select  activityId,  activityType, startTime, endTime, rewardEndTime, onOff, taskState,expireItems,imageUrl,modifyTime from activity_config a where   activityType=:type
	</sql-query>
	
	
	<sql-query name="queryAllName">
		select nickName from inst_player
	</sql-query>
	<sql-query name="queryByName">
		select nickName from inst_player where nickName=:nickName
	</sql-query>
	<sql-query name="queryAllInstIdCounter">
		<return class="com.game2sky.prilib.dbs.model.InstIdCounter" />
		select id,zoneId,type,nextStepVal from inst_id_counter
	</sql-query>
	<sql-query name="getSysDbVersion">
		<return class="com.game2sky.publib.dbs.model.InstSysDbVersion" />
		select version,modifyTime from inst_sys_db_version
	</sql-query>
	<sql-query name="queryServerMailByTime">
		<return class="com.game2sky.publib.dbs.model.InstServerMail" />
		select
		 mailId, serverId, channelIds, sendTime, deadTime, closed, mailDictId, title, content, sender, itemBases, items, awardId, modifyTime
		from inst_server_mail a where deadTime &gt; :deadTime
	</sql-query>
	<sql-query name="queryServerMailReceivedRecordByPlayerId">
		<return class="com.game2sky.publib.dbs.model.InstServerMailReceivedRecord" />
		select guid,  playerId,  serverMailId,  receivedTime,modifyTime from inst_server_mail_received_record a where  playerId = :playerId
	</sql-query>
	<sql-query name="queryAllBlockPlayer">
		<return class="com.game2sky.publib.dbs.model.InstBlock" />
		SELECT playerId,blockStartTime,blockEndTime,blockReason,blockDesc,modifyTime from inst_block where blockEndTime &gt;= :time
	</sql-query>
	<sql-query name="deleteMail">
		delete from inst_player_mail where
		sendTime
		&lt; :sendTime or mailStatus = 3
	</sql-query>
	<sql-query name="queryGlobalShopLimitData">
		<return class="com.game2sky.publib.dbs.model.InstGlobalShopLimitData" />
		SELECT goodsIndex,refreshTime,curTimes,playerInfo,modifyTime FROM inst_globalshop_limitdata
	</sql-query>
</hibernate-mapping>