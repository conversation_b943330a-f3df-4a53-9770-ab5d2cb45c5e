package com.game2sky.prilib.benfu.logic.system.controller;

import com.alibaba.fastjson.JSON;
import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.player.FormationItem;
import com.game2sky.prilib.communication.game.player.FormationTypeEnum;
import com.game2sky.prilib.communication.game.player.PlayerFormation;
import com.game2sky.prilib.communication.game.struct.ItemBaseType;
import com.game2sky.prilib.core.dict.domain.DictHero;
import com.game2sky.prilib.core.dict.domain.DictSkill;
import com.game2sky.prilib.core.redis.RedisManager;
import com.game2sky.prilib.core.socket.logic.activity.monthlySignIn.ActivityMonthlySignInModel;
import com.game2sky.prilib.core.socket.logic.base.InstIdService;
import com.game2sky.prilib.core.socket.logic.constant.InstIdType;
import com.game2sky.prilib.core.socket.logic.hero.HeroModel;
import com.game2sky.prilib.core.socket.logic.hero.IHeroManager;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.login.LoginService;
import com.game2sky.prilib.dbs.dao.InstCurrencyDao;
import com.game2sky.prilib.dbs.dao.InstFormationDao;
import com.game2sky.prilib.dbs.dao.InstHeroDao;
import com.game2sky.prilib.dbs.model.InstCurrency;
import com.game2sky.prilib.dbs.model.InstFormation;
import com.game2sky.prilib.dbs.model.InstHero;
import com.game2sky.prilib.redis.RedisDao;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.game.item.BagType;
import com.game2sky.publib.communication.game.struct.ItemBase;
import com.game2sky.publib.communication.game.test.CSGm;
import com.game2sky.publib.communication.game.test.CSTestStress;
import com.game2sky.publib.communication.game.test.SCTestStress;
import com.game2sky.publib.dbs.dao.InstContainerDataDao;
import com.game2sky.publib.dbs.dao.InstPlayerDao;
import com.game2sky.publib.dbs.model.InstContainerData;
import com.game2sky.publib.dbs.model.InstPlayer;
import com.game2sky.publib.dbs.model.InstShopLimitData;
import com.game2sky.publib.dict.domain.DictItem;
import com.game2sky.publib.dict.domain.DictSceneNavMesh;
import com.game2sky.publib.framework.common.http.AMHttp;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.socket.logic.commonshop.ShopLimitManager;
import com.game2sky.publib.socket.logic.currency.CurrencyModel;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.item.IContainerManager;
import com.game2sky.publib.socket.logic.item.model.BaseContainer;
import com.game2sky.publib.socket.logic.item.model.ContainerOperating;
import com.game2sky.publib.socket.logic.item.model.ItemBag;
import org.apache.commons.httpclient.HttpException;
import org.apache.mina.util.ConcurrentHashSet;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.publib.framework.web.base.BaseController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;

@Controller
public class TestController extends BaseController {
	
//	@SuppressWarnings("unchecked")
//	@RequestMapping(path = "/analyseData/{sId}")
//	// 收集数据
//	public ModelAndView analyseData(@PathVariable int sId) {
//		// http://127.0.0.1:22302/analyseData/7001;
//		/*<sql-query name="queryAllPlayer">
//		<return class="com.game2sky.publib.dbs.model.InstPlayer" />
//		SELECT * from inst_player where serverId &gt;= :sId and level &gt;= :playerLevel
//		</sql-query>*/
//		int zoneId = 43;
//		int playerLevel = 50;
//		InstPlayerDao instPlayerDao = Globals.getDbsDataSource().getBenfuDao(InstPlayerDao.class,
//			zoneId);
//		List<Map<String, String>> dataList = new ArrayList<Map<String,String>>();
//		String query = "queryAllPlayer";
//		List<InstPlayer> list = instPlayerDao.getDbService().findByNamedQueryAndNamedParam(query, new String[] { "sId", "playerLevel" },
//			new Object[] { sId , playerLevel}, "player");
//		System.err.println("线上玩家数量：" + list.size());
//		for (InstPlayer instPlayer : list) {
//			Map<String, String> map = new HashMap<String, String>();
//			Long playerId = instPlayer.getPlayerId();
//			InstContainerDataDao containerDataDao = Globals.getDbsDataSource().getBenfuDao(InstContainerDataDao.class,
//				zoneId);
//			InstContainerData instContainerData = containerDataDao.get(playerId);
//			int chuji = 0;
//			int zhongji = 0;
//			int gaoji = 0;
//			int aoyi = 0;
//			int gutong = 0;
//			if (instContainerData != null) {
//				ItemBag itemBag = JsonUtils.jacksonParseString(instContainerData.getItemBagData(), ItemBag.class);
//				chuji = itemBag.getItemTotalNumber(212007);
//				zhongji = itemBag.getItemTotalNumber(213007);
//				gaoji = itemBag.getItemTotalNumber(214007);
//				aoyi = itemBag.getItemTotalNumber(215007);
//				gutong = itemBag.getItemTotalNumber(214005);
//			}
//			InstCurrencyDao currencyDao = Globals.getDbsDataSource().getBenfuDao(InstCurrencyDao.class,
//				zoneId);
//			InstCurrency instCurrency = currencyDao.get(playerId);
//			CurrencyModel model = JsonUtils.S2O(instCurrency.getCurrency(), CurrencyModel.class);
//			map.put("playerId", String.valueOf(playerId));
//			map.put("name", instPlayer.getNickName());
//			map.put("level", String.valueOf(instPlayer.getLevel()));
//			map.put("buleDimond", String.valueOf(model.getCurrencyValue(ItemBaseType.COIN1)));
//			map.put("coin", String.valueOf(model.getCurrencyValue(ItemBaseType.COIN3)));
//			map.put("chuji", String.valueOf(chuji));
//			map.put("zhongji", String.valueOf(zhongji));
//			map.put("gaoji", String.valueOf(gaoji));
//			map.put("aoyi", String.valueOf(aoyi));
//			map.put("gutong", String.valueOf(gutong));
//
//			// 阵容相关
//			InstFormationDao instFormationDao = Globals.getDbsDataSource().getBenfuDao(InstFormationDao.class,
//				zoneId);
//			InstHeroDao instHeroDao = Globals.getDbsDataSource().getBenfuDao(InstHeroDao.class,
//				zoneId);
//
//			InstFormation instFormation = instFormationDao.get(playerId);
//			if (instFormation ==null) {
//				continue;
//			}
//			List<PlayerFormation> formationInfos = JsonUtils.S2O(instFormation.getFormationInfos(), new TypeReference<ArrayList<PlayerFormation>>() {});
//			HashMap<Integer, Integer> formationIndexMap = JsonUtils.S2O(instFormation.getFormationIndexMap(),new TypeReference<HashMap<Integer,Integer>>() {});
//			Integer index = formationIndexMap.get(FormationTypeEnum.SOLO.value());
//			PlayerFormation playerFormation = formationInfos.get(index);
//			List<FormationItem> formationItems = playerFormation.getFormationItems();
//			for (int i = 0; i < formationItems.size(); i++) {
//				long heroGuid = formationItems.get(i).getHeroGuid();
//				InstHero instHero = instHeroDao.get(heroGuid);
//				if (instHero != null) {
//					map.put("heroLevel" + (i+1), String.valueOf(instHero.getLevel()));
//					map.put("heroBreach" + (i+1), String.valueOf(instHero.getBreach()));
//				}
//			}
//			dataList.add(map);
//		}
//
//		try {
//			writeToExcel("E:/工作/数据统计/GW/GW_DATA.xls", "Sheet1", dataList);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return returnString("OK");
//	}
//
//	   /**
//     * 往excel中写入(已存在的数据无法写入).
//     * @param fileDir    文件路径
//     * @param sheetName  表格索引
//     * @param object
//     * @throws Exception
//     */
//	public static void writeToExcel(String fileDir, String sheetName, List<Map<String, String>> mapList) throws Exception {
//		// 创建workbook
//		File file = new File(fileDir);
//		HSSFWorkbook workbook = null;
//		try {
//			workbook = new HSSFWorkbook(new FileInputStream(file));
//		} catch (FileNotFoundException e) {
//			e.printStackTrace();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
//		// 流
//		FileOutputStream out = null;
//		HSSFSheet sheet = workbook.getSheet(sheetName);
//		// 获取表格的总行数
//		// int rowCount = sheet.getLastRowNum() + 1; // 需要加一
//		// 获取表头的列数
//		int columnCount = sheet.getRow(0).getLastCellNum();
//		try {
//			// 获得表头行对象
//			HSSFRow titleRow = sheet.getRow(0);
//			if (titleRow != null) {
//				for (int rowId = 0; rowId < mapList.size(); rowId++) {
//					Map map = mapList.get(rowId);
//					HSSFRow newRow = sheet.createRow(rowId + 1);
//					for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) { // 遍历表头
//						String mapKey = titleRow.getCell(columnIndex).toString().trim().toString().trim();
//						HSSFCell cell = newRow.createCell(columnIndex);
//						cell.setCellValue(map.get(mapKey) == null ? null : map.get(mapKey).toString());
//					}
//				}
//			}
//
//			out = new FileOutputStream(fileDir);
//			workbook.write(out);
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			try {
//				out.close();
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//		}
//	}
//
//	@Autowired
//	LoginService loginService;
//
//	@RequestMapping(path = "/calculateSize")
//	public ModelAndView calculateSize() {
//		Set<AbstractHuman> allHuman = Globals.getAllHuman();
//		for (AbstractHuman abstractHuman : allHuman) {
//			Human human = (Human) abstractHuman;
//			CSGm gm = new CSGm("stressTestSQL");
//			Message message = Message.build(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
//			Globals.getMsgProcessDispatcher().put(message);
//		}
//		return returnString("ok");
//	}
//
//	// 外网短连接
//	@RequestMapping(path = "/addItemToAllHuman")
//	public ModelAndView addItemToAllHuman() {
//		Set<AbstractHuman> humans = Globals.getAllHuman();
//		for (AbstractHuman human : humans) {
//			CSGm gm = new CSGm("stressTestGMContainer");
//			Message message = Message.build(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
//			Globals.getMsgProcessDispatcher().put(message);
//		}
//		return returnJSON("success!");
//	}
//
//	@RequestMapping(path = "/allPlayerHeroLvLup")
//	public ModelAndView allHumanHeroLevelUp() {
//		Set<AbstractHuman> humans = Globals.getAllHuman();
//		for (AbstractHuman human : humans) {
//			human.getPlayerManager().addExp(999999999);
//			Human human2 = (Human) human;
//			Collection<DictHero> allHeroes = DictHero.getAllHeros();
//			IHeroManager heroManager = human2.getHeroManager();
//			for (DictHero hero : allHeroes) {
//				if (hero.getHeroId() < 113010	&& (hero.getModelId() != 0)
//					&& (null != DictSkill.getStartSkillByHeroId(hero.getHeroId()))) {
//					heroManager.addOneHero(hero.getHeroId());
//				}
//			}
//			Map<Long, HeroModel> heroMap = heroManager.getHeroMap();
//			for (Map.Entry<Long, HeroModel> heroModelEntry : heroMap.entrySet()) {
//				HeroModel heroModel = heroModelEntry.getValue();
//				heroManager.addExp(heroModel.getGuid(), 999999999);
//			}
//		}
//		return returnString("all hero level up success!");
//	}
//
//	@RequestMapping(path = "/allHumanContainerFull")
//	public ModelAndView allHumanContainerFull() {
//		Set<AbstractHuman> humans = Globals.getAllHuman();
////		Map<Integer, DictItem> itemMap = DictItem.getAllItems();
//		Collection<DictItem> allItems = DictItem.getAllItems();
//
//		List<ItemBase> items = new ArrayList<ItemBase>();
////		for (Map.Entry<Integer, DictItem> dictItemEntry : itemMap.entrySet()) {
////			int itemId = dictItemEntry.getValue().getItemId();
////			int itemType = dictItemEntry.getValue().getItemType();
////			ItemBase item = new ItemBase();
////			item.setId(itemId);
////			item.setType(itemType);
////			item.setNum(1);
////			items.add(item);
////		}
//
//		for (DictItem dictItemEntry : allItems) {
//			int itemId = dictItemEntry.getItemId();
//			int itemType = dictItemEntry.getItemType();
//			ItemBase item = new ItemBase();
//			item.setId(itemId);
//			item.setType(itemType);
//			item.setNum(1);
//			items.add(item);
//		}
//
//		for (AbstractHuman human : humans) {
//			IContainerManager containerManager = human.getContainerManager();
//			BaseContainer baseContainer = containerManager.getContainer(BagType.COMMON_STORAGE_TWO.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_STORAGE_TWO.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_STORAGE_THREE.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_STORAGE_THREE.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			baseContainer.addItemBases(items);
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_STORAGE_FOUR.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_STORAGE_FOUR.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_STORAGE_FIVE.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_STORAGE_FIVE.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_TASK_BAG.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_TASK_BAG.value());
//			}
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_NORMAL_BAG.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_NORMAL_BAG.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			baseContainer = containerManager.getContainer(BagType.COMMON_STORAGE_ONE.value());
//			if (baseContainer == null) {
//				baseContainer = containerManager.addNewContainer(BagType.COMMON_STORAGE_ONE.value());
//			}
//			baseContainer.addNewCell(baseContainer.getMaxSize() - baseContainer.getSize());
//			for (ItemBase item : items) {
//				ContainerOperating ret = baseContainer.addItem(item);
//				if (ret.getResult() != ContainerOperating.SUCCESS) {
//					break;
//				}
//			}
//
//			for (BagType bagType : BagType.values()) {
//				containerManager.modifiedAutoRefresh(bagType.value(), true);
//			}
//		}
//		return returnString("all human container full!");
//	}
//
//	@RequestMapping(path = "b")
//	public ModelAndView b(Channel channel) {
//		int zoneId = 1;
//		long playerId = 100010001483L;
//		AbstractHuman human = Globals.getHuman(zoneId, playerId);
//
////		sceneClientCenter.switchBindKuafu(human);
//		return returnJSON("");
//	}
//
//	@RequestMapping(path = "a")
//	public ModelAndView a(HttpServletRequest request) {
//		int zoneId = 1;
//		int sceneMapId = 1;
//		// 取负载较轻的线
//		RedisDao redisDao = RedisManager.getRedisDao(zoneId);
//		try {
//			List<String> zGetByScore = redisDao.zGetByScore(
//				RedisKeyManager.getLoadLine(CoreBoot.getMemoryDataKey(zoneId), sceneMapId), 0, 80, String.class);
//			return returnJSON(zGetByScore);
//		} catch (Exception e) {
//			AMLog.LOG_ERROR.error("", e);
//		}
//		return returnJSON("");
//	}
//
//	@ProtoStuffMapping(PSEnum.CSTestStress)
//	public void csTestStress(CSTestStress cs, Head head, AbstractHuman self) {
//
//		String jsonString = JSON.toJSONString(cs);
//		SCTestStress parseObject = JSON.parseObject(jsonString, SCTestStress.class);
////		for (int i = 0; i < 50; i++) {
//		self.push2Gateway(parseObject);
////		}
//	}
//
//	@RequestMapping(value = "/testLog", method = RequestMethod.GET)
//	public ModelAndView testLog(HttpServletRequest request) {
//		AMLog.LOG_COMMON.info("11111.stdout.info");
//		AMLog.LOG_COMMON.debug("11111.stdout.debug");
//		AMLog.LOG_COMMON.warn("11111.stdout.warn");
//		AMLog.LOG_COMMON.error("11111.stdout.error");
//		return returnString("success");
//	}
//
//	@RequestMapping(value = "/testLog1", method = RequestMethod.GET)
//	public ModelAndView testLog1(HttpServletRequest request) throws InterruptedException {
////		Map<String, String> paramsMap = getParamsMap(request);
////		final Integer thread = Integer.valueOf(paramsMap.get("thread"));
////		final Integer times = Integer.valueOf(paramsMap.get("times"));
//
////        CoreBoot.execute(new Runnable() {
////            @Override
////            public void run() {
////                NioEventLoopGroup nioEventLoopGroup = new NioEventLoopGroup(thread);
////                final CountDownLatch countDownLatch = new CountDownLatch(thread);
////                long startTime = System.currentTimeMillis();
////                for (int i = 0; i < thread; i++) {
////                    final int threadn = i;
////                    nioEventLoopGroup.execute(new Runnable() {
////                        @Override
////                        public void run() {
////                            for (int j = 0; j < times; j++) {
////                                long currentTimeMillis = System.currentTimeMillis();
////                                AMLog.LOG_COMMON.info(currentTimeMillis + "  " + threadn);
////                                AMLog.LOG_BI.info(currentTimeMillis + "  " + threadn);
////                                AMLog.LOG_ERROR.error(currentTimeMillis + "  " + threadn);
//////								AMLog.LOG_HTTP.info(currentTimeMillis + "  " + threadn);
//////								AMLog.LOG_INTERFACE.info(currentTimeMillis + "  " + threadn);
//////								AMLog.LOG_SQL.info(currentTimeMillis + "  " + threadn);
//////								AMLog.LOG_RESOURCE.info(currentTimeMillis + "  " + threadn);
////                            }
////                            countDownLatch.countDown();
////                        }
////                    });
////                }
////                try {
////                    countDownLatch.await();
////                    long endTime = System.currentTimeMillis();
////                    AMLog.LOG_COMMON.info(" log test " + thread + " thread run " + times + " times evetyone cost time " + (endTime - startTime));
////                } catch (InterruptedException e) {
////                    AMLog.LOG_COMMON.error("", e);
////                } finally {
////                    nioEventLoopGroup.shutdownNow();
////                }
////            }
////        });
//
//		return returnString("success");
//	}
//
//	@RequestMapping(value = "/testPathFinder", method = RequestMethod.GET)
//	public ModelAndView testPathFinder(HttpServletRequest request) {
//		ServerGraph graph = DictSceneNavMesh.getCacheMap().get(1);
//		long serBegin = System.nanoTime();
//		PathFinderService pathFinderService = new PathFinderService();
//		pathFinderService.init(graph);
//		Vector2f startPos = new Vector2f(18.17f, 5.556667f);
//		Vector2f endPos = new Vector2f(1.41f, 7.066667f);
//		long timeBegin = System.nanoTime();
//		ArrayList<Vector2f> wayPoints = pathFinderService.smoothPath(startPos, endPos);
//		long timeEnd = System.nanoTime();
//		String retStr = new String();
//		for (Vector2f point : wayPoints) {
//			AMLog.LOG_COMMON.info(point.toString());
//			retStr += point.toString() + "<br>";
//		}
//		retStr += "Service Initialize Time: " + (timeBegin - serBegin) + " nanoseconds.<br>";
//		retStr += "Smooth Time: " + (timeEnd - timeBegin) + " nanoseconds.<br>";
//		retStr += "Service Init+Smooth Time: " + (timeEnd - serBegin) + " nanoseconds.<br>";
//		return returnString(retStr);
//	}
//
//	/**
//	 * <p>根据URL参数测试多边形寻径</p>
//	 * <p>配合http_load做随机参数的并发测试</p>
//	 *
//	 * @param beginID 起点凸多边形
//	 * @param endID   终点凸多边形
//	 * @param request GET请求
//	 * @return 路径字符串
//	 */
//	@RequestMapping(value = "/testPathFinderByURL/{beginID},{endID}", method = RequestMethod.GET)
//	public ModelAndView testPathFinderByURL(@PathVariable int beginID, @PathVariable int endID,
//											HttpServletRequest request) {
//		long serBegin = System.nanoTime();
//		ServerGraph graph = DictSceneNavMesh.getCacheMap().get(1);
//		PathFinderService pathFinderService = new PathFinderService();
//		pathFinderService.init(graph);
//		Vector2f startPos = graph.getPolygons()[beginID].getCenter();
//		Vector2f endPos = graph.getPolygons()[endID].getCenter();
//		long timeBegin = System.nanoTime();
//		AMLog.LOG_COMMON.info("IDs:" + beginID + "," + endID);
//		ArrayList<Vector2f> wayPoints = pathFinderService.smoothPath(startPos, endPos);
//		long timeEnd = System.nanoTime();
//		String retStr = new String();
//		for (Vector2f point : wayPoints) {
//			AMLog.LOG_COMMON.info(point.toString());
//			retStr += point.toString() + "<br>";
//		}
//		retStr += "Service Initialize Time: " + (timeBegin - serBegin) / 1000000.0f + " milliseconds.<br>";
//		retStr += "Smooth Time: " + (timeEnd - timeBegin) / 1000000.0f + " milliseconds.<br>";
//		retStr += "Service Init+Smooth Time: " + (timeEnd - serBegin) / 1000000.0f + " milliseconds.<br>";
//		AMLog.LOG_COMMON.info("Service Init+Smooth Time: " + (timeEnd - serBegin) / 1000000.0f + " milliseconds.\n");
//		return returnString(retStr);
//	}
//
//	///////////// 场景测试：：：：：：：：：：：：
//
//	/**
//	 * 根据URL获取LineCast信息
//	 *
//	 * @param beginID 	起点
//	 * @param endID 	终点
//	 * @param request 	httpRequest
//	 * @return 是否可达
//	 */
//	@RequestMapping(value = "/testLineCastByURL/{beginID},{endID}", method = RequestMethod.GET)
//	public ModelAndView testLineCastByURL(	@PathVariable int beginID, @PathVariable int endID,
//											HttpServletRequest request) {
//		long serBegin = System.nanoTime();
//		ServerGraph graph = DictSceneNavMesh.getCacheMap().get(3);
//		LineCastService lcs = new LineCastService();
//		lcs.init(graph);
//		Vector2f startPos = graph.getPolygons()[beginID].getCenter();
//		Vector2f endPos = graph.getPolygons()[endID].getCenter();
//		boolean ret = lcs.castLineLogic(startPos, endPos);
//		startPos = startPos.plus(new Vector2f(graph.getShiftX(), graph.getShiftY()));
//		endPos = endPos.plus(new Vector2f(graph.getShiftX(), graph.getShiftY()));
//		ret = graph.castLineLogicWithShift(startPos, endPos);
//		long serEnd = System.nanoTime();
//		return returnString(Boolean.toString(ret) + "<br>" + "Time: " + (serEnd - serBegin));
//	}
//
//	/**
//	 *
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/testSceneInfo", method = RequestMethod.GET)
//	public ModelAndView testSceneInfo(HttpServletRequest request) {
//		String retStr = new String();
//		ServerGraph graph1 = DictSceneNavMesh.getCacheMap().get(1);
//		ServerGraph graph2 = DictSceneNavMesh.getCacheMap().get(2);
//		retStr += "场景1：<br>";
//		retStr += "偏移量：" + graph1.getShiftX() + ", " + graph1.getShiftY() + "<br>";
//		retStr += "场景长宽：" + graph1.getSceneWidth() + ", " + graph1.getSceneHeight() + "<br>";
//		ServerGraph graph3 = DictSceneNavMesh.getCacheMap().get(3);
//		Vector2f[] vector2fs = graph3.getVertexes();
//		float max = Float.MIN_VALUE;
//		float min = Float.MAX_VALUE;
//		for (Vector2f v2f : vector2fs) {
//			if (v2f.getX() > max) {
//				max = v2f.getX();
//			}
//			if (v2f.getX() < min) {
//				min = v2f.getX();
//			}
//		}
//		max += graph3.getShiftX();
//		min += graph3.getShiftX();
//		retStr += "场景中极限信息 最大：" + max + " 最小：" + min + "<br>";
//		return returnString(retStr);
//	}
//
//	///////////// 场景测试：：：：：：：：：：：： ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
//
//	// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓ 商店测试 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
//	@RequestMapping(value = "/testDBSChange", method = RequestMethod.GET)
//	public ModelAndView testDBSChange(HttpServletRequest request) {
//		InstShopLimitData limitData = new InstShopLimitData();
//		AbstractHuman human = (AbstractHuman) Globals.getHuman(42, 100420000005L);
//
//		ShopLimitManager manager = new ShopLimitManager(human);
//		manager.load();
//		manager.setModified();
//		manager.save();
//
////		InstNPCShopLimitDataDao dao = Globals.getDbsDataSource().getBenfuDao(InstNPCShopLimitDataDao.class, 42);
////		limitData.setPlayerId(100420000001L);
////		limitData.setLimitData("123456");
////		dao.update(limitData);
////		dao.get(100420000001L);
////		dao.update(limitData);
//		return returnString("update" + 1);
//	}
//	// ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 商店测试 ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
//
//	/*
//	@RequestMapping(value = "/randomName", method = RequestMethod.GET)
//		public ModelAndView refreshConfigCache(HttpServletRequest request) {
//			try {
//
//				Random random = new Random();
//				long nextLong = random.nextLong();
//				Head head = new Head();
//				head.setSid(40);
//				head.setPid(nextLong);
//
//				CSGetRandomName csGetRandomName = new CSGetRandomName();
//				csGetRandomName.setGender(1);
//				SCGetRandomName scGetRandomName = loginService.csGetRandomName(csGetRandomName, head);
//				return returnJSON(scGetRandomName.getName());
//			} catch (Exception e) {
//				e.printStackTrace();
//				return returnString("");
//			}
//		}
//		@RequestMapping(value = "/randomNameAndCreatePlayer", method = RequestMethod.GET)
//		public ModelAndView randomNameAndCreatePlayer(HttpServletRequest request) {
//			try {
//
//				Random random = new Random();
//				int nextLong = random.nextInt(1000);
//				Head head = new Head();
//				head.setSid(40);
//
//				CSGetRandomName csGetRandomName = new CSGetRandomName();
//				csGetRandomName.setGender(1);
//				SCGetRandomName scGetRandomName = loginService.csGetRandomName(csGetRandomName, head);
//
//				String name = scGetRandomName.getName();
//				AMLog.LOG_COMMON.info("name:"+name);
//				CSCreatePlayer csCreatePlayer = new CSCreatePlayer();
//				csCreatePlayer.setGender(1);
//				csCreatePlayer.setPlayerName(name);
//
//				CSPlayerLogin csUserLogin = new CSPlayerLogin();
//				csUserLogin.setPlatformUid("guohao"+nextLong);
//				csUserLogin.setZoneId(40);
//				SCPlayerLogin csUserLogin2 = loginService.csPlayerLogin(csUserLogin, head);
//				loginService.csCreatePlayer(csCreatePlayer, head);
//				loginService.refreshCanUseNames();
//
//				return returnJSON(name);
//			} catch (Exception e) {
//				e.printStackTrace();
//				return returnString("");
//			}
//		}*/
//
//
//	/**
//	 *
//	 * @param request
//	 * @return
//	 */
//	@RequestMapping(value = "/testIdService", method = RequestMethod.GET)
//	public ModelAndView testIdService(HttpServletRequest request) {
//		final Set<Long> startSet = new ConcurrentHashSet<>();
//		final Set<Long> endSet = new ConcurrentHashSet<>();
//
//		for (int i = 0; i < 10; i++) {
//			 new Thread(new Runnable() {
//				@Override
//				public void run() {
//					InstIdService instIdService = CoreBoot.getBean(InstIdService.class);
//					for (int j = 1; j <= 10000; j++) {
//						try {
//							System.out.println(j);
//							long nextId = instIdService.getNextId(40, InstIdType.PLAYER);
//							if(j == 1){
//								startSet.add(nextId);
//							}
//							if(j == 10000){
//								endSet.add(nextId);
//							}
//						} catch (Exception e) {
//							e.printStackTrace();
//						}
//					}
//				}
//			}).start();;
//		}
//		while(true){
//			if(startSet.size() == 10 && endSet.size() == 10){
//				break;
//			}
//			try {
//				Thread.sleep(100);
//			} catch (InterruptedException e) {
//			}
//		}
//		System.out.println("start"+Collections.min(startSet));
//		System.out.println("end"+Collections.max(endSet));
//		return returnString("");
//	}
	
	@RequestMapping(value = "/testActivity", method = RequestMethod.GET)
	public ModelAndView testActivity(HttpServletRequest request) throws HttpException, IOException {
		Map<String, String> paramsMap = getParamsMap(request);
		String type = paramsMap.get("type");
//		String activityId = paramsMap.get("activityId");
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("zoneId", "88");
		paramMap.put("verifyKey", "game2Sky_Acitvity_Reload");
//		paramMap.put("activityType", "1");
//		paramMap.put("activityId", activityId);
//		paramMap.put("onOff", "0");
//		paramMap.put("startTime",DateUtil.getTodayStartTime()+"");
//		paramMap.put("endTime",(DateUtil.getTodayStartTime()+31*24*3600000l)+"");
//		paramMap.put("rewardEndTime",(new Date().getTime()+31*24*3600000l)+"");
//
//
		if("1".equals(type))
		{
			List<ActivityMonthlySignInModel> datas = new ArrayList<ActivityMonthlySignInModel>();

			for(int i=1;i<=31;i++)
			{
				ActivityMonthlySignInModel shop = new ActivityMonthlySignInModel();
				shop.setId(1201707+i);
				shop.setActivityId(Long.parseLong(paramsMap.get("activityId")));
				shop.setYear(2024);
				shop.setMonth(6);
				shop.setDay(i);
				shop.setReward("1001,212001,2;1001,213001,2;1001,214001,2");

				datas.add(shop);
				if(i%5==0)
				{
					shop.setIsImportant(1);
				}
			}
			paramMap.put("data",JSON.toJSONString(datas));
			String httpPost = AMHttp.httpPost("http://127.0.0.1:22302/activity/addActivity",
				paramMap);
			return returnString(httpPost);
		}
		else if("2".equals(type))
		{
			String httpPost = AMHttp.httpPost("http://127.0.0.1:22302/activity/delActivity",
				paramMap);
			return returnString(httpPost);
		}
		else if("3".equals(type))
		{
			String httpPost = AMHttp.httpPost("http://127.0.0.1:22302/activity/turnOffActivity",
				paramMap);
			return returnString(httpPost);
		}
		else if("4".equals(type))
		{
			List<ActivityMonthlySignInModel> datas = new ArrayList<ActivityMonthlySignInModel>();

			for(int i=1;i<=31;i++)
			{
				ActivityMonthlySignInModel shop = new ActivityMonthlySignInModel();
				shop.setId(1201707+i);
				shop.setActivityId(Long.parseLong(paramsMap.get("activityId")));
				shop.setYear(2024);
				shop.setMonth(6);
				shop.setDay(i);
				shop.setReward("1001,212001,2");
				datas.add(shop);
				if(i%5==0)
				{
					shop.setIsImportant(1);
				}
			}
			paramMap.put("data",JSON.toJSONString(datas));
			String httpPost = AMHttp.httpPost("http://127.0.0.1:22302/activity/updateActivity",
				paramMap);
			return returnString(httpPost);
		}
		else if("5".equals(type))
		{
			String httpPost = AMHttp.httpPost("http://127.0.0.1:22302/activity/reloadActivity",
				paramMap);
			return returnString(httpPost);
		}
		return returnString("OK");
	}
	
}
