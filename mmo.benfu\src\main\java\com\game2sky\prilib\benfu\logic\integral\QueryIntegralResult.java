package com.game2sky.prilib.benfu.logic.integral;

/**
 *
 * <AUTHOR>
 * @version v0.1 2018年12月5日 下午8:22:07  zhoufang
 */
public class QueryIntegralResult {
	
	private long playerId;
	
	private String name;
	
	private int integral;
	
	private int riskLevel;
	
	private long bannedEndTime;
	
	private long fight;
	
	public QueryIntegralResult(){
		
	}
	
	public QueryIntegralResult(long playerId,String name,int integral){
		this.playerId = playerId;
		this.name = name;
		this.integral = integral;
	}

	public long getPlayerId() {
		return playerId;
	}

	public void setPlayerId(long playerId) {
		this.playerId = playerId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIntegral() {
		return integral;
	}
	
	public void setIntegral(int integral) {
		this.integral = integral;
	}

	public int getRiskLevel() {
		return riskLevel; 
	}

	public void setRiskLevel(int riskLevel) {
		this.riskLevel = riskLevel;
	}

	public long getBannedEndTime() {
		return bannedEndTime;
	}

	public void setBannedEndTime(long bannedEndTime) {
		this.bannedEndTime = bannedEndTime;
	}
	
	public long getFight() {
		return fight;
	}
	
	public void setFight(long fight) {
		this.fight = fight;
	}
	
}
