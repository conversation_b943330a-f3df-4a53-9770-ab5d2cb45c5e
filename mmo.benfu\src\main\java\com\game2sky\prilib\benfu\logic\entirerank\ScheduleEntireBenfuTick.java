package com.game2sky.prilib.benfu.logic.entirerank;

import com.game2sky.prilib.communication.game.entirerank.SSEntireRankBenfuTick;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 通用全服排行本服心跳.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月25日 下午4:08:22  Jet Lee
 */
public class ScheduleEntireBenfuTick extends ScheduledMessage {

	public static final int DELAY = 20000;
	
    public static final int PERIOD = 1000;
    

    public ScheduleEntireBenfuTick() {
        super(System.currentTimeMillis());
    }

    @Override
    public void execute() {
        // 这里只是发送消息
    	SSEntireRankBenfuTick tick = new SSEntireRankBenfuTick();
    	
        Message message = Message.buildByServerId(0L, 0, tick, null, null, null);
        Globals.getMsgProcessDispatcher().put(message);
    }
}
