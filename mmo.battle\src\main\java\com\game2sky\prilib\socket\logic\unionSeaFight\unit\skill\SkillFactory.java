package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFSkillType;

/**
 * 公会战技能工厂.
 *
 * <AUTHOR>
 * @version v0.1 2018年10月25日 上午8:56:56  lidong
 */
public class SkillFactory
{
	public static USFSkill createSkillByType(USFSkillType skillType)
	{
		switch (skillType)
		{
			case NORMAL_USF_SKILL:
				return new USFSkill();
			case TOWOER_BASE_USF_SKILL:
				return new USFFortressBaseSkill();
			case DURATION_USF_SKILL:
				return new DurationUsfSkill();
			case DURATION_RANDOM_USF_SKILL:
				return new DurationRandomUsfSkill();
			default:
				return new USFSkill();
		}
	}
}
