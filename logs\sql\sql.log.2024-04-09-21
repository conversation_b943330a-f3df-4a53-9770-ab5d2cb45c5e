2024-04-09 21:20:41,203 [DbLandService-woker-thread-2] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.SQLGrammarException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:67) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.player.PlayerDeviceInfoManager.updateEntity(PlayerDeviceInfoManager.java:31) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
