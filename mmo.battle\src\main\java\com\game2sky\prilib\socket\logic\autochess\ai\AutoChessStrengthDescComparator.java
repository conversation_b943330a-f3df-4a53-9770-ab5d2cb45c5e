package com.game2sky.prilib.socket.logic.autochess.ai;

import java.util.Comparator;
import java.util.Map.Entry;

import com.game2sky.prilib.socket.logic.autochess.AutoChessUtils;

/**
 * TODO 强度降序排
 *
 * <AUTHOR>
 * @version v0.1 2019年6月25日 下午5:52:52  guojijun
 */
public class AutoChessStrengthDescComparator implements Comparator<Entry<Integer, Integer>> {

	public static AutoChessStrengthDescComparator INSTANCE = new AutoChessStrengthDescComparator();

	@Override
	public int compare(Entry<Integer, Integer> o1, Entry<Integer, Integer> o2) {
		int chessId1 = o1.getValue();
		int chessId2 = o2.getValue();
		int buyStrength1 = AutoChessUtils.getStrength(chessId1);
		int buyStrength2 = AutoChessUtils.getStrength(chessId2);
		int diff = buyStrength2 - buyStrength1;
		if (diff != 0) {
			return diff;
		}
		int rare1 = AutoChessUtils.getRare(chessId1);
		int rare2 = AutoChessUtils.getRare(chessId2);
		diff = rare2 - rare1;
		if (diff != 0) {
			return diff;
		}
		return chessId1 - chessId2;
	}

}
