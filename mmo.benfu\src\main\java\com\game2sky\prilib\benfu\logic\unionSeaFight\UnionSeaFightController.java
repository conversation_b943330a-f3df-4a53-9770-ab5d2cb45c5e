package com.game2sky.prilib.benfu.logic.unionSeaFight;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.unionSeaFight.CSLookUSFReplayList;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFActivity;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFActivityApplyInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFBattleRoundInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFGetOtherFormation;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFGetRankInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFLookFightReplay;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFOneKeySetFightList;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFPlayerAutoJoin;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFPlayerJoin;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFSetFightPlayer;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFSetPromotions;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFUnionApply;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFUnionAutoApply;
import com.game2sky.prilib.communication.game.unionSeaFight.SSBenfuUSFActivitySchedule;
import com.game2sky.prilib.communication.game.unionSeaFight.SSGetUSFUnionInfoReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSSendUSFUnionMatchResult;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFActivityOper;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCenter2BenfuStage;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCreateRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFFightAllotReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFFightResult;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncActivityData2Benfu;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 中心服的海战消息处理
 * <AUTHOR>
 *
 */
@Controller
public class UnionSeaFightController extends BaseController {

	@Autowired
	BenfuUnionSeaFightService seaFightService;

	
	/**
	 * 收到中心服的同步海战信息（本服重启时的消息）
	 * @param cs
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFSyncActivityData2Benfu)
	public void ssUSFSyncActivityData2Benfu(SSUSFSyncActivityData2Benfu cs) {
		seaFightService.syncActivityData2Benfu(cs);
	}
	
	/**
	 * 获取海战活动信息
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFActivity)
	public void csUSFActivity(CSUSFActivity cs, Human human) {
		seaFightService.getUnionSeaFightActivity(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFActivityApplyInfo)
	public void csUSFActivityApplyInfo(CSUSFActivityApplyInfo cs, Human human) {
		seaFightService.getUSFActivityApplyInfo(human);
	}

	/**
	 * 海战：会长自动报名
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFUnionAutoApply)
	public void csUSFUnionAutoApply(CSUSFUnionAutoApply cs, Human human) {
		seaFightService.doUnionSeaFightAutoApply(human, cs.getUnionAutoApply());
	}

	/**
	 * 海战：会长报名
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFUnionApply)
	public void csUSFUnionApply(CSUSFUnionApply cs, Human human) {
		seaFightService.doUnionSeaFightApply(human);
	}

	/**
	 * 海战：玩家参赛
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerJoin)
	public void csUSFPlayerJoin(CSUSFPlayerJoin cs, Human human) {
		seaFightService.doPlayerJoinUSFActivity(human);
	}

	/**
	 * 海战：玩家自动参赛
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerAutoJoin)
	public void csUSFPlayerAutoJoin(CSUSFPlayerAutoJoin cs, Human human) {
		seaFightService.doPlayerAutoJoinUSFActivity(human, cs.getPlayerAutoJoin());
	}

	/**
	 * 海战：获取战斗场次按钮
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFBattleRoundInfo)
	public void csUSFBattleRoundInfo(CSUSFBattleRoundInfo cs, Human human) {
		seaFightService.sendSCUSFBattleRoundInfo(human);
	}

	/**
	 * 海战：会长手动设置上下阵人员
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFSetFightPlayer)
	public void csUSFSetFightPlayer(CSUSFSetFightPlayer cs, Human human) {
		seaFightService.setFightPlayer(human, cs.getOnPlayerId(), cs.getOffPlayerId());
	}

	/**
	 * 海战：会长或副会长一键设置参战列表
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFOneKeySetFightList)
	public void csUSFOneKeySetFightList(CSUSFOneKeySetFightList cs, Human human) {
		seaFightService.oneKeySetFightList(human);
	}

	/**
	 * 海战产看战斗录像
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFLookFightReplay)
	public void csUSFLookFightReplay(CSUSFLookFightReplay cs, Human human) {
		seaFightService.csUSFLookFightReplay(cs,human);
	}

	/**
	 * 定时器
	 * @param msg
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSBenfuUSFActivitySchedule)
	public void ssBenfuUSFActivitySchedule(SSBenfuUSFActivitySchedule msg) {
		seaFightService.doUSFActivitySchedule(msg.getGroup());
	}

	/**
	 * 本服处理，来自中心服的消息 ： 向本服索要海战公会
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGetUSFUnionInfoReq)
	public void ssGetUSFUnionInfoReq(SSGetUSFUnionInfoReq msg) {
		seaFightService.doSSGetUSFUnionInfoReq(msg.getGroup());
	}

	/**
	 * 本服处理，来自中心服的消息 ： 分配战斗服
	 * @param cs
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFFightAllotReq)
	public void ssUSFFightAllotReq(SSUSFFightAllotReq msg) {
		seaFightService.dealUSFFightAllotReq(msg.getServerId(), msg.getFightTimeId(), msg.getFightAllotInfo());
	}

	/**
	 * 中心服给本服发送匹配结果
	 * @param msg
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSendUSFUnionMatchResult)
	public void ssSendUSFUnionMatchResult(SSSendUSFUnionMatchResult msg) {
		seaFightService.dealUSFUnionMatchResult(msg.getGroup(), msg.getServerId(), msg.getMatchResult(), msg.getFirstLeftUnionList(), msg.getSecondLeftUnionList());
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFActivityOper)
	public void ssUSFActivityOper(SSUSFActivityOper msg) {
		seaFightService.operUSFByGM(0, msg.getParams());
	}
	

	/**中心服通知本服进度*/
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFCenter2BenfuStage)
	public void sendBenfuStage(SSUSFCenter2BenfuStage msg) {
		seaFightService.sendBenfuStage(msg.getGroupId(), msg.getServerId(), msg.getStage(), msg.getUnionId());
	}
	
	
	/**战斗服通知本服创建战斗结果*/
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFCreateRes)
	public void ssUSFCreateRes(SSUSFCreateRes msg) {
		seaFightService.dealUSFCreateRes(msg.getServerId(), msg.getUnionId(), msg.getUqId(), msg.getSucc());
	}
	
	/**
	 * 玩家进入海战
	 * 
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerEnter)
	public void csUSFPlayerEnter(Human human) {
		this.seaFightService.doUSFPlayerEnter(human, false);
	}

	/**
	 * 战斗服响应玩家进入海战
	 * 
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFPlayerEnterRes)
	public void ssUSFPlayerEnterRes(Human human, SSUSFPlayerEnterRes ssUSFPlayerEnterRes) {
		this.seaFightService.doUSFPlayerEnterRes(human, ssUSFPlayerEnterRes);
	}

	/**
	 * 玩家退出海战
	 * 
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerQuit)
	public void csUSFPlayerQuit(Human human) {
		this.seaFightService.doUSFPlayerQuit(human, true);
	}

	/**
	 *  战斗服连接断开
	 * 
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFClientBattleChannelInactive)
	public void ssUSFClientBattleChannelInactive(Human human) {
		this.seaFightService.doUSFClientBattleChannelInactive(human);
	}

	/***
	 * 由战斗服推送过来的战斗结果
	 * 
	 * @param ssUSFFightResult
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFFightResult)
	public void ssUSFFightResult(SSUSFFightResult ssUSFFightResult) {
		this.seaFightService.doUSFFightResult(ssUSFFightResult);
	}

	/**
	 * 同步排行榜响应
	 * 
	 * @param ssUSFSyncRanksRes
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFSyncRanksRes)
	public void ssUSFSyncRanksRes(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		this.seaFightService.ssUSFSyncRanksRes(ssUSFSyncRanksRes);
	}
	

	/**
	 * 同步排行榜响应
	 * 
	 * @param ssUSFSyncRanksRes
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFGetRankInfo)
	public void csUSFGetRankInfo(CSUSFGetRankInfo csUSFGetRankInfo,Human human) {
		this.seaFightService.csUSFGetRankInfo(csUSFGetRankInfo,human);
	}
	
	/**
	 * 同步排行榜响应
	 * 
	 * @param ssUSFSyncRanksRes
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFGetOtherFormation)
	public void csUSFGetOtherFormation(CSUSFGetOtherFormation csUSFGetOtherFormation,Human human) {
		this.seaFightService.csUSFGetOtherFormation(csUSFGetOtherFormation,human);
	}

	/**
	 * 获取可升变数据请求
	 * 
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFGetPromotionTechInfos)
	public void csUSFGetPromotionTechInfos(Human human) {
		this.seaFightService.doUSFGetPromotionTechInfos(human);
	}
	
	/**
	 * 设置升变数据请求
	 * 
	 * @param csUSFSetPromotions
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFSetPromotions)
	public void csUSFSetPromotions(CSUSFSetPromotions csUSFSetPromotions,Human human) {
		this.seaFightService.doUSFSetPromotions(csUSFSetPromotions, human);
	}
	
	
	/**
	 * 设置升变数据请求
	 * 
	 * @param csUSFSetPromotions
	 * @param human
	 */
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLookUSFReplayList)
	public void csLookUSFReplayList(CSLookUSFReplayList csLookUSFReplayList,Human human) {
		this.seaFightService.csLookUSFReplayList(csLookUSFReplayList, human);
	}
	
}
