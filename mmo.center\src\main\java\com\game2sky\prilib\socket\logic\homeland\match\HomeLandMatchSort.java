package com.game2sky.prilib.socket.logic.homeland.match;


/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年6月1日 下午5:25:39  daixl
 */
@Deprecated
public class HomeLandMatchSort {
// extends BarrelSort<HomeLandMatchBarrel>
//	@Override
//	public HomeLandMatchBarrel createInstance(Long id) {
//		return new HomeLandMatchBarrel(id);
//	}
//
//	public void addElement(long playerId, int warValue) {
//		HomeLandMatchBarrel addElement = addElement((long) warValue);
//		addElement.addPlayerId(playerId);
//	}
//
//	public void deleteElement(long playerId, int warValue) {
//		HomeLandMatchBarrel barrel = getBarrel((long) warValue);
//		if (barrel == null) {
//			return;
//		}
//		barrel.removePlayerId(playerId);
//	}
//
//	public TreeMap<Long, HashSet<Long>> getByWarValueRegion(long upper, long lower, long warValue) {
////
////		long increase = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_WARVALUE_INCREASE_ONE_TIME);
////
////		TreeMap<Long, HashSet<Long>> result = new TreeMap<Long, HashSet<Long>>();
////		Iterator<HomeLandMatchBarrel> iterator = this.barrelList.iterator();
////		while (iterator.hasNext()) {
////			HomeLandMatchBarrel next = iterator.next();
////			long target = next.getId();
////			if (target >= lower && target <= upper) {
////				HomeLandMatchBarrel homeLandMatchBarrel = barrelMap.get(target);
////				if (homeLandMatchBarrel == null || CollectionUtils.isEmpty(homeLandMatchBarrel.getPlayerIds())) {
////					continue;
////				}
////
////				long divide = Math.abs(target - warValue);
////				long num = divide / increase;
////				long mod = divide % increase;
////				if (mod > 0) {
////					num++;
////				}
////
////				long key = num * increase;
////				if (key == 0) {
////					key = increase;
////				}
////				HashSet<Long> hashSet = result.get(key);
////				if (hashSet == null) {
////					hashSet = new HashSet<>();
////					result.put(key, hashSet);
////				}
////				hashSet.addAll(homeLandMatchBarrel.getPlayerIds());
////			}
////		}
////		return result;
//		return null;
//	}
}
