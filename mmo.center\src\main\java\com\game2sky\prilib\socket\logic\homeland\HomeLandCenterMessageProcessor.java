package com.game2sky.prilib.socket.logic.homeland;

import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.communication.MessageType;
import com.game2sky.publib.framework.thread.ExecutableMessageHandler;
import com.game2sky.publib.framework.thread.QueueMessageProcessor;
import com.game2sky.publib.framework.thread.message.IMessageProcessor;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年6月1日 上午10:27:04  daixl
 */
public class HomeLandCenterMessageProcessor implements IMessageProcessor<Message> {

	private QueueMessageProcessor mainProcessor;

	public HomeLandCenterMessageProcessor() {
		mainProcessor = new QueueMessageProcessor(new ExecutableMessageHandler(), "HomeLandCenter");
		mainProcessor.start();
		Globals.getMsgProcessDispatcher().setHomelandMsgProcessor(mainProcessor);
	}

	@Override
	public void start() {
	}

	@Override
	public void stop() {
		this.mainProcessor.stop();
	}

	@Override
	public void put(Message msg) {
		if (msg.messageType() == MessageType.HOMELAND_MSG) {
			this.mainProcessor.put(msg);
		} else {
			if (AMLog.LOG_ERROR.isErrorEnabled()) {
				AMLog.LOG_ERROR.error("HomeLandCenterMessageProcessor收到不能处理的消息,msg={}", msg);
			}
		}
	}

	@Override
	public boolean isFull() {
		return this.mainProcessor.isFull();
	}

	@Override
	public int messageQueueSize() {
		int queueSize = this.mainProcessor.messageQueueSize();
		return queueSize;
	}

	@Override
	public void printMessageQueue() {
		this.mainProcessor.printMessageQueue();
	}
}