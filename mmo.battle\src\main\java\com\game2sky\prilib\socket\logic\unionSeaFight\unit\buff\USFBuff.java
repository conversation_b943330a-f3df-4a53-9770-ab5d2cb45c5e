package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff;

import gnu.trove.map.hash.TIntObjectHashMap;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.*;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.common.HeroPropEnum;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.dict.domain.DictUSFBuff;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler.IUSFBuffHandler;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkill;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.ABC;
import com.game2sky.publib.framework.util.KV;
import com.game2sky.publib.framework.util.StringUtils;

/**
 * TODO 海战BUFF
 *
 * <AUTHOR>
 * @version v0.1 2018年10月17日 下午3:23:25  guojijun
 */
public class USFBuff implements IUSFConsts, IBattleTriggerEvent {
	private static int BUFF_ID = 1;

	private USFClientBuffInfo clientInfo;
	private USFBuffType buffType;
	private int groupId;
	private int priority;
	private boolean isClientSide;
	private boolean canRepeat;// 优先级，用于buff替换
	private boolean isShowFlyText; // 是否显示飘字效果
	// 普通战场用的buff列表
	private int[] battleBufferIds;
	/**
	 * 卸载类型
	 */
	private USFBuffTriggerType unloadType;

	// 发出buff的单位
	private AbstractUSFUnit sourceUnit;
	/** 发出buff的技能 */
	private USFSkill sourceSkill;
	// 属于的单位
	private AbstractUSFUnit ownerUnit;

	/**
	 * 触发次数
	 */
	private int triggerTimes = 0;

	private long lastEffectTime;

	public static final int HP_BUFF_ID = 100;

	private static List<Integer> CLIENT_FACTOR_ARRAY;

	static {
		CLIENT_FACTOR_ARRAY = new ArrayList<Integer>();
		CLIENT_FACTOR_ARRAY.add(HeroPropEnum.atk.value());
		CLIENT_FACTOR_ARRAY.add(HeroPropEnum.def.value());
		CLIENT_FACTOR_ARRAY.add(HeroPropEnum.speed.value());
	}

	public static List<USFBuff> createBuff(String buffIdsStr) {
		if (StringUtils.isEmpty(buffIdsStr)) {
			return null;
		}
		int[] buffIds = StringUtils.getIntList(buffIdsStr);
		if (buffIds == null || buffIds.length == 0) {
			return null;
		}
		ArrayList<USFBuff> buffs = new ArrayList<>(buffIds.length);
		HashSet<Integer> idSet = new HashSet<Integer>();
		for (int buffId : buffIds) {
			DictUSFBuff dictUSFBuff = DictUSFBuff.getDictUSFBuff(buffId);
			if (!idSet.add(dictUSFBuff.getBuffId())) {
				continue;
			}
			USFBuff buff = createBuff(dictUSFBuff);
			if (buff != null) {
				buffs.add(buff);
			}
		}
		return buffs;
	}

	public static USFBuff createBuff(DictUSFBuff dictUSFBuff) {
		USFBuff buff = new USFBuff();
		buff.SetDictData(dictUSFBuff);
		return buff;
	}

	public static ArrayList<USFClientBuffProInfo> parseBuffProInfos(int buffId, String valueEffect) {
		if (StringUtils.isEmpty(valueEffect)) {
			return null;
		}
		try {
			List<ABC<String, String, String>> list = StringUtils.parseABCString(valueEffect);
			if (CollectionUtils.isEmpty(list)) {
				return null;
			}
			ArrayList<USFClientBuffProInfo> proInfos = new ArrayList<>(list.size());
			for (ABC<String, String, String> abc : list) {
				int propId = Integer.parseInt(abc.getA());
				if (HeroPropEnum.valueOf(propId) == null && propId != HP_BUFF_ID) {
					AMLog.LOG_UNION.warn("海战BUFF属性类型错误,buffId={},属性{}.", buffId, propId);
					continue;
				}
				int addType = Integer.parseInt(abc.getB());
				if (addType != USF_BUFF_ADD_TYPE_FIXED && addType != USF_BUFF_ADD_TYPE_PERCENT) {
					AMLog.LOG_UNION.warn("海战BUFF加成类型错误,buffId={},属性{}", buffId, addType);
					continue;
				}
				float addValue = Float.parseFloat(abc.getC());
				USFClientBuffProInfo proInfo = new USFClientBuffProInfo(propId, addType, addValue);
				proInfos.add(proInfo);
//				else
//				{
//					AMLog.LOG_UNION.warn("海战BUFF加成值错误,buffId={}", dictUSFBuff.getBuffId());
//					continue;
//				}
			}
			return proInfos;
		} catch (Exception e) {
			AMLog.LOG_UNION.error("海战BUFF解析失败,buffId=" + buffId, e);
			return null;
		}
	}

	public static ArrayList<USFClientBuffExtraProInfo> parseExtraProInfos(DictUSFBuff dictUSFBuff) {
		String valueEffect = dictUSFBuff.getUsfProperties();
		if (StringUtils.isEmpty(valueEffect)) {
			return null;
		}
		try {
			List<ABC<String, String, String>> list = StringUtils.parseABCString(valueEffect);
			if (CollectionUtils.isEmpty(list)) {
				return null;
			}
			ArrayList<USFClientBuffExtraProInfo> proInfos = new ArrayList<>(list.size());
			for (ABC<String, String, String> abc : list) {
				int propId = Integer.parseInt(abc.getA());
				if (USFUnitProType.valueOf(propId) == null) {
					AMLog.LOG_UNION.warn("海战BUFF专属属性类型错误,buffId={},属性{}.", dictUSFBuff.getBuffId(), propId);
					continue;
				}
				int addType = Integer.parseInt(abc.getB());
				if (addType != USF_BUFF_ADD_TYPE_FIXED && addType != USF_BUFF_ADD_TYPE_PERCENT) {
					AMLog.LOG_UNION.warn("海战BUFF加成类型错误,buffId={},属性{}", dictUSFBuff.getBuffId(), addType);
					continue;
				}
				int addValue = Integer.parseInt(abc.getC());
				USFClientBuffExtraProInfo proInfo = new USFClientBuffExtraProInfo(propId, addType, addValue);
				proInfos.add(proInfo);
			}
			return proInfos;
		} catch (Exception e) {
			AMLog.LOG_UNION.error("海战BUFF解析失败,buffId=" + dictUSFBuff.getBuffId(), e);
			return null;
		}
	}

	public USFBuff() {
	}

	public void SetDictData(DictUSFBuff dictUSFBuff) {
		setBuffType(USFBuffType.valueOf(dictUSFBuff.getBuffType()));
		setGroupId(dictUSFBuff.getGroupId());
		setPriority(dictUSFBuff.getPriority());
		setClientSide(dictUSFBuff.getIsClientSide() == 1);
		setBattleBufferIds(StringUtils.getIntList(dictUSFBuff.getBattleBuffers()));
		canRepeat = dictUSFBuff.getCanRepeat() == 1;
		USFClientBuffInfo clientInfo = new USFClientBuffInfo();
		clientInfo.setGuid(BUFF_ID++);
		clientInfo.setBuffId(dictUSFBuff.getBuffId());
		clientInfo.setDuration(dictUSFBuff.getDuration());
		clientInfo.setProInfos(parseBuffProInfos(dictUSFBuff.getBuffId(), dictUSFBuff.getValueEffect()));
		clientInfo.setExtraProInfos(parseExtraProInfos(dictUSFBuff));
		if (dictUSFBuff.getTotalTime() != 0) {
			clientInfo.setTotalTime(dictUSFBuff.getTotalTime());
			long now = Globals.getTimeService().now();
			clientInfo.setExpiredTime(now + clientInfo.getTotalTime());
		}
//		else if(dictUSFBuff.getTotalTime() < 0)
//		{
//			clientInfo.setTotalTime(1);
//			clientInfo.setExpiredTime(USFightBattleService.getNow()
//				+ clientInfo.getTotalTime());
//		}
		this.isShowFlyText = dictUSFBuff.getIsShowFlyText() == 1;
		setClientInfo(clientInfo);
	}

	public USFClientBuffInfo originalCopyTo() {
		this.clientInfo.setSourceUid(0);
		if (this.sourceUnit != null) {
			this.clientInfo.setSourceUid(this.sourceUnit.getId());
		}
		return this.clientInfo;
	}

	public USFClientBuffInfo copyClientInfoToSend() {
		float factor = BaseService.getConfigValue(PriConfigKeyName.USF_BUFF_CLIENT_FACTOR);
		List<USFClientBuffProInfo> proInfos = new ArrayList<USFClientBuffProInfo>();
		if (clientInfo.getProInfos() != null) {
			for (USFClientBuffProInfo info : clientInfo.getProInfos()) {
				if (CLIENT_FACTOR_ARRAY.contains(info.getPropId())) {
					proInfos.add(new USFClientBuffProInfo(info.getPropId(), info.getAddType(), info.getAddValue()
																								/ factor));
				} else {
					proInfos.add(new USFClientBuffProInfo(info.getPropId(), info.getAddType(), info.getAddValue()));
				}
			}
		}
		int sourceUid = 0;
		if (this.sourceUnit != null) {
			sourceUid = sourceUnit.getId();
		}
		USFClientBuffInfo buffInfo = new USFClientBuffInfo(clientInfo.getGuid(), clientInfo.getBuffId(),
			clientInfo.getDuration(), clientInfo.getExpiredTime(), proInfos, clientInfo.getTotalTime(),
			clientInfo.getState(), sourceUid, clientInfo.getExtraProInfos());
		return buffInfo;
	}

	public void load() {
		IUSFBuffHandler handler = USFBuffFactory.getBuffHandle(buffType);
		if (handler != null) {
			try {
				handler.load(this);
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("[USFBuff] [load] [error]", e);
			}
		}
	}

	public void doEffect(long now) {
		lastEffectTime = now;
		doHPChange();
		doUsfPropertyEffect();
		// 根据buff类型，处理不同的逻辑
		// 例如1.对据点的效果（buff）有提升。需要判断目标是据点
		// 2.对某些buff的valueEffect值有影响。（白胡子船）
		doHandler();
	}

	/**
	 * buff影响的血量变化（恢复塔给上的buff等等）
	 */
	private void doHPChange() {
		List<USFClientBuffProInfo> proInfos = clientInfo.getProInfos();
		if (CollectionUtils.isEmpty(proInfos)) {
			return;
		}
		boolean isPlayer = ownerUnit instanceof USFPlayer;
		if (!isPlayer) {
			return;
		}
		USFPlayer player = (USFPlayer) ownerUnit;
		// 计算其他buff对当前buff的效果影响
		float extroValue = 0; // 额外加成值
		float extropercent = 0; // 额外加成百分比
		int buffId = clientInfo.getBuffId();
		TIntObjectHashMap<TIntObjectHashMap<KV<Integer, Float>>> affectMap = player.getFightController().getAffectMap();
		for (TIntObjectHashMap<KV<Integer, Float>> map : affectMap.valueCollection()) {
			KV<Integer, Float> kv = map.get(buffId);
			if (kv == null) {
				continue;
			}
			if (kv.getK() == USF_BUFF_ADD_TYPE_FIXED) {
				// 加成值
				extroValue += kv.getV();
			} else {
				// 加成百分比
				extropercent += kv.getV();
			}
		}
		for (USFClientBuffProInfo info : proInfos) {
			if (info.getPropId() != HP_BUFF_ID) {
				continue;
			}
			// 先加额外值，再乘以半分比
			float addValue = (info.getAddValue() + extroValue) * (1 + extropercent);
			boolean isChange = player.getFightController().changeHpByBuff(info.getAddType() == 1, addValue);
			if (!isChange || !this.isShowFlyText || this.getClientInfo().getTotalTime() < 0) {
				continue;
			}

			SCUSFUnitBuffChange scUSFUnitBuffChange = new SCUSFUnitBuffChange();
			scUSFUnitBuffChange.setUnitId(ownerUnit.getId());
			ArrayList<USFClientBuffInfo> tmpUpdateBuffs = new ArrayList<USFClientBuffInfo>();
			USFClientBuffInfo copyTo = this.copyClientInfoToSend();
			tmpUpdateBuffs.add(copyTo);
			scUSFUnitBuffChange.setBuffInfos(tmpUpdateBuffs);

			// 塔内周期性buff，只让自己看到
			boolean isDurationTower = false;
			if (sourceUnit instanceof USFFortress && unloadType == USFBuffTriggerType.LEAVE_TOWER) {
				isDurationTower = true;
			}
			if (!isDurationTower) {
				ownerUnit.getCamp().getUsFight().broadcast(scUSFUnitBuffChange);
			} else {
				player.pushMsg(scUSFUnitBuffChange);
			}
		}
	}

	protected void doUsfPropertyEffect() {
		if (getOwnerUnit().getUnitType() == USFUnitType.USF_UNIT_FORTRESS) {
			return;
		}
		List<USFClientBuffExtraProInfo> proInfos = clientInfo.getExtraProInfos();
		if (proInfos == null || proInfos.size() == 0) {
			return;
		}
		USFPlayer owner = (USFPlayer) getOwnerUnit();
		for (USFClientBuffExtraProInfo info : proInfos) {
			USFUnitProType proType = USFUnitProType.valueOf(info.getPropId());
			if (proType == null) {
				continue;
			}
			switch (proType) {
//				case USF_UNIT_PRO_SPEED:
//					break;
				case USF_UNIT_PRO_DIVE_NUMBER:
					if (info.getAddType() == USF_BUFF_ADD_TYPE_FIXED) {
						owner.addDiveNumber(info.getAddValue());
					} else {
						int num = owner.getCouldDiveNumber();
						if (num > 0) {
							int n = (int) (info.getAddValue() / 100.0 * num);
							owner.addDiveNumber(n);
						}
					}
					break;
//				case USF_UNIT_PRO_DIVE_STATE:
//					break;
				case USF_UNIT_PRO_ROB_STATE:
					owner.setRobState(info.getAddValue());
					break;
				case USF_UNIT_PRO_ROB_PROB:
					owner.setRobProb(info.getAddValue());
					break;
				default:
					break;
			}
		}
	}

	/**
	 * 根据buff类型做不同的实现
	 */
	private void doHandler() {
		IUSFBuffHandler handler = USFBuffFactory.getBuffHandle(buffType);
		if (handler != null) {
			try {
				handler.handler(this);
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("[USFBuff] [doHandler] [error]", e);
			}
		}
	}

	public boolean isOwnerClosed() {
		return sourceSkill.isClosed();
	}

	public boolean isArriveDurationTime(long now) {
		int duration = clientInfo.getDuration();
		int totalTime = clientInfo.getTotalTime();
		// duration大于0必然是周期型buff，如果total小于，是瞬发buff，需要在tick中触发，其实在load中触发也是可以的
		if ((duration > 0 || totalTime < 0) && now - lastEffectTime > duration) {
			return true;
		}
		return false;
	}

	public boolean isTimeOver(long now) {
		if (clientInfo.getTotalTime() != 0 && this.getExpiredTime() <= now) {
			return true;
		}
		return false;
	}

	public void unload() {
		IUSFBuffHandler handler = USFBuffFactory.getBuffHandle(buffType);
		try {
			handler.unload(this);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[USFBuff] [unload] [error]", e);
		}
	}

	public USFBuff copy() {
		USFClientBuffInfo newClientInfo = new USFClientBuffInfo();
		newClientInfo.setGuid(BUFF_ID++);
		newClientInfo.setBuffId(this.clientInfo.getBuffId());
		newClientInfo.setDuration(this.clientInfo.getDuration());

		if (this.clientInfo.getTotalTime() != 0) {
			newClientInfo.setTotalTime(this.clientInfo.getTotalTime());
			long now = Globals.getTimeService().now();
			long expiredTime = now + newClientInfo.getTotalTime();
			newClientInfo.setExpiredTime(expiredTime);
		}
		List<USFClientBuffProInfo> tmpList = this.clientInfo.getProInfos();
		newClientInfo.setProInfos(copyProInfos(tmpList));
		newClientInfo.setExtraProInfos(clientInfo.getExtraProInfos());
		USFBuff newBuff = new USFBuff();
		newBuff.setClientInfo(newClientInfo);
		newBuff.setBuffType(buffType);
		newBuff.setGroupId(groupId);
		newBuff.setPriority(priority);
		newBuff.setClientSide(isClientSide);
		newBuff.canRepeat = this.canRepeat;
		newBuff.unloadType = this.unloadType;
		newBuff.isShowFlyText = this.isShowFlyText;
		newBuff.battleBufferIds = this.battleBufferIds;
		return newBuff;
	}

	public static List<USFClientBuffProInfo> copyProInfos(List<USFClientBuffProInfo> tmpList) {
		if (CollectionUtils.isEmpty(tmpList)) {
			return null;
		}
		ArrayList<USFClientBuffProInfo> proInfos = new ArrayList<>(tmpList.size());
		for (USFClientBuffProInfo proInfo : tmpList) {
			USFClientBuffProInfo newProInfo = new USFClientBuffProInfo();
			newProInfo.setPropId(proInfo.getPropId());
			newProInfo.setAddType(proInfo.getAddType());
			newProInfo.setAddValue(proInfo.getAddValue());
			proInfos.add(newProInfo);
		}
		return proInfos;
	}

	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,
								Object... args) {
//		if (this.triggerType == triggerType)
//		{
//			List<AbstractUSFUnit> selectTargets = selectTarget(selectType, sourceUnit, targetUnit, args);
//			if(CollectionUtils.isEmpty(selectTargets))
//			{
//				return;
//			}
//			for(AbstractUSFUnit target : selectTargets)
//			{
//				//TODO 添加buff，拷贝之类的
//				//target.addBuff();
//			}
//			
//		} 
//		else 
		if (this.unloadType == triggerType) {
			if (triggerType == USFBuffTriggerType.LEAVE_TOWER && ownerUnit instanceof USFFortress) {
				// 据点触发了离开据点的事件，并且离开者（oppositeUnit）和当前buff的持有者是一个人，则移除当前buff.否则不移除。因为持有者没离开，就不移除
				if (triggerUnit == ownerUnit && oppositeUnit == this.sourceUnit) {
					ownerUnit.removeBuff(this);
				}
				return;
			}
			ownerUnit.removeBuff(this);
		}
	}

	@Override
	public int hashCode() {
		return this.getGuid();
	}

	@Override
	public boolean equals(Object obj) {
		if (obj instanceof USFBuff) {
			return ((USFBuff) obj).getGuid() == this.getGuid();
		}
		return false;
	}

	public USFClientBuffInfo getClientInfo() {
		return clientInfo;
	}

	public void setClientInfo(USFClientBuffInfo clientInfo) {
		this.clientInfo = clientInfo;
	}

	public int getGuid() {
		return this.clientInfo.getGuid();
	}

	public int getBuffId() {
		return this.clientInfo.getBuffId();
	}

	public int getDuration() {
		return this.clientInfo.getDuration();
	}

	public long getExpiredTime() {
		return this.clientInfo.getExpiredTime();
	}

	public List<USFClientBuffProInfo> getProInfos() {
		return this.clientInfo.getProInfos();
	}

	public USFBuffType getBuffType() {
		return buffType;
	}

	public void setBuffType(USFBuffType buffType) {
		this.buffType = buffType;
	}

	public int getGroupId() {
		return groupId;
	}

	public void setGroupId(int groupId) {
		this.groupId = groupId;
	}

	public int getPriority() {
		return priority;
	}

	public void setPriority(int priority) {
		this.priority = priority;
	}

	public boolean isClientSide() {
		return isClientSide;
	}

	public void setClientSide(boolean isClientSide) {
		this.isClientSide = isClientSide;
	}

	public AbstractUSFUnit getSourceUnit() {
		return sourceUnit;
	}

	public void setSourceUnit(AbstractUSFUnit sourceUnit) {
		this.sourceUnit = sourceUnit;
	}

	public AbstractUSFUnit getOwnerUnit() {
		return ownerUnit;
	}

	public void setOwnerUnit(AbstractUSFUnit ownerUnit) {
		this.ownerUnit = ownerUnit;
		this.lastEffectTime = ownerUnit.getNow();
	}

	public USFBuffTriggerType getUnloadType() {
		return unloadType;
	}

	public void setUnloadType(USFBuffTriggerType unloadType) {
		this.unloadType = unloadType;
	}

	public int getTriggerTimes() {
		return triggerTimes;
	}

	public void setTriggerTimes(int triggerTimes) {
		this.triggerTimes = triggerTimes;
	}

	public boolean isCanRepeat() {
		return canRepeat;
	}

	public void setCanRepeat(boolean canRepeat) {
		this.canRepeat = canRepeat;
	}

	public USFSkill getSourceSkill() {
		return sourceSkill;
	}

	public void setSourceSkill(USFSkill sourceSkill) {
		this.sourceSkill = sourceSkill;
	}

	public int[] getBattleBufferIds() {
		return battleBufferIds;
	}

	public void setBattleBufferIds(int[] battleBufferIds) {
		this.battleBufferIds = battleBufferIds;
	}

	public boolean isShowFlyText() {
		return isShowFlyText;
	}

	public void setShowFlyText(boolean isShowFlyText) {
		this.isShowFlyText = isShowFlyText;
	}

	public long getLastEffectTime() {
		return lastEffectTime;
	}

	public void setLastEffectTime(long lastEffectTime) {
		this.lastEffectTime = lastEffectTime;
	}

}
