<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.game2sky</groupId>
		<artifactId>mmo.build</artifactId>
		<version>${mmo.version}</version>
	</parent>

	<artifactId>mmo.benfu</artifactId>
	<name>mmo.benfu</name>

	<dependencies>

		<dependency>
			<groupId>com.game2sky</groupId>
			<artifactId>mmo.core</artifactId>
			<version>${mmo.version}</version>

			<exclusions>
<!--				<exclusion>-->
<!--					<groupId>org.hibernate</groupId>-->
<!--					<artifactId>hibernate</artifactId>-->
<!--				</exclusion>-->
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-core</artifactId>
				</exclusion>
<!--				<exclusion>-->
<!--					<groupId>org.hibernate</groupId>-->
<!--					<artifactId>hibernate-annotations</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>org.hibernate</groupId>-->
<!--					<artifactId>hibernate-commons-annotations</artifactId>-->
<!--				</exclusion>-->
			</exclusions>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.9.8</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.9.8</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.9.8</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
	
</project>
