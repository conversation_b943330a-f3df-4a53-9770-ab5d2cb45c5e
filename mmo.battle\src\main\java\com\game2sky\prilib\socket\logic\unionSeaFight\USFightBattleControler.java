package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.communication.game.unionSeaFight.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO 工会海战
 *
 * <AUTHOR>
 * @version v0.1 2018年6月12日 下午3:42:40  guojijun
 */
@Controller
public class USFightBattleControler {

	@Autowired
	private USFightBattleService usFightService;

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFTick)
	public void ssUSFTick() {
		this.usFightService.tick(System.currentTimeMillis());
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFCreateReq)
	public void ssUSFCreateReq(SSUSFCreateReq ssUSFCreateReq) {
		this.usFightService.createFight(ssUSFCreateReq);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFPlayerEnterReq)
	public void ssUSFPlayerEnter(SSUSFPlayerEnterReq ssUSFPlayerEnterReq) {
		this.usFightService.playerEnter(ssUSFPlayerEnterReq);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFPlayerQuit)
	public void ssUSFPlayerQuit(SSUSFPlayerQuit ssUSFPlayerQuit) {
		this.usFightService.playerQuit(ssUSFPlayerQuit);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerMove)
	public void csUSFPlayerMove(Human human, CSUSFPlayerMove csUSFPlayerMove) {
		this.usFightService.playerMove(human, csUSFPlayerMove);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFPlayerBot)
	public void csUSFPlayerBot(Human human, CSUSFPlayerBot csUSFPlayerBot) {
		this.usFightService.playerBot(human, csUSFPlayerBot);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFGetFightInfo)
	public void csUSFGetFightInfo(Human human, CSUSFGetFightInfo csUSFGetFightInfo) {
		this.usFightService.getFightInfo(human, csUSFGetFightInfo);
	}

	@Deprecated
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFSyncPlayerFormation)
	public void ssUSFSyncPlayerFormation(Human human, SSUSFSyncPlayerFormation ssUSFSyncPlayerFormation) {
		this.usFightService.syncPlayerFormation(human, ssUSFSyncPlayerFormation.getFightSides());
	}

	@Deprecated
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFSyncPlayerShip)
	public void ssUSFSyncPlayerShip(Human human, SSUSFSyncPlayerShip ssUSFSyncPlayerShip) {
		this.usFightService.syncPlayerShip(human, ssUSFSyncPlayerShip.getShipInfo());
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFSwitchPlayerMicState)
	public void csUSFSwitchPlayerMicState(Human human, CSUSFSwitchPlayerMicState csUSFSwitchPlayerMicState) {
		this.usFightService.switchPlayerMicState(human, csUSFSwitchPlayerMicState);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSUSFGetFightRecords)
	public void ssUSFGetFightRecords(Human human, SSUSFGetFightRecords ssUSFGetFightRecords) {
		this.usFightService.getFightRecords(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFLookFightReplay)
	public void ssUFLookFightReplay(Human human, CSUSFLookFightReplay csUSFLookFightReplay) {
		this.usFightService.lookFightReplay(human, csUSFLookFightReplay.getFightId());
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUSFFortressPromotion)
	public void csUSFFortressPromotion(Human human,CSUSFFortressPromotion csUSFFortressPromotion) {
		this.usFightService.fortressPromotion(human, csUSFFortressPromotion);
	}

	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSUFRequestForCombatLogInfo)
	public void csUFRequestForCombatLogInfo(Human human,CSUFRequestForCombatLogInfo csUFRequestForCombatLogInfo) {
		this.usFightService.csUFRequestForCombatLogInfo(human, csUFRequestForCombatLogInfo);
	}
}
