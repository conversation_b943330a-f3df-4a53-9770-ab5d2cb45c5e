package com.game2sky.publib.benfu.web.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.unnalShare.UnnalShareManager;
import com.game2sky.prilib.core.socket.logic.warchess.PlayerWarChessData;
import com.game2sky.prilib.core.socket.logic.warchess.WarChessData;
import com.game2sky.publib.Globals;
import com.game2sky.publib.benfu.web.service.BenfuBackService;
import com.game2sky.publib.communication.common.ISysCallBack;
import com.game2sky.publib.framework.web.WebApi;
import com.game2sky.publib.framework.web.base.BaseController;
import com.game2sky.publib.framework.web.base.Result;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年11月26日 下午8:31:14 
 */
@Controller
public class BenfuController extends BaseController {
	@Autowired
	private BenfuBackService benfuBackService;
	
	@RequestMapping("/self/operBenfuTest")
	public ModelAndView operBenfuTest(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
//		Result result = benfuBackService.operBenfuTest(paramsMap);
		
		int groupId = 0;
		try {
			String groupStr = paramsMap.get("groupId");
			if(groupStr != null && !groupStr.isEmpty()) {
				groupId = Integer.parseInt(groupStr);
			}
		} catch(Exception e) {
			com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.error("无法解析groupId, paramsMap="+paramsMap, e);
		}
		// 删除的zoneId
		String removeZoneIds = paramsMap.get("removeZoneIds");
		gnu.trove.set.hash.TIntHashSet removeZoneIdSet = new gnu.trove.set.hash.TIntHashSet();
		if (removeZoneIds != null && !removeZoneIds.isEmpty()) {
			String[] removeZoneIdArr = removeZoneIds.split(",");
			for (String zondIdStr : removeZoneIdArr) {
				if(zondIdStr.isEmpty()) {
					continue;
				}
				try {
					int zoneId = Integer.parseInt(zondIdStr.trim());
					if(zoneId <= 0) {
						continue;
					}
					removeZoneIdSet.add(zoneId);
				} catch(Exception e) {
					com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.error("无法解析removeZoneIds, paramsMap="+paramsMap, e);
				}
			}
		}
		//添加的zoneId
		String addZoneIds = paramsMap.get("addZoneIds");
		gnu.trove.set.hash.TIntHashSet addZoneIdSet = new gnu.trove.set.hash.TIntHashSet();
		if (addZoneIds != null && !addZoneIds.isEmpty()) {
			String[] addZoneIdArr = addZoneIds.split(",");
			for (String zondIdStr : addZoneIdArr) {
				if(zondIdStr.isEmpty()) {
					continue;
				}
				try {
					int zoneId = Integer.parseInt(zondIdStr.trim());
					if(zoneId <= 0) {
						continue;
					}
					addZoneIdSet.add(zoneId);
				} catch(Exception e) {
					com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.error("无法解析addZoneIds, paramsMap="+paramsMap, e);
				}
			}
		}
		
		
		String flag = "";
		try {
			com.game2sky.prilib.benfu.logic.unionSeaFight.BenfuUnionSeaFightService benfuService = com.game2sky.CoreBoot.getBean(com.game2sky.prilib.benfu.logic.unionSeaFight.BenfuUnionSeaFightService.class);
			java.lang.reflect.Field field = benfuService.getClass().getDeclaredField("groupZoneIdArrMap");
			if("groupZoneIdArrMap".equals(field.getName())) {
				field.setAccessible(true);
				java.util.Map<Integer, gnu.trove.set.hash.TIntHashSet> groupZoneIdArrMap = (java.util.Map<Integer, gnu.trove.set.hash.TIntHashSet>)field.get(benfuService);
				com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.warn("@USF_ERROR_GROUP  groupZoneIdArrMap="+groupZoneIdArrMap);
				
				if(groupId > 0) {
					gnu.trove.set.hash.TIntHashSet groupZoneSet = groupZoneIdArrMap.get(groupId);
					com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.warn("@USF_ERROR_GROUP groupZoneIdArrMap.{} -> {}", groupId, groupZoneSet);
					
					if(!removeZoneIdSet.isEmpty()) {// 处理删除某个zoneId
						for (int rzid : removeZoneIdSet._set) {
							if(rzid > 0) {
								groupZoneSet.remove(rzid);
							}
						}
						com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.warn("@USF_ERROR_GROUP 移除 {} 后,  groupZoneIdArrMap.{} 变成了: {}", 
								removeZoneIds, groupId, groupZoneSet);
					}
					
					if(!addZoneIdSet.isEmpty()) {// 处理增加某个zoneId
						for (int rzid : addZoneIdSet._set) {
							if(rzid > 0) {
								groupZoneSet.add(rzid);
							}
						}
						com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.warn("@USF_ERROR_GROUP 添加 {} 后,  groupZoneIdArrMap.{} 变成了:{}", 
								addZoneIds, groupId, groupZoneSet);
					}
				}
				flag = "success";
			} else {
				flag = "none";
			}
		} catch(Throwable t) {
			flag = "Throwable";
			com.game2sky.publib.framework.common.log.AMLog.LOG_UNION.error("无法解析反射处理, paramsMap="+paramsMap, t);
		}
		
		return returnJSON("结果:"+flag);
	}
	

	@RequestMapping("/self/showIntergalFight")
	public ModelAndView showIntergalFight(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		if(paramsMap == null || paramsMap.isEmpty()) {
			
		}
		
//		com.game2sky.prilib.communication.game.integralArena.SSChangePlayerIntegral ss = new com.game2sky.prilib.communication.game.integralArena.SSChangePlayerIntegral();
//		ss.setOnlyNolimit(true);
//		ss.setType(4);
//		int zoneId = com.game2sky.CoreBoot.getAllZoneId().get(0);
//		com.game2sky.publib.framework.communication.Message msg =com.game2sky.publib.framework.communication.Message.buildByZoneId(0L, zoneId, ss, null, null, null);
//		com.game2sky.publib.Globals.getMsgProcessDispatcher().put(msg);
		
		return returnString("success");
	}
	

	@RequestMapping("/self/showUSFFight")
	public ModelAndView showUSFFight(HttpServletRequest request) {
        Map<String, String> paramsMap = getParamsMap(request);
        java.util.Collection<Integer> allZoneId = com.game2sky.publib.Globals.getAllZoneId();
        StringBuilder  data = new StringBuilder ();
        String fun = paramsMap.get("fun");
//        if(fuction == 3||fuction==1){//捞取并发邮件
//            com.game2sky.prilib.core.socket.logic.ladderWarCommon.ILadderWarManager ladderWarManager = com.game2sky.publib.Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
//            com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
//            Map<Long, com.game2sky.prilib.communication.game.ladderwar.LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap();
//            java.util.Set<Long> playerSet = playerInfoMap.keySet();//产赛选手
//            java.util.List<com.game2sky.publib.communication.game.struct.ItemBase> fanhuanItem = com.game2sky.publib.socket.logic.base.ItemBaseTools.
//                    convertFromString("1,0,100");//具体返还和策划确认
//            String content = "本次争霸赛取消,返还押注金额100蓝砖";//TODO 找策划要
//            //发邮件
//            for (Long aLong : playerSet) {
//                java.util.List<Long> guessPlayerList = com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarBenfuRedisDao.clearPlayerGuessData(zoneId, matchTree, aLong);
//                for (Long playerId : guessPlayerList) {//遍历押注的人
//                    com.game2sky.publib.socket.logic.mail.MailService.sendMail(com.game2sky.publib.socket.logic.mail.model.MailDictIdEnum.LADDERWAR_GUESS_REWARD,
//                            "押注返还邮件", content, "系统", playerId, fanhuanItem, null, zoneId);//补偿邮件
//                }
//            }
//        }


        if (fun.equals("getGuessData")) {//捞取并发邮件
            for (Integer zoneId : allZoneId) {
                com.game2sky.prilib.core.socket.logic.ladderWarCommon.ILadderWarManager ladderWarManager = com.game2sky.publib.Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
                com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
                java.util.List<com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarGuessModel> allGuessData = com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarBenfuRedisDao.getAllGuessData(zoneId, matchTree);
                String prefix = "ladderwarGuessc61bffa1294042e68fbeb1c7154983c6"+"_"+zoneId;
                for (com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarGuessModel allGuessDatum : allGuessData) {
                    String log = allGuessDatum.getSource() + ",";
                    data.append(log);
                    com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error(prefix + "_" + log);
                }
            }

            for (Integer zoneId : allZoneId) {
                com.game2sky.prilib.core.socket.logic.ladderWarCommon.ILadderWarManager ladderWarManager = com.game2sky.publib.Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
                com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
                java.util.List<com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarSupportListModel> ladderWarSupportModelListALL = com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarBenfuRedisDao.getLadderWarSupportModelListALL(zoneId, matchTree);
                String prefix2 = "ladderwarSupport61bffa1294042e68fbeb1c7154983c6"+"_"+zoneId;
                for (com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarSupportListModel supportListModel : ladderWarSupportModelListALL) {
                    java.util.List<com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarSupportModel> list = supportListModel.getList();
                    if(list!=null){
                        for (com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarSupportModel supportModel : list) {
                            String log2 = supportModel.getSource() +",";
                            com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error(prefix2 + "_" + log2);
                        }
                    }
                }
            }
        }


        if (fun.equals("clearLadderwarRedisKey")) { //删除redis Key
            for (Integer zoneId : allZoneId) {
                com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error("0clearLadderwarRedisKey,zoneId:" + zoneId);
                //删除rediskey
                com.game2sky.prilib.redis.RedisDao redisDao = com.game2sky.prilib.core.redis.RedisManager.getRedisDao(zoneId);
                try {
                    java.util.Set<Object> ladderwarKeys = redisDao.getRedisTemplate().keys("ladderwar*");

                    com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error("1clearLadderwarRedisKey:" + ladderwarKeys.toString());

                    for (Object ladderwarKey : ladderwarKeys) {
                        String key = ladderwarKey.toString();
                        redisDao.del(key);
                        com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error("2clearLadderwarRedisKey:" + key + "success");
                    }

                } catch (Exception e) {
                    com.game2sky.publib.framework.common.log.AMLog.LOG_BI.error("3clearLadderwarRedisKey:fail", e);
                }
            }

        }


        return returnString("success" + data);
    }
	
	
	@RequestMapping(WebApi.BI_INTEGRAL_BRANCH_RANK)
	public ModelAndView queryIntegralBranchRank(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		Result result = benfuBackService.queryIntegralBranchRank(paramsMap);
		return returnJSON(result);
	}
	
	
	@RequestMapping(WebApi.BI_INTEGRAL_MODIFY_COIN)
	public ModelAndView modifyIntegralCoin(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		Result result = benfuBackService.modifyIntegralCoin(paramsMap);
		return returnJSON(result);
	}
	
	@RequestMapping(WebApi.BI_ANNUAL_SHARE)
	public  ModelAndView annualShareAll(HttpServletRequest request){
		Result result = UnnalShareManager.generateAllPlayer();
		return returnJSON(result);
	}
	
	@RequestMapping("/bi/annualShareActive")
	public  ModelAndView annualShareActive(HttpServletRequest request){
		Result result = UnnalShareManager.generateActivePlayer();
		return returnJSON(result);
	}

	@RequestMapping("/self/warChess")
	public ModelAndView getWarChess(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		long playerId = Long.parseLong(paramsMap.get("playerId"));
		int serverId = Integer.parseInt(paramsMap.get("serverId"));

		Human human = getHuman(playerId, serverId);
		if(human == null) {
			return returnString("找不到Human");
		}
		Result result = new Result();
		
		final PlayerWarChessData playerWarChessData = human.getWarChessManager().getPlayerWarChessData();
		if(playerWarChessData == null) {
			return returnString("Human无PlayerWarChessData数据");
		}
		
		String type = paramsMap.get("type");

		if("monitorPlayer".equalsIgnoreCase(type)){
			final int isMonitor = Integer.parseInt(paramsMap.get("monitor"));
			Globals.createAndExecuteSceneMessageCallBack(human, new ISysCallBack() {

				@Override
				public void apply() {
					playerWarChessData.setNeedLog(isMonitor > 0);
				}
			});

		}else if("PlayerWarChessData".equalsIgnoreCase(type)) {
			
			
		} else if("WarChessData".equalsIgnoreCase(type)) {
			WarChessData WarChessData = human.getWarChessManager().getPlayerWarChessData().getWarChessData();
			if(WarChessData == null) {
				return returnString("Human无WarChessData数据");
			} else {
				
			}
		}
		return returnJSON(result);
	}

	private Human getHuman(long playerId, int serverId) {
		Human human = null;
		if(serverId > 0) {
			int zoneId = Globals.getZoneId(serverId);
			human = Globals.getHuman(zoneId, playerId, Human.class);
		}
		if(human == null) {
			for (int zoneId : Globals.getAllZoneId()){
				human = Globals.getHuman(zoneId, playerId, Human.class);
				if(human != null) {
					break;
				}
			}
		}
		return human;
	}
}
