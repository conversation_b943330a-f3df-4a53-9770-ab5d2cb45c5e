<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="ERROR" monitorInterval="1">

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level (%F\:%L) - %msg%n" />
		</Console>
		<RollingRandomAccessFile name="commonLog"
			fileName="logs/common/common.log" filePattern="logs/common/common.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/common" maxDepth="1">
					<IfFileName glob="common.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="errorLog"
			fileName="logs/error/error.log" filePattern="logs/error/error.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/error" maxDepth="1">
					<IfFileName glob="error.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="httpLog"
			fileName="logs/http/http.log" filePattern="logs/http/http.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/http" maxDepth="1">
					<IfFileName glob="http.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="resourceLog"
			fileName="logs/resource/resource.log" filePattern="logs/resource/resource.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/resource" maxDepth="1">
					<IfFileName glob="resource.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="interfaceLog"
			fileName="logs/interface/interface.log" filePattern="logs/interface/interface.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/interface" maxDepth="1">
					<IfFileName glob="interface.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="interfaceSlowLog"
			fileName="logs/interfaceSlow/interfaceSlow.log" filePattern="logs/interfaceSlow/interfaceSlow.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/interfaceSlow" maxDepth="1">
					<IfFileName glob="interfaceSlow.log.*">
						<IfLastModified age="168H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="biLog"
			fileName="logs/bi/bi.log" filePattern="logs/bi/bi.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/bi" maxDepth="1">
					<IfFileName glob="bi.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="chatLog"
								 fileName="logs/chat/chat.log" filePattern="logs/chat/chat.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
					pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
										   modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/chat" maxDepth="1">
					<IfFileName glob="chat.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="monitorLog"
			fileName="logs/monitor/monitor.log" filePattern="logs/monitor/monitor.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/monitor" maxDepth="1">
					<IfFileName glob="monitor.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="sqlLog"
			fileName="logs/sql/sql.log" filePattern="logs/sql/sql.log.%d{yyyy-MM-dd-HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="sqlErrorLog"
			fileName="logs/sqlError/sql.log" filePattern="logs/sqlError/sql.log.%d{yyyy-MM-dd}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level(%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="payLog"
			fileName="logs/pay/pay.log" filePattern="logs/pay/pay.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/pay" maxDepth="1">
					<IfFileName glob="pay.log.*">
						<IfLastModified age="720H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="unionLog"
			fileName="logs/union/union.log" filePattern="logs/union/union.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level (%F\:%L) - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/union" maxDepth="1">
					<IfFileName glob="union.log.*">
						<IfLastModified age="720H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<RollingRandomAccessFile name="fatalLog"
			fileName="logs/fatal/fatal.log" filePattern="logs/fatal/fatal.log.%d{yyyy.MM.dd.HH}">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
			</Policies>
			<DefaultRolloverStrategy>
				<Delete basePath="logs/fatal" maxDepth="1">
					<IfFileName glob="fatal.log.*">
						<IfLastModified age="72H" />
					</IfFileName>
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<!-- jms异步消息队列日志，需要activemq-log4j2.jar的支持 -->
		<!-- log4j.appender.JMS=com.yunchang.plugin.activemq.log4j.ActivemqQueueAppender 
			#地址 log4j.appender.JMS.brokerURL=failover:(tcp://127.0.0.1:61616) #将要入的队列名称 
			log4j.appender.JMS.queue=hhw.player.complain.input #最大阻塞队列，超过多少就要清空了。 log4j.appender.JMS.maxBlock=2000 
			#失败的数据写到哪个文件。如果配置了就会写，不想写就不要打开该配置 #log4j.appender.JMS.failToFile=/tmp/temp.txt -->
		<!-- <ActiveMq name="activemqLog" brokerURL="failover:(tcp://127.0.0.1:61616)" 
			queue="hhw.player.complain.input"> <PatternLayout pattern="%d{yyyy-MM-dd 
			HH:mm:ss.SSS} [%t] [%-5p] {%F:%L} - %m%n" /> </ActiveMq> -->

	<!-- 1.endpiont 在本地内网测试时使用http://cmq-queue-gz.api.qcloud.com，上腾讯云后请使用 http://cmq-queue-gz.api.tencentyun.com 
		2.queue 为消息队列名称，航海王为hhwlog 可选参数： 1.batchSize，多少条数据提交一次给队里服务器，默认15，腾讯限制16条以内 
		2.maxBlock 阻塞了多少条后的消息自动丢失，防止内存被撑爆了，默认2000 3.failToFile 失败的文件,如果配置了该参数，处理失败的消息会往对应的文件里写日志。看情况而定。 -->

		<CMQ name="Cmq" endpoint="http://cmq-queue-gz.api.qcloud.com"
			queue="hhwlog">
			<PatternLayout
				pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] [%-5p] {%F:%L} - %m%n" />
		</CMQ>
	</Appenders>

	<Loggers>
		<Root level="info" additivity="false" includeLocation="true">
			<AppenderRef ref="Console" />
		</Root>
		<Logger name="commonLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="Console" />
			<appender-ref ref="commonLog" />
		</Logger>
		<Logger name="errorLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="Console" />
			<appender-ref ref="errorLog" />
		</Logger>
		<Logger name="httpLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="httpLog" />
		</Logger>
		<Logger name="resourceLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="resourceLog" />
		</Logger>
		<Logger name="interfaceLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="Console" />
			<appender-ref ref="interfaceLog" />
		</Logger>
		<Logger name="interfaceSlowLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="interfaceSlowLog" />
		</Logger>
		<Logger name="biLog" level="info" additivity="true"
			includeLocation="true">
			<appender-ref ref="biLog" />
		</Logger>
		<Logger name="chatLog" level="info" additivity="true"
				includeLocation="true">
			<appender-ref ref="chatLog" />
		</Logger>
		<Logger name="monitorLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="monitorLog" />
		</Logger>
		<Logger name="sqlLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="sqlLog" />
		</Logger>
		<Logger name="sqlErrorLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="sqlErrorLog" />
		</Logger>
		<Logger name="payLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="Console" />
		</Logger>
		<Logger name="unionLog" level="info" additivity="false"
			includeLocation="true">
			<appender-ref ref="unionLog" />
		</Logger>
		<Logger name="fatalLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="fatalLog" />
		</Logger>
		<Logger name="activemqLog" level="error" additivity="false"
			includeLocation="true">
			<appender-ref ref="Console" />
		</Logger>


		<!-- <logger name="org.hibernate.SQL" level="info" /> -->

		<logger name="org.hibernate" level="OFF" />
		<logger name="org.hibernate.SQL" level="OFF" />
		<logger name="org.hibernate.type" level="OFF" />

	</Loggers>

</Configuration>