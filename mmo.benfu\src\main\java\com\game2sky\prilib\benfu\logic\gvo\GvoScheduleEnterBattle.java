package com.game2sky.prilib.benfu.logic.gvo;

import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleRes;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO 进入跨服场景
 *
 * <AUTHOR>
 * @version v0.1 2020年10月21日 下午3:15:41  guojijun
 */
public class GvoScheduleEnterBattle extends ScheduledMessage {

	public static final int DELAY = 1000;
	public static final int RETRY_MAX_COUNT = 10;

	private int serverId;
	private long playerId;
	private SSGvoAllocBattleRes ssGvoAllocBattleRes;

	public GvoScheduleEnterBattle() {
		super(System.currentTimeMillis());
	}

	@Override
	public void execute() {
		ssGvoAllocBattleRes.setExecTimes(ssGvoAllocBattleRes.getExecTimes() + 1);
		Message message = Message.buildByServerId(playerId, serverId, ssGvoAllocBattleRes, null, null, null);
		Globals.getMsgProcessDispatcher().put(message);
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public long getPlayerId() {
		return playerId;
	}

	public void setPlayerId(long playerId) {
		this.playerId = playerId;
	}

	public SSGvoAllocBattleRes getSsGvoAllocBattleRes() {
		return ssGvoAllocBattleRes;
	}

	public void setSsGvoAllocBattleRes(SSGvoAllocBattleRes ssGvoAllocBattleRes) {
		this.ssGvoAllocBattleRes = ssGvoAllocBattleRes;
	}

}
