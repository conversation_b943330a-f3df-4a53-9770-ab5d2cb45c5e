package com.game2sky.prilib.benfu.logic.ladderwar;

import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.ladderwar.log.LadderWarGuessLog;
import com.game2sky.prilib.benfu.logic.ladderwar.log.LadderWarSupportLog;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.ladderwar.CSGetLadderWarGuessInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionFightingEnter;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionSupport;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionSupportRefresh;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarBaseInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarGetNicePointHistory;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarGuessRequest;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarRewardRequest;
import com.game2sky.prilib.communication.game.ladderwar.CSOnceLadderCompetitionData;
import com.game2sky.prilib.communication.game.ladderwar.CompetitionState;
import com.game2sky.prilib.communication.game.ladderwar.LadderCompetitionPlayerData;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarBattleInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarCreateFightInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarGridInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarNicePlayerInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarPlayerFightState;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarPlayerInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarRewardInfo;
import com.game2sky.prilib.communication.game.ladderwar.LadderWarTeamInfo;
import com.game2sky.prilib.communication.game.ladderwar.OnceCompetitionData;
import com.game2sky.prilib.communication.game.ladderwar.SCConfirmReady;
import com.game2sky.prilib.communication.game.ladderwar.SCGetLadderWarGuessInfo;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderCompetitionSupport;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderCompetitionSupportRefresh;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderWarBaseInfo;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderWarGetNicePointHistory;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderWarGuessRequest;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderWarInfo;
import com.game2sky.prilib.communication.game.ladderwar.SCLadderWarRewardRequest;
import com.game2sky.prilib.communication.game.ladderwar.SCOnceLadderCompetitionData;
import com.game2sky.prilib.communication.game.ladderwar.SCReadyFightWindow;
import com.game2sky.prilib.communication.game.ladderwar.SSCenterGetFormation;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderCompetitionSupport;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarCreateFight;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarGuessRequest;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerEnterReq;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerEnterRes;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerFightStateChange;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerInfoChange;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarStartFight;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarStartFightInScene;
import com.game2sky.prilib.communication.game.ladderwar.SSReqLadderWarMatchTree;
import com.game2sky.prilib.communication.game.ladderwar.SSResLadderWarMatchTree;
import com.game2sky.prilib.communication.game.ladderwar.SSScheduleLadderWar;
import com.game2sky.prilib.communication.game.ladderwar.SSTOCenterFormation;
import com.game2sky.prilib.communication.game.ladderwar.SupportPlayerData;
import com.game2sky.prilib.communication.game.player.CommonFormationData;
import com.game2sky.prilib.communication.game.player.CommonFormationType;
import com.game2sky.prilib.communication.game.player.FormationItem;
import com.game2sky.prilib.communication.game.player.PlayerFormation;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictLadderWarRankReward;
import com.game2sky.prilib.core.schedule.servertask.event.IMinuteUpdateTaskEventListener;
import com.game2sky.prilib.core.schedule.servertask.event.IMinuteUpdateTaskEventSource;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.base.CommonService;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightRole;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSailor;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.battle.fdefine.EnumDefine;
import com.game2sky.prilib.core.socket.logic.commonformation.CommonFightDataModel;
import com.game2sky.prilib.core.socket.logic.commonformation.CommonFormationDataModel;
import com.game2sky.prilib.core.socket.logic.fight.manager.CommonFightManager;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.ILadderWarManager;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarBenfuRedisDao;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarCommonService;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.LadderWarConstants;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarBattleStageEnum;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarMatchTree;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarPlayerReadyInfo;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.data.LadderWarTime;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.event.ILadderWarEventSource;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarGuessModel;
import com.game2sky.prilib.core.socket.logic.ladderWarCommon.model.LadderWarSupportModel;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneCommonService;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.prilib.dbs.dao.InstCommonFormationDataDao;
import com.game2sky.prilib.dbs.model.InstCommonFormationData;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.IPSEnum;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.communication.game.struct.ItemBase;
import com.game2sky.publib.event.EventSourceManager;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossBattleTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveListener;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveSource;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventLinstener;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;
import com.game2sky.publib.framework.util.StringUtils;
import com.game2sky.publib.log.LogHelper;
import com.game2sky.publib.socket.logic.base.ItemBaseTools;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;

/**
 * 本服争霸赛服务.
 *
 * <AUTHOR> Lee
 * @version v0.1 2018年12月3日 下午4:42:29  Jet Lee
 */
@Service
@EventSubscriber(eventSources = { ITargetServerRestartEventSource.class, IClientBattleChannelInactiveSource.class , IMinuteUpdateTaskEventSource.class})
public class LadderWarBenfuService extends LadderWarCommonService implements ITargetServerRestartEventLinstener, IClientBattleChannelInactiveListener ,IMinuteUpdateTaskEventListener {
	
	@Override
	public void onTargetServerRestart(ServerTypeEnum serverType, Channel channel, int restartType) {
		if (serverType != ServerTypeEnum.QUANFU) {
			return;
		}
		// 向中心服请求赛程树信息
		for (Integer zoneId : Globals.getAllZoneId()) {
			if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.LADDER_WAR, zoneId)) {
				return;
			}
		}
		// 整个本服请求一遍就可以
		List<Integer> allServerId = CoreBoot.getAllServerId();
		SSReqLadderWarMatchTree ssReq = new SSReqLadderWarMatchTree(allServerId);
		int defaultServerId = allServerId.get(0);
		CoreBoot.benfuToCenter(ssReq, 0, defaultServerId);
	}
	
	@Override
	public void onMinuteUpdateTask(int zoneId, long curExeTime, int curHour, int curMinute) {
		if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.LADDER_WAR, zoneId)) {
			return;
		}
		SSScheduleLadderWar msg = new SSScheduleLadderWar();
		Message build = Message.buildByZoneId(0L, zoneId, msg, null, null, null);
		Globals.getMsgProcessDispatcher().put(build);
	}
	
	public void ssResLadderWarMatchTree(SSResLadderWarMatchTree msg, Head head) {
		List<Integer> serverIds = msg.getServerIds(); // 当前争霸赛赛程树包含的serverId
		List<Integer> allServerId = CoreBoot.getAllServerId(); // 当前本服包含的所有serverId
		Set<Integer> zoneIds = new HashSet<Integer>();
		for (int serverId : serverIds) {
			if (allServerId.contains(serverId)) {
				// 当前本服包含此serverId
				int zoneId = CoreBoot.getZoneId(serverId);
				if (zoneId > 0) {
					zoneIds.add(zoneId);
				}
			}
		}
		for (Integer zoneId : zoneIds) {
			if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.LADDER_WAR, zoneId)) {
				continue;
			}
			ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
			ladderWarManager.reloadMatchTree(msg);
		}
	}
	
	public void ssLadderWarPlayerInfoChange(SSLadderWarPlayerInfoChange msg, Head head) {
		List<DisplayInformationType> values = msg.getValues();
		long playerId = head.getPid();
		int zoneId = head.getZoneId();
		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		if (ladderWarManager.isOpen()) {
			for (DisplayInformationType informationData : values) {
				ladderWarManager.playerInfoChange(playerId, informationData.getType(), informationData.getValue());
			}
		}
	}

	public void ssLadderWarStartFight(SSLadderWarStartFight msg) {
		int zoneId = Globals.getZoneId(msg.getServerId());
		LadderWarCreateFightInfo createFightInfo = msg.getCreateFightInfo();

		if (createFightInfo.getAddress() == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight没有分配战斗服,msg={}", msg);
			return;
		}

		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		if (matchTree == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight没有找到赛制树,msg={}", msg);
			return;
		}
		if (matchTree.getMatchTreeId() != createFightInfo.getMatchTreeId()) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight赛制树不一致,benfuMatchTreeId={},msg={}", matchTree.getMatchTreeId(),
				msg);
			return;
		}

		LadderWarTeamInfo teamInfo = matchTree.geTeamInfoById(createFightInfo.getTeamId());
		if (teamInfo == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight没有找到组数据,msg={}", msg);
			return;
		}

		LadderWarBattleInfo battleInfo = ladderWarManager.getBattleInfo(createFightInfo.getTeamId(),
			createFightInfo.getTeamRound());
		if (battleInfo == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight没有找到LadderWarBattleInfo,msg={}", msg);
			return;
		}

		int index = matchTree.containsPlayer(createFightInfo.getTeamId(), msg.getPlayerId());
		if (index < 0) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight发起玩家不在该组,msg={}", msg);
			return;
		}


		PlayerDisplayInformation displayInformation = Globals.getPlayerDisplayInformation(msg.getPlayerId(), zoneId);
		if (displayInformation == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight玩家不存在,serverId={},playerId={}", msg.getServerId(),
				msg.getPlayerId());
			return;
		}

		battleInfo.setAddress(createFightInfo.getAddress());
		battleInfo.setBattleState(CompetitionState.FIGHTING);

		CommonFightDataModel commonFightDataModel = null;
		try {
			switch (createFightInfo.getTeamRound()) {
				case 1:
					commonFightDataModel = CommonFightManager.getFFightSideByType(msg.getPlayerId(), zoneId,
						CommonFormationType.LADDER_COMPETITION_FIGHT1);
					break;
				case 2:
					commonFightDataModel = CommonFightManager.getFFightSideByType(msg.getPlayerId(), zoneId,
						CommonFormationType.LADDER_COMPETITION_FIGHT2);
					break;
				case 3:
					commonFightDataModel = CommonFightManager.getFFightSideByType(msg.getPlayerId(), zoneId,
						CommonFormationType.LADDER_COMPETITION_FIGHT3);
					if (commonFightDataModel == null) {
						commonFightDataModel = CommonFightManager.getFFightSideByType(msg.getPlayerId(), zoneId,
							CommonFormationType.CURRENT_FORMATION);
					}
					break;
				default:
					AMLog.LOG_COMMON.error("@LadderWar.StartFight战斗场次错误,msg={}", msg);
					return;
			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[@LadderWar.StartFight查询阵容报错]", e);
		}
		
		if (commonFightDataModel == null) {
			AMLog.LOG_COMMON.error("@LadderWar.StartFight没有设置阵容,msg={}", msg);
			return;
		}
		//去掉助战
		FFightSide fightSide = commonFightDataModel.getFFightSide();
		fightSide.setAssistSailors(new ArrayList<FFightSailor>());
		FFightRole fightRole = fightSide.roles.get(0);
		ArrayList<FormationItem> formationItems = new ArrayList<>(fightRole.sailors.size());
		for (FFightSailor fightSailor : fightRole.sailors) {
			FormationItem formationItem = new FormationItem(fightSailor.uid, fightSailor.heroId, fightSailor.fpId,
				fightSailor.fashionId);
			formationItems.add(formationItem);
		}
		PlayerFormation formation = new PlayerFormation(formationItems, fightSide.formationId, 0, 0,null);

		ServerSocketAddress serverSocketAddress = ServerSocketAddress.parseProto(createFightInfo.getAddress());
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(serverSocketAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			CrossServerManager.init(serverSocketAddress, true);
		}

		boolean isReady = false;
		LadderWarPlayerReadyInfo playerReadyInfo = ladderWarManager.getPlayerReadyInfo(msg.getPlayerId());
		if (playerReadyInfo != null && playerReadyInfo.getIndex() == createFightInfo.getTeamRound()
			&& playerReadyInfo.getBattleRound() == teamInfo.getRound()) {
			isReady = true;
		}

		int camp = (index == 0 ? EnumDefine.FightCampEnum_Left : EnumDefine.FightCampEnum_Right);
		SSLadderWarCreateFight ssLadderWarCreateFight = new SSLadderWarCreateFight(createFightInfo, msg.getServerId(),
			msg.getPlayerId(), displayInformation, formation, commonFightDataModel.getFightData(), isReady, camp);
		
		boolean bl = CrossServerManager.isCrossServerConnected(serverSocketAddress.getServerType(),
			serverSocketAddress.getFlag());
		if (bl) {
			this.sendCreateFightMsg(ssLadderWarCreateFight);
		} else {
			ScheduleLadderWarStartFight scheduleLadderWarStartFight = new ScheduleLadderWarStartFight(
				ssLadderWarCreateFight, this, serverSocketAddress);
			Globals.getScheduleService().scheduleOnce(scheduleLadderWarStartFight, ScheduleLadderWarStartFight.DELAY);
		}
	}
	
	void sendCreateFightMsg(SSLadderWarCreateFight msg) {
		int zoneId = Globals.getZoneId(msg.getServerId());
		Human human = (Human) Globals.getHuman(zoneId, msg.getPlayerId());
		if (human != null && human.isOnline() && msg.getIsReady()) {
			// 发送到场景线程改变玩家状态
			AMLog.LOG_COMMON.info("@LadderWar.StartFight玩家在线,转到场景线程发起战斗,msg={}", msg);
			SSLadderWarStartFightInScene ssLadderWarStartFightInScene = new SSLadderWarStartFightInScene(msg);
			Message message = Message.buildByZoneId(msg.getPlayerId(), zoneId, ssLadderWarStartFightInScene, null, null,
				null);
			Globals.getMsgProcessDispatcher().put(message);
			return;
		}
		msg.setIsReady(false);
		LadderWarCreateFightInfo createFightInfo = msg.getCreateFightInfo();
		boolean flag = CoreBoot.dispatch2Server(msg, 0, 0, createFightInfo.getAddress().getFlag());
		AMLog.LOG_COMMON.info("@LadderWar.StartFight发起战斗,flag:{}, msg={}", flag, msg);
	}

	public void ssLadderWarStartFightInScene(SSLadderWarStartFightInScene ssLadderWarStartFightInScene, Human human) {
		SSLadderWarCreateFight msg = ssLadderWarStartFightInScene.getMsg();
		boolean isReady = false;
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(msg.getCreateFightInfo().getAddress());
		boolean succ = human.enterCrossBattle(CrossBattleTypeEnum.LADDER_WAR, serverAddress);
		if (succ) {
			isReady = true;
		}
		msg.setIsReady(isReady);
		AMLog.LOG_COMMON.info("@LadderWar.StartFight发起战斗,msg={}", msg);
		CoreBoot.dispatch2Server(msg, 0, 0, serverAddress.getFlag());
	}
	
	private boolean checkLadderWar(Human human, IPSEnum psEnum) {
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.LADDER_WAR, human, psEnum)) {
			return false;
		}
		int zoneId = CoreBoot.getZoneId(human.getServerId());
		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		if (!ladderWarManager.isOpen()) {
			// 活动暂未开启
			human.pushErrorMessageToClient(psEnum.getCode(), ErrorCodeConstants.HOLIDAY_ACTIVITY_NOT_OPEN);
			return false;
		}
		return true;
	}

	public void csLadderWarBaseInfo(CSLadderWarBaseInfo msg, Human human) {
		if (human == null) {
			SCLadderWarBaseInfo resMsg = new SCLadderWarBaseInfo(0, 0, 0, 0);
			Message build = Message.buildByServerId(0l, 0, resMsg, null, null, null);
			msg.getChannel().writeAndFlush(build);
			return;
		}
		if (SwitchConditionManager.checkState(PrivSwitchEnum.LADDER_WAR, human)) {
			int zoneId = CoreBoot.getZoneId(human.getServerId());
			ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit()
				.getLadderWarManager();
			SCLadderWarBaseInfo resMsg = ladderWarManager.getLadderWarBaseInfo();
			human.push2Gateway(resMsg);
		} else {
			SCLadderWarBaseInfo resMsg = new SCLadderWarBaseInfo(0, 0, 0, 0);
			human.push2Gateway(resMsg);
		}
	}

	public void csLadderWarInfo(CSLadderWarInfo msg, Human human) {
		if (human == null) {
			return;
		}
		if (!checkLadderWar(human, PrivPSEnum.SCLadderWarInfo)) {
			return;
		}
		int zoneId = CoreBoot.getZoneId(human.getServerId());
		long playerId = human.getPlayerId();
		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		SCLadderWarInfo ladderWarInfo = ladderWarManager.getLadderWarInfo(playerId);
		human.push2Gateway(ladderWarInfo);
	}

	public void csOnceLadderCompetitionData(CSOnceLadderCompetitionData msg, Human human) {
		if (!checkLadderWar(human, PrivPSEnum.SCOnceLadderCompetitionData)) {
			return;
		}
		int groupId = msg.getGroupId();
		int zoneId = CoreBoot.getZoneId(human.getServerId());
		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		LadderWarTeamInfo teamInfo = matchTree.geTeamInfoById(groupId);
		if (teamInfo == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCOnceLadderCompetitionData.getCode(),
				ErrorCodeConstants.HOLIDAY_ACTIVITY_NOT_OPEN);
			return;
		}
		List<LadderCompetitionPlayerData> playerDatas = new ArrayList<LadderCompetitionPlayerData>();
		List<Integer> playerGrids = teamInfo.getPlayerGrids();
		for (int i = 0; i < playerGrids.size(); i++) {
			int gridId = playerGrids.get(i);
			LadderWarGridInfo gridInfo = matchTree.getGridInfos().get(gridId);
			long playerId = gridInfo.getPlayerId();
			if (playerId > 0) {
				LadderWarPlayerInfo ladderWarPlayerInfo = matchTree.getPlayerInfoMap().get(playerId);
				playerDatas.add(new LadderCompetitionPlayerData(ladderWarPlayerInfo.getDisplayInfo(),
					ladderWarPlayerInfo.getRank(), ladderWarPlayerInfo.getScore(), i == 0, ladderWarPlayerInfo
						.getSupportNum() ,ladderWarPlayerInfo.getDisplayServerId()));
			}
		}
		LadderWarTime ladderWarTime = ladderWarManager.getLadderWarTime();
		List<OnceCompetitionData> competitionDatas = new ArrayList<OnceCompetitionData>();
		List<LadderWarBattleInfo> battleInfos = teamInfo.getBattleInfos();
		int round = teamInfo.getRound();
		for (int i = 0; i < battleInfos.size(); i++) {
			LadderWarBattleInfo battleInfo = battleInfos.get(i);
			int battleIndex = i + 1;
			int startTeamRound = matchTree.getTeamRound(battleIndex, LadderWarBattleStageEnum.FIGHT_START.getValue());
			long startTime = ladderWarTime.getTimeByRound(round, startTeamRound);
			int endTeamRound = matchTree.getTeamRound(battleIndex, LadderWarBattleStageEnum.FIGHT_RESULT.getValue());
			long endTime = ladderWarTime.getTimeByRound(round, endTeamRound);
			competitionDatas.add(new OnceCompetitionData(battleInfo.getBattleState().value(), battleInfo
				.getWinPlayerId(), battleIndex, startTime, endTime, groupId, battleInfo.getFightRecord()));
		}
		long supportPlayerId = 0;
		List<LadderWarSupportModel> ladderWarSupportModelList = LadderWarBenfuRedisDao.getLadderWarSupportModelList(
			zoneId, human.getPlayerId(),ladderWarManager.getMatchTree ());
		for (LadderWarSupportModel ladderWarSupportModel : ladderWarSupportModelList) {
			if (teamInfo.getRound() == ladderWarSupportModel.getRound()) {
				supportPlayerId = ladderWarSupportModel.getTarget();
				break;
			}
		}
		SCOnceLadderCompetitionData scMsg = new SCOnceLadderCompetitionData(playerDatas, competitionDatas,
			supportPlayerId);
		human.push2Gateway(scMsg);
	}

	/**
	 * 进入战斗
	 * 
	 * @param msg
	 * @param self
	 */
	public void enterFight(CSLadderCompetitionFightingEnter msg, Human self) {
		if (self.getSceneObject().getTeamId() > 0) {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFight组队状态下不能进入战斗,playerId={}", self.getPlayerId());
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_ENTER_FIGHT_IN_TEAM);
			return;
		}
		boolean bl = SceneCommonService.canBeginRaid(PrivPSEnum.SCLadderCompetitionFightingEnter, self);
		if (!bl) {
			return;
		}
		ILadderWarManager ladderWarManager = Globals.getMemoryData(self.getZoneId()).getMemoryDataInit()
			.getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		if (matchTree == null) {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFight没有找到赛制树,playerId={},zoneId={}", self.getPlayerId(),
				self.getZoneId());
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_NOT_OPEN_OR_CLOSED);
			return;
		}
		LadderWarBattleInfo battleInfo = ladderWarManager.getBattleInfo(msg.getGroupId(), msg.getTeamRound());
		if (battleInfo == null) {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFight当前没有进行中的战斗,playerId={},msg={}", self.getPlayerId(), msg);
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_NOT_OPEN_OR_CLOSED);
			return;
		}
		switch (battleInfo.getBattleState()) {
			case FINISH:
				AMLog.LOG_COMMON.warn("@LadderWar.enterFight战斗已经结束,playerId={},msg={}", self.getPlayerId(), msg);
				self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
					ErrorCodeConstants.LADDER_WAR_CLOSED);
				return;
			case PREPARE:
				AMLog.LOG_COMMON.warn("@LadderWar.enterFight战斗还没有开始,playerId={},msg={}", self.getPlayerId(), msg);
				self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
					ErrorCodeConstants.LADDER_WAR_NOT_OPEN);
				return;
			case FIGHTING:
				break;
		}

		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(battleInfo.getAddress());
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(serverAddress.getFlag());
		if (connectStatus != ClientConnectStatus.CONNECTED) {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFight和战斗服的连接断开,开始重新建立,playerId={},serverFlag={}", self.getPlayerId(),
				serverAddress.getFlag());
			CrossServerManager.init(serverAddress, true);
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_NOT_OPEN_OR_CLOSED);
			return;
		}

		boolean succ = self.enterCrossBattle(CrossBattleTypeEnum.LADDER_WAR, serverAddress);
		if (!succ) {
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_STATE_ERROR);
			return;
		}

		SSLadderWarPlayerEnterReq ssLadderWarPlayerEnterReq = new SSLadderWarPlayerEnterReq(matchTree.getMatchTreeId(),
			msg.getGroupId(), self.getServerId(), self.getPlayerId());
		succ = CoreBoot.dispatch2Server(ssLadderWarPlayerEnterReq, 0, 0, serverAddress.getFlag());
		if (!succ) {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFight发送SSLadderWarPlayerEnterReq失败,playerId={},serverFlag={}",
				self.getPlayerId(), serverAddress.getFlag());
			self.exitCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
			self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(),
				ErrorCodeConstants.LADDER_WAR_NOT_OPEN_OR_CLOSED);
		}
	}

	/**
	 * 进入战斗结果
	 * 
	 * @param msg
	 * @param self
	 */
	public void enterFightRes(SSLadderWarPlayerEnterRes msg, Human self) {
		boolean bl = self.isCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
		if (bl) {
			int errorCode = msg.getErrorCode();
			if (errorCode > 0) {
				AMLog.LOG_COMMON.warn("@LadderWar.enterFightRes玩家进入战斗失败,playerId={},errorCode={}", self.getPlayerId(),
					errorCode);
				self.exitCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
				self.pushErrorMessageToClient(PrivPSEnum.SCLadderCompetitionFightingEnter.getCode(), errorCode);
			} else {
				String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, self.getPlayerId());
				AMLog.LOG_COMMON.info("@LadderWar.enterFightRes玩家进入战斗,屏蔽场景消息,playerId={},serverFlag={}",
					self.getPlayerId(), serverFlag);
				self.getSceneObject().stopReceiveSceneMsg();
			}
		} else {
			AMLog.LOG_COMMON.warn("@LadderWar.enterFightRes玩家收到SSLadderWarPlayerEnterRes消息,但是已经不再争霸赛状态了,playerId={}",
				self.getPlayerId());
		}
	}
	
	/**
	 * 玩家战斗状态改变
	 * 
	 * @param msg
	 */
	public void playerFightStateChange(SSLadderWarPlayerFightStateChange msg, Human human) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
		if (!bl) {
			return;
		}
		switch (msg.getState()) {
			case LADDER_WAR_PLAYER_FIGHT_STATE_FIGHTING:
				human.getSceneObject().stopReceiveSceneMsg();
				AMLog.LOG_COMMON.info("@LadderWar.playerFightStateChange玩家进入争霸赛,屏蔽场景消息,playerId={}",
					human.getPlayerId());
				break;
			case LADDER_WAR_PLAYER_FIGHT_STATE_EXIT:
				human.exitCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
				AMLog.LOG_COMMON.info("@LadderWar.playerFightStateChange玩家退出争霸赛战斗,playerId={}", human.getPlayerId());
				break;
			case LADDER_WAR_PLAYER_FIGHT_STATE_DISCONNECT:
				AMLog.LOG_COMMON.warn("@LadderWar.playerFightStateChange争霸赛中和战斗服断开连接,强制踢掉玩家,playerId={}",
					human.getPlayerId());
				human.exitCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
				AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
					"@Minc.close.usf.client.battle.channel.inactive");
				break;
		}
	}
	
	@Override
	public void onClientBattleChannelInactive(AbstractHuman abstractHuman) {
		Human human = (Human) abstractHuman;
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.LADDER_WAR);
		if (!isCrossBattle) {
			return;
		}
		SSLadderWarPlayerFightStateChange ssLadderWarPlayerFightStateChange = new SSLadderWarPlayerFightStateChange(
			LadderWarPlayerFightState.LADDER_WAR_PLAYER_FIGHT_STATE_DISCONNECT);
		Message msg = Message.buildByZoneId(abstractHuman.getPlayerId(), abstractHuman.getZoneId(),
			ssLadderWarPlayerFightStateChange, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
	}
	
	private boolean checkCanReady(Human human) {
		if (!checkLadderWar(human, PrivPSEnum.SCOnceLadderCompetitionData)) {
			return false;
		}
		ILadderWarManager ladderWarManager = Globals.getMemoryData(human.getZoneId()).getMemoryDataInit()
			.getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		long playerId = human.getPlayerId();
		if (!ladderWarManager.isInBattleRound(playerId, ladderWarManager.getBattleRound())) {
			// 当前玩家没有在当前轮里
			return false;
		}
		int teamRound = matchTree.getTeamRound();
		if (teamRound >= LadderWarConstants.MAX_TEAM_ROUND_COUNT) {
			// 当前阶段已经到了结束战斗以后的强制结算阶段
			return false;
		}
		int battleStage = matchTree.getCurrentBattleStage();
		if (battleStage != LadderWarBattleStageEnum.READY_TIME.getValue()) {
			return false;
		}
		return true;
	}
	
	/**
	 * 请求弹窗
	 * 
	 * @param human
	 */
	public void readyFightWindow(Human human) {
		if (!checkCanReady(human)) {
			return;
		}
		ILadderWarManager ladderWarManager = Globals.getMemoryData(human.getZoneId()).getMemoryDataInit()
			.getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		long playerId = human.getPlayerId();
		LadderWarTeamInfo teamInfo = matchTree.getTeamInfo(playerId);
		if (teamInfo == null) {
			// 没找到玩家所在组
			return;
		}
		if (ladderWarManager.canBattle(human)) {
			SCReadyFightWindow readyWindow = ladderWarManager.getReadyWindow(teamInfo, playerId);
			if (readyWindow != null) {
				human.push2Gateway(readyWindow);
			}
		} else {
			// 不可以战斗，发送提示
			human.pushErrorMessageToClient(PrivPSEnum.SCReadyFightWindow.getCode(),
				ErrorCodeConstants.LADDER_WAR_READY_NOTICE);
			return;
		}
	}

	/**
	 * 请求确认准备
	 * 
	 * @param human
	 */
	public void confirmReady(Human human) {
		if (!checkCanReady(human)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCConfirmReady.getCode(),
				ErrorCodeConstants.LADDER_WAR_CANNOT_READY);
			return;
		}
		ILadderWarManager ladderWarManager = Globals.getMemoryData(human.getZoneId()).getMemoryDataInit()
			.getLadderWarManager();
		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree();
		long playerId = human.getPlayerId();
		int battleRound = matchTree.getBattleRound();
		int battleIndex = matchTree.getCurrentBattleIndex(); // 当前第几场战斗
		LadderWarPlayerReadyInfo playerReadyInfo = ladderWarManager.getPlayerReadyInfo(playerId);
		if (playerReadyInfo != null) {
			if (playerReadyInfo.getBattleRound() != battleRound || playerReadyInfo.getIndex() != battleIndex) {
				// 准备信息过时（可能是上次战斗前的准备），则更新准备信息
				playerReadyInfo.setBattleRound(battleRound);
				playerReadyInfo.setIndex(battleIndex);
			}
		} else {
			ladderWarManager.addPlayerReadyInfo(playerId, new LadderWarPlayerReadyInfo(battleRound, battleIndex));
		}
		SCConfirmReady scConfirmReady = new SCConfirmReady(true, false);
		human.push2Gateway(scConfirmReady);
	}

	public void ssScheduleLadderWar(SSScheduleLadderWar msg, Head head) {
		int zoneId = head.getZoneId();
		ILadderWarManager ladderWarManager = Globals.getMemoryData(zoneId).getMemoryDataInit().getLadderWarManager();
		ladderWarManager.tick();
	}


	public void csLadderWarRewardRequest(CSLadderWarRewardRequest msg, Human human) { //奖励
		SCLadderWarRewardRequest ret = new SCLadderWarRewardRequest ();
		List<LadderWarRewardInfo> rewardInfoList = new ArrayList<> ();
		for (int i = DictLadderWarRankReward.getMaxType (); ; i--) {
			DictLadderWarRankReward dictLadderWarReward = DictLadderWarRankReward.getDictByType (i);
			if (dictLadderWarReward == null) {
				break;
			}
			String reward = dictLadderWarReward.getReward ();
			if(StringUtils.isEmpty (reward)){
				continue;
			}
			LadderWarRewardInfo ladderWarRewardInfo = new LadderWarRewardInfo ();
			ladderWarRewardInfo.setRewardType (i);
			ladderWarRewardInfo.setItems (ItemBaseTools.convertFromString (reward));
			java.util.List<Integer> titleId = new ArrayList<> ();
			titleId.add (dictLadderWarReward.getTitleId ());
			ladderWarRewardInfo.setTitleId (titleId);
			rewardInfoList.add (ladderWarRewardInfo);
		}
		ret.setRewardItem (rewardInfoList);
		human.push2Gateway (ret);

	}

	public void csLadderCompetitionSupport(CSLadderCompetitionSupport msg, Human self) {//支持
		if (!checkLadderWar(self, PrivPSEnum.SCLadderCompetitionSupport)) {
			return;
		}

		long targetPlayerId = msg.getPlayerId ();//被支持的人

		ILadderWarManager ladderWarManager = Globals.getMemoryData (self.getZoneId ()).getMemoryDataInit ().getLadderWarManager ();

		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree ();
		Map<Long, LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap ();
		LadderWarPlayerInfo ladderWarPlayerInfo = playerInfoMap.get (targetPlayerId);

		LadderWarTime ladderWarTime = ladderWarManager.getLadderWarTime ();
		int curRound = ladderWarManager.getBattleRound ();
		if (curRound > ladderWarTime.getRoundBattleTimes ().size () || curRound <= 0) {//正常情况不会 走到第二个判断
			self.pushErrorMessageToClient (PrivPSEnum.SCLadderCompetitionSupport.getCode (), ErrorCodeConstants.LADDER_WAR_SUPPORT_TIMEOUT);
			return; //全部结束了
		}

		List<LadderWarTeamInfo> teamInfos = ladderWarManager.getMatchTree ().getTeamInfos ();
		Map<Integer, LadderWarGridInfo> gridInfos = ladderWarManager.getMatchTree ().getGridInfos ();
		boolean isCurRoundPlayer =false ;//是否是当前轮的对战玩家
        boolean isChampionMatch = false ;//是冠军赛的两个人吗

		for (LadderWarTeamInfo teamInfo : teamInfos) {
			if(teamInfo.getRound () == curRound ||  (curRound == LadderWarConstants.MAX_BATTLE_ROUND -1 &&
                    teamInfo.getRound () == LadderWarConstants.MAX_BATTLE_ROUND )){ //季军赛阶段可以支持冠军
				List<Integer> playerGrids = teamInfo.getPlayerGrids ();
				for (Integer playerGrid : playerGrids) {
					long playerId = gridInfos.get (playerGrid).getPlayerId ();
					if(targetPlayerId == playerId){
						isCurRoundPlayer = true; //找到被支持的人了
                        if(teamInfo.getRound () == LadderWarConstants.MAX_BATTLE_ROUND ){
                            isChampionMatch = true;
                        }
						break;
					}
				}

			}
		}
        if(!isCurRoundPlayer){
            //支持的人不在当前轮中
            self.pushErrorMessageToClient (PrivPSEnum.SCLadderCompetitionSupport.getCode (), ErrorCodeConstants.SYSTEM_ERROR);
            return;
        }

        if(curRound == LadderWarConstants.MAX_BATTLE_ROUND -1 && isChampionMatch){ //季军赛阶段支持冠军
            curRound = LadderWarConstants.MAX_BATTLE_ROUND; //轮数变成冠军
        }


        long roundStartTime = ladderWarTime.getRoundBattleTimes ().get (curRound - 1).getTeamRoundTimes ().get (0);
        long now = System.currentTimeMillis ();

        if (now > roundStartTime) {
            //本轮支持时间已过,不可支持
            self.pushErrorMessageToClient (PrivPSEnum.SCLadderCompetitionSupport.getCode (), ErrorCodeConstants.LADDER_WAR_SUPPORT_TIMEOUT);
            return;
        }

		if (ladderWarPlayerInfo != null) {

			long source = self.getPlayerId ();
			long target = msg.getPlayerId ();
			int zoneId = self.getZoneId ();
			boolean canSupport = LadderWarBenfuRedisDao.canSupport (zoneId, source, target, curRound, ladderWarManager.getMatchTree ());

			if (canSupport) {

				SSLadderCompetitionSupport ssLadderCompetitionSupport = new SSLadderCompetitionSupport ();
				ssLadderCompetitionSupport.setPlayerId (targetPlayerId);
				ssLadderCompetitionSupport.setNum (1);
				ssLadderCompetitionSupport.setMatchTreeId (ladderWarManager.getMatchTree ().getMatchTreeId ());

				int defaultServerId = CoreBoot.getServerIdList (zoneId).get (0);
				boolean toCenterSucc = CoreBoot.benfuToCenter (ssLadderCompetitionSupport, 0, defaultServerId);
                if(toCenterSucc){
					try {
						LadderWarBenfuRedisDao.addLadderWarSupportModel (zoneId, source, target, curRound,msg.getGroupId (),ladderWarManager.getMatchTree ());
					} catch (Exception e) {
						AMLog.LOG_ERROR.error (", csLadderWarGuessRequest ERROR with zoneId = " + self.getZoneId (), e);
						self.pushErrorMessageToClient (PrivPSEnum.SSLadderCompetitionSupport.getCode (), ErrorCodeConstants.
								SYSTEM_ERROR);
						return;
					}
					ladderWarPlayerInfo.setSupportNum (ladderWarPlayerInfo.getSupportNum () + 1);//本服先执行加1
					SCLadderCompetitionSupport scLadderCompetitionSupport = new SCLadderCompetitionSupport ();
					scLadderCompetitionSupport.setPlayerId (target);
					self.push2Gateway (scLadderCompetitionSupport);
				}

			} else {
				self.pushErrorMessageToClient (PrivPSEnum.SCLadderCompetitionSupport.getCode (), ErrorCodeConstants.LADDER_WAR_SUPPORT_ROUND_REPEAT);
				return;
			}
		}

		LadderWarSupportLog log = new LadderWarSupportLog (self); //BI
		log.setIntegralBranchId(matchTree.getIntegralBranchId());
		log.setMatchTreeId(matchTree.getMatchTreeId());
		log.setSessionIndex(matchTree.getSessionIndex());
		log.setBattleRound(matchTree.getBattleRound());
		log.setTargetId (msg.getPlayerId ());
		if (AMLog.LOG_BI.isInfoEnabled()) {
			LogHelper.logBI(log);
		}
		
		//抛出事件
		ILadderWarEventSource eventSource = EventSourceManager.getInstance().getEventSource(ILadderWarEventSource.class);
		if (eventSource != null) {
			eventSource.onLadderWarSupport(self);
		}

	}

	public void csLadderCompetitionSupportRefresh(CSLadderCompetitionSupportRefresh msg, Human self) {//支持刷新
		int groupId = msg.getGroupId ();
		ILadderWarManager ladderWarManager = Globals.getMemoryData (self.getZoneId ()).getMemoryDataInit ().getLadderWarManager ();

		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree ();
		List<LadderWarTeamInfo> teamInfos = matchTree.getTeamInfos ();
		List<Integer> playerGrids = null; //被支持的组的两个人

		for (LadderWarTeamInfo teamInfo : teamInfos) {
			if (teamInfo.getTeamId () == groupId) {
				playerGrids = teamInfo.getPlayerGrids ();
				Map<Long, LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap ();
				Map<Integer, LadderWarGridInfo> gridInfos = matchTree.getGridInfos ();
				LadderWarGridInfo ladderWarGridInfoOne = gridInfos.get (playerGrids.get (0));
				LadderWarPlayerInfo one = playerInfoMap.get (ladderWarGridInfoOne.getPlayerId ());
				LadderWarGridInfo ladderWarGridInfoTwo = gridInfos.get (playerGrids.get (1));
				LadderWarPlayerInfo two = playerInfoMap.get (ladderWarGridInfoTwo.getPlayerId ());

				List<LadderWarPlayerInfo> playerList = new ArrayList<> ();

				playerList.add (one);
				playerList.add (two);
				SCLadderCompetitionSupportRefresh scLadderCompetitionSupportRefresh = new SCLadderCompetitionSupportRefresh ();
				java.util.List<SupportPlayerData> supportPlayerDataList = new ArrayList<> ();

				for (LadderWarPlayerInfo ladderWarPlayerInfo : playerList) {
					SupportPlayerData supportPlayerData = new SupportPlayerData ();
					supportPlayerData.setPlayerId (ladderWarPlayerInfo.getPlayerId ());
					supportPlayerData.setSupportNum (ladderWarPlayerInfo.getSupportNum ());
					supportPlayerDataList.add (supportPlayerData);
				}
				scLadderCompetitionSupportRefresh.setSupportPlayerData (supportPlayerDataList);
				self.push2Gateway (scLadderCompetitionSupportRefresh);
				break;
			}
		}


	}

	public void csGetLadderWarGuessInfo(CSGetLadderWarGuessInfo msg, Human self) {//押注信息

		ILadderWarManager ladderWarManager = Globals.getMemoryData (self.getZoneId ()).getMemoryDataInit ().getLadderWarManager ();


		LadderWarGuessModel ladderWarGuessModel = null;
		try {
			ladderWarGuessModel = LadderWarBenfuRedisDao.getLadderWarGuessModel (self.getZoneId (), self.getPlayerId (),ladderWarManager.getMatchTree ());
		} catch (Exception e) {
			AMLog.LOG_ERROR.error (", getLadderWarGuessModel ERROR with zoneId = " + self.getZoneId (), e);
			self.pushErrorMessageToClient (PrivPSEnum.SCGetLadderWarGuessInfo.getCode (), ErrorCodeConstants.
					SYSTEM_ERROR);
			return;
		}



		boolean flag = false;//标记 是否查看的就是押注的人
		boolean hasGuess = false;//标记 是否查看的就是押注的人
		boolean timeOut = checkGuessTime(ladderWarManager);


		if(ladderWarGuessModel != null){
			hasGuess = true;
			if(msg.getPlayerId () != 0 && ladderWarGuessModel.getTarget () == msg.getPlayerId ()){//请求的就是我押注的
				flag = true;//标记
			}
        }

        if(hasGuess){//已经竞赛了
			if(flag){
				//一直都放过

			}else{ //查看的别人
				if(timeOut){
					self.pushErrorMessageToClient (PrivPSEnum.SCGetLadderWarGuessInfo.getCode (), ErrorCodeConstants.
							LADDER_WAR_SUPPORT_GUESS_TIME_ILLEGALITY);
				}else{
					self.pushErrorMessageToClient (PrivPSEnum.SCGetLadderWarGuessInfo.getCode (), ErrorCodeConstants.
							LADDER_WAR_SUPPORT_REPEAT);
				}
				return;
			}

		}else{//没竞赛
			if(timeOut){
				self.pushErrorMessageToClient (PrivPSEnum.SCGetLadderWarGuessInfo.getCode (), ErrorCodeConstants.
						LADDER_WAR_SUPPORT_GUESS_TIME_ILLEGALITY);
				return;
			}
		}

		SCGetLadderWarGuessInfo scGetLadderWarGuessInfo = new SCGetLadderWarGuessInfo ();
		String guessReward = BaseService.getConfigStringByDefault ("ladderWarGuessNeedItems","3,0,200");
		List<ItemBase> itemBases = ItemBaseTools.convertFromString (guessReward);
		int expend = itemBases.get (0).getNum ();
		scGetLadderWarGuessInfo.setExpend (expend);

		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree ();
		Map<Long, LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap ();
		int guessNumSum = 0;
		for (LadderWarPlayerInfo ladderWarPlayerInfo : playerInfoMap.values ()) {
			guessNumSum = guessNumSum + ladderWarPlayerInfo.getGuessNum ();
		}
		LadderWarPlayerInfo ladderWarPlayerInfo = playerInfoMap.get (msg.getPlayerId ());
		int earn = -1;
		if(flag){ //查看押过的人
			if(ladderWarPlayerInfo.getGuessNum () != 0){
				earn = expend * guessNumSum  / ladderWarPlayerInfo.getGuessNum ();
			}else{
				earn = expend;//正常情款不会走到这里,防止主服发过来的数据是 0
			}

		}else{ //还没押注,假设我押注他
			earn = expend * (guessNumSum + 1) / (ladderWarPlayerInfo.getGuessNum () + 1);
		}

		int minEarn = BaseService.getConfigIntByDefault ("ladderWarGuessIncomeMin", 200);
		int maxEarn = BaseService.getConfigIntByDefault ("ladderWarGuessIncomeMax", 5000);
        if(earn < minEarn){
			earn = minEarn;
		}
		if(earn >  maxEarn){
			earn = maxEarn;
		}
		scGetLadderWarGuessInfo.setEarn (earn);
		self.push2Gateway (scGetLadderWarGuessInfo);

	}

	public void csLadderWarGuessRequest(CSLadderWarGuessRequest msg, Human self) {//押注

		if (!checkLadderWar(self, PrivPSEnum.SCLadderWarGuessRequest)) {
			return;
		}
		SCLadderWarGuessRequest ret = new SCLadderWarGuessRequest ();

		long source = self.getPlayerId ();
		long target = msg.getPlayerId ();
		int zoneId = self.getZoneId ();

		ILadderWarManager ladderWarManager = Globals.getMemoryData (self.getZoneId ()).getMemoryDataInit ().getLadderWarManager ();

		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree ();
		Map<Long, LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap ();
		LadderWarPlayerInfo ladderWarPlayerInfo = playerInfoMap.get (target);
		if (ladderWarPlayerInfo != null) {

			boolean isTimeError = checkGuessTime(ladderWarManager);

			if(isTimeError){
                self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.LADDER_WAR_SUPPORT_GUESS_TIME_ILLEGALITY);
                return;
            }

			boolean canGuess = false;

			try {
				canGuess = LadderWarBenfuRedisDao.canGuess (zoneId, source,matchTree);
			} catch (Exception e) {
				AMLog.LOG_ERROR.error (", csLadderWarGuessRequest ERROR with zoneId = " + self.getZoneId (), e);
				self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.
						SYSTEM_ERROR);
				return;
			}
			if (canGuess) {
				//扣钱
				String guessReward = BaseService.getConfigStringByDefault ("ladderWarGuessNeedItems","3,0,200");
				List<ItemBase> itemBases = ItemBaseTools.convertFromString (guessReward);
				boolean result = CommonService.checkRemoveItemBases(self, itemBases);//判断资源是否充足
				if (!result) {
					self.pushErrorMessageToClient(PrivPSEnum.SCLadderWarGuessRequest.getCode(), ErrorCodeConstants.RESOURCE_NOT_ENOUGH);
					return;
				}else{
					SSLadderWarGuessRequest toCenter = new SSLadderWarGuessRequest ();

					toCenter.setPlayerId (msg.getPlayerId ());
					toCenter.setMatchTreeId (ladderWarManager.getMatchTree ().getMatchTreeId ());
					toCenter.setNum (1);
					int defaultServerId = CoreBoot.getServerIdList (zoneId).get (0);
					boolean succ = CoreBoot.benfuToCenter (toCenter, 0, defaultServerId);
					if( ! succ){
						AMLog.LOG_ERROR.error (", csLadderWarGuessRequest ERROR with zoneId = " + self.getZoneId ());
						self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.
								SYSTEM_ERROR);
						return;
					}
				}

				boolean settle = CommonService.settle (PrivPSEnum.CSLadderWarGuessRequest, self, itemBases, null);
				if(!settle){ //正常这行代码是走不到的
					self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.RESOURCE_NOT_ENOUGH);
					return;
				}
				LadderWarBenfuRedisDao.addLadderWarGuessModel (zoneId, source, target,matchTree);//增加记录
				ret.setPlayerId (msg.getPlayerId ());

				ret.setSucc (1);
				ret.setPlayerId (target);// /
				self.push2Gateway (ret);


			} else {
				self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.LADDER_WAR_SUPPORT_REPEAT);
			    return;
			}
		} else {
			self.pushErrorMessageToClient (PrivPSEnum.SCLadderWarGuessRequest.getCode (), ErrorCodeConstants.SYSTEM_ERROR);
			return;
		}

		LadderWarGuessLog log = new LadderWarGuessLog (self);
		log.setIntegralBranchId(matchTree.getIntegralBranchId());
		log.setMatchTreeId(matchTree.getMatchTreeId());
		log.setSessionIndex(matchTree.getSessionIndex());
		log.setTargetId (msg.getPlayerId ());
		if (AMLog.LOG_BI.isInfoEnabled()) {
			LogHelper.logBI(log);
		}


		//抛出事件
		ILadderWarEventSource eventSource = EventSourceManager.getInstance().getEventSource(ILadderWarEventSource.class);
		if (eventSource != null) {
			eventSource.onLadderWarGuess(self);
		}

	}

    private boolean checkGuessTime(ILadderWarManager ladderWarManager) {
        LadderWarTime ladderWarTime = ladderWarManager.getLadderWarTime ();

        long firstRoundStartTime = ladderWarTime.getRoundBattleTimes ().get (0).getTeamRoundTimes ().get (0);
        long now = System.currentTimeMillis ();
        if (now > firstRoundStartTime || now < ladderWarTime.getOpenTime ()) {
            return true;//时间有问题
        }
        return false;
    }


    public void csLadderWarGetNicePointHistory(CSLadderWarGetNicePointHistory msg, Human self) {
		long source = self.getPlayerId ();
		int zoneId = self.getZoneId ();
		ILadderWarManager ladderWarManager = Globals.getMemoryData (self.getZoneId ()).getMemoryDataInit ().getLadderWarManager ();
		List<LadderWarSupportModel> ladderWarSupportModelList = LadderWarBenfuRedisDao.getLadderWarSupportModelList (zoneId, source,ladderWarManager.getMatchTree ());
		SCLadderWarGetNicePointHistory ret = new SCLadderWarGetNicePointHistory ();
		java.util.List<LadderWarNicePlayerInfo> nicePlayerInfo = new ArrayList<> ();


		LadderWarMatchTree matchTree = ladderWarManager.getMatchTree ();
		Map<Long, LadderWarPlayerInfo> playerInfoMap = matchTree.getPlayerInfoMap ();
		String costString = BaseService.getConfigStringByDefault ("ladderWarMatchName", "32强,16强,8强,4强,季军赛,冠军赛,冠军赛,冠军赛");
		String[] name = costString.split(",");
		for (LadderWarSupportModel ladderWarSupportModel : ladderWarSupportModelList) {
			long targetPlayerId = ladderWarSupportModel.getTarget ();
			LadderWarPlayerInfo ladderWarPlayerInfo = playerInfoMap.get (targetPlayerId);
			LadderWarNicePlayerInfo ladderWarNicePlayerInfo = new LadderWarNicePlayerInfo ();

			if (ladderWarPlayerInfo != null) {
				ladderWarNicePlayerInfo.setDisplayServerId (ladderWarPlayerInfo.getDisplayServerId ());
				ladderWarNicePlayerInfo.setDisplayInfo (ladderWarPlayerInfo.getDisplayInfo ());

			}else{//可能因为作弊,把这个人替换了
				continue;
			}
			ladderWarNicePlayerInfo.setDateTime (ladderWarSupportModel.getSupportTime ());
			int status = ladderWarSupportModel.getStatus ();
			if (status >= 0) {
				ladderWarNicePlayerInfo.setIsWinner (status);
			} else {
				ladderWarNicePlayerInfo.setIsWinner (2);
			}

			if(ladderWarNicePlayerInfo.getIsWinner () ==0 ){
				int round = ladderWarSupportModel.getRound ();
				long target = ladderWarSupportModel.getTarget ();
				List<LadderWarTeamInfo> teamInfos = ladderWarManager.getMatchTree ().getTeamInfos ();
				for (LadderWarTeamInfo teamInfo : teamInfos) {
					if(teamInfo.getRound () == round ){
						if(teamInfo.getWinPlayerId () > 0){
							if(target == teamInfo.getWinPlayerId ()){//1 胜利 ,2 失败 ,0 无状态
								ladderWarNicePlayerInfo.setIsWinner (1);
							}else{
								LadderWarPlayerInfo teamFailPlayerInfo = ladderWarManager.getMatchTree ().getTeamFailPlayerInfo (teamInfo);
							    if(teamFailPlayerInfo != null && target == teamFailPlayerInfo.getPlayerId ()){
									ladderWarNicePlayerInfo.setIsWinner (2);
								}
							}
						}
					}
					if(ladderWarNicePlayerInfo.getIsWinner () != 0){ //有结果了,退出
						break;
					}

				}

			}
			ladderWarNicePlayerInfo.setProgressName (name[ladderWarSupportModel.getRound ()-1]);
			nicePlayerInfo.add (ladderWarNicePlayerInfo);

		}

		ret.setNicePlayerInfo (nicePlayerInfo);
		self.push2Gateway (ret);
	}
	
	/**
	 * 中心服获取玩家阵容信息
	 * 
	 * @param msg
	 */
	public void SSCenterGetFormation(SSCenterGetFormation msg, Head head) {
		int matchTreeId = msg.getMatchTreeId();
		long playerId = msg.getPlayerId();
		int zoneId = Globals.getZoneId(head.getServerId());
		Human human = Globals.getRealHuman(zoneId, playerId);
		List<CommonFormationData> data = new ArrayList<CommonFormationData>();
		//在线
		if (human != null) {
			data.add(human.getCommonFormationDataManager().getCommonFormationData(CommonFormationType.LADDER_COMPETITION_FIGHT1));
			data.add(human.getCommonFormationDataManager().getCommonFormationData(CommonFormationType.LADDER_COMPETITION_FIGHT2));
			data.add(human.getCommonFormationDataManager().getCommonFormationData(CommonFormationType.LADDER_COMPETITION_FIGHT3));
		}else{
			// 如果不在线就从数据库load
			InstCommonFormationDataDao benfuDao = Globals.getDbsDataSource().getBenfuDao(InstCommonFormationDataDao.class, zoneId);
			InstCommonFormationData instCommonFormationData = benfuDao.get(playerId);
			CommonFormationDataModel convertFromEntity = CommonFormationDataModel.convertFromEntity(instCommonFormationData);
			HashMap<Integer, CommonFormationData> formationDataMap = convertFromEntity.getFormationDataMap();
			data.add(formationDataMap.get(CommonFormationType.LADDER_COMPETITION_FIGHT1.value()));
			data.add(formationDataMap.get(CommonFormationType.LADDER_COMPETITION_FIGHT2.value()));
			data.add(formationDataMap.get(CommonFormationType.LADDER_COMPETITION_FIGHT3.value()));
		}
		
		List<Integer> res = new ArrayList<>();
		for (CommonFormationData c : data) {
			if(c == null){
				res.add(0);
			}else{
				res.add(1);
			}
		}
		
		SSTOCenterFormation ss = new SSTOCenterFormation();
		ss.setMatchTreeId(matchTreeId);
		ss.setPlayerId(playerId);
		ss.setFormations(res);
		int defaultServerId = CoreBoot.getServerIdList(zoneId).get(0);
		CoreBoot.benfuToCenter(ss, 0, defaultServerId);
		
	}


}
