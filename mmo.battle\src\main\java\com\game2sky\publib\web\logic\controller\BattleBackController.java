package com.game2sky.publib.web.logic.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.game2sky.publib.framework.web.WebApi;
import com.game2sky.publib.framework.web.base.BaseController;
import com.game2sky.publib.framework.web.base.Result;
import com.game2sky.publib.web.logic.service.BattleBackService;


/**
 * 战斗服专属后台
 *
 * <AUTHOR>
 */
@Controller
public class BattleBackController extends BaseController {

	@Autowired
	private BattleBackService battleBackService;
	
	
	
	@RequestMapping(WebApi.MANAGER_BATTLE_SERVER_INFO)
	public ModelAndView getServerInfo(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		Result result = battleBackService.getServerInfo(paramsMap);
		return returnJSON(result);
	}
	
	@RequestMapping(WebApi.MANAGER_INTEGRAL_PVP_FIGHT)
	public ModelAndView integralPVPFight(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		Result result = battleBackService.getIntegralPVPFightInfo(paramsMap);
		return returnJSON(result);
	}

	@RequestMapping(WebApi.MANAGER_INTEGRAL_PLAYERIDS)
	public ModelAndView integralPlayerids(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		Result result = battleBackService.getIntegralPlayerids(paramsMap);
		return returnJSON(result);
	}
	
	
	
	
}
