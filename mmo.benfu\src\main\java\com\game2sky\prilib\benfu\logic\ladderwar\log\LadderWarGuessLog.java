package com.game2sky.prilib.benfu.logic.ladderwar.log;

import com.game2sky.publib.log.CustomLog;
import com.game2sky.publib.log.CustomLogPrefixType;
import com.game2sky.publib.socket.logic.human.AbstractHuman;

/**
 * 比武竞猜
 * Created by GY on 2018/12/20.
 */
public class LadderWarGuessLog extends CustomLog {

    private int matchTreeId;
    /**
     * 第几届
     */
    private int sessionIndex;

    // 天梯分组
    private int integralBranchId;

    private long targetId;

    public long getTargetId() {
        return targetId;
    }

    public void setTargetId(long targetId) {
        this.targetId = targetId;
    }

    public int getMatchTreeId() {
        return matchTreeId;
    }

    public void setMatchTreeId(int matchTreeId) {
        this.matchTreeId = matchTreeId;
    }

    public int getSessionIndex() {
        return sessionIndex;
    }

    public void setSessionIndex(int sessionIndex) {
        this.sessionIndex = sessionIndex;
    }

    public int getIntegralBranchId() {
        return integralBranchId;
    }

    public void setIntegralBranchId(int integralBranchId) {
        this.integralBranchId = integralBranchId;
    }

    public LadderWarGuessLog(AbstractHuman human) {
        super (CustomLogPrefixType.LadderWarGuess.getPrefix (), human);
    }
}
