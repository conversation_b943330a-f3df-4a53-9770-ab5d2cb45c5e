package com.game2sky.prilib.socket.logic.centerrank;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankClearRank;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerInfoToCenter;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerToCenter;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankReq;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 通用全服排行功能中心服协议处理.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月26日 下午3:42:03  Jet Lee
 */
@Controller
public class EntireRankCenterController extends BaseController {

    @Autowired
    private EntireRankCenterService service;

    /**
     * 中心服通用全服排行功能tick
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankCenterTick)
    public void ssEntireRankCenterTick() {
    	EntireRankCenterManager.getInstance().tick();
    }
    
    /**
     * 本服将玩家信息推送给中心服
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankPlayerToCenter)
    public void ssEntireRankPlayerToCenter(SSEntireRankPlayerToCenter msg) {
    	service.ssEntireRankPlayerToCenter(msg);
    }
    
    /**
     * 本服请求排行信息
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankReq)
    public void ssEntireRankReq(SSEntireRankReq msg, Head head) {
    	service.ssEntireRankReq(msg, head);
    }
    
    /**
     * 请求大多数玩家排名
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankPlayerRankReq)
    public void ssEntireRankPlayerRankReq(SSEntireRankPlayerRankReq msg) {
    	service.ssEntireRankPlayerRankReq(msg);
    }
    
    /**
     * 请求大多数玩家排名
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankScoreRankReq)
    public void ssEntireRankScoreRankReq(SSEntireRankScoreRankReq msg) {
    	service.ssEntireRankScoreRankReq(msg);
    }
    
    /**
     * 本服告知中心服清空排行信息
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankClearRank)
    public void ssEntireRankClearRank(SSEntireRankClearRank msg) {
    	service.ssEntireRankClearRank(msg);
    }
    
    /**
     * 玩家信息同步到中心服
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankPlayerInfoToCenter)
    public void ssEntireRankPlayerInfoToCenter(SSEntireRankPlayerInfoToCenter msg) {
    	service.ssEntireRankPlayerInfoToCenter(msg);
    }

    
}
