package com.game2sky.prilib.socket.logic.autochess.match;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessStartFight;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.autochess.AutoChessCenterManager;
import com.game2sky.prilib.socket.logic.autochess.data.AutoChessMatchPlayer;
import com.game2sky.publib.communication.game.struct.ServerAddress;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.util.KV;
import com.game2sky.publib.framework.util.MathUtils;
import com.game2sky.publib.framework.util.TimeUtils;
import com.game2sky.publib.match.constants.MatchType;
import com.game2sky.publib.socket.logic.base.IdTools;
import com.game2sky.publib.socket.logic.server.battle.BattleServerInfo;
import com.game2sky.publib.socket.logic.server.battle.IServerSelector;

/**
 * 自走棋匹配逻辑.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月9日 下午5:35:25  Jet Lee
 */
public class AutoChessMatchHandler {

	public static void match() {
		AutoChessMatchSort matchSort = AutoChessCenterManager.getInstance().getAutoChessMatchSort();
		List<AutoChessMatchBarrel> barrelList = matchSort.getBarrelList();
		if (matchSort.getTotal() <= 0) {
			// 匹配池里没人，不做匹配操作
			return;
		}
		int maxMatchTime = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_MAX_MATCH_TIME);
		int maxGrade = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_MATCH_AI_MAX_GRADE);
		
		List<List<AutoChessMatchPlayer>> successList = new ArrayList<List<AutoChessMatchPlayer>>();
		long now = System.currentTimeMillis();
		List<AutoChessMatchPlayer> matchPlayers = null;
		for (int i = 0; i < barrelList.size(); i++) {
			boolean success = false;
			do {
				matchPlayers = doMatch(barrelList, i, now);
				success = isFull(matchPlayers);
				if (!success) {
					// 房间没满人，再检测房间中的人有没有等待太长时间的，如果有，则直接算匹配成功。后期填充机器人
					for (AutoChessMatchPlayer autoChessMatchPlayer : matchPlayers) {
						if (now - autoChessMatchPlayer.getBeginMatchTime() > maxMatchTime) {
							// 等待时间超过最大匹配时间
							success = true;
							break;
						}
					}
					if (success) {
						// 超过最大匹配时间，但是人不够，原则上添加机器人。但是这里要做一个额外判断，
						// 如果这组玩家里有段位超过某个值的，则不添加机器人，也不算匹配成功
						for (AutoChessMatchPlayer autoChessMatchPlayer : matchPlayers) {
							if (autoChessMatchPlayer.getGrade() > maxGrade) {
								// 玩家段位太高，不允许匹配成功，添加机器人
								success = false;
								break;
							}
						}
					}
				}
				if (success) {
					successList.add(matchPlayers);
					// 匹配成功，从桶里移除相应玩家
					for (AutoChessMatchPlayer matchPlayer : matchPlayers) {
						matchSort.deleteElement(matchPlayer.getPlayerId(), matchPlayer.getGrade());
					}
				}
			} while (success);// 如果匹配成功，并且已经从桶里移除相关玩家，则继续从当前桶做匹配
		}
		if (!successList.isEmpty()) {
			// 匹配成功的玩家
			for (List<AutoChessMatchPlayer> tmpMatchPlayers : successList) {
				onMatchSuccess(tmpMatchPlayers, now);
			}
		}
	}

	private static List<AutoChessMatchPlayer> doMatch(List<AutoChessMatchBarrel> barrelList, int index, long now) {
		List<AutoChessMatchPlayer> tmpMatchPlayers = new ArrayList<AutoChessMatchPlayer>();// 临时房间
		addMatchPlayer(barrelList.get(index), tmpMatchPlayers);
		if (isFull(tmpMatchPlayers)) {
			// 人数够了直接返回
			return tmpMatchPlayers;
		}
		// 人数不够，则从相邻的桶里找能够匹配的数据
		// 先从比当前桶分数低的桶里找
		for (int j = index - 1; j > 0; j--) {
			AutoChessMatchBarrel barrel = barrelList.get(j);
			Long targetGrade = barrel.getId();
			if (!canMatch(tmpMatchPlayers, targetGrade, now)) {
				// 积分不满足，差距太大，直接跳出。
				break;
			}
			addMatchPlayer(barrel, tmpMatchPlayers);
		}
		if (isFull(tmpMatchPlayers)) {
			// 人数够了直接返回
			return tmpMatchPlayers;
		}
		// 再从比当前桶分数高的桶里找
		for (int j = index + 1; j < barrelList.size(); j++) {
			AutoChessMatchBarrel barrel = barrelList.get(j);
			Long targetGrade = barrel.getId();
			if (!canMatch(tmpMatchPlayers, targetGrade, now)) {
				// 积分不满足，差距太大，直接跳出。
				break;
			}
			addMatchPlayer(barrel, tmpMatchPlayers);
		}
		return tmpMatchPlayers;
	}

	private static boolean canMatch(List<AutoChessMatchPlayer> matchPlayers, long targetGrade, long now) {
		if (matchPlayers.isEmpty()) {
			return true;
		}
		int maxOffSet = 0; // 根据匹配时间算出房间里的成员，最大的允许间隔上下限
		int avgGrade = 0; // 房间里的平均积分
		List<KV<String, String>> kvs = BaseService.getConfigValue(PriConfigKeyName.AUTO_CHESS_TIMEOUT_OFFSET);
		for (AutoChessMatchPlayer player : matchPlayers) {
			long beginMatchTime = player.getBeginMatchTime();
			long timeout = now - beginMatchTime;
			int offset = getOffset(kvs, timeout);
			maxOffSet = MathUtils.max(maxOffSet, offset);
			avgGrade += player.getGrade();
		}
		avgGrade /= matchPlayers.size();
		if (MathUtils.abs(targetGrade - avgGrade) <= maxOffSet) {
			return true;
		}
		return false;
	}

	private static int getOffset(List<KV<String, String>> kvs, long timeout) {
		int offset = 0;
		for (KV<String, String> kv : kvs) {
			if (timeout >= Integer.parseInt(kv.getK()) * TimeUtils.SECOND) {
				offset = Integer.parseInt(kv.getV());
			} else {
				return offset;
			}
		}
		return offset;
	}
	
	private static void addMatchPlayer(AutoChessMatchBarrel barrel, List<AutoChessMatchPlayer> tmpMatchPlayers) {
		AutoChessMatchSort matchSort = AutoChessCenterManager.getInstance().getAutoChessMatchSort();
		LinkedList<Long> playerIds = barrel.getPlayerIds();
		// 先把当前桶里的玩家放入临时房间里，但是不能超过房间最大人数
		for (Iterator<Long> iterator = playerIds.iterator(); iterator.hasNext();) {
			Long playerId = (Long) iterator.next();
			if (isFull(tmpMatchPlayers)) {
				// 人数够了，直接跳出
				return;
			}
			AutoChessMatchPlayer matchPlayer = matchSort.getMatchPlayer(playerId);
			if (matchPlayer != null) {
				tmpMatchPlayers.add(matchPlayer);
			} else {
				iterator.remove();// 移除错误数据
				AMLog.LOG_ERROR.error("[AutoChessMatchHandler] [match] [自走棋桶里有的playerId，内存数据没有该玩家匹配数据，逻辑有问题]");
			}
		}
	}

	/**
	 * 是否满足匹配人数
	 * @param matchPlayers
	 * @return
	 */
	private static boolean isFull(List<AutoChessMatchPlayer> matchPlayers) {
		if (matchPlayers == null) {
			return false;
		}
		int playerNum = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_PLAYER_NUM);
		return matchPlayers.size() >= playerNum;
	}

	private static void onMatchSuccess(List<AutoChessMatchPlayer> tmpMatchPlayers, long now) {
		// 分配战斗服
		ServerAddress serverAddress = null;
		IServerSelector serverSelector = AMFrameWorkBoot.getBean(IServerSelector.class);
		BattleServerInfo battleServerInfo = serverSelector.select(MatchType.AUTO_CHESS.getMaxNum());
		if (battleServerInfo == null) {
			// 没有可用战斗服，重新放回匹配队列里
			AMLog.fatal("autochess onMatchSuccess battleSelector error!");
			AutoChessMatchSort matchSort = AutoChessCenterManager.getInstance().getAutoChessMatchSort();
			// 重新进入匹配池
			for (AutoChessMatchPlayer matchPlayer : tmpMatchPlayers) {
				matchSort.addElement(matchPlayer);
			}
			return;
		} else {
			battleServerInfo.place(MatchType.AUTO_CHESS.getMaxNum(), TimeUnit.SECONDS, 5);
			serverAddress = battleServerInfo.toServerAddress();
		}
		List<Long> playerIds = new ArrayList<Long>();
		List<Integer> serverIds = new ArrayList<Integer>();
		for (AutoChessMatchPlayer matchPlayer : tmpMatchPlayers) {
			playerIds.add(matchPlayer.getPlayerId());
			serverIds.add(matchPlayer.getServerId());
		}
		int battleDelayTime = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_BATTLE_DELAY_TIME);
		long enterBattleTime = now + battleDelayTime;
		long nextId = IdTools.nextId();
		for (AutoChessMatchPlayer matchPlayer : tmpMatchPlayers) {
			// 通知本服，匹配成功,准备战斗
			SSAutoChessStartFight ssMsg = new SSAutoChessStartFight();
			long playerId = matchPlayer.getPlayerId();
			ssMsg.setPlayerId(playerId);
			int serverId = matchPlayer.getServerId();
			ssMsg.setServerId(serverId);
			ssMsg.setAddress(serverAddress);
			ssMsg.setBattleGuid(nextId);
			ssMsg.setPlayerIds(playerIds);
			ssMsg.setServerIds(serverIds);
			ssMsg.setEnterBattleTime(enterBattleTime);
			CoreBoot.centerToBenfu(ssMsg, playerId, serverId);
		}
		AMLog.LOG_COMMON.info("" + JsonUtils.O2S(tmpMatchPlayers));
	}

}
