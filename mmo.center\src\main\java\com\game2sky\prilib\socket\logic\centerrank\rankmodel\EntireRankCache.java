package com.game2sky.prilib.socket.logic.centerrank.rankmodel;

import java.util.List;

import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerInfo;

/**
 * 通用全服排行的排行榜数据.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月26日 下午4:49:53  Jet Lee
 */
public class EntireRankCache {
	
	private List<EntireRankPlayerInfo> rankList = null;
	
	public List<EntireRankPlayerInfo> getRankList() {
		return rankList;
	}
	
	public void setRankList(List<EntireRankPlayerInfo> rankList) {
		this.rankList = rankList;
	}

}
