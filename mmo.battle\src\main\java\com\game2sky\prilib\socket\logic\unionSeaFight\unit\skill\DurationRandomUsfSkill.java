package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitBuffChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffInfo;
import com.game2sky.prilib.core.dict.domain.DictUsfSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuffFactory;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;

/**
 * 周期性随机buff技能
 *
 * <AUTHOR>
 * @version v0.1 2018年10月25日 上午9:00:16  lidong
 */
public class DurationRandomUsfSkill extends DurationUsfSkill
{
	/** 所属的塔 */
	private USFFortress ownerTower;

	private List<USFBuff> buffLibrary;

	@Override
	public void setData(DictUsfSkill dictUsfSkill)
	{
		super.setData(dictUsfSkill);
		if (usfBuffers != null && usfBuffers.size() > 0)
		{
			buffLibrary = new ArrayList<USFBuff>(usfBuffers);
			usfBuffers.clear();
			usfBuffers.add(buffLibrary.get(ThreadLocalRandom.current().nextInt(buffLibrary.size())));
		}
	}

	@Override
	public boolean IsArriveDuration(long now)
	{
		return duration > 0 && now - lastDurationTime >= duration;
	}

	@Override
	public void doDuration()
	{
		lastDurationTime = ownerTower.getNow();
		int oldId = usfBuffers.size() > 0 ? usfBuffers.get(0).getGuid():-1;
		usfBuffers.clear();
		usfBuffers.add(buffLibrary.get(ThreadLocalRandom.current().nextInt(buffLibrary.size())));
		int newId = usfBuffers.get(0).getGuid();
		if(newId == oldId)
		{
			return;
		}
		SCUSFUnitBuffChange scUSFUnitBuffChange = new SCUSFUnitBuffChange();
		scUSFUnitBuffChange.setUnitId(this.owner.getId());
		if(oldId != -1)
		{
			ArrayList<Integer> removeList = new ArrayList<Integer>();
			removeList.add(oldId);
			scUSFUnitBuffChange.setRemoveBuffGuids(removeList);
		}
		
		USFBuff newBuff = usfBuffers.get(0);
		if (!newBuff.isClientSide())
		{
			ArrayList<USFClientBuffInfo> addBuffInfos = new ArrayList<USFClientBuffInfo>();
			addBuffInfos.add(newBuff.copyClientInfoToSend());	
			scUSFUnitBuffChange.setBuffInfos(addBuffInfos);
		}
		
		this.owner.getCamp().getUsFight().broadcast(scUSFUnitBuffChange);
		
		LinkedList<IUSFUnitFightable> defenders = ownerTower.getDefenders();
		if(defenders == null || defenders.size() <= 0)
		{
			return;
		}
		List<USFBuff> removeBuffs = new ArrayList<USFBuff>();
		for (IUSFUnitFightable defender : defenders)
		{
			if(defender instanceof AbstractUSFUnit == false)
			{
				continue;
			}
			List<USFBuff> dBuffs = defender.getBuffs();
			if (dBuffs != null && dBuffs.size() > 0)
			{
				for (USFBuff buff : dBuffs)
				{
					if (buff.getSourceSkill() == this)
					{
						removeBuffs.add(buff);
					}
				}
				if (removeBuffs.size() > 0)
				{
					for(USFBuff rb : removeBuffs)
					{
						defender.removeBuff(rb);
					}
				}
				removeBuffs.clear();
			}
			for (USFBuff buff : getUsfBuffers())
			{
				USFBuff copyBuff = buff.copy();
				copyBuff.setUnloadType(unloadType);
				copyBuff.setSourceUnit(owner);
				copyBuff.setOwnerUnit((AbstractUSFUnit)defender);
				copyBuff.setSourceSkill(this);
				USFBuffFactory.addExtraProps(owner, copyBuff.getClientInfo().getProInfos());
				defender.addBuff(copyBuff);
			}
		}
	}

	@Override
	public void setOwner(AbstractUSFUnit owner)
	{
		super.setOwner(owner);
		ownerTower = (USFFortress) owner;
	}

}
