package com.game2sky.prilib.benfu.logic;

import java.util.List;

import com.game2sky.prilib.core.dict.domain.DictHero;
import com.game2sky.publib.Globals;
import com.game2sky.publib.async.IIoOperation;
import com.game2sky.publib.communication.game.test.CSGm;
import com.game2sky.publib.dict.domain.DictItem;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.socket.logic.human.AbstractHuman;

/**
 * Created by wuyidi on 2017/7/4.
 *
 * <AUTHOR>
 * @version v0.1 2017年07月04日 13:44 wuyidi
 */
public class TestAsyncGMGruop implements IIoOperation {

	private List<AbstractHuman> humanList;

	public TestAsyncGMGruop(List<AbstractHuman> humanList) {
		this.humanList = humanList;
	}

	@Override
	public int doStart() {
		return IIoOperation.STAGE_START_DONE;
	}

	@Override
	public int doIo() {
		for (AbstractHuman human : humanList) {
			doGMs(human);
		}
		return IIoOperation.STAGE_IO_DONE;
	}

	@Override
	public int doStop() {
		return IIoOperation.STAGE_STOP_DONE;
	}

	public void doGMs(AbstractHuman human) {
		this.doInitGM(human);
		this.doContainerGM(human);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
		this.doMoneyGM(human);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
		this.doShopGM(human);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
		this.doAddHero(human);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
		this.doChatGM(human);
	}

	private void doInitGM(AbstractHuman human) {
		CSGm gm = new CSGm("addExp 999999");
		Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
		gm = new CSGm("dailyActivity");
		msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(10L);
		} catch (Exception e) {

		}
	}

	private void doContainerGM(AbstractHuman human) {
		int count = 20;
		for (DictItem dictItem : DictItem.getAllItems()) {
			if (count == 0) {
				break;
			}
			int itemId = dictItem.getItemId();
			String gmStr = "addItem " + itemId + " 10";
			CSGm gm = new CSGm(gmStr);
			Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
			try {
				Thread.sleep(5L);
			} catch (Exception e) {

			}
			count--;
		}
	}

	private void doMoneyGM(AbstractHuman human) {
		CSGm gm = new CSGm("addmoney 3 999999");
		Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(5L);
		} catch (Exception e) {

		}
		gm = new CSGm("addPayDiamond 999999");
		msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(5L);
		} catch (Exception e) {

		}
		gm = new CSGm("addmoney 4 999999");
		msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(5L);
		} catch (Exception e) {

		}
		gm = new CSGm("addmoney 5 999999");
		msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
		try {
			Thread.sleep(5L);
		} catch (Exception e) {

		}
	}

	private void doShopGM(AbstractHuman human) {
		CSGm gm = new CSGm("buyFromNPCShop");
		for (int i = 0; i < 20; i++) {
			Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
			try {
				Thread.sleep(5L);
			} catch (Exception e) {

			}
		}
		gm = new CSGm("buyFromMallShop");
		for (int i = 0; i < 20; i++) {
			Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
			try {
				Thread.sleep(6L);
			} catch (Exception e) {

			}
		}
	}

	private void doAddHero(AbstractHuman human) {
		int count = 10;
		for (DictHero dictHero : DictHero.getAllHeros()) {
			if (count == 0) {
				break;
			}
			int heroId = dictHero.getHeroId();
			String gmStr = "addHero " + heroId;
			CSGm gm = new CSGm(gmStr);
			Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
			try {
				Thread.sleep(5L);
			} catch (Exception e) {

			}
			count--;
		}
	}

	private void doChatGM(AbstractHuman human) {
		for (AbstractHuman toHuman : this.humanList) {
			if (toHuman.getPlayerId() != human.getPlayerId()) {
				CSGm gm = new CSGm("sendChat " + toHuman.getPlayerId());
				Message msg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), gm, null, null, null);
				Globals.getMsgProcessDispatcher().put(msg);
				try {
					Thread.sleep(5L);
				} catch (Exception e) {

				}
			}
		}
	}
}
