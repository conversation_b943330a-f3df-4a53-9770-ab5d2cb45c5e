package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFightStateChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.fsm.AbstractFsmController;

/**
 * TODO 海战状态机
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 上午11:22:01  guojijun
 */
public class USFightFsmController extends AbstractFsmController<USFStateType> {

	private final USFight usFight;
	private USFClientStateInfo clientInfo;

	public USFightFsmController(USFight usFight) {
		this.usFight = usFight;
		this.init();
	}

	private void init() {
		USFightFsmStateNone fsmStateNone = new USFightFsmStateNone(this);
		fsmStateNone.enter();
		this.curState = fsmStateNone;
		this.addFsmState(fsmStateNone);
		this.addFsmState(new USFightFsmStatePrepare(this));
		this.addFsmState(new USFightFsmStateRunning(this));
		this.addFsmState(new USFightFsmStateClose(this));
	}

	@Override
	protected void onSwitchState(USFStateType oldStateType, USFStateType curStateType) {
		AMLog.LOG_UNION.debug("海战状态切换,fight={},oldStateType={},curStateType={}", this.usFight.getId(), oldStateType,
			curStateType);
		SCUSFightStateChange fightStateChange = new SCUSFightStateChange();
		fightStateChange.setStateInfo(this.copyTo());
		this.usFight.broadcast(fightStateChange);
	}

	public USFight getUsFight() {
		return usFight;
	}

	public USFClientStateInfo copyTo() {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientStateInfo();
		} else {
			this.clientInfo.setPrepareInfo(null);
			this.clientInfo.setRunningInfo(null);
			this.clientInfo.setCloseInfo(null);
		}
		this.clientInfo.setStateType(this.getCurStateType());
		AbstractUSFightFsmState fightFsmState = this.getCurState();
		fightFsmState.copyTo(this.clientInfo);
		return this.clientInfo;
	}

}
