package com.game2sky.prilib.socket.logic.centerrank;

import com.game2sky.prilib.communication.game.entirerank.SSEntireRankCenterTick;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 中心服通用全服排行心跳.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月26日 下午4:33:56  <PERSON>
 */
public class ScheduleEntireRankCenterTick extends ScheduledMessage {

    /** 心跳10秒一次就够了。因为通用排行功能心跳的主意功能是每隔1分钟刷新一下排行榜。每隔10分钟保存一下数据到redis */
    public static final int PERIOD = 10000;

    public ScheduleEntireRankCenterTick() {
        super(System.currentTimeMillis());
    }

    @Override
    public void execute() {
        // 这里只是发送消息
    	SSEntireRankCenterTick tick = new SSEntireRankCenterTick();
        Message message = Message.buildByServerId(0L, 0, tick, null, null, null);
        Globals.getMsgProcessDispatcher().put(message);
    }
}
