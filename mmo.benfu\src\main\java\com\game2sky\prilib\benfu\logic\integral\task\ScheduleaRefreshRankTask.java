package com.game2sky.prilib.benfu.logic.integral.task;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;
import java.util.Map.Entry;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.integral.IntegralGameManager;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralArenaRankReq;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 天梯定时刷新排行榜任务
 *
 * <AUTHOR>
 * @version v0.1 2020年4月20日 下午2:01:14  zhoufang
 */
public class ScheduleaRefreshRankTask extends ScheduledMessage {

	public static final long PERIOD = 7500L;
	public final Map<Integer, LinkedList<Integer>> branchServerIds;
	public final Map<Integer,IntegralAreaType> lastAreaTypeMap;

	public ScheduleaRefreshRankTask() {
		super(System.currentTimeMillis());
		this.branchServerIds = new HashMap<>(IntegralGameManager.getInstance().getGroupServerIds());
		this.lastAreaTypeMap = new HashMap<>(branchServerIds.size());
		for(Integer branch : branchServerIds.keySet()){
			lastAreaTypeMap.put(branch, null);
		}
	}

	@Override
	public void execute() {
		if (!CoreBoot.START_SUCCESS || branchServerIds.isEmpty()) {
			return;
		}
		for (Entry<Integer, LinkedList<Integer>> entry : branchServerIds.entrySet()) {
			int areaType = getAreaType(entry.getKey());
			SSIntegralArenaRankReq req = new SSIntegralArenaRankReq(entry.getKey(), areaType, 0, null, true);
			Message msg = Message.buildByServerId(0L, entry.getValue().getFirst(), req, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
		}
	}

	/**
	 * 本次应该请求刷新的赛区
	 * @param branch
	 * @return
	 */
	private int getAreaType(int branch) {
		boolean newVersion = IntegralGameManager.getInstance().getGroupById(branch).isNewVersion();
		IntegralAreaType lastType = this.lastAreaTypeMap.get(branch);
		IntegralAreaType nowAreaType = null;
		if(newVersion){
			// 此分组是新版本
			if(lastType == null || !IntegralAreaType.isNewVersion(lastType.value())){
				// 新版默认进练习赛
				nowAreaType = IntegralAreaType.NewDefault();
			}else{
				// 上次请求的也是新版
				nowAreaType = lastType == IntegralAreaType.PRACTICE ? IntegralAreaType.PINNACLE: IntegralAreaType.PRACTICE;
			}
		}else{
			// 此分组是旧版本
			if(lastType == null || !IntegralAreaType.isOldVersion(lastType.value())){
				// 旧版默认无限制赛
				nowAreaType = IntegralAreaType.oldDefault();
			}else{
				// 上次请求的也是旧版
				nowAreaType = lastType == IntegralAreaType.NOLIMIT ? IntegralAreaType.AMEND: IntegralAreaType.NOLIMIT;
			}
		}
		this.lastAreaTypeMap.put(branch, nowAreaType);
		return nowAreaType.value();
	}

}
