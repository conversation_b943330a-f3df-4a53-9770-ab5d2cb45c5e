package com.game2sky.prilib.benfu.logic.integral;

import com.game2sky.prilib.core.socket.logic.integralCommon.thread.IntegralThreadGroup;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.communication.MessageType;
import com.game2sky.publib.framework.thread.ExecutableMessageHandler;
import com.game2sky.publib.framework.thread.QueueMessageProcessor;

/**
 * <AUTHOR>
 * @version v0.1 2018年3月27日 上午11:39:48  zhoufang
 */
public class IntegralGameThreadGroup extends IntegralThreadGroup{
	
	@Override
	public void init() {
		// 只需要启动一根主线程
		this.mainProcessor = new QueueMessageProcessor(new ExecutableMessageHandler(), "IntegralMainThread");
	}
	
	@Override
	public void put(Message msg) {
		MessageType messageType = msg.messageType();
		if (messageType == MessageType.INTEGRAL_MAIN_MSG) {
			this.mainProcessor.put(msg);
			return;
		}
		AMLog.LOG_ERROR.error("Error MessageType msg {}, the messageType is {}", msg.getTypeName(), messageType);
	}
}
