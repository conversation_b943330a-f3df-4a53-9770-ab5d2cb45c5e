package com.game2sky.prilib.socket.logic.integral.data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 *
 * <AUTHOR>
 * @version v0.1 2018年3月30日 上午11:01:28  Administrator
 */
public class IntegralAreaRankCache {
	
	private static AtomicBoolean useNewRank = new AtomicBoolean(false);

	/** 当前排行榜缓存 */
	private List<IntegralRankItem> rankList = new ArrayList<IntegralRankItem>();
	
	public List<IntegralRankItem> getRankList() {
		return this.newRankList;
	}

	public void setRankList(List<IntegralRankItem> rankList) {
		this.newRankList = new LinkedList<IntegralRankItem>(rankList);
	}
	
	public List<IntegralRankItem> getRealRankList() {
		return useNewRank.get() ? this.newRankList : this.rankList;
	}
	
	public static void changeRankType(boolean isOpen){
		useNewRank.set(isOpen);
	}
	
	public IntegralRankItem createRankItem(long playerId, int serverId, int displayServerId, int integral, long integralTime, PlayerDisplayInformation information) {
		IntegralRankItem item = new IntegralRankItem();
		item.setPlayerId(playerId);
		item.setServerId(serverId);
		item.setIntegral(integral);
		item.setIntegralTime(integralTime);
		item.setInformation(information);
		item.setDisplayServerId(displayServerId);
		return item;
	}

	/*****************增加一种新方式***************************************************/

	/** 当前排行榜缓存 (新保存方式)*/
	private final RankCompare compare = new RankCompare();
	private LinkedList<IntegralRankItem> newRankList = new LinkedList<IntegralRankItem>();
	
	public void recordNewRank(long playerId, int serverId, int displayServerId, int integral, long integralTime, PlayerDisplayInformation information) {
		try{
			if (newRankList.size() >= IntegralConst.RANK_MAX && integral < newRankList.getLast().getIntegral()) {
				return;
			}
			// 加入排行榜
			deleteFromNewRank(playerId);
			IntegralRankItem rankItem = createRankItem(playerId, serverId, displayServerId, integral, integralTime, information);
			addNewRankItem(rankItem);
		}catch(Throwable t){
			AMLog.LOG_ERROR.error("记录新排行榜错误,playerId={},integral={},integralTime={}",playerId,integral,integralTime,t);
		}
	}

	private void deleteFromNewRank(long playerId) {
		Iterator<IntegralRankItem> iterator = newRankList.iterator();
		IntegralRankItem item = null;
		while(iterator.hasNext()){
			item = iterator.next();
			if(item.getPlayerId() == playerId){
				iterator.remove();
				break;
			}
		}
	}

	private void addNewRankItem(IntegralRankItem rankItem) {
//		if(newRankList.size() == 0 || rankItem.getIntegral() < newRankList.getLast().getIntegral()){
//			// 最后一名
//			newRankList.add(rankItem);
//			return;
//		}
//		if(rankItem.getIntegral() == newRankList.getLast().getIntegral() && rankItem.getIntegralTime() >= newRankList.getLast().getIntegralTime()){
//			// 最后一名
//			newRankList.add(rankItem);
//			return;
//		}
//		if(rankItem.getIntegral() > newRankList.getFirst().getIntegral()){
//			// 第一名
//			newRankList.addFirst(rankItem);
//		}else{
//			// 中间名次
//			Collections.sort(newRankList,compare);
//		}
		newRankList.add(rankItem);
		Collections.sort(newRankList,compare);
		if (newRankList.size() > IntegralConst.RANK_MAX){
			newRankList.removeLast();
		}
	}
	
	class RankCompare implements Comparator<IntegralRankItem>{

		@Override
		public int compare(IntegralRankItem o1, IntegralRankItem o2) {
			if (o1.getIntegral() > o2.getIntegral()) {
				return -1;
			}
			if (o1.getIntegral() < o2.getIntegral()) {
				return 1;
			}
			if (o1.getIntegralTime() > o2.getIntegralTime()) {
				return 1;
			}
			if (o1.getIntegralTime() < o2.getIntegralTime()) {
				return -1;
			}
			return 0;
		}
		
	}
	
	public LinkedList<IntegralRankItem> getNewRankList() {
		return newRankList;
	}

}
