package com.game2sky.prilib.socket.logic.autochess.match;

import java.util.LinkedList;

import com.game2sky.prilib.socket.logic.integral.sort.Barrel;

/**
 * 自走棋匹配积分桶.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月8日 下午3:57:59  <PERSON>
 */
public class AutoChessMatchBarrel extends Barrel {

	/** 同一段位的玩家Ids */
	private LinkedList<Long> playerIds = new LinkedList<Long>();

	public AutoChessMatchBarrel(Long id) {
		this.id = id;
	}

	public void addPlayerId(Long playerId) {
		this.playerIds.addLast(playerId);
		this.haveNumber = playerIds.size();
	}

	public void removePlayerId(Long playerId) {
		this.playerIds.remove(playerId);
		this.haveNumber = playerIds.size();
	}

	public LinkedList<Long> getPlayerIds() {
		return playerIds;
	}
}
