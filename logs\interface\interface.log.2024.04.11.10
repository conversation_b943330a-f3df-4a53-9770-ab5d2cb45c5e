2024-04-11 10:23:28,714 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [1/2/3/SSActivityTeamBossStart/0] - {"body":{"activityId":0},"head":{"len":2,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:23:29,020 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [24/2/26/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:23:29,843 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [10/3/13/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:23:30,008 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:23:30,319 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetMailList/************] - {"body":{"mails":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:30,576 [LoginProcessorGroup-thread-1] INFO (BaseService.java:110) - [2/728/730/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:23:30,591 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:30,596 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0xd6902dd1, L:/127.0.0.1:12306 - R:/127.0.0.1:43306]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"kvBeon9zRAWk7/NKmL75lg==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:30,998 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85608,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:23:31,756 [LadderWarBenfu] INFO (BaseService.java:110) - [0/0/0/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:31,757 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:31,825 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [58/1/59/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:31,826 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:31,826 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [59/0/59/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:31,828 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCScenePreEnter/************] - {"body":{"pos":{"x":-1.156,"y":10.897,"z":104.305},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:31,863 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSyncTime/************] - {"body":{"clientTime":1712805811859,"serverTime":1712805811861},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:36,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,663 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [62/2/64/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:36,724 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,728 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,729 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,744 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,747 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [17/0/17/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,749 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [20/0/20/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,752 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [21/0/21/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,756 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [9/0/9/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,758 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,760 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,760 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,761 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,761 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,761 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,761 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,761 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,762 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,763 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,770 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:23:36,770 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [26/0/26/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:23:36,799 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,800 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712805816784&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,800 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [4/0/4/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,803 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,805 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [6/0/6/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,808 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [8/0/8/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,809 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [8/86/94/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:36,810 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [11/0/11/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,821 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [10/0/10/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,826 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,835 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [26/0/26/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,839 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [33/0/33/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,842 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [37/0/37/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,868 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [40/0/40/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,870 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [67/0/67/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,872 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [67/0/67/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,874 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [69/0/69/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,875 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [70/0/70/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,876 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [71/0/71/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,878 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [72/0/72/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,879 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [74/0/74/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":181},"head":{"len":5,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,880 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [75/0/75/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [76/0/76/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:36,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [73/0/73/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:37,157 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [49/3/52/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:23:37,159 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:23:38,204 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,205 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [47/0/47/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,206 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,207 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [48/1/49/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,209 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [49/0/49/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,209 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,210 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [50/0/50/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,216 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,704 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,704 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [61/0/61/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,705 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [30/0/30/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,706 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,751 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:38,751 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,768 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:23:38,768 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [46/0/46/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:44,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:23:47,414 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [45/0/45/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:23:47,414 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:23:50,066 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:23:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:23:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:00,048 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:24:01,428 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:24:01,428 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:24:06,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:10,059 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:24:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:19,800 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:24:19,800 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:24:21,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:29,038 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:24:29,038 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:24:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:30,033 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:24:33,898 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [47/0/47/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:24:33,898 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:24:36,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:44,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:47,944 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [39/0/39/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:24:47,944 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:24:50,043 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:24:51,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:24:59,106 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:00,040 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:25:06,350 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [54/0/54/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:25:06,350 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:25:06,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:10,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:25:14,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:20,370 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:25:20,370 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:25:21,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:29,010 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:25:29,010 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:25:29,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:30,063 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:25:36,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:38,789 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [59/0/59/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:25:38,789 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:25:44,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:50,009 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:25:51,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:25:57,117 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [11/0/11/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:25:57,117 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:25:59,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:00,058 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:26:06,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:10,047 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:26:11,211 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [41/0/41/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:26:11,211 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:26:14,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:21,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:25,256 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [30/0/30/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:26:25,256 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:26:29,008 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [15/0/15/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:26:29,008 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:26:29,102 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:30,064 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:26:36,611 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:39,300 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [19/0/19/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:26:39,300 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:26:44,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:50,013 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:26:51,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:26:57,702 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [43/0/43/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:26:57,702 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:26:59,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:00,073 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [48/0/48/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:27:06,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:10,026 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:27:11,748 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [31/0/31/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:27:11,748 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:27:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:21,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:29,052 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [61/0/61/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:27:29,052 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:27:29,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:30,043 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:27:30,153 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [51/0/51/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:27:30,153 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:27:36,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:44,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:48,517 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [40/0/40/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:27:48,517 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:27:50,023 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:27:51,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:27:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:00,014 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [0/0/0/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:28:02,547 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [17/0/17/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:28:02,547 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:28:06,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:10,006 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:28:14,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:20,916 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [1/0/1/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:28:20,916 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:28:21,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:29,042 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:28:29,042 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [45/0/45/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:28:29,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:30,033 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:28:34,977 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [6/0/6/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:28:34,977 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:28:36,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:44,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:50,034 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:28:51,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:28:53,376 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [24/0/24/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:28:53,376 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:28:59,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:00,037 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:29:06,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:10,036 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:29:11,760 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [31/0/31/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:29:11,760 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:29:14,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:25,807 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [20/0/20/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:29:25,807 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:29:29,055 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:29:29,055 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [60/0/60/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:29:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:30,050 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:29:36,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:44,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:44,198 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [36/0/36/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:29:44,199 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:29:49,995 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:29:51,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:29:58,254 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [30/0/30/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:29:58,254 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:29:59,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:00,074 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:30:06,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:10,028 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:30:14,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:16,617 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [23/0/23/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:30:16,617 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:30:21,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:29,007 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:30:29,007 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:30:29,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:30,061 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:30:30,668 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [18/0/18/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:30:30,668 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:30:36,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:44,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:44,719 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [9/0/9/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:30:44,719 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:30:50,023 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:30:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:30:59,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:00,027 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [15/0/15/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:31:03,093 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [4/0/4/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:31:03,093 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:31:06,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:10,035 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:31:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:17,200 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [48/0/48/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:31:17,200 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:31:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:29,030 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:31:29,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:31:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:30,032 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:31:31,258 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [53/0/53/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:31:31,258 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:31:36,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:44,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:49,648 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [59/0/59/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:31:49,648 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:31:50,037 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:31:51,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:31:59,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:00,034 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:32:03,681 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [43/0/43/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:32:03,681 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:32:06,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:10,012 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:32:14,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:21,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:22,048 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [32/0/32/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:32:22,048 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:32:29,016 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:32:29,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:32:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:30,010 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:32:36,135 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [61/0/61/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:32:36,135 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:32:36,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:44,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:50,062 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:32:50,171 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [40/0/40/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:32:50,171 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:32:51,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:32:59,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:00,068 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [60/0/60/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:33:04,223 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [27/0/27/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:33:04,223 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:33:06,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:10,022 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:33:14,106 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:18,268 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [22/0/22/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:33:18,268 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:33:21,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:29,020 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [17/0/17/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:33:29,020 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:33:29,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:30,018 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:33:36,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:36,663 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [36/0/36/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:33:36,663 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:33:37,348 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:33:37,349 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [59/0/59/CSGetStatisticsInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:33:44,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:50,055 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:33:50,721 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:33:50,721 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [33/0/33/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:33:51,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:33:59,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:00,073 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [62/0/62/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:34:04,776 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [34/0/34/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:34:04,776 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:34:06,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:10,005 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:34:14,102 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:21,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:23,176 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [54/0/54/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:34:23,176 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:34:29,019 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:34:29,019 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:34:29,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:30,006 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:34:36,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:37,193 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [16/0/16/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:34:37,193 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:34:44,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:50,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:34:51,246 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [5/0/5/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:34:51,246 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:34:51,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:34:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:00,042 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:35:06,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:10,027 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:35:11,813 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [23/0/23/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:35:11,813 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:35:14,102 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:21,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:29,021 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:35:29,021 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:35:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:30,019 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:35:30,188 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [20/0/20/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:35:30,188 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:35:36,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:44,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:44,257 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [25/0/25/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:35:44,257 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:35:50,007 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:35:51,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:35:59,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:00,057 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:36:02,644 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [43/0/43/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:36:02,644 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:36:06,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:10,024 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:36:14,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:21,035 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [50/0/50/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:36:21,035 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:36:21,611 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:29,044 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:36:29,044 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:36:29,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:30,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:36:32,185 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [56/0/56/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:36:36,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:44,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:50,009 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:36:51,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:36:59,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:00,059 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:37:06,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:10,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:37:14,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:21,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:29,035 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:37:29,035 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:37:29,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:30,031 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:37:36,611 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:44,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:50,012 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:37:51,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:37:59,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:00,024 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [15/0/15/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:38:06,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:10,038 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:38:14,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:29,014 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [15/0/15/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:38:29,014 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:38:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:30,012 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:38:36,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:44,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:50,025 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:38:51,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:38:59,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:00,070 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [60/0/60/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:39:06,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:10,045 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:39:14,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:29,054 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:39:29,054 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:39:29,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:30,044 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:39:36,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:44,115 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:50,053 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:39:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:39:59,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:00,066 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [62/0/62/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:40:06,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:10,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:40:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:21,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:29,030 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:40:29,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:40:29,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:30,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:40:36,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:44,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:50,009 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:40:51,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:40:59,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:00,062 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:41:06,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:10,008 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:41:14,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:21,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:29,038 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:41:29,038 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:41:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:30,023 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:41:36,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:44,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:50,041 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:41:51,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:41:59,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:00,066 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:42:06,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:10,014 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:42:14,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:21,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:29,007 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:42:29,007 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:42:29,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:30,063 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:42:36,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:44,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:50,028 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:42:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:42:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:00,047 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:43:06,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:10,014 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:43:14,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:21,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:29,054 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [62/0/62/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:43:29,054 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:43:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:30,050 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:43:36,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:44,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:50,018 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:43:51,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:43:59,115 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:00,018 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:44:06,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:10,054 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:44:14,102 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:21,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:29,040 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [40/0/40/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:44:29,040 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:44:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:30,023 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:44:36,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:44,115 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:50,062 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:44:51,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:44:59,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:00,035 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [29/0/29/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:45:06,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:09,999 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:45:14,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:21,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:29,048 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [48/0/48/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:45:29,048 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:45:29,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:30,038 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:45:36,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:44,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:50,048 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:45:51,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:45:59,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:00,063 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [61/0/61/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:46:06,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:10,026 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:46:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:21,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:29,047 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:46:29,047 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:46:29,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:30,039 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:46:36,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:44,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:50,015 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:46:51,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:46:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:00,042 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:47:06,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:10,052 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:47:14,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:21,611 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:29,032 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:47:29,032 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:47:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:30,029 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:47:36,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:44,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:50,034 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:47:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:47:59,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:00,068 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [62/0/62/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:48:06,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:10,011 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:48:14,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:21,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:29,041 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:48:29,041 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:48:29,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:30,035 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:48:36,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:44,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:50,003 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:48:51,601 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:48:59,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:00,073 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [61/0/61/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:49:06,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:10,030 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:49:14,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:29,068 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:49:29,068 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:49:29,115 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:30,059 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:49:36,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:44,105 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:50,015 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:49:51,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:49:59,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:00,033 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:50:06,616 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:10,050 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:50:14,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:21,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:29,067 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:50:29,067 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:50:29,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:30,063 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:50:36,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:44,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:50,010 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:50:51,609 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:50:59,106 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:00,036 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:51:06,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:10,065 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:51:14,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:21,608 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:29,036 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:51:29,036 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:51:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:30,005 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:51:36,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:44,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:50,048 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:51:51,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:51:59,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:00,058 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:52:06,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:10,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:52:14,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:21,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:29,015 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:52:29,015 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:52:29,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:30,013 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:52:36,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:44,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:50,034 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:52:51,605 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:52:59,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:00,051 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [47/0/47/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:53:06,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:09,994 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:53:14,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:21,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:29,036 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:53:29,036 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:53:29,114 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:30,052 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:53:36,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:44,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:50,031 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:53:51,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:53:59,108 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:00,070 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [62/0/62/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:54:06,603 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:10,017 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:54:14,109 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:21,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:29,033 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:54:29,033 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:54:29,112 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:30,024 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:54:36,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:44,106 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:50,043 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:54:51,610 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:54:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:00,049 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:00,051 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":101,"message":"Ckbot53nprvpppbpoobmgqzotY/lvIDlkK/liankvZk15YiG6ZKf77yM6K+35ZCE5L2N6Ii56ZW/5YGa5aW95YeG5aSH77yBEAIYACAA","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":90,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:00,053 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":3006,"message":"Cl0QABpKVGV4dOi3neemu+mmlumihuaCrOi1j+W8gOWQr+WJqeS9mTXliIbpkp/vvIzor7flkITkvY3oiLnplb/lgZrlpb3lh4blpIfvvIEgs8yt2uwxKAAwADoAQAAQABgCIAE=","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":114,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:00,060 [ChatReceiveThread] INFO (BaseService.java:110) - [0/2/2/SSPushChannelChat/0] - {"body":{"chatFrom":"SYSTEM","fromId":0,"msg":{"content":"Text??????????5?????????????","contentType":"TEXT","customContacterChatInfo":{},"flag":0,"fromId":0,"isHorn":false,"sendTime":1712807700019},"toChatChannel":"CHAT_CHANNEL_SYSTEM","toId":0},"head":{"len":99,"pid":0,"serverId":1,"zoneId":1}}
2024-04-11 10:55:06,612 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:10,062 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:14,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:21,602 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:29,024 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:29,024 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:55:29,101 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:30,023 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:36,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:44,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:50,025 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:55:51,611 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:55:59,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:00,042 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:56:06,613 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:07,051 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [5/0/5/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:07,353 [LoginProcessorGroup-thread-2] INFO (BaseService.java:110) - [0/255/255/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:07,365 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:07,365 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0xd137bbed, L:/127.0.0.1:12306 - R:/127.0.0.1:44180]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"V27132F2SkeAWoFGpfQvOA==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:07,457 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMailList/************] - {"body":{"mails":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:08,655 [LadderWarBenfu] INFO (BaseService.java:110) - [0/0/0/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:08,655 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:08,669 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [4/0/4/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:08,669 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:08,732 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [54/0/54/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:08,732 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-1.156,"y":10.897,"z":104.305},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:08,792 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712807768782,"serverTime":1712807768792},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:10,064 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:56:12,337 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [24/0/24/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:12,460 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,460 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,460 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,460 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,460 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,461 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,461 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,472 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,473 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,474 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,474 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712807772460&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,474 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,474 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,475 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,475 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,475 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,475 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,477 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,477 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,479 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,479 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,480 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,481 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [61/21/82/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:12,481 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:12,490 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:12,491 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [19/0/19/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:12,708 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [60/0/60/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:12,708 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:13,955 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [29/0/29/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:13,955 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:13,955 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [29/0/29/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:13,955 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [29/0/29/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:13,955 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [17/0/17/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:13,955 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:13,955 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:13,955 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:14,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:14,331 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [13/0/13/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:14,331 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:14,392 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [55/0/55/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:14,392 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:14,392 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [12/0/12/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:14,392 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:14,424 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [44/0/44/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:14,424 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:21,615 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:21,788 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [40/2/42/CSGm/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:21,788 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":0,"msg":"Connect to the server disconnected, please try again","opcode":0,"type":3},"head":{"len":60,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:21,879 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:29,027 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:29,027 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:56:29,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:30,021 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:56:36,607 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:41,093 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [0/0/0/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:41,334 [LoginProcessorGroup-thread-3] INFO (BaseService.java:110) - [0/199/199/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:41,355 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0x09baf249, L:/127.0.0.1:12306 - R:/127.0.0.1:44199]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"7zSVeychScqqJgg4EwuGWA==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:42,955 [LadderWarBenfu] INFO (BaseService.java:110) - [0/0/0/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:42,955 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:42,978 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [12/0/12/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:42,978 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [12/0/12/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:42,978 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:42,978 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-1.156,"y":10.897,"z":104.305},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:42,999 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712807802998,"serverTime":1712807802999},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:44,111 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,398 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [48/0/48/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,462 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,474 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,474 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,474 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,475 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,476 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,476 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,476 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712807805462&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,476 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,477 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,477 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,477 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [16/15/31/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:45,477 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,477 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,477 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,478 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:45,525 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [63/0/63/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:45,525 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [51/0/51/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:45,712 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [8/0/8/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:45,712 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:56:46,583 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,583 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,583 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,583 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,583 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,583 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,583 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,583 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,768 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [2/0/2/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,768 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [23/0/23/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,768 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,768 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,768 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [13/0/13/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,768 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:46,768 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [2/0/2/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:46,768 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-11 10:56:50,057 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:56:51,604 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:56:55,628 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [57/0/57/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:56:55,628 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:56:59,104 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:00,041 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [31/0/31/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:57:06,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:09,860 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [35/0/35/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:57:09,860 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:57:10,046 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:57:14,113 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:21,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:28,239 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [32/0/32/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:57:28,239 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:57:29,045 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [48/0/48/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:57:29,045 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:57:29,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:30,040 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:57:36,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:42,312 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [52/0/52/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:57:42,312 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:57:44,107 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:50,027 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:57:51,606 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:57:59,110 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:00,027 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:58:00,650 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [3/0/3/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:58:00,650 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:58:06,614 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:10,022 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:58:14,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:14,739 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [41/0/41/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-11 10:58:14,739 [IO_LOOP-6-3] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:58:21,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:23,299 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [59/0/59/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-11 10:58:29,010 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:58:29,010 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:58:29,102 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:30,063 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:58:36,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:44,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:50,016 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:58:51,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:58:59,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:00,017 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [16/0/16/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:59:06,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:10,020 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:59:14,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:21,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:29,027 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [37/0/37/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-11 10:59:29,027 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:59:29,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:30,037 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:59:36,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:44,103 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:50,011 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-11 10:59:51,600 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-11 10:59:59,100 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
