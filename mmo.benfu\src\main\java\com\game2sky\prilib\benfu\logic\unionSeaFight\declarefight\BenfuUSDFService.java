package com.game2sky.prilib.benfu.logic.unionSeaFight.declarefight;

import gnu.trove.set.hash.TIntHashSet;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.unionSeaFight.BenfuUnionSeaFightService;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.BrifeUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareEnemyListInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarConfirm;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.CSDeclareWarInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.DeclareCodeEnum;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.DeclareEnemyType;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.DeclareState;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.EnemyUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SCDeclareEnemyListInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SCDeclareWarConfirm;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SCDeclareWarInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SCDeclareWarListInfo;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSBenfuUSDFScheduleMessage;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSGetEnemyUnionListFromCenter;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSendEnemyUnionListToBenfu;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncChangeUnionInfoToBenfu;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncDeclareMessageToCenter;
import com.game2sky.prilib.communication.game.unionSeaDeclareFight.SSSyncFightUnionInfoToBenfu;
import com.game2sky.prilib.communication.game.unionSeaFight.DeclareUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.UnionSeaFightType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.data.USFFightTime;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightServerGroup;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightTime;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.prilib.core.socket.logic.union.Union;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightActivityData;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightModel;
import com.game2sky.prilib.core.socket.logic.union.util.CheckAuthorityUtil;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.mail.MailService;
import com.game2sky.publib.socket.logic.mail.model.MailDictIdEnum;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;
import com.game2sky.publib.util.TimeUtil;

/**
 * 公会约战
 *
 * <AUTHOR>
 * @version v0.1 2018年12月3日 下午8:39:51  daixl
 */
@Service
public class BenfuUSDFService {

	@Autowired
	BenfuUnionSeaFightService benfuUnionSeaFightService;

	/**
	 * 处理中心服向本服同步公会信息
	 * 
	 * @param msg
	 */
	public void ssSyncChangeUnionInfoToBenfu(SSSyncChangeUnionInfoToBenfu msg) {

		int groupId = msg.getGroupId();
		List<BrifeUnionInfo> unionList = msg.getUnionList();

		BenfuUSDFUnionMemberManager.refreshAll(groupId, unionList);
	}

	public void ssBenfuUSDFScheduleMessage(SSBenfuUSDFScheduleMessage msg) {

		boolean checkMasterSwitch = SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.UNION_SEA_DECLARE_FIGHT,
			msg.getZoneId());
		if (!checkMasterSwitch) {
			return;
		}

		int groupId = msg.getGroupId();
		int zoneId = msg.getZoneId();
		BenfuUSDFUnionMemberManager.benfuUSDFUnionSeaFightSchedule(groupId, zoneId);
	}

//	public void ssSyncDeclareMessageToBenfu(SSSyncDeclareMessageToBenfu msg, Human human) {
//		if (human == null) {
//			return;
//		}
//		int resultCode = msg.getResultCode();
//		DeclareCodeEnum codeEnum = DeclareCodeEnum.valueOf(resultCode);
//
//		switch (codeEnum) {
//			case DECLARE_SUCCESS:
//				SCDeclareWarConfirm confirm = new SCDeclareWarConfirm();
//				confirm.setResult(resultCode);
//				human.push2Gateway(confirm);
//				return;
//			case DECLARE_ENEMY_NOT_SATISFIED:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_THIS_UNION_NOT_SATIFIED);
//				return;
//			case ENEMY_HAS_BEEN_DECLARED:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ENEMY_IS_DECLARED_BY_OTHERS);
//				return;
//			case ENEMY_HAS_DECLARE_OTHER_SAME_TIME:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_THIS_UNION_THIS_TIME_IS_BUSY);
//				return;
//			case SELF_HAS_BEEN_DECLARED_SAME_TIME:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_MYSELF_HAS_DEEN_DECLARED_THIS_TIME);
//				return;
//			case SELF_HAS_DECLARED_OTHER:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_MYSELF_HAS_DECLARE_OTHERS);
//				return;
//			case LEVEL_NOT_SATIFIED:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_LEVEL_NOT_SATIFIED);
//				return;
//			case SCORE_NOT_SATIFIED:
//				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
//					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_SCORE_NOT_SATIFIED);
//				return;
//			default:
//				break;
//		}
//	}

	public void ssSyncFightUnionInfoToBenfu(SSSyncFightUnionInfoToBenfu msg) {

		// 宣战结果，如果为0 则宣战成功，其他为异常情况
		int resultCode = msg.getResultCode();
		// 发起宣战的玩家id（会长，副会长，被宣战工会传0过来）
		long playerId = msg.getPlayerId();
		// 被宣战玩家的serverId（与playerId共同作用，推送宣战结果给客户端）
		int serverId = msg.getServerId();
		int zoneId = Globals.getZoneId(serverId);

		// 宣战的玩家（如果是被宣战，则为null）
		AbstractHuman human = Globals.getHuman(zoneId, playerId);

		if (resultCode != ErrorCodeConstants.OPERATION_SUCCESS) {// 处理宣战不成功
			// 如果玩家不存在，或者不在线，直接返回
			if (human == null || !human.isOnline()) {
				return;
			}
			DeclareCodeEnum codeEnum = DeclareCodeEnum.valueOf(resultCode);
			switch (codeEnum) {
				case DECLARE_ENEMY_NOT_SATISFIED:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_THIS_UNION_NOT_SATIFIED);
					return;
				case ENEMY_HAS_BEEN_DECLARED:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ENEMY_IS_DECLARED_BY_OTHERS);
					return;
				case ENEMY_HAS_DECLARE_OTHER_SAME_TIME:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_THIS_UNION_THIS_TIME_IS_BUSY);
					return;
				case SELF_HAS_BEEN_DECLARED_SAME_TIME:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_MYSELF_HAS_DEEN_DECLARED_THIS_TIME);
					return;
				case SELF_HAS_DECLARED_OTHER:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_MYSELF_HAS_DECLARE_OTHERS);
					return;
				case LEVEL_NOT_SATIFIED:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_LEVEL_NOT_SATIFIED);
					return;
				case SCORE_NOT_SATIFIED:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_SCORE_NOT_SATIFIED);
					return;
				case USDF_ACTIVITY_NOT_EXISTS:
					human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
						ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ACTIVITY_NOT_EXISTS);
					return;
				default:
					return;
			}
		} else {// 处理宣战成功
			long unionId = msg.getUnionId();
			Union union = Globals.getUnion(zoneId, unionId, Union.class);
			UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
			USFUnionFightInfo unionInfo = msg.getUnionInfo();

			if (unionInfo.getShowTime() == null || unionInfo.getShowTime().isEmpty()) {
				UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
					.getUnionSeaFightActivityDataByServerId(serverId);
				USFFightTime usfFightTime = usfActivityData.getDictUSFActivity().getUSFFightTime(
					unionInfo.getFightTimeId());
				unionInfo.setShowTime(usfFightTime.getShowTime());
			}
			unionSeaFightModel.addBatchFightInfo(unionInfo);
			DeclareUnionInfo declareUnionInfo = msg.getDeclareUnionInfo();
			if (declareUnionInfo != null) {
				unionSeaFightModel.addDeclareUnionInfo(declareUnionInfo);
			}

			Integer groupId = DictUnionSeaFightServerGroup.getGroupId(serverId);
			if (groupId != null) {
				BenfuUSDFUnionMemberManager.deleteUnion(groupId.intValue(), unionInfo.getDefenderId());
			}

			if (human != null && human.isOnline()) {// 给宣战玩家推送结果
				SCDeclareWarConfirm confirm = new SCDeclareWarConfirm();
				confirm.setResult(resultCode);
				human.push2Gateway(confirm);
			}

			if (unionId == unionInfo.getDefenderId()) {// 被宣战，发送邮件通知
				UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
					.getUnionSeaFightActivityDataByServerId(serverId);
				List<Long> allUnionPlayer = union.getAllUnionPlayer();
				Long[] playerIds = allUnionPlayer.toArray(new Long[allUnionPlayer.size()]);
				int matchUnionApplyCanChangeCondition = benfuUnionSeaFightService.matchUnionApplyCanChangeCondition(
					union, usfActivityData, false);
				if (matchUnionApplyCanChangeCondition == ErrorCodeConstants.USF_UNION_LESS_PLAYER) {// 人数不够，发特殊邮件
					MailService
						.sendSystemMailWithParams(
							MailDictIdEnum.DECLARE_SEA_FIGHT_DECLARED_NOTICE_MAIL_PLAYER_NOT_ENOUGH, null, zoneId,
							playerIds, unionInfo.getEnemyServerId(), unionInfo.getEnemyUnionName(),
							unionInfo.getShowTime());
				} else {
					MailService.sendSystemMailWithParams(MailDictIdEnum.DECLARE_SEA_FIGHT_DECLARED_NOTICE_MAIL, null,
						zoneId, playerIds, unionInfo.getEnemyServerId(), unionInfo.getEnemyUnionName(),
						unionInfo.getShowTime());
				}
			} else if (unionId == unionInfo.getAttackerId()) {// 宣战，发送邮件
				List<Long> allUnionPlayer = union.getAllUnionPlayer();
				Long[] playerIds = allUnionPlayer.toArray(new Long[allUnionPlayer.size()]);
				MailService.sendSystemMailWithParams(MailDictIdEnum.DECLARE_SEA_FIGHT_DECLARE_NOTICE_MAIL, null,
					zoneId, playerIds, unionInfo.getEnemyServerId(), unionInfo.getEnemyUnionName(),
					unionInfo.getShowTime());
			}
		}
	}

	/**
	 * 打开宣战界面
	 * 
	 * @param msg
	 * @param human
	 */
	public void getDeclareWarInfo(CSDeclareWarInfo msg, Human human) {

		int defaultCheck = defaultCheckWithoutPermission(human);
		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarInfo.getCode(), defaultCheck);
			return;
		}

		UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
			.getUnionSeaFightActivityDataByServerId(human.getServerId());

		List<Integer> timeIds = new ArrayList<Integer>();
		List<DictUnionSeaFightTime> list = DictUnionSeaFightTime
			.getByActivityId(usfActivityData.getDictUSFActivityId());
		for (DictUnionSeaFightTime dictUnionSeaFightTime : list) {
			if (dictUnionSeaFightTime.getSeaFightType() != UnionSeaFightType.UNION_DECLARE_SEA_FIGHT.value()) {
				continue;
			}
			timeIds.add(dictUnionSeaFightTime.getId());
		}

		Collections.sort(timeIds);

		Union union = Globals.getUnion(human.getZoneId(), human.getUnionId(), Union.class);
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();

		SCDeclareWarInfo declareWarInfo = new SCDeclareWarInfo();
		declareWarInfo.setTimeFrames(timeIds);
		declareWarInfo.setState(DeclareState.DECLARE_NONE.value());
		USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy(true);
		if (declareEnemy != null) {
			declareWarInfo.setEnemy(declareEnemy);
			declareWarInfo.setState(DeclareState.HAS_DECLARED.value());
			declareWarInfo.setDeclareTimeId(declareEnemy.getFightTimeId());
		}

		USFUnionFightInfo declaredEnemy = unionSeaFightModel.getDeclareEnemy(false);
		if (declaredEnemy != null) {
			declareWarInfo.setDeclaredTimeId(declaredEnemy.getFightTimeId());
		}

		human.push2Gateway(declareWarInfo);
	}

//	/**
//	 * 获取可宣战的列表
//	 * 
//	 * @param msg
//	 * @param human
//	 */
//	public void getDeclareWarList(CSDeclareWarListInfo msg, Human human) {
//
//		int defaultCheck = defaultCheckWithoutPermission(human);
//		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
//			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarListInfo.getCode(), defaultCheck);
//			return;
//		}
//
//		Union union = Globals.getUnion(human.getZoneId(), human.getUnionId(), Union.class);
//		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
//		List<BrifeUnionInfo> allMatchCondition = BenfuUSDFUnionMemberManager.getAllMatchCondition(human.getUnionId(),
//			human.getZoneId(), human.getServerId());
//		unionSeaFightModel.refreshUSDFCache(allMatchCondition, human.getPlayerId());
//
//		SCDeclareWarListInfo info = new SCDeclareWarListInfo();
//
//		USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy();
//		if (declareEnemy != null) {
//			BrifeUnionInfo declareUnionInfo = unionSeaFightModel.getDeclareUnionInfo(declareEnemy.getDefenderId());
//			if (declareUnionInfo != null) {
//				info.setFirst(declareUnionInfo);
//			}
//		}
//
//		int num = BaseService.getConfigIntByDefault(PriConfigKeyName.USDF_UNION_LIST_NUM);
//		if (info.getFirst() != null) {
//			num--;
//		}
//		List<BrifeUnionInfo> usdfList = unionSeaFightModel.getUSDFList(num, true, human.getPlayerId());
//		info.setTargets(usdfList);
//
//		human.push2Gateway(info);
//
//	}
//
//	/**
//	 * 刷新可宣战列表
//	 * 
//	 * @param msg
//	 * @param human
//	 */
//	public void updateDeclareWarList(CSDeclareWarUpdate msg, Human human) {
//
//		int defaultCheck = defaultCheckWithoutPermission(human);
//		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
//			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarListInfo.getCode(), defaultCheck);
//			return;
//		}
//
//		Union union = Globals.getUnion(human.getZoneId(), human.getUnionId(), Union.class);
//
//		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
//
//		SCDeclareWarListInfo info = new SCDeclareWarListInfo();
//		USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy();
//		if (declareEnemy != null) {
//			BrifeUnionInfo declareUnionInfo = unionSeaFightModel.getDeclareUnionInfo(declareEnemy.getDefenderId());
//			if (declareUnionInfo != null) {
//				info.setFirst(declareUnionInfo);
//			}
//		}
//
//		int num = BaseService.getConfigIntByDefault(PriConfigKeyName.USDF_UNION_LIST_NUM);
//		if (info.getFirst() != null) {
//			num--;
//		}
//		List<BrifeUnionInfo> usdfList = unionSeaFightModel.getUSDFList(num, false, human.getPlayerId());
//		info.setTargets(usdfList);
//
//		human.push2Gateway(info);
//
//		boolean showRepeat = unionSeaFightModel.showRepeat(human.getPlayerId());
//		if (showRepeat) {
//			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarListInfo.getCode(),
//				ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_REFRESH_DUPLICATE_UNION);
//		}
//	}

	public void getDeclareWarList(Human human, boolean isFirst, int timeId) {

		int defaultCheck = defaultCheckWithoutPermission(human);
		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarListInfo.getCode(), defaultCheck);
			return;
		}

		Union union = Globals.getUnion(human.getZoneId(), human.getUnionId(), Union.class);
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();

		if (isFirst) {
			List<BrifeUnionInfo> allMatchCondition = BenfuUSDFUnionMemberManager.getAllMatchCondition(
				human.getUnionId(), human.getZoneId(), human.getServerId());
			unionSeaFightModel.refreshUSDFCache(allMatchCondition, human.getPlayerId());
		}

		SCDeclareWarListInfo info = new SCDeclareWarListInfo();

		USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy(true);
		if (declareEnemy != null) {
			BrifeUnionInfo declareUnionInfo = unionSeaFightModel.getDeclareUnionInfo(declareEnemy.getDefenderId());
			if (declareUnionInfo != null) {
				info.setFirst(declareUnionInfo);
			}
		}

		int num = BaseService.getConfigIntByDefault(PriConfigKeyName.USDF_UNION_LIST_NUM);
		if (info.getFirst() != null) {
			num--;
		}
		List<BrifeUnionInfo> usdfList = unionSeaFightModel.getUSDFList(num, isFirst, human.getPlayerId(), timeId);
		info.setTargets(usdfList);
		human.push2Gateway(info);

		boolean showRepeat = unionSeaFightModel.showRepeat(human.getPlayerId());
		if (showRepeat) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarListInfo.getCode(),
				ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_REFRESH_DUPLICATE_UNION);
		}
	}

	/**
	 * 宣战
	 * 
	 * @param msg
	 * @param human
	 */
	public void declareWarConfirm(CSDeclareWarConfirm msg, Human human) {

		int defaultCheck = defaultCheck(human);
		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(), defaultCheck);
			return;
		}

		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		long unionId = msg.getUnionId();
		if (unionId == union.getUnionId()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
				ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_MYSELF_CANNOT_DECLARE_MYSELF);
			return;
		}

		long currTime = System.currentTimeMillis();
		UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
			.getUnionSeaFightActivityDataByServerId(human.getServerId());

		int coditionError = benfuUnionSeaFightService.matchUnionApplyCondition(union, usfActivityData, true);
		if (coditionError != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(), coditionError);
			return;
		}

		// 不在宣战时间内
		if (!usfActivityData.inApplyTimeScope(currTime)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
				ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_NOT_DECLARE_TIME);
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight 当前不在宣战时间内 Human.{}, 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			return;
		}

		int dayInWeek = TimeUtil.getDayInWeek();
		TIntHashSet set = BaseService.getConfigValue(PriConfigKeyName.USDF_CAN_DECLARE_DAYS);
		if (!set.contains(dayInWeek)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
				ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_DECLARE_SPECIAL_TIME);
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight 当前不在宣战时间内 Human.{}, 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			return;
		}

		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		Integer groupId = DictUnionSeaFightServerGroup.getGroupId(human.getServerId());

		BrifeUnionInfo myself = union.convert2BrifeUnionInfo();

		// 强制个人报名
		benfuUnionSeaFightService.forcePlayerJoin(currTime, usfActivityData, union, unionSeaFightModel, human);

		int listId = msg.getListId();
		int timeId = msg.getTimeId();
		long enemyUnionId = msg.getUnionId();

		if (listId == DeclareEnemyType.ENEMY_CAN_DECLARE.value()) {
			BrifeUnionInfo enemy = BenfuUSDFUnionMemberManager.getByGroupIdAndUnionId(groupId, unionId);
			if (enemy == null) {
				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_THIS_UNION_NOT_SATIFIED);
				return;
			}

			boolean checkLevel = UnionSeaFightActivityData.checkLevel(myself, enemy);
			if (!checkLevel) {
				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_LEVEL_NOT_SATIFIED);
				return;
			}

			boolean checkScore = UnionSeaFightActivityData.checkScore(myself, enemy);
			if (!checkScore) {
				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(),
					ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_SCORE_NOT_SATIFIED);
				return;
			}
		} else if (listId == DeclareEnemyType.ENEMY_ENEMY_LIST.value()) {
			int canDeclareEnemy = unionSeaFightModel.canDeclareEnemy(enemyUnionId, timeId);
			if (canDeclareEnemy != ErrorCodeConstants.OPERATION_SUCCESS) {
				human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(), canDeclareEnemy);
				return;
			}
		} else {

		}

		int checkCanDeclare = unionSeaFightModel.checkCanDeclare(timeId);
		if (checkCanDeclare != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareWarConfirm.getCode(), checkCanDeclare);
			return;
		}
		SSSyncDeclareMessageToCenter messageToCenter = new SSSyncDeclareMessageToCenter(groupId.intValue(),
			union.convert2BrifeUnionInfo(), enemyUnionId, timeId, human.getPlayerId(), human.getServerId(), listId);
		CoreBoot.benfuToKuafu(messageToCenter, 0, 0);
	}

	/**
	 * 获取敌人列表
	 * 
	 * @param msg
	 * @param human
	 */
	public void getDeclareEnemyListInfo(CSDeclareEnemyListInfo msg, Human human) {

		int defaultCheck = defaultCheckWithoutPermission(human);
		if (defaultCheck != ErrorCodeConstants.OPERATION_SUCCESS) {
			human.pushErrorMessageToClient(PrivPSEnum.SCDeclareEnemyListInfo.getCode(), defaultCheck);
			return;
		}

		long unionId = human.getUnionId();
		Union union = Globals.getUnion(human.getZoneId(), unionId, Union.class);
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();

		long lastRefreshEnemyTime = unionSeaFightModel.getLastRefreshEnemyTime();
		long now = Globals.getTimeService().now();
		long configLongByDefault = BaseService.getConfigLongByDefault(PriConfigKeyName.USDF_ENEMY_UNION_REFRESH_PART);
		if ((lastRefreshEnemyTime + configLongByDefault) < now) {
			Integer groupId = DictUnionSeaFightServerGroup.getGroupId(human.getServerId());
			SSGetEnemyUnionListFromCenter center = new SSGetEnemyUnionListFromCenter(groupId.intValue(), unionId,
				human.getServerId(), human.getPlayerId());
			CoreBoot.benfuToKuafu(center, 0, human.getZoneId());
		} else {
//			List<EnemyUnionInfo> enemyList = unionSeaFightModel.getEnemyList();
//			SCDeclareEnemyListInfo enemyListInfo = new SCDeclareEnemyListInfo();
//			enemyListInfo.setTargets(enemyList);
//
//			USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy();
//			if (declareEnemy != null) {
//				EnemyUnionInfo enemyUnionInfo = unionSeaFightModel.getEnemyUnionInfo(declareEnemy.getDefenderId());
//				if (enemyUnionInfo != null && enemyUnionInfo.getWinTimes() > 0) {
//					enemyListInfo.setFirst(enemyUnionInfo);
//				}
//			}
//			human.push2Gateway(enemyListInfo);
			sendEnemyListToClient(human, unionSeaFightModel);
		}
	}

	/**
	 * 中心服向本服同步仇敌消息，并返回客户端
	 * 
	 * @param msg
	 */
	public void ssSendEnemyUnionListToBenfu(SSSendEnemyUnionListToBenfu msg) {
		List<EnemyUnionInfo> infos = msg.getInfos();
		long unionId = msg.getUnionId();
		int serverId = msg.getServerId();
		long playerId = msg.getPlayerId();

		Union union = Globals.getUnion(Globals.getZoneId(serverId), unionId, Union.class);
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();

		unionSeaFightModel.refreshUSDFEnemyCache(infos);

		AbstractHuman human = Globals.getHumanByServerId(serverId, playerId);
//		if (human != null && human.isOnline()) {
//			SCDeclareEnemyListInfo returnMessage = new SCDeclareEnemyListInfo();
//			returnMessage.setTargets(infos);
//
//			USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy();
//			if (declareEnemy != null) {
//				EnemyUnionInfo enemyUnionInfo = unionSeaFightModel.getEnemyUnionInfo(declareEnemy.getDefenderId());
//				if (enemyUnionInfo != null && enemyUnionInfo.getWinTimes() > 0) {
//					returnMessage.setFirst(enemyUnionInfo);
//				}
//			}
//
//			human.push2Gateway(returnMessage);
//		}
		sendEnemyListToClient(human, unionSeaFightModel);
	}

	private void sendEnemyListToClient(AbstractHuman human, UnionSeaFightModel unionSeaFightModel) {
		if (human == null || human.isOffline()) {
			return;
		}

		List<EnemyUnionInfo> enemyList = unionSeaFightModel.getEnemyList();
		SCDeclareEnemyListInfo enemyListInfo = new SCDeclareEnemyListInfo();
		enemyListInfo.setTargets(enemyList);

		USFUnionFightInfo declareEnemy = unionSeaFightModel.getDeclareEnemy(true);
		if (declareEnemy != null) {
			EnemyUnionInfo enemyUnionInfo = unionSeaFightModel.getEnemyUnionInfo(declareEnemy.getDefenderId());
			if (enemyUnionInfo != null && enemyUnionInfo.getWinTimes() > 0) {
				enemyListInfo.setFirst(enemyUnionInfo);
			}
			Iterator<EnemyUnionInfo> iterator = enemyList.iterator();
			while (iterator.hasNext()) {
				EnemyUnionInfo next = iterator.next();
				if (next.getUid() == declareEnemy.getDefenderId()) {
					iterator.remove();
					break;
				}
			}
		}
		human.push2Gateway(enemyListInfo);
	}

	private int defaultCheck(Human human) {

		int defaultCheckWithoutPermission = defaultCheckWithoutPermission(human);
		if (defaultCheckWithoutPermission != ErrorCodeConstants.OPERATION_SUCCESS) {
			return defaultCheckWithoutPermission;
		}

		UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
			.getUnionSeaFightActivityDataByServerId(human.getServerId());
		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (!CheckAuthorityUtil.checkPresidentOrVicePresident(union, human.getPlayerId())) {
			AMLog.LOG_UNION.warn("@UnionSeaFight 必须是会长或副会长才能申请，权限不足,  Human.{}, 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			return ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ROLE_ERROR;
		}

		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

	private int defaultCheckWithoutPermission(Human human) {
		// 检测是否开启约战
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.UNION_SEA_DECLARE_FIGHT, human)) {
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight 未开启约战   human.{}", human.getPlayerId());
			return ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ACTIVITY_NOT_EXISTS;
		}
		long currTime = System.currentTimeMillis();
		DictUnionSeaFightServerGroup dictGroup = DictUnionSeaFightServerGroup.getByServerId(human.getServerId());

		// 未开启活动
		if (dictGroup == null) {
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight 未开启约战  human.{}", human.getPlayerId());
			return ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ACTIVITY_NOT_EXISTS;
		}

		// 是否开启
		if (!dictGroup.isOpen(currTime)) {
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight 未开启约战  human.{}", human.getPlayerId());
			return ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ACTIVITY_NOT_EXISTS;
		}

		// 未配置约战活动
		UnionSeaFightActivityData usfActivityData = benfuUnionSeaFightService
			.getUnionSeaFightActivityDataByServerId(human.getServerId());
		if (usfActivityData == null) {
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight  Human.{}, 没有活动", human.getPlayerId());
			return ErrorCodeConstants.UNION_SEA_DECLARE_FIGHT_ACTIVITY_NOT_EXISTS;
		}

		Union union = (Union) Globals.getUnion(human.getZoneId(), human.getUnionId());
		if (union == null || union.isDeleteState()) {
			AMLog.LOG_UNION.warn("@UnionSeaDeclareFight  Human.{}, 没有公会 , 活动:{}", human.getPlayerId(),
				usfActivityData.toString());
			return ErrorCodeConstants.NO_UNION;
		}

		return ErrorCodeConstants.OPERATION_SUCCESS;
	}

}
