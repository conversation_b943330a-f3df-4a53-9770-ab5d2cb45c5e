package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.IUSFRouteNode;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;

/**
 * TODO 可移动的
 *
 * <AUTHOR>
 * @version v0.1 2018年6月13日 下午7:36:12  guojijun
 */
public interface IUSFUnitMoveable extends IUSFRouteNode {

	USFCamp getCamp();

	/**
	 * 准备移动
	 * 
	 * @param targetFortressId 目标据点ID(字典表ID)
	 * @return 
	 */
	int readyToMove(int targetFortressId);

	/**
	 *  能否移动
	 *  
	 * @return
	 */
	boolean canMove();

	/**
	 *  尝试移动
	 */
	void tryMove();

	/**
	 * 尝试继续移动
	 */
	void tryMoveOn();

//	/**
//	 * 刷新位置
//	 * 
//	 * @param time
//	 */
//	void refreshPos(long now);

	/**
	 * 刷新航线进度
	 * 
	 * @param now
	 */
	void refreshProgress(long now);

	/**
	 * 获取状态机
	 * 
	 * @return
	 */
	<T extends AbstractUSFUnitFsmController> T getFsmController();

	/**
	 * 获取所在据点
	 * 
	 * @return
	 */
	USFFortress getCurFortress();

	/**
	 * 获取目标据点
	 * 
	 * @return
	 */
	USFFortress getTargetFortress();

	/**
	 * 下一个目标据点
	 */
	void nextTargetFortress();

	/**
	 * 增加移动距离
	 * 
	 * @param addMoveDis
	 */
	void incrMoveDis(int addMoveDis);
	
	/**
	 * 获取潜行状态
	 * @return
	 */
	int getDiveState();
}
