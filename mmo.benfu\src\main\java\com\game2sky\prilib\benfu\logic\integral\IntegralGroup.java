package com.game2sky.prilib.benfu.logic.integral;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.integralArena.IntegralAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentBase;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralPlayerChange;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.communication.game.integralArena.IntegralState;
import com.game2sky.prilib.communication.game.integralArena.SSBenfuIntegralPlayer;
import com.game2sky.prilib.communication.game.player.CommonFormationData;
import com.game2sky.prilib.communication.game.player.CommonFormationType;
import com.game2sky.prilib.communication.game.rank.RankType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.commonformation.ICommonFormationDataManager;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.human.state.ActivityState;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.scene.unit.component.state.RoleStateManager;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.util.DateUtil;

/**
 * 本服天梯分组
 * <AUTHOR>
 * @version v0.1 2019年7月16日 下午2:22:44  zhoufang
 */
public class IntegralGroup {

	public int groupId;

	public IntegralGroup(int groupId) {
		this.groupId = groupId;
	}

	/** Ladder Race Basic Information Cache */
	private IntegralBaseCache baseCache = new IntegralBaseCache();
	/** Real-time ranking cache */
	private Map<Integer, IntegralCurrentRank> rankCache = new HashMap<Integer, IntegralCurrentRank>();
	/** Player Id in the ranking list  */
	private Set<Long> rankPlayers = new HashSet<Long>();
	/** Player information */
	private Map<Long, IntegralGamePlayer> players = new HashMap<Long, IntegralGamePlayer>();
	/** Changes in player basic information*/
	private Map<Long, Map<Integer, String>> infoChanges = new HashMap<Long, Map<Integer, String>>();
	/** Is this group currently a new version? */
	private boolean isNewVersion;

	public void initArea() {
		Map<Integer, DictIntegralArenaArea> areaMap = IntegralConfigCenter.getAreaMap();
		for (Integer key : areaMap.keySet()) {
			if (IntegralAreaType.isErrorType(key)) {
				continue;
			}
			IntegralCurrentRank rank = new IntegralCurrentRank();
			rank.setAreaType(key);
			rank.setRankList(new ArrayList<IntegralRankItem>());
			rankCache.put(key, rank);
		}
	}

	public IntegralBaseCache getBaseCache() {
		return baseCache;
	}

	public void setBaseCache(IntegralBaseCache baseCache) {
		this.baseCache = baseCache;
	}

	public void refreshRankCache(List<IntegralCurrentRank> rankList) {
		for (IntegralCurrentRank rank : rankList) {
			if (rank.getRankList() == null) {
				continue;
			}
			if (IntegralAreaType.isErrorType(rank.getAreaType())) {
				continue;
			}
			rankCache.put(rank.getAreaType(), rank);
			AMLog.LOG_COMMON.info("天梯分组[{}]下的赛区[{}]排行榜刷新,当前排行榜版本[{}]", groupId, rank.getAreaType(), rank.getVersion());
		}
		// 计算排行榜上的玩家
		Set<Long> tempRanlPlayers = new HashSet<Long>();
		for (IntegralCurrentRank rank : rankCache.values()) {
			// 不计算无用排行榜
			if (isNewVersion && !IntegralAreaType.isNewVersion(rank.getAreaType())) {
				continue;
			}
			if (!isNewVersion && !IntegralAreaType.isOldVersion(rank.getAreaType())) {
				continue;
			}
			for (IntegralRankItem item : rank.getRankList()) {
				tempRanlPlayers.add(item.getPlayerId());
			}
		}
		this.rankPlayers = tempRanlPlayers;
	}

	/**
	 * 刷新基本信息缓存
	 * @param baseInfo
	 */
	public void refreshBaseCache(IntegralCurrentBase baseInfo) {
		this.baseCache.setSeason(baseInfo.getSeason());
		this.baseCache.setWeek(baseInfo.getWeek());
		this.baseCache.setWeekStartTime(baseInfo.getWeekStartTime());
		this.baseCache.setSeasonEndTime(baseInfo.getSeasonEndTime());
		this.baseCache.setSendWeekReward(false);
		this.baseCache.getLastRankCache().clear();
		if (baseInfo.getLastRanks() != null) {
			for (IntegralCurrentRank rank : baseInfo.getLastRanks()) {
				this.baseCache.getLastRankCache().put(rank.getAreaType(), rank.getRankList());
			}
		}
//		boolean oldVersion = this.isNewVersion;
		this.isNewVersion = baseInfo.getIsNewVersion();
		if (isNewVersion) {
			// 本服只要开过1次新玩法, 就不能再强制进入新玩法
			IntegralGameManager gameManager = IntegralGameManager.getInstance();
			if (!gameManager.alreadyInNewVersion(groupId)) {
				gameManager.saveBranchEnterNewVersion(groupId);
			}
		}

//		if (!oldVersion && isNewVersion) {
//			// 第一次开启新版本,推送给本组所有玩家
//			notifyAllHuman();
//		}
		IntegralGameRedis.saveBaseCache(groupId);
	}

//	private void notifyAllHuman() {
//		LinkedList<Integer> serverIdList = IntegralGameManager.getInstance().getGroupServerIds().get(groupId);
//		Set<AbstractHuman> allHuman = Globals.getAllHuman();
//		SCIntegralStartNewVersion resp = new SCIntegralStartNewVersion();
//		for (AbstractHuman human : allHuman) {
//			if (!serverIdList.contains(human.getServerId())) {
//				continue;
//			}
//			human.push2Gateway(resp);
//		}
//	}

	public void weekReset(long weekStartTime, boolean forbicly) {
		boolean sendReward = false;
		try {
			if (forbicly) {
				sendReward = true;
				sendReward(true);
			} else {
				if (!baseCache.isSendWeekReward() && DateUtil.compareWeek(weekStartTime, baseCache.getWeekStartTime()) > 0) {
					sendReward = true;
					sendReward(false);
				}
			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("@IntegralGameManager.weekReset发奖错误", e);
		} finally {
			if (sendReward) {
				baseCache.setSendWeekReward(true);
				IntegralGameRedis.saveBaseCache(groupId);
			}
		}
	}

	public void start() {
		if (baseCache.getWeekStartTime() == 0) {
			// 第一次启动
			return;
		}
		// gjj 2019-01-30 16:11:00 将启服检测关闭
		if (!baseCache.isSendWeekReward() && DateUtil.compareWeekWithNow(baseCache.getWeekStartTime()) > 0) {
			AMLog.fatal("本服已经到了下一周,且上周未发奖,上周开始时间为{}", new Timestamp(baseCache.getWeekStartTime()));
		}
	}

	/**
	 * 获取排行榜
	 * @param type RankType.value
	 * @return
	 */
	public List<IntegralRankItem> getRankList(int type) {
		List<IntegralRankItem> list = null;
		RankType rankType = RankType.valueOf(type);
		switch (rankType) {
			case INTEGRAL_NOLIMIT_NOW:
				list = rankCache.get(IntegralAreaType.NOLIMIT.value()).getRankList();
				break;
			case INTEGRAL_AMEND_NOW:
				list = rankCache.get(IntegralAreaType.AMEND.value()).getRankList();
				break;
			case INTEGRAL_PRACTICE_NOW:
				list = rankCache.get(IntegralAreaType.PRACTICE.value()).getRankList();
				break;
			case INTEGRAL_PINNACLE_NOW:
				list = rankCache.get(IntegralAreaType.PINNACLE.value()).getRankList();
				break;
			case INTEGRAL_NOLIMIT_LAST:
				list = baseCache.getLastRankCache().get(IntegralAreaType.NOLIMIT.value());
				break;
			case INTEGRAL_AMEND_LAST:
				list = baseCache.getLastRankCache().get(IntegralAreaType.AMEND.value());
				break;
			case INTEGRAL_PRACTICE_LAST:
				list = baseCache.getLastRankCache().get(IntegralAreaType.PRACTICE.value());
				break;
			case INTEGRAL_PINNACLE_LAST:
				list = baseCache.getLastRankCache().get(IntegralAreaType.PINNACLE.value());
				break;
			default:
				break;
		}
		return list;
	}

	public int getPlayerRank(int areaType, Long playerId) {
		IntegralCurrentRank currentRank = rankCache.get(areaType);
		if (currentRank == null || currentRank.getRankList() == null) {
			return -1;
		}
		List<IntegralRankItem> rankList = new ArrayList<IntegralRankItem>(currentRank.getRankList());
		for (int i = 0; i < rankList.size(); i++) {
			if (rankList.get(i).getPlayerId() == playerId) {
				return i + 1;
			}
		}
		return -1;
	}

	private void sendReward(boolean forbicly) {
		AMLog.LOG_COMMON.info("分组[{}]开始发放天梯奖励,当前是否为新版本:{}", groupId, isNewVersion);
		IntegralSendRewardTask task = isNewVersion ? new IntegralNewVersionRewardTask(this)
			: new IntegralOldVersionRewardTask(this);
		// 发放本周奖励
		task.sendWeekEndReward();

		// 是否是本赛季最后一周
		int seasonWeek = IntegralConfigCenter.getSeasonWeekByBranch(groupId);
		boolean isLastWeek = baseCache.getWeek() >= seasonWeek;
		if (forbicly || isLastWeek) {

			// 2018-11-5 赛季末发放排行奖励, 取2个赛区排名较高的发放
			AMLog.LOG_COMMON.info("天梯第{}赛季第{}周结束,结束时间是{},准备发放排名奖励", baseCache.getSeason(), baseCache.getWeek(),
				baseCache.getSeasonEndTime());
			task.sendSeasonEndReward();
			// 清空内存数据
			resetData();
			// 清空玩家redis数据
			IntegralGameRedis.clearGroupPlayer(this.groupId);
		} else {
			// 不是最后一周
			AMLog.LOG_COMMON.info("天梯目前第{}赛季第{}周, 赛季结束需要{}周, 无法发放排名奖励,清零玩家的本周战斗次数", baseCache.getSeason(),
				baseCache.getWeek(), seasonWeek);
			weekResetAllPlayer();
		}
	}

	/**
	 * 重置数据
	 */
	private void resetData() {
		this.players.clear();
		this.rankCache.clear();
		this.rankPlayers.clear();
		initArea();
	}

	/** 周重置 */
	public void weekResetAllPlayer() {
		Map<Integer, DictIntegralArenaArea> areaMap = IntegralConfigCenter.getAreaMap();
		List<IntegralGamePlayer> playerList = new ArrayList<>(players.values());
		Map<Integer, List<IntegralGamePlayer>> serverPlayers = new HashMap<>();
		for (IntegralGamePlayer player : playerList) {
			// 记录一份临时信息
			List<IntegralGamePlayer> tmpList = serverPlayers.get(player.getServerId());
			if (tmpList == null) {
				tmpList = new ArrayList<IntegralGamePlayer>();
				serverPlayers.put(player.getServerId(), tmpList);
			}
			tmpList.add(player);

			try {
				// 清零本周战斗次数
				player.clearWeekFightCount();
				// 削减玩家积分
				for (DictIntegralArenaArea dict : areaMap.values()) {
					int areaType = dict.getAreaType();
					if (!IntegralAreaType.isNewVersion(areaType)) {
						continue;
					}
					if (dict.getWeekDeductIntegral() <= 0) {
						// 此赛区积分不变
						if (player.haveAreaData(areaType)) {
							player.unsafeModifyIntegralTime(areaType, System.currentTimeMillis());
						}
						continue;
					}
					// 削减此赛区积分
					int areaIntegral = player.getIntegral(areaType);
					if (areaIntegral > dict.getDeductLowLimit()) {
						// 玩家需要扣分
						int last = areaIntegral - dict.getWeekDeductIntegral();
						last = last - last % 100;
						last = last < dict.getDeductLowLimit() ? dict.getDeductLowLimit() : last;
						player.unsafeModifyIntegral(areaType, last);
						player.unsafeModifyIntegralTime(areaType, System.currentTimeMillis());
//					int change = areaIntegral - last;
//					player.addIntegral(dict.getAreaType(), false, change);
						AMLog.LOG_COMMON.info("天梯玩家[{}]周结算,赛区[{}]的积分由[{}]变为[{}]", player.getPlayerId(),
							areaType, areaIntegral, last);
						continue;
					} 
					if (player.haveAreaData(areaType)) {
						// 玩家不需要扣分
						player.unsafeModifyIntegralTime(areaType, System.currentTimeMillis());
					}
				}
				IntegralGameRedis.savePlayer(player);
			} catch (Throwable t) {
				AMLog.LOG_ERROR.error("玩家{}周重置异常", player.getPlayerId(), t);
			}
		}
		// 上报中心服
		for (Entry<Integer, List<IntegralGamePlayer>> entry : serverPlayers.entrySet()) {
			int serverId = entry.getKey();
			SSBenfuIntegralPlayer info = new SSBenfuIntegralPlayer();
			info.setIsReConnect(true);
			info.setServerId(serverId);
			info.setPlayers(new ArrayList<IntegralCenterPlayerInfo>());
			int displayServerId = CoreBoot.getServerDisplayId(serverId);
			info.setDisplayServerId(displayServerId);
			if (entry.getValue() != null) {
				for (IntegralGamePlayer player : entry.getValue()) {
					info.getPlayers().add(player.toCenterProto());
					AMLog.LOG_COMMON.info("游戏服serverId={}....周重置完毕, 玩家{}数据准备上报中心服", serverId, player.getPlayerId());
				}
			}
			IntegralCommonService.benfuToCenter(info, 0, info.getServerId());
			AMLog.LOG_COMMON.info("游戏服serverId={},推送天梯玩家数量size={}", serverId, info.getPlayers().size());
		}
	}

	public Map<Long, IntegralGamePlayer> getPlayers() {
		return players;
	}

	public Map<Integer, IntegralCurrentRank> getRankCache() {
		return rankCache;
	}

	public Set<Long> getRankPlayers() {
		return rankPlayers;
	}

	public void playerInfoChange(Long playerId, int type, String value) {
		Map<Integer, String> changeMap = infoChanges.get(playerId);
		if (changeMap == null) {
			changeMap = new HashMap<Integer, String>();
			infoChanges.put(playerId, changeMap);
		}
		changeMap.put(type, value);
	}

	public List<IntegralPlayerChange> getPlayerInfoChanges() {
		// 每次最多发送100人的数据
		List<IntegralPlayerChange> list = new ArrayList<IntegralPlayerChange>();
		if (infoChanges.isEmpty()) {
			return list;
		}
		List<Long> removeKeys = new ArrayList<Long>();
		for (Long playerId : infoChanges.keySet()) {
			if (list.size() >= 100) {
				break;
			}
			IntegralPlayerChange change = new IntegralPlayerChange();
			change.setPlayerId(playerId);
			change.setValues(new ArrayList<DisplayInformationType>());
			Map<Integer, String> map = infoChanges.get(playerId);
			for (Entry<Integer, String> entry : map.entrySet()) {
				DisplayInformationType type = new DisplayInformationType();
				type.setType(entry.getKey());
				type.setValue(entry.getValue());
				change.getValues().add(type);
			}
			list.add(change);
			removeKeys.add(playerId);
		}
		for (Long playerId : removeKeys) {
			infoChanges.remove(playerId);
		}
		return list;
	}

	public boolean isNewVersion() {
		return isNewVersion;
	}

	/**
	 * 返回玩家在某个赛区的信息
	 * @param playerId
	 * @param areaType
	 * @return
	 */
	IntegralAreaPlayerInfo toPlayerAreaProto(long playerId, int serverId, int areaType) {
		IntegralGamePlayer gamePlayer = getPlayers().get(playerId);
		if (gamePlayer == null) {
			// 玩家第一次加入天梯
			gamePlayer = new IntegralGamePlayer(playerId, serverId);
		}
		Human human = (Human) Globals.getHuman(Globals.getZoneId(serverId), playerId);
		if (IntegralAreaType.isNewVersion(areaType)) {
			// 第一次进入新版天梯玩法要给每个赛区都初始化一套默认阵容
			ICommonFormationDataManager formationManager = human.getCommonFormationDataManager();
			// 练习赛阵容
			CommonFormationData practiceFormation = formationManager
				.getCommonFormationData(CommonFormationType.INTEGRAL_PRACTICE_FORMATION);
			if (practiceFormation == null) {
				practiceFormation = new CommonFormationData();
				practiceFormation.setFormationType(CommonFormationType.INTEGRAL_PRACTICE_FORMATION.value());
				practiceFormation.setShipGuid(human.getShipSystemManager().getCurrentFightShipGuid());
				practiceFormation.setPlayerFormation(human.getFormationManager().getCurentFormationInfo());
				human.getCommonFormationDataManager().updateCommonFormationData(practiceFormation);
			}
			// 巅峰赛阵容
			CommonFormationData pinnacleFormation = formationManager
				.getCommonFormationData(CommonFormationType.INTEGRAL_PINNACLE_FORMATION);
			if (pinnacleFormation == null) {
				pinnacleFormation = new CommonFormationData();
				pinnacleFormation.setFormationType(CommonFormationType.INTEGRAL_PINNACLE_FORMATION.value());
				pinnacleFormation.setShipGuid(human.getShipSystemManager().getCurrentFightShipGuid());
				pinnacleFormation.setPlayerFormation(human.getFormationManager().getCurentFormationInfo());
				human.getCommonFormationDataManager().updateCommonFormationData(pinnacleFormation);
			}
		}
		if (!gamePlayer.haveAreaData(areaType)) {
			// 第一次进入此赛区
			if (areaType == IntegralAreaType.PINNACLE.value()) {
				// 进入的巅峰赛区 需要 练习赛满足条件
				int practiceIntegral = gamePlayer.getIntegral(IntegralAreaType.PRACTICE.value());
				int needIntegral = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_OPEN_PINNACLE);
				if (practiceIntegral < needIntegral) {
					return null;
				}
				AMLog.LOG_ERROR.error("玩家{}没有通过正常战斗途径开启巅峰赛{}", playerId, areaType);
			}
			// 初始化赛区信息
			gamePlayer.initAreaInfo(areaType);
			addPlayer(gamePlayer);
			// 发送给中心服
			sendPlayerAreaToCenter(gamePlayer, areaType);
			IntegralGameRedis.savePlayer(gamePlayer);
		}
		IntegralAreaPlayerInfo areaProto = gamePlayer.toAreaInfo(areaType);
		int rank = getPlayerRank(areaType, playerId);
		areaProto.setRank(rank);
		RoleStateManager stateManager = human.getSceneObject().getRoleStateManager();
		if (stateManager.containState(ActivityState.KUAFU_MATCHING)) {
			int matchType = IntegralCommonService.getMatchType(playerId);
			if (areaType == matchType) {
				areaProto.setState(IntegralState.MATCH);
				long startMatchTime = IntegralCommonService.getStartMatchTime(playerId);
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				AMLog.LOG_COMMON.info("玩家playerId={} 的开始匹配时间为={}", playerId, sdf.format(new Date(startMatchTime)));
				areaProto.setStartMatchTime(startMatchTime);
			} else {
				areaProto.setState(IntegralState.FREE);
			}
		} else if (stateManager.containState(ActivityState.KUAFU_FIGHTING)) {
			areaProto.setState(IntegralState.FIGHT);
		} else {
			areaProto.setState(IntegralState.FREE);
		}
		return areaProto;
	}

	public void addPlayer(IntegralGamePlayer gamePlayer) {
		if (!this.players.containsKey(gamePlayer.getPlayerId())) {
			this.players.put(gamePlayer.getPlayerId(), gamePlayer);
		}
	}

	/**
	 * 
	 * @param gamePlayer
	 * @param areaType
	 */
	public void sendPlayerAreaToCenter(IntegralGamePlayer gamePlayer, int areaType) {
		SSBenfuIntegralPlayer resp = new SSBenfuIntegralPlayer();
		resp.setIsReConnect(false);
		resp.setServerId(gamePlayer.getServerId());
		resp.setDisplayServerId(CoreBoot.getServerDisplayId(gamePlayer.getServerId()));
		resp.setPlayers(new ArrayList<IntegralCenterPlayerInfo>());
		resp.getPlayers().add(gamePlayer.toCenterProtoWithAreaType(areaType));
		IntegralCommonService.benfuToCenter(resp, 0, gamePlayer.getServerId());
	}

	public IntegralGamePlayer getPlayerInfo(long playerId) {
		return this.players.get(playerId);
	}

	public boolean haveAreaData(long playerId, int areaType) {
		IntegralGamePlayer playerInfo = getPlayerInfo(playerId);
		if (playerInfo == null) {
			return false;
		}
		return playerInfo.haveAreaData(areaType);
	}
}
