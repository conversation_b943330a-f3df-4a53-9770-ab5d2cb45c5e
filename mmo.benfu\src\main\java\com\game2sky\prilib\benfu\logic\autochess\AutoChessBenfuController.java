package com.game2sky.prilib.benfu.logic.autochess;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.autochess.AutoChessBaseInfo;
import com.game2sky.prilib.communication.game.autochess.AutoChessRankPlayerInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessBuyPacket;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessChooseShip;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessGetExtraPackInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessGetMapShopInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessPlayerEnter;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessUseMap;
import com.game2sky.prilib.communication.game.autochess.CSCancelAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.CSGetAutoChessTaskReward;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessCancelMatch;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessConnectBattle;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerEnterRes;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankRes;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerStateChange;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessSettlement;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessStartFight;
import com.game2sky.prilib.communication.game.autochess.SSRefreshAutoChessRank;
import com.game2sky.prilib.communication.game.autochess.SSRefreshGameAutoChessBase;
import com.game2sky.prilib.communication.game.autochess.SSSendTaskDataToBenfu;
import com.game2sky.prilib.core.socket.logic.autochess.AutoChessBenfuManager;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.WebApi;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 争霸赛本服协议处理.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月9日 下午4:32:43  Jet Lee
 */
@Controller
public class AutoChessBenfuController extends BaseController {

	@Autowired
	private AutoChessBenfuService service;
	
    /**
     * 本服自走棋匹配tick
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessBenfuTick)
    public void ssAutoChessCenterTick() {
        AutoChessBenfuManager.getInstance().tick();
    }

	/**
	 * 变更玩家的积分GM
	 * @param request
	 * @return
	 */
	@RequestMapping(value = WebApi.BI_AUTO_CHESS_CHANGE_SCORE)
	public ModelAndView changeScore(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		if (!paramsMap.containsKey("playerId") || !paramsMap.containsKey("serverId")
			|| !paramsMap.containsKey("changeScore")) {
			return returnString("缺少参数");
		}
		long playerId = Long.valueOf(paramsMap.get("playerId"));
		int serverId = Integer.valueOf(paramsMap.get("serverId"));
		int changeScore = Integer.valueOf(paramsMap.get("changeScore"));
		if (changeScore > 10000) {
			return returnString("分数不合法");
		}
		SSAutoChessSettlement ssAutoChessSettlement = new SSAutoChessSettlement();
		ssAutoChessSettlement.setPlayerId(playerId);
		ssAutoChessSettlement.setServerId(serverId);
		ssAutoChessSettlement.setChangeScore((int) changeScore);
		Message build = Message.build(0L, serverId, 0, ssAutoChessSettlement, null, null, null);
		Globals.getMsgProcessDispatcher().put(build);
		return returnString("OK");
	}

	/**
	 * 周重置GM
	 * @param request
	 * @return
	 */
	@RequestMapping(value = WebApi.BI_AUTO_CHESS_BENFU_WEEK_RESET)
	public ModelAndView weekReset(HttpServletRequest request) {
		Map<String, String> paramsMap = getParamsMap(request);
		if (!paramsMap.containsKey("season") || !paramsMap.containsKey("week")
			|| !paramsMap.containsKey("isWeekEnd")) {
			return returnString("缺少参数");
		}
		int season = Integer.valueOf(paramsMap.get("season"));
		int week = Integer.valueOf(paramsMap.get("week"));
		boolean isWeekEnd = Boolean.valueOf(paramsMap.get("isWeekEnd"));
		AutoChessBaseInfo baseInfo = new AutoChessBaseInfo(season, week);
		List<AutoChessRankPlayerInfo> rankList = AutoChessBenfuManager.getInstance().getRankList();
		List<AutoChessRankPlayerInfo> lastSeasonRanks = AutoChessBenfuManager.getInstance().getLastSeasonRanks();
		SSRefreshGameAutoChessBase ss = new SSRefreshGameAutoChessBase(baseInfo, isWeekEnd, lastSeasonRanks, rankList);
		Message build = Message.build(0L, 0, 0, ss, null, null, null);
		Globals.getMsgProcessDispatcher().put(build);
		return returnString("OK");
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessCancelMatch)
	public void ssAutoChessCancelMatch(SSAutoChessCancelMatch msg, Head head) {
		service.ssAutoChessCancelMatch(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessStartFight)
	public void ssAutoChessStartFight(SSAutoChessStartFight msg, Human human) {
		service.ssAutoChessStartFight(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessInfo)
	public void csAutoChessInfo(CSAutoChessInfo msg, Human human) {
		service.csAutoChessInfo(msg, human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessChooseShip)
	public void csAutoChessChooseShip(CSAutoChessChooseShip msg, Human human) {
		service.csAutoChessChooseShip(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessMatch)
	public void csAutoChessMatch(CSAutoChessMatch msg, Human human) {
		service.csAutoChessMatch(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSCancelAutoChessMatch)
	public void csCancelAutoChessMatch(CSCancelAutoChessMatch msg, Human human) {
		service.csCancelAutoChessMatch(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessConnectBattle)
	public void ssAutoChessConnectBattle(SSAutoChessConnectBattle ssAutoChessConnectBattle, Human human) {
		service.ssAutoChessConnectBattle(ssAutoChessConnectBattle, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessPlayerStateChange)
	public void ssAutoChessPlayerStateChange(SSAutoChessPlayerStateChange msg, Human human) {
		this.service.ssAutoChessPlayerStateChange(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessPlayerQuit)
	public void csAutoChessPlayerQuit(Human human) {
		this.service.csAutoChessPlayerQuit(human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSRefreshGameAutoChessBase)
	public void ssRefreshGameAutoChessBase(SSRefreshGameAutoChessBase msg, Head head) {
		service.ssRefreshGameAutoChessBase(msg);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSRefreshAutoChessRank)
	public void ssRefreshAutoChessRank(SSRefreshAutoChessRank msg, Head head) {
		service.ssRefreshAutoChessRank(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessPlayerEnter)
	public void csAutoChessPlayerEnter(CSAutoChessPlayerEnter msg, Human human) {
		service.csAutoChessPlayerEnter(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessPlayerEnterRes)
	public void ssAutoChessPlayerEnterRes(SSAutoChessPlayerEnterRes msg, Human human) {
		service.ssAutoChessPlayerEnterRes(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessSettlement)
	public void ssAutoChessSettlement(SSAutoChessSettlement msg) {
		service.ssAutoChessSettlement(msg);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessGetMapShopInfo)
	public void csAutoChessGetMapShopInfo(CSAutoChessGetMapShopInfo msg,Human human) {
		service.csAutoChessGetMapShopInfo(msg, human);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessUseMap)
	public void csAutoChessUseMap(CSAutoChessUseMap msg,Human human) {
		service.csAutoChessUseMap(msg, human);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessGetExtraPackInfo)
	public void csAutoChessGetExtraPackInfo(CSAutoChessGetExtraPackInfo msg,Human human) {
		service.csAutoChessGetExtraPackInfo(msg, human);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSAutoChessBuyPacket)
	public void csAutoChessBuyPacket(CSAutoChessBuyPacket msg,Human human) {
		service.csAutoChessBuyPacket(msg, human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSGetAutoChessWeeklyTaskList)
	public void getAutoChessWeeklyTaskList(Human human){
		service.getAutoChessWeeklyTaskList(human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSGetAutoChessTaskReward)
	public void getAutoChessTaskReward(CSGetAutoChessTaskReward msg, Human human){
		service.getAutoChessTaskReward(msg, human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSendTaskDataToBenfu)
	public void sendTaskDataToBenfu(SSSendTaskDataToBenfu msg, Human human) {
		service.sendTaskDataToBenfu(msg, human);
	}
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessPlayerRankRes)
	public void ssAutoChessPlayerRankRes(SSAutoChessPlayerRankRes msg, Head head) {
		service.ssAutoChessPlayerRankRes(msg);
	}
}
