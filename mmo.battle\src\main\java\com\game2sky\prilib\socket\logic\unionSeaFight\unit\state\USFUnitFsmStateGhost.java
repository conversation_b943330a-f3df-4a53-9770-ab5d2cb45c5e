package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateGhostInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;

/**
 * TODO 彻底死亡状态
 *
 * <AUTHOR>
 * @version v0.1 2018年8月26日 下午1:34:20  guojijun
 */
public class USFUnitFsmStateGhost extends AbstractUSFUnitFsmState {

	private static final USFClientUnitStateGhostInfo CLIENT_INFO = new USFClientUnitStateGhostInfo();

	public USFUnitFsmStateGhost(AbstractUSFUnitFsmController controller) {
		super(USFUnitStateType.USF_UNIT_STATE_GHOST, controller);
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		clientUnitStateInfo.setGhostInfo(CLIENT_INFO);
	}

}
