package com.game2sky.prilib.socket.logic.homeland.matchv2;


/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 下午2:11:09  daixl
 */
@Deprecated
public class HomeLandCreamMatchHandler {
//
//	private static TLongLongHashMap matchPlayers = new TLongLongHashMap();
//
//	public static boolean checkCanMatch(long playerId, long now) {
//		long value = matchPlayers.get(playerId);
//		if (value > now) {
//			return false;
//		}
//		return true;
//	}
//
//	public static List<Long> match(SSRequestCanRobList msg, int times, long now, SSResponseCanRobList robList) {
////		// 将玩家加入正在匹配列表，防止玩家多次匹配，会在匹配结束以后，删除这个记录
////		matchPlayers.put(msg.getPlayerId(), now + 10 * 1000);
////
////		List<Long> result = new ArrayList<Long>();
////		Set<Long> excludeIds = new HashSet<Long>();
////		List<Long> enemyIds = msg.getEnemyIds();
////		if (enemyIds != null) {
////			excludeIds.addAll(enemyIds);
////		}
////		List<Long> friendIds = msg.getFriendIds();
////		if (friendIds != null) {
////			excludeIds.addAll(friendIds);
////		}
////		excludeIds.add(msg.getPlayerId());
////
////		int[] canRobGroups = DictHomeRobRefreshCost.getCanRobGroups(times);
////		if (canRobGroups != null) {
////			HomeLandCreamMatchSort sort = CenterHomelandGlobalManager.getSort();
////			int getOneBarrel = 1;
////			for (int groupId : canRobGroups) {
////				HomeLandCreamMatchBarrel barrel = sort.getBarrel(groupId);
////				List<Long> randomOne = barrel.randomPlayerIds(excludeIds, getOneBarrel);
////				getOneBarrel = (getOneBarrel - randomOne.size()) + 1;
////				result.addAll(randomOne);
////			}
////
////			int length = canRobGroups.length;
////			int size = result.size();
////			int needAdd = length - size;
////			if (needAdd > 0) {
////				if (!CollectionUtils.isEmpty(enemyIds)) {
////					robList.setNeedClean(true);
////					excludeIds.removeAll(enemyIds);
////				}
////				excludeIds.addAll(result);
////				getOneBarrel = 1;
////				for (int groupId : canRobGroups) {
////					getOneBarrel = Math.min(getOneBarrel, needAdd);
////					HomeLandCreamMatchBarrel barrel = sort.getBarrel(groupId);
////					List<Long> randomOne = barrel.randomPlayerIds(excludeIds, getOneBarrel);
////					result.addAll(randomOne);
////					needAdd -= randomOne.size();
////					if (needAdd <= 0) {
////						break;
////					}
////					getOneBarrel = (getOneBarrel - randomOne.size()) + 1;
////				}
////			}
////		}
////
////		matchPlayers.remove(msg.getPlayerId());
////		return result;
//
//		return null;
//	}
}
