package mmo.battle.graph;

import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientPlayerInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateNormalInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.game.struct.FPoint3;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年6月12日 下午8:00:50  Administrator
 */
public class PBTest {

	public static void main(String[] args) {
		USFClientUnitInfo clientUnitInfo = new USFClientUnitInfo();
		clientUnitInfo.setId(1);
		clientUnitInfo.setPos(new FPoint3(1, 2, 3));
		clientUnitInfo.setDir(new FPoint3(0, 0, 0));
		clientUnitInfo.setCampType(USFCampType.USF_CAMP_RED);
		clientUnitInfo.setUnitType(USFUnitType.USF_UNIT_PLAYER);

		USFClientUnitStateInfo clientUnitStateInfo = new USFClientUnitStateInfo();
		clientUnitStateInfo.setStateType(USFUnitStateType.USF_UNIT_STATE_NORMAL);
		clientUnitStateInfo.setNormalInfo(new USFClientUnitStateNormalInfo());
		clientUnitInfo.setStateInfo(clientUnitStateInfo);

		USFClientPlayerInfo clientPlayerInfo = new USFClientPlayerInfo();
		clientUnitInfo.setPlayerInfo(clientPlayerInfo);

		System.out.println(clientUnitInfo);

		long time = System.currentTimeMillis();
		SerializationUtil.serialize(clientUnitInfo);
		System.out.println(System.currentTimeMillis() - time);

		time = System.currentTimeMillis();
		for (int i = 0; i < 100; i++) {
			SerializationUtil.serialize(clientUnitInfo);
		}
		System.out.println(System.currentTimeMillis() - time);
	}

}
