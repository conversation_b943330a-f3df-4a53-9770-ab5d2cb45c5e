# 场景的monitor日志输出帧率条件，低于这个帧率才输出
scene.monitor.warnFrame = 15

# 场景的monitor日志输出频率，单位毫秒
scene.monitor.frame = 10000

#一次执行的玩家消息上限数量，小于等于0，则无限制
player.msg.excute.maxNum=36

#-------
#玩家身上的消息池大小和检测阀值 (该值要小于玩家消息队列大小100)
player.msg.limit = 90
#消息队列检测阀值，超过该值时，需要清理移动和心跳消息
player.msg.exam.threshold = 36
#消息队列检测阀值的清理底线，清理后保证队列中至少剩余的移动类消息数
player.msg.exam.keep.num = 12
#-------

# 从客户端来的数据信息最长长度
client.message.max.len = 99999

# 场景线程数
thread.num.scene = 16
# 世界线程数
thread.num.world = 2

# 异步非绑定
thread.num.async = 2
# 异步绑定
thread.num.async.bind = 2

# channel的flush间隔
channel.flush.interval = 16

# 天梯赛单线程场景数
SceneRunner.IntegralScene.num = 200

game.flow.init.time.out = 6000000
game.flow.login.time.out = 6000000
game.flow.reconnect.time.out = 6000000
game.flow.offing.time.out = 6000

netty.tcp.so.back.log = 100
netty.boss.thread.num = 1
netty.io.thread.num = 8

# 进入后台多久，服务端主动断开连接，单位（毫秒）
channel.back.dead.time = 30000
# 连续后多久未收到登录请求，服务端主动断开连接，单位（毫秒）
channel.active.dead.time = 10000
# socket 超时时间，长时间接收不到客户端的消息服务器会关闭跟客户端之间的连接
channel.active.timeout = 30000



#配置服内网负载均衡IP
center.config.url.andr=http://************
center.config.url.gw=http://************
center.config.url.ios=http://*************
center.config.url.yh=http://************
#主服内网负载均衡IP
main.server.url.andr=http://*************
main.server.url.gw=http://************
main.server.url.ios=http://*************
main.server.url.yh=http://************


scene.client.num=1



#类热更目录
mmo.class.reload.path=/data/op/class.reload.path


#Interface日志慢监控开关
interface.slow.open=true
#通用慢监控时长 (毫秒)
interface.slow.time=10


#HTTP统一超时时间 (毫秒)
http.timeout=3000


#Fatal报警开关-HTTP状态，目前仅限400类
alarm.http.status=true

#Interface日志慢监控开关
interface.slow.open=true
#通用慢监控时长 (毫秒)
interface.slow.time=10

#Fatal报警-报警开关
alarm.reconnect.open=true
#重连失败5次，则报警
alarm.reconnect.fail.num=5

#线程存活的监控
alarm.thread.alive.open=true
alarm.thread.alive.name.scope=SceneTaskScheduler,SceneRunner
#Fatal线程丢失监听时间，遇到直接报警，1秒
alarm.thread.alive.checkTime=1000
#周期60秒 出现10次则报警
alarm.thread.alive.period=10000
alarm.thread.alive.num=10

#聊天或主消息线程监听事件设置为60秒
alarm.thread.alive.checkTime.QueueMessageProcessor=60000
#周期10分钟 出现10次则报警
alarm.thread.alive.period.QueueMessageProcessor=600000
alarm.thread.alive.num.QueueMessageProcessor=10

#定时器线程监听事件设置为10秒
alarm.thread.alive.checkTime.ScheduleService=10000
#周期10分钟 出现5次则报警
alarm.thread.alive.period.ScheduleService=600000
alarm.thread.alive.num.ScheduleService=5

#周期1小时 出现10次则报警
alarm.thread.alive.period.ScheduleService=3600000
alarm.thread.alive.num.ScheduleService=10

#副本所用SceneRunner的数量
temp.scene.runner.count=100
