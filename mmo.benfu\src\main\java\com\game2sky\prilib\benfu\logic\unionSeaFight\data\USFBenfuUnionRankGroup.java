package com.game2sky.prilib.benfu.logic.unionSeaFight.data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.rank.USFRankInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksRes;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBenfuSeasonInfo;
import com.game2sky.prilib.core.dict.domain.DictUSFRankAwards;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightActivity;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightServerGroup;
import com.game2sky.prilib.core.socket.logic.exeMail.ExeMailService;
import com.game2sky.prilib.core.socket.logic.exeMail.dataType.GetTitleExeMail;
import com.game2sky.prilib.core.socket.logic.exeMail.manager.IExeMailManager;
import com.game2sky.prilib.core.socket.logic.union.Union;
import com.game2sky.prilib.core.socket.logic.union.constants.UnionConstants;
import com.game2sky.prilib.core.socket.logic.union.constants.UnionSeaFightConstant;
import com.game2sky.prilib.core.socket.logic.union.unionMember.IUnionMemberManager;
import com.game2sky.prilib.core.socket.logic.union.unionSeaFight.UnionSeaFightModel;
import com.game2sky.publib.Globals;
import com.game2sky.publib.dbs.model.PubEntityPriority;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.dbs.orm.CommonEntity;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.socket.logic.IEntity;
import com.game2sky.publib.socket.logic.MemoryData;
import com.game2sky.publib.socket.logic.mail.MailService;
import com.game2sky.publib.socket.logic.mail.model.MailDictIdEnum;
import com.game2sky.publib.socket.logic.union.AbstractUnion;

/**
 * TODO 海战排行分组
 *
 * <AUTHOR>
 * @version v0.1 2018年9月9日 下午4:45:37  guojijun
 */
public class USFBenfuUnionRankGroup implements IEntity<CommonEntity> {

	public static final String ENTITY_CLASS_NAME = USFBenfuUnionRankGroup.class.getSimpleName();

	/**分组ID*/
	private int groupId;
	/**上一个赛季的数据*/
	private USFBenfuSeasonInfo lastSeasonInfo;
	/**当前赛季的数据*/
	private USFBenfuSeasonInfo curSeasonInfo;
	/**重置时间*/
	private long resetTime;
	/**时间戳*/
	private long timeStamp;

	public USFBenfuUnionRankGroup(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		this.syncRanks(ssUSFSyncRanksRes);
	}

	public void syncRanks(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		AMLog.LOG_UNION.info("海战排行榜发生改变,开始重置,groupId={}", ssUSFSyncRanksRes.getGroupId());
		this.groupId = ssUSFSyncRanksRes.getGroupId();
		this.lastSeasonInfo = ssUSFSyncRanksRes.getLastSeasonInfo();
		if(lastSeasonInfo != null){
			fullDisPlayServerId(lastSeasonInfo.getRanks());
			fullDisPlayServerId(lastSeasonInfo.getServerRanks());
		}
		this.curSeasonInfo = ssUSFSyncRanksRes.getCurSeasonInfo();
		if(curSeasonInfo != null){
			fullDisPlayServerId(curSeasonInfo.getRanks());
			fullDisPlayServerId(curSeasonInfo.getServerRanks());
		}
		// 如果本服记录的重置时间和不一样，说明需要重置了。
		long needResetTime = ssUSFSyncRanksRes.getResetTime();
		// 时间上看需要重置
		if (this.resetTime != needResetTime) {
			this.resetUnionIntegral(ssUSFSyncRanksRes);
			trySendMonthRankAward(ssUSFSyncRanksRes);
		}
		this.resetTime = ssUSFSyncRanksRes.getResetTime();
		this.timeStamp = ssUSFSyncRanksRes.getTimeStamp();
	}

	private void fullDisPlayServerId(List<USFRankInfo> lastSeasonRanks) {
		if (CollectionUtils.isNotEmpty(lastSeasonRanks)) {
			for (USFRankInfo usfRankInfo : lastSeasonRanks) {
				usfRankInfo.setDisplayServerId(CoreBoot.getServerDisplayId(usfRankInfo.getServerId()));
			}
		}
	}

	/**
	 * 公会排行榜发奖
	 * @param ssUSFSyncRanksRes 中心服过来的最新排行榜数据
	 */
	private void trySendMonthRankAward(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		//称号结束时间
		long titleEndTime = ssUSFSyncRanksRes.getResetTime();
		//单独判断排行榜上的每一个公会
		USFBenfuSeasonInfo seasonInfo = ssUSFSyncRanksRes.getLastSeasonInfo();
		if (seasonInfo == null) {
			AMLog.LOG_UNION.info("海战排行榜发奖,上次的排行榜为空,groupId={}", ssUSFSyncRanksRes.getGroupId());
			return;
		}
		int seasonId = seasonInfo.getId();
		List<USFRankInfo> lastMonthUnions = seasonInfo.getServerRanks();
		if (CollectionUtils.isEmpty(lastMonthUnions)) {
			AMLog.LOG_UNION.info("海战排行榜发奖,上次的排行榜为空,groupId={}", ssUSFSyncRanksRes.getGroupId());
			return;
		}
		for (USFRankInfo usfRankInfo : lastMonthUnions) {
			int serverId = usfRankInfo.getServerId();
			int zoneId = Globals.getZoneId(serverId);
			int rank = usfRankInfo.getRank();
			DictUSFRankAwards dictUSFRankAwards = DictUSFRankAwards.getByRank(rank);
			if (dictUSFRankAwards == null) {
				continue;
			}
			long unionId = usfRankInfo.getUnionId();
			Union union = (Union) Globals.getUnion(zoneId, unionId);
			if (union == null) {
				continue;
			}
			// 给这个公会发排行奖励
			sendOneUnionUSFMonthReward(union,seasonId, dictUSFRankAwards, titleEndTime,rank);
		}
	}
	/**
	 * 给单个公会发奖
	 * @return
	 */
	private void sendOneUnionUSFMonthReward(Union union,int seasonId, DictUSFRankAwards dictUSFRankAwards, long endTime,int rank) {
		UnionSeaFightModel unionSeaFightModel = union.getUnionSeaFightManager().getUnionSeaFightModel();
		//已经发过了
		if(unionSeaFightModel.getSendRewardSeasonId() >= seasonId){
			return;
		}
		int managerTitleId = dictUSFRankAwards.getManagerTitleId();
		int memberTitleId = dictUSFRankAwards.getMemberTitleId();
		IUnionMemberManager unionMemberManager = union.getUnionMemberManager();
		List<Long> allUnionPlayer = union.getAllUnionPlayer();
		Set<Long> joinedPlayerIds = unionSeaFightModel.getJoinPlayersLastSession();
		long unionId = union.getUnionId();
		AMLog.LOG_COMMON.info("sendOneUnionUSFMonthReward start union {} dictUSFRankAwardsId {}",unionId,dictUSFRankAwards.getId());
		unionSeaFightModel.setSendRewardSeasonId(seasonId);
		List<Long> awardPids = new ArrayList<>();
		for (Long playerId : allUnionPlayer) {
			// 如果他报过名就给发称号
			if (joinedPlayerIds == null || !joinedPlayerIds.contains(playerId)) {
				continue;
			}
			awardPids.add(playerId);
			GetTitleExeMail titleExeMail = new GetTitleExeMail();
			int unionMemberRole = unionMemberManager.getUnionMemberRole(playerId);
			if(UnionConstants.UNION_MEMBER == unionMemberRole){
				titleExeMail.setTitleId(memberTitleId);
			}else if(UnionConstants.UNION_PRESIDENT == unionMemberRole 
					|| UnionConstants.UNION_VICEPRESIDENT == unionMemberRole){
				titleExeMail.setTitleId(managerTitleId);
			}
			titleExeMail.setEndTime(endTime);
			int zoneId = union.getZoneId();
			IExeMailManager exeMailManager = ExeMailService.getExeMailManager(zoneId);
			exeMailManager.addOnePlayerExeMail(titleExeMail, zoneId, playerId);
			
			MailService.sendSystemMailWithParams(MailDictIdEnum.USF_RANK_REWARD, null, zoneId, playerId, rank);
		}
		unionSeaFightModel.clearLastSessionJoinPlayer();
		union.getUnionSeaFightManager().setModified();
		AMLog.LOG_COMMON.info("sendOneUnionUSFMonthReward end players {}", JsonUtils.O2S(awardPids));
	}
	
	private void resetUnionIntegral(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		USFBenfuSeasonInfo seasonInfo = ssUSFSyncRanksRes.getLastSeasonInfo();
		if (seasonInfo == null) {
			AMLog.LOG_UNION.info("海战没有上一届数据,无需重置积分,groupId={}", ssUSFSyncRanksRes.getGroupId());
			return;
		}
		DictUnionSeaFightServerGroup dictUnionSeaFightServerGroup = DictUnionSeaFightServerGroup
			.getByStrategyId(ssUSFSyncRanksRes.getGroupId());
		if (dictUnionSeaFightServerGroup == null) {
			AMLog.LOG_UNION.warn("海战重置积分没有找到DictUnionSeaFightServerGroup,groupId={}", ssUSFSyncRanksRes.getGroupId());
			return;
		}
		Set<Integer> zoneIdSet = dictUnionSeaFightServerGroup.toZoneIds(CoreBoot.getAllServerId());
		if (zoneIdSet == null) {
			AMLog.LOG_UNION.warn("海战重置积分没有找到对应的zoneId,groupId={}", ssUSFSyncRanksRes.getGroupId());
			return;
		}
		int initIntegral = UnionSeaFightConstant.UNION_DEFAULT_INTEGRAL;
		DictUnionSeaFightActivity dictUnionSeaFightActivity = DictUnionSeaFightActivity
			.getByStrategyId(dictUnionSeaFightServerGroup.getActivityId());
		if (dictUnionSeaFightActivity != null) {
			initIntegral = dictUnionSeaFightActivity.getInitIntegral();
		} else {
			AMLog.LOG_UNION.warn("海战重置积分没有找到DictUnionSeaFightActivity,groupId={}", ssUSFSyncRanksRes.getGroupId());
		}
		for (int zoneId : zoneIdSet) {
			MemoryData memoryData = Globals.getMemoryData(zoneId);
			if (memoryData == null) {
				continue;
			}
			Map<Long, AbstractUnion> unionMap = memoryData.getUnionMap();
			if (unionMap == null || unionMap.isEmpty()) {
				continue;
			}
			ArrayList<AbstractUnion> unionList = new ArrayList<>(unionMap.values());
			for (AbstractUnion abstractUnion : unionList) {
				Union union = (Union) abstractUnion;
				union.getUnionSeaFightManager().resetIntegral(seasonInfo.getId(), initIntegral);
			}
		}
	}

	@Override
	public CommonEntity convert2Entity() {
		String id = String.valueOf(this.groupId);
		String data = JSON.toJSONString(this);
		CommonEntity entity = new CommonEntity(id, ENTITY_CLASS_NAME, data, PubEntityPriority.PlayerCommentPriority);
		return entity;
	}
	
	public int getGroupId() {
		return groupId;
	}

	public void setGroupId(int groupId) {
		this.groupId = groupId;
	}

	public USFBenfuSeasonInfo getLastSeasonInfo() {
		return lastSeasonInfo;
	}

	public void setLastSeasonInfo(USFBenfuSeasonInfo lastSeasonInfo) {
		this.lastSeasonInfo = lastSeasonInfo;
	}

	public USFBenfuSeasonInfo getCurSeasonInfo() {
		return curSeasonInfo;
	}

	public void setCurSeasonInfo(USFBenfuSeasonInfo curSeasonInfo) {
		this.curSeasonInfo = curSeasonInfo;
	}

	public long getResetTime() {
		return resetTime;
	}

	public void setResetTime(long resetTime) {
		this.resetTime = resetTime;
	}

	public long getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(long timeStamp) {
		this.timeStamp = timeStamp;
	}

}
