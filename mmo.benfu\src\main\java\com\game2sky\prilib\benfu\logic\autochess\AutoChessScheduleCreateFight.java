package com.game2sky.prilib.benfu.logic.autochess;

import com.game2sky.prilib.communication.game.autochess.SSAutoChessConnectBattle;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessCreateFight;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO 延迟创建战斗
 *
 * <AUTHOR>
 * @version v0.1 2019年5月14日 下午6:04:52  guojijun
 */
public class AutoChessScheduleCreateFight extends ScheduledMessage {

	public static final int DELAY = 1000;
	public static final int RETRY_MAX_COUNT = 10;

	private ServerSocketAddress serverSocketAddress;
	private SSAutoChessCreateFight ssAutoChessCreateFight;
	/**重试次数*/
	private int retryCount;

	public AutoChessScheduleCreateFight(ServerSocketAddress serverSocketAddress,
										SSAutoChessCreateFight ssAutoChessCreateFight) {
		super(System.currentTimeMillis());
		this.serverSocketAddress = serverSocketAddress;
		this.ssAutoChessCreateFight = ssAutoChessCreateFight;
	}

	@Override
	public void execute() {
		PlayerDisplayInformation displayInfo = this.ssAutoChessCreateFight.getPlayerSyncInfo().getDisplayInfo();
		long playerId = displayInfo.getPlayerId();
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(this.serverSocketAddress.getFlag());
		SSAutoChessConnectBattle ssAutoChessConnectBattle = null;
		if (connectStatus == ClientConnectStatus.CONNECTED) {
			this.cancel();
			ssAutoChessConnectBattle = new SSAutoChessConnectBattle(true, this.serverSocketAddress.toProto(),
				this.ssAutoChessCreateFight);
		} else {
			if (this.retryCount++ < RETRY_MAX_COUNT) {
				CrossServerManager.init(serverSocketAddress, true);
			} else {
				AMLog.LOG_COMMON.warn("@AutoChess连接战斗服超时,无法进入战斗,playerId={},serverFlag={}", playerId,
					this.serverSocketAddress.getFlag());
				this.cancel();
				ssAutoChessConnectBattle = new SSAutoChessConnectBattle();
				ssAutoChessConnectBattle.setSucc(false);
			}
		}
		if (ssAutoChessConnectBattle != null) {
			int serverId = displayInfo.getServerId();
			Message message = Message.buildByServerId(playerId, serverId, ssAutoChessConnectBattle, null, null, null);
			Globals.getMsgProcessDispatcher().put(message);
		}
	}

}
