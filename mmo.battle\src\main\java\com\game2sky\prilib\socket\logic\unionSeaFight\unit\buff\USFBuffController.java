package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff;

import gnu.trove.map.hash.TIntObjectHashMap;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.equipment.PropValue;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitBuffChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffProInfo;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightRole;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSailor;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFTickable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill.USFSkillController;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.KV;

/**
 * TODO 海战BUFF控制器
 *
 * <AUTHOR>
 * @version v0.1 2018年10月17日 下午4:00:15  guojijun
 */
public class USFBuffController implements IUSFBuff, IUSFTickable, IUSFConsts
{

//	private static final int TICK_COUNT_MAX = 10;

	private final AbstractUSFUnit unit;
//	private int tickCount = 0;

	private final ArrayList<USFBuff> buffs;
	private final ArrayList<USFBuff> removeBuffs;
	private final ArrayList<USFBuff> updateBuffs;
	private final TIntObjectHashMap<PropValue> propValueMap;

	public USFBuffController(AbstractUSFUnit unit)
	{
		this.unit = unit;
		this.buffs = new ArrayList<>(5);
		this.removeBuffs = new ArrayList<>(5);
		this.updateBuffs = new ArrayList<>(5);
		this.propValueMap = new TIntObjectHashMap<>();
		// TODO 换塔逻辑
	}

//	private ArrayList<USFBuff> createTempleteBuffs(int[] buffIds)
//	{
//		if (buffIds == null || buffIds.length == 0)
//		{
//			return null;
//		}
//		ArrayList<USFBuff> buffs = new ArrayList<USFBuff>(buffIds.length);
//		for (int id : buffIds)
//		{
//			DictUSFBuff buff = DictUSFBuff.getDictUSFBuff(id);
//			if (buff == null)
//			{
//				continue;
//			}
//			USFBuff createBuff = USFBuff.createBuff(buff);
//			if (createBuff != null)
//			{
//				createBuff.setOwnerUnit(unit);
//				buffs.add(createBuff);
//			}
//		}
//		return buffs;
//	}

	private List<USFBuff> triggerBuffs = new ArrayList<USFBuff>();

	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit trigerUnit, AbstractUSFUnit oppositeUnit,
								Object... args)
	{

		if (buffs != null && buffs.size() != 0)
		{
			triggerBuffs.addAll(buffs);
			for (USFBuff buff : triggerBuffs)
			{
				buff.triggerEvent(triggerType, trigerUnit, oppositeUnit);
			}
			triggerBuffs.clear();
		}
	}

	/**
	 * 同步改变给client
	 */
	private void syncChange()
	{
		if (this.removeBuffs.isEmpty() && this.updateBuffs.isEmpty())
		{
			return;
		}
		try
		{
			// boolean update = false;
			ArrayList<Integer> tmpRemoveBuffGuids = null;
			ArrayList<USFClientBuffInfo> tmpUpdateBuffs = null;
			if (this.removeBuffs.size() > 0)
			{
				tmpRemoveBuffGuids = new ArrayList<>(this.removeBuffs.size());
				for (USFBuff removeBuff : this.removeBuffs)
				{
					// if (removeBuff.getBuffType() ==
					// SceneBuffEffectType.ADD_PROPERTY.value()) {
					// prosChange = true;
					// }
					if (!removeBuff.isClientSide() && !updateBuffs.contains(removeBuff))
					{
						// update = true;
						tmpRemoveBuffGuids.add(removeBuff.getGuid());
					}
				}

			}
			if (this.updateBuffs.size() > 0)
			{
				tmpUpdateBuffs = new ArrayList<>(this.updateBuffs.size());
				for (USFBuff updateBuff : this.updateBuffs)
				{
					// if (updateBuff.getBuffType() ==
					// SceneBuffEffectType.ADD_PROPERTY.value()) {
					// prosChange = true;
					// }
					if (!updateBuff.isClientSide())
					{
						if(!removeBuffs.contains(updateBuff))
						{
							USFClientBuffInfo copyTo = updateBuff.copyClientInfoToSend();
							if(copyTo.getDuration() > 0)
							{
								copyTo.setState(1);
							}
							tmpUpdateBuffs.add(copyTo);
						}						
					}
				}
			}
			// if (update)
			// {
			SCUSFUnitBuffChange scUSFUnitBuffChange = new SCUSFUnitBuffChange();
			scUSFUnitBuffChange.setUnitId(this.unit.getId());
			scUSFUnitBuffChange.setRemoveBuffGuids(tmpRemoveBuffGuids);
			scUSFUnitBuffChange.setBuffInfos(tmpUpdateBuffs);
			this.unit.getCamp().getUsFight().broadcast(scUSFUnitBuffChange);
			// }
//			if (!prosChange)
//			{
//				return;
//			}
		} finally
		{
			this.removeBuffs.clear();
			this.updateBuffs.clear();
		}

	}

	@Override
	public void tick(long now)
	{
//		if(unit instanceof USFPlayer)
//		{
//			USFPlayer player = (USFPlayer)unit;
//			if(player.getMoveController().getShipId() == 130002)
//			{
//				//System.out.println("黄金梅里号" + player.getDisplayInfo().getNickName());
//			}
//		}
//
//		this.tickCount++;
//		if (this.tickCount % TICK_COUNT_MAX > 0)
//		{
//			return;
//		}
//		this.tickCount = 0;
		if (this.buffs.isEmpty() && this.removeBuffs.isEmpty() && this.updateBuffs.isEmpty())
		{
			return;
		}
		// 清理时效性过期的BUFF
		try
		{
			List<USFBuff> tmpRemoveList = null;
			for(USFBuff buff : buffs)
			{
				if (buff.isArriveDurationTime(now))
				{
					buff.doEffect(now);
				}
				if (buff.isOwnerClosed() || buff.isTimeOver(now))
				{
					if(tmpRemoveList == null)
					{
						tmpRemoveList = new ArrayList<USFBuff>();
					}
					tmpRemoveList.add(buff);
				}
			}
			if(tmpRemoveList != null)
			{
				for(USFBuff rBuff : tmpRemoveList)
				{
					removeBuff(rBuff);
				}
			}
//			for (Iterator<USFBuff> iter = this.buffs.iterator(); iter.hasNext();)
//			{
//				USFBuff buff = iter.next();
//				if (buff.isArriveDurationTime(now))
//				{
//					buff.doEffect(now);
//				}
//
//				if (buff.isOwnerClosed() || buff.isTimeOver(now))
//				{
//					iter.remove();
//					long totalTime = buff.getClientInfo().getTotalTime();
//					if(totalTime > 1000 || totalTime == 0 )
//					{
//						this.removeBuffs.add(buff);		
//					}
//					buff.unload();
//					if (AMLog.LOG_UNION.isDebugEnabled())
//					{
//						AMLog.LOG_UNION.debug("海战单位移除BUFF,unitId={},buff={}", this.unit.getId(), buff.getClientInfo());
//					}
//				}
//
//			}
			this.syncChange();
		} finally
		{
			this.removeBuffs.clear();
			this.updateBuffs.clear();
		}
	}

	public List<USFClientBuffInfo> getShowBuffs()
	{
		ArrayList<USFClientBuffInfo> buffInfos = new ArrayList<>(this.buffs.size());
		for (USFBuff buff : this.buffs)
		{
			if (!buff.isClientSide())
			{
				buffInfos.add(buff.getClientInfo());
			}
		}
		//添加buff据点里技能的buff在里面,这个地方代码不优雅，不过暂时先这样吧
		if(unit instanceof USFFortress)
		{
			USFSkillController skillController = unit.getSkillController();
			if(skillController != null)
			{
				List<USFSkill> skills = skillController.getSkills();
				if(skills != null)
				{
					for(USFSkill skill: skills)
					{
						if(skill.isClosed())
						{
							continue;
						}
						for(USFBuff buff: skill.getUsfBuffers())
						{
							buffInfos.add(buff.getClientInfo());
						}
					}
				}
			}
		}
		return buffInfos;
	}

	@Override
	public List<USFBuff> getBuffs()
	{
		return this.buffs;
	}

	@Override
	public void addBuff(USFBuff addBuff)
	{
		if (!addBuff.isCanRepeat())
		{
			Iterator<USFBuff> iterator = buffs.iterator();
			while (iterator.hasNext())
			{
				USFBuff next = iterator.next();
				if (addBuff.getGroupId() != next.getGroupId())
				{
					continue;
				}
				// 一个集合里不会有重复的groupId
				if (addBuff.getPriority() < next.getPriority())
				{
					return;
				}
				if(next.getClientInfo().getBuffId() == addBuff.getClientInfo().getBuffId() 
						&& addBuff.getClientInfo().getTotalTime() == 0)
				{
					return;
				}
				removeBuffs.add(next);
				next.unload();
				iterator.remove();
				if (AMLog.LOG_UNION.isDebugEnabled())
				{
					AMLog.LOG_UNION.debug("海战单位移除BUFF,unitId={},buff={}", this.unit.getId(), next.getClientInfo());
				}
				break;
			}
		}
//		boolean isHpBuff = false;
//		List<USFClientBuffProInfo> proInfos = addBuff.getClientInfo().getProInfos();
//		if(proInfos != null && proInfos.size() == 1)
//		{
//			if(proInfos.get(0).getPropId() == USFBuff.HP_BUFF_ID)
//			{
//				isHpBuff = true;
//			}
//		}
//		if(!isHpBuff)
//		{
		
		updateBuffs.add(addBuff);		
//		}
		buffs.add(addBuff);
		addBuff.load();
		if (AMLog.LOG_UNION.isDebugEnabled())
		{
			AMLog.LOG_UNION.debug("海战单位添加BUFF,unitId={},buff={}", this.unit.getId(), addBuff.getClientInfo());
		}
	}

	@Override
	public void removeBuff(USFBuff removeBuff)
	{
		boolean bl = this.buffs.remove(removeBuff);
		if (bl)
		{
			long totalTime = removeBuff.getClientInfo().getTotalTime();
			if(totalTime > 100 || totalTime == 0 )
			{
				this.removeBuffs.add(removeBuff);		
			}
			removeBuff.unload();
		}
		if (AMLog.LOG_UNION.isDebugEnabled())
		{
			AMLog.LOG_UNION.debug("海战单位移除BUFF{},unitId={},buff={}", bl, this.unit.getId(), removeBuff.getClientInfo());
		}
	}

	@Override
	public void refreshProps(FFightSide fightSide)
	{
		if (CollectionUtils.isEmpty(fightSide.roles))
		{
			return;
		}
		FFightRole fightRole = fightSide.roles.get(0);
		for (FFightSailor fightSailor : fightRole.sailors)
		{
			this.refreshProps(fightSailor);
		}
	}

	private void refreshProps(FFightSailor fightSailor)
	{
		try
		{
			TIntObjectHashMap<TIntObjectHashMap<KV<Integer, Float>>> affectMap = null;
			if (unit instanceof USFPlayer) {
				affectMap = ((USFPlayer) unit).getFightController().getAffectMap();
			}
			for (USFBuff buff : this.buffs)
			{
				List<USFClientBuffProInfo> proInfos = buff.getProInfos();
				int buffId = buff.getClientInfo().getBuffId();
				// 计算其他buff对当前buff的效果影响
				float extroValue = 0; // 额外加成值
				float extropercent = 0; // 额外加成百分比
				if (affectMap != null) {
					for (TIntObjectHashMap<KV<Integer, Float>> map : affectMap.valueCollection()) {
						KV<Integer, Float> kv = map.get(buffId);
						if (kv == null) {
							continue;
						}
						if (kv.getK() == USF_BUFF_ADD_TYPE_FIXED) {
							// 加成值
							extroValue += kv.getV();
						} else {
							// 加成百分比
							extropercent += kv.getV();
						}
					}
				}
				if (!CollectionUtils.isEmpty(proInfos))
				{
					for (USFClientBuffProInfo proInfo : proInfos)
					{
						if (proInfo.getPropId() == USFBuff.HP_BUFF_ID)
						{
							continue;
						}
						PropValue propValue = this.propValueMap.get(proInfo.getPropId());
						if (propValue == null)
						{
							propValue = new PropValue(proInfo.getPropId(), 0);
							this.propValueMap.put(proInfo.getPropId(), propValue);
						}
						double addValue = proInfo.getAddValue();
						// 计算额外影响。 先加额外值，再乘以半分比
						addValue = (addValue + extroValue) * (1 + extropercent);
						if (proInfo.getAddType() == USF_BUFF_ADD_TYPE_FIXED)
						{
						} else if (proInfo.getAddType() == USF_BUFF_ADD_TYPE_PERCENT)
						{
							double base = fightSailor.getPropValue(propValue.getPropId());
							addValue = base * addValue;
						}
						propValue.setPropValue(propValue.getPropValue() + addValue);
					}
				}
				
				int[] battleBufferIds = buff.getBattleBufferIds();
				if (battleBufferIds != null && battleBufferIds.length > 0)
				{
					if (fightSailor.getFightBuffIds() == null)
					{
						fightSailor.fightBuffIds = new ArrayList<Integer>();
					}
					for (int id : battleBufferIds)
					{
						fightSailor.addFightBuff(id,1);
					}
				}

			}
			if (!this.propValueMap.isEmpty())
			{
				for (PropValue propValue : this.propValueMap.valueCollection())
				{
					fightSailor.addPropValue(propValue);
				}
			}
		} finally
		{
			this.propValueMap.clear();
		}
	}

	public void removeAllBuff()
	{
		for (USFBuff buff : buffs)
		{
			if (!removeBuffs.contains(buff))
			{
				removeBuffs.add(buff);
			}
			buff.unload();
		}
		buffs.clear();
	}

	public TIntObjectHashMap<PropValue> getPropValueMap()
	{
		return propValueMap;
	}
}
