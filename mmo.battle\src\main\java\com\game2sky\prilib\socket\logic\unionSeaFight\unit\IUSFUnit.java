package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFTickable;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IUSFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.publib.communication.game.struct.FPoint3;

/**
 * TODO 海战单位接口
 *
 * <AUTHOR>
 * @version v0.1 2018年6月7日 下午5:01:27  guojijun
 */
public interface IUSFUnit extends IUSFTickable, IUSFBuff {

	int getId();

	USFUnitType getUnitType();

	FPoint3 getPos();

	FPoint3 getDir();

	USFCamp getCamp();

	USFCampType getCampType();

	USFFortress getCurFortress();

	USFClientUnitInfo copyTo();
	
	List<Integer> getSkillIds(); 

	/**
	 * 获取状态机
	 * 
	 * @return
	 */
	<T extends AbstractUSFUnitFsmController> T getFsmController();

	/**
	 *  当复活
	 */
	void onRes();
}
