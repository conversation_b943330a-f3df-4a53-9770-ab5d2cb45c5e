package com.game2sky.prilib.socket.logic.autochess.ai;

/**
 * TODO 机器人状态
 *
 * <AUTHOR>
 * @version v0.1 2019年6月21日 下午4:08:37  guojijun
 */
public enum AutoChessRobotStateEnum {
										/**升级*/
										STATE_UPGRADE_LEVEL,
										/**购买棋子*/
										STATE_BUG_CHESS,
										/**刷新购买棋子*/
										STATE_REFRESH_BUG_CHESS,
										/**出售棋子*/
										STATE_SELL_CHESS,
										/**升星*/
										STATE_UPGRADE_STAR,
										/**调整阵容前*/
										STATE_BEFORE_CHANGE_FORMATION,
										/**调整阵容*/
										STATE_CHANGE_FORMATION,
										/**休息*/
										STATE_SLEEP,;
}
