package com.game2sky.prilib.socket.logic.integral.ai;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import com.game2sky.prilib.communication.game.player.FormationItem;
import com.game2sky.prilib.communication.game.player.PlayerFormation;
import com.game2sky.prilib.core.dict.domain.DictFormationPos;
import com.game2sky.prilib.core.dict.domain.DictHero;
import com.game2sky.prilib.core.dict.domain.DictIntegralRobot;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.formation.FormationConstant;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.base.IdTools;

/**
 * 天梯机器人
 * <AUTHOR>
 * @version v0.1 2020年5月10日 下午2:41:52  zhoufang
 */
public class IntegralRobot {
	
	private static final ConcurrentHashMap<Integer,List<IntegralRobot>> defaultRobots = new ConcurrentHashMap<>();
	
	protected long robotPlayerId;
	protected int serverId;
	protected PlayerFormation playerFormation;
	protected FFightSide fightSide;
	
	public static void initRobots(){
		Set<Integer> areaSet = IntegralConfigCenter.getAreaMap().keySet();
		for (Integer areaType : areaSet) {
			DictIntegralRobot dictRobot = DictIntegralRobot.getDictRobot(areaType);
			if (dictRobot == null) {
				continue;
			}
			// 随机英雄配置
			List<DictHero> heroList = randomHeros(dictRobot);
			// 随机阵法
			int formationId = randomFormationId(dictRobot.getFormationInfo());
			// 生成阵容
			PlayerFormation formation = createFormation(formationId, heroList);
			
		}
		
	}
	
	/**
	 * 随机英雄
	 * @param dictRobot
	 * @return
	 */
	private static List<DictHero> randomHeros(DictIntegralRobot dictRobot) {
		// 可随机的所有英雄列表 key:英雄定义类型
		Map<Integer, LinkedList<DictHero>> definitionMap = new HashMap<Integer, LinkedList<DictHero>>();
		// 可随机的英雄总数
		int totalHeroSize = 0;
		// 英雄定义类型
		int[] heroTypeInfo = dictRobot.getHeroTypeInfo();
		for (int i = 0; i < heroTypeInfo.length; i++) {
			List<DictHero> heroListByDefinitionId = DictHero.getHeroListByDefinitionId(heroTypeInfo[i]);
			if (heroListByDefinitionId == null || heroListByDefinitionId.isEmpty()) {
				continue;
			}
			LinkedList<DictHero> list = new LinkedList<DictHero>();
			for (DictHero dict : heroListByDefinitionId) {
				if (dict.getQuality() >= dictRobot.getHeroQuality()) {
					list.add(dict);
				}
			}
			if (list.isEmpty()) {
				continue;
			}
			totalHeroSize += list.size();
			Collections.shuffle(list);
			definitionMap.put(heroTypeInfo[i], list);
		}
		if (totalHeroSize < FormationConstant.MAX_FORMATION_ITEM_NUM) {
			AMLog.LOG_STDOUT.info("天梯生成AI机器人错误,无法随机出5个英雄.keySet=" + definitionMap.keySet());
			System.exit(1);
		}
		// 开始随机英雄
		int haveSize = 0; // 已有英雄数量
		// 随机出来的英雄列表
		Map<Integer, List<DictHero>> randomMap = new HashMap<Integer, List<DictHero>>();
		for (Entry<Integer, LinkedList<DictHero>> entry : definitionMap.entrySet()) {
			// 首先每个定义类型的英雄都随机一个
			List<DictHero> tmpList = new ArrayList<DictHero>();
			tmpList.add(entry.getValue().pollFirst());
			randomMap.put(entry.getKey(), tmpList);
			haveSize++;
		}
		Random random = new Random();
		List<Integer> definitionList = new ArrayList<Integer>(definitionMap.keySet());
		while (haveSize < FormationConstant.MAX_FORMATION_ITEM_NUM) {
			if (definitionList.isEmpty()) {
				break;
			}
			int randomIndex = random.nextInt(definitionList.size());
			int randomDefinition = definitionList.get(randomIndex);
			LinkedList<DictHero> linkedList = definitionMap.get(randomDefinition);
			if (linkedList.isEmpty()) {
				definitionList.remove(randomIndex);
				continue;
			}
			randomMap.get(randomDefinition).add(linkedList.pollFirst());
			haveSize++;
		}
		// 随机结束, 根据站位排序
		List<DictHero> reslustList = new ArrayList<DictHero>();
		int[] heroPlaceInfo = dictRobot.getHeroPlaceInfo();
		for (int i = 0; i < heroPlaceInfo.length; i++) {
			reslustList.addAll(randomMap.get(heroPlaceInfo[i]));
		}
		return reslustList;
	}

	private static PlayerFormation createFormation(int formationId, List<DictHero> randomHeros) {
		PlayerFormation formation = new PlayerFormation();
		formation.setFormationId(formationId);
		List<DictFormationPos> formationPoses = DictFormationPos.getFormationPoses(formationId);
		Collections.sort(formationPoses);
		List<FormationItem> formationItems = new ArrayList<FormationItem>(randomHeros.size());
		for (int i = 0; i < formationPoses.size(); i++) {
			DictHero dictHero = randomHeros.get(i);
			FormationItem formationItem = new FormationItem();
			formationItem.setHeroGuid(IdTools.nextId());
			formationItem.setHeroId(dictHero.getHeroId());
			formationItem.setBattlePos(formationPoses.get(i).getPos());
			formationItems.add(formationItem);
		}
		formation.setFormationItems(formationItems);
		// TODO 助战
		return formation;
	}

	private static int randomFormationId(int[] formationInfo) {
		int index = new Random().nextInt(formationInfo.length);
		return formationInfo[index];
	}
}
