package com.game2sky.prilib.benfu.logic.integral;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer;
import com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer.IntegralAreaInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.core.socket.logic.awardSend.AwardSendType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig;
import com.game2sky.prilib.core.socket.logic.title.TitleService;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.awardSend.IAwardSendService;

/**
 * 天梯旧版本发奖任务
 * <AUTHOR>
 * @version v0.1 2020年4月22日 下午2:55:44  zhoufang
 */
public class IntegralOldVersionRewardTask implements IntegralSendRewardTask{
	
	private IntegralGroup group;
	
	public IntegralOldVersionRewardTask(IntegralGroup group){
		this.group = group;
	}
	
	@Override
	public void sendWeekEndReward(){
		// 每周只发放积分奖励,2个赛区取奖励多的那一份
		List<IntegralGamePlayer> playerList = new ArrayList<IntegralGamePlayer>(group.getPlayers().values());
		
		IAwardSendService awardSendService = CoreBoot.getBean(IAwardSendService.class);
		TitleService titleService = CoreBoot.getBean(TitleService.class);
		for (IntegralGamePlayer player : playerList) {
			IntegralAreaInfo nolimitAreaInfo = player.getAreaInfos().get(IntegralAreaType.NOLIMIT.value());
			IntegralAreaInfo amendAreaInfo = player.getAreaInfos().get(IntegralAreaType.AMEND.value());
			// 本周无限制赛区的积分和参赛次数
			int nolimitCount = nolimitAreaInfo == null ? 0 : nolimitAreaInfo.getWeekFightCount();
			int nolimitIntegral = nolimitAreaInfo == null ? 0 : nolimitAreaInfo.getIntegral();
			// 本周修正赛赛区的积分和参赛次数
			int amendCount = amendAreaInfo == null ? 0 : amendAreaInfo.getWeekFightCount();
			int amendIntegral = amendAreaInfo == null ? 0 : amendAreaInfo.getIntegral();
			// 玩家在无限制赛区可获取的奖励配置
			IntegralRewardConfig config_Nolimit = player.getIntegralRewardConfig(IntegralAreaType.NOLIMIT.value());
			// 玩家在修正赛区可获取的奖励配置
			IntegralRewardConfig config_Amend = player.getIntegralRewardConfig(IntegralAreaType.AMEND.value());
			if (config_Nolimit == null && config_Amend == null) {
				AMLog.LOG_COMMON.info("天梯积分奖励----->玩家{}无法发放积分奖励,无限制赛区本周积分{},战斗场次{}...修正赛区本周积分{},战斗场次{}",player.getPlayerId(),
					nolimitIntegral,nolimitCount,amendIntegral,amendCount);
				continue;
			}
			// 判断2份奖励,取得可以得到的那份奖励
			boolean isNolimit = false;// 是否发放无限制赛奖励
			if (config_Nolimit != null) {
				if (config_Amend == null) {
					isNolimit = true;
				} else {
					int value_Nolimit = config_Nolimit.getIntegralValue();
					int value_Amend = config_Amend.getIntegralValue();
					isNolimit = value_Nolimit >= value_Amend;
				}
			}
			int integral = isNolimit ? nolimitIntegral : amendIntegral;
			IntegralRewardConfig realConfig = isNolimit ? config_Nolimit : config_Amend;
			int zoneId = Globals.getZoneId(player.getServerId());
			awardSendService.addAward(AwardSendType.INTEGRAL_AWARD, zoneId, player.getPlayerId(), realConfig.items,
				new Object[] {}, new Object[] { integral });
			if (realConfig.title != null) {
				titleService.addTitle(zoneId, player.getPlayerId(), Integer.parseInt(realConfig.title));
			}
			AMLog.LOG_COMMON.info("天梯积分奖励----->玩家{}的奖励发送成功,无限制赛区本周积分{},战斗场次{}...修正赛区本周积分{},战斗场次{}",player.getPlayerId(),
				nolimitIntegral,nolimitCount,amendIntegral,amendCount);
		}
	}
	
	@Override
	public void sendSeasonEndReward(){
		Map<Integer, IntegralCurrentRank> rankCache = group.getRankCache();
		IAwardSendService awardSendService = CoreBoot.getBean(IAwardSendService.class);
		TitleService titleService = CoreBoot.getBean(TitleService.class);
		
		// 无限制赛排名数据和奖励配置
		List<IntegralRankItem> nolimitRankList = new ArrayList<IntegralRankItem>();
		if(rankCache.containsKey(IntegralAreaType.NOLIMIT.value())){
			nolimitRankList.addAll(rankCache.get(IntegralAreaType.NOLIMIT.value()).getRankList());
		}
		List<IntegralRewardConfig> nolimitRewardConfigs = IntegralConfigCenter.getIntegralRankRewards().get(
			IntegralAreaType.NOLIMIT.value());
		
		// 修正赛排名数据和奖励配置
		List<IntegralRankItem> amendRankList = new ArrayList<IntegralRankItem>();
		if(rankCache.containsKey(IntegralAreaType.AMEND.value())){
			amendRankList.addAll(rankCache.get(IntegralAreaType.AMEND.value()).getRankList());
		}
		List<IntegralRewardConfig> amendRewardConfigs = IntegralConfigCenter.getIntegralRankRewards().get(
			IntegralAreaType.AMEND.value());
		AMLog.LOG_COMMON.info("准备发放排名奖励,当前无限制赛排行榜人数{},修正赛排行榜人数{}",nolimitRankList.size(),amendRankList.size());
		
		// 修正赛玩家排名
		Map<Long, Integer> amendPlayerRanks = new HashMap<Long, Integer>();
		for (int i = 0; i < amendRankList.size(); i++) {
			IntegralRankItem item = amendRankList.get(i);
			amendPlayerRanks.put(item.getPlayerId(), i + 1);
		}

		// 已经在无限制赛发送过奖励的玩家
		Set<Long> sendNolimitPlayers = new HashSet<Long>();
		// 发送无限制赛奖励
		for (IntegralRewardConfig config : nolimitRewardConfigs) {
			for (int i = config.before; i <= config.after; i++) {
				if (nolimitRankList.size() < i) {
					break;
				}
				IntegralRankItem rankItem = nolimitRankList.get(i - 1);
				if (!CoreBoot.getAllServerId().contains(rankItem.getServerId())) {
					continue;
				}
				Integer amendRank = amendPlayerRanks.get(rankItem.getPlayerId());
				if (amendRank != null && amendRank < i) {
					// 玩家在修正赛有更高的名次
					continue;
				}
				// 发送奖励
				sendNolimitPlayers.add(rankItem.getPlayerId());
				int zoneId = Globals.getZoneId(rankItem.getServerId());
				awardSendService.addAward(AwardSendType.IntegralNoLimitRankReward, zoneId, rankItem.getPlayerId(),
					config.items, new Object[] {}, new Object[] { i });
				if (config.title != null) {
					titleService.addTitle(zoneId, rankItem.getPlayerId(), Integer.parseInt(config.title));
				}
				AMLog.LOG_COMMON.info("发送天梯无限制赛排名奖励,playerId={},rank={}, 积分={}", rankItem.getPlayerId(), i,
					rankItem.getIntegral());
			}
		}

		// 发送修正赛奖励
		for (IntegralRewardConfig config : amendRewardConfigs) {
			for (int i = config.before; i <= config.after; i++) {
				if (amendRankList.size() < i) {
					break;
				}
				IntegralRankItem rankItem = amendRankList.get(i - 1);
				if (!CoreBoot.getAllServerId().contains(rankItem.getServerId())) {
					continue;
				}
				if (sendNolimitPlayers.contains(rankItem.getPlayerId())) {
					// 玩家已经在无限制赛区发过奖励
					continue;
				}
				// 发送奖励
				int zoneId = Globals.getZoneId(rankItem.getServerId());
				awardSendService.addAward(AwardSendType.IntegralAmendRankReward, zoneId, rankItem.getPlayerId(),
					config.items, new Object[] {}, new Object[] { i });
				if (config.title != null) {
					titleService.addTitle(zoneId, rankItem.getPlayerId(), Integer.parseInt(config.title));
				}
				AMLog.LOG_COMMON.info("发送天梯修正赛排名奖励,playerId={},rank={}, 积分={}", rankItem.getPlayerId(), i,
					rankItem.getIntegral());
			}
		}
	}
}
