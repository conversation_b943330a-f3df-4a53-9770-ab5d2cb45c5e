package com.game2sky.prilib.benfu.logic.entirerank;

import io.netty.channel.Channel;

import org.springframework.stereotype.Service;

import com.game2sky.prilib.communication.game.entirerank.EntireRankType;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerInfoChange;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankSendSortData;
import com.game2sky.prilib.communication.game.entirerank.SSRefreshEntireRank;
import com.game2sky.prilib.core.socket.logic.entirerank.EntireRankBenfuManager;
import com.game2sky.prilib.core.socket.logic.entirerank.EntireRankFactory;
import com.game2sky.prilib.core.socket.logic.entirerank.rankhelper.IEntireRankHelper;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventLinstener;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;

/**
 * 全服排行功能本服逻辑处理.
 *
 * <AUTHOR> Lee
 * @version v0.1 2020年2月24日 下午5:53:12  Jet Lee
 */
@Service
@EventSubscriber(eventSources = { ITargetServerRestartEventSource.class })
public class EntireRankBenfuService implements ITargetServerRestartEventLinstener {
	
	public void init() {
		EntireRankBenfuManager.getInstance().init();
		// 启动定时器
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleEntireBenfuTick(),
			ScheduleEntireBenfuTick.DELAY, ScheduleEntireBenfuTick.PERIOD);
	}

	@Override
	public void onTargetServerRestart(ServerTypeEnum serverType, Channel channel, int restartType) {
		if (serverType != ServerTypeEnum.QUANFU) {
			return;
		}
		AMLog.LOG_COMMON.info("[EntireRank] [link to center] [begin]");

		// 链接中心服，请求通用排行信息
		for (EntireRankType rankType : EntireRankType.values()) {
			IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(rankType);
			if (rankHelper.isOpen()) {
				rankHelper.entireRankReq(); // 请求排行榜数据
				rankHelper.sendPlayerRankInfoToCenter(); // 推送排行榜数据
			}
		}
		
		AMLog.LOG_COMMON.info("[EntireRank] [link to center] [end]");
	}

	public void ssEntireRankPlayerRankRes(SSEntireRankPlayerRankRes msg) {
		EntireRankBenfuManager.getInstance().refreshPlayerRank(msg);
	}

	public void ssRefreshEntireRank(SSRefreshEntireRank msg) {
		EntireRankBenfuManager.getInstance().refreshRank(msg.getRankType(), msg.getTopRanks(), msg.getTotalNum());
	}

	public void ssEntireRankScoreRankRes(SSEntireRankScoreRankRes msg) {
		IEntireRankHelper rankHelper = EntireRankFactory.getRankHelper(msg.getRankType());
		rankHelper.receiveScoreRank(msg.getPlayerId(), msg.getServerId(), msg.getScore(), msg.getRank());
	}

	public void ssEntireRankSendSortData(SSEntireRankSendSortData msg) {
		EntireRankBenfuManager.getInstance().saveSortData(msg.getRankType(), msg.getSortDatas());
	}

	public void ssEntireRankPlayerInfoChange(SSEntireRankPlayerInfoChange msg, Head head) {
		long playerId = head.getPid();
		for (DisplayInformationType informationData : msg.getValues()) {
			EntireRankBenfuManager.getInstance().playerInfoChange(playerId, informationData.getType(), informationData.getValue());
		}
	}

}
