package com.game2sky;

import com.game2sky.prilib.socket.logic.integral.IntegralArenaFightManager;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.server.inner.SSBattleServerLoadReportReq;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.schedule.ScheduledMessage;
import com.game2sky.publib.socket.logic.MemoryData;

/**
 * TODO 汇报负载定时任务
 *
 * <AUTHOR>
 * @version v0.1 2017-11-4 下午4:27:12  guojijun
 */
public class ScheduledLoadReporter extends ScheduledMessage {

	/**汇报首次延时*/
	public static final int SCHEDULED_DELAY = 5000;
	/**汇报时间间隔*/
	public static final int SCHEDULED_PERIOD = 3000;

	private final SSBattleServerLoadReportReq loadReport;

	public ScheduledLoadReporter(long createTime) {
		super(createTime);
		this.loadReport = new SSBattleServerLoadReportReq();
		this.loadReport.setServerflag(BattleCoreBoot.SELF_FLAG);
		this.loadReport.setIp(CoreBoot.SELF_IP);
		this.loadReport.setServerId(CoreBoot.sceneProcessId);
		this.loadReport.setPort(CoreBoot.SELF_PORT);
	}

	@Override
	public void execute() {
		MemoryData memoryData = Globals.getMemoryData();
		this.loadReport.setPlayerCount(memoryData.getHumanMapSize());
		this.loadReport.setSceneCount(0);
		//天梯的战斗数量
		this.loadReport.setIntegralFightNum(IntegralArenaFightManager.getIntegralFightNum());
		//天梯的人数
		this.loadReport.setIntegralPlayerNum(IntegralArenaFightManager.getIntegralPlayerNum());
		
		//海战的战斗数量
		this.loadReport.setUnionSeaFightNum(0);
		//海战的玩家数量
		this.loadReport.setUnionSeaPlayerNum(0);

		boolean flag = BattleCoreBoot.pushToCenter(this.loadReport);
		if(AMLog.LOG_COMMON.isInfoEnabled()) {
			AMLog.LOG_COMMON.info("Number of reports to the central service of battle service, flag:{}, msg:{}", flag, loadReport);
		}		
	}

}
