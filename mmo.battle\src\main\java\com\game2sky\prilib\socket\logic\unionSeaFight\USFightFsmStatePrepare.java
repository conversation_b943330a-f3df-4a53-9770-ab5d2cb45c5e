package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.sql.Timestamp;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStatePrepareInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.TimeUtils;

/**
 * TODO 海战准备状态
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 上午11:37:12  guojijun
 */
public class USFightFsmStatePrepare extends AbstractUSFightFsmState {

	private USFClientStatePrepareInfo clientInfo;
	private long timeout;

	public USFightFsmStatePrepare(USFightFsmController controller) {
		super(USFStateType.USF_STATE_PREPARE, controller);
	}

	@Override
	public void enter() {
		USFightFsmController controller = this.getController();
		USFight usFight = controller.getUsFight();
		this.timeout = System.currentTimeMillis();
		this.timeout = this.timeout + TimeUtils.SECOND * usFight.getPrepareTime();
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			AMLog.LOG_UNION.info("海战进入准备阶段,usfight={},正式开始时间time={}", usFight, new Timestamp(this.timeout));
		}
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
		USFightFsmController fsmController = this.getController();
		USFight usFight = fsmController.getUsFight();
		if (now < this.timeout) {
			usFight.onPrepare(now);
		} else {
			this.controller.switchState(USFStateType.USF_STATE_RUNNING);
		}
	}

	@Override
	protected void copyTo(USFClientStateInfo clientStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientStatePrepareInfo();
		}
		this.clientInfo.setEndTime(this.timeout);
		clientStateInfo.setPrepareInfo(this.clientInfo);
	}
}
