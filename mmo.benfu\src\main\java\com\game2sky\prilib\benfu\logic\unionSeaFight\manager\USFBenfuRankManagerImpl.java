package com.game2sky.prilib.benfu.logic.unionSeaFight.manager;

import com.game2sky.prilib.benfu.logic.unionSeaFight.data.USFBenfuUnionRankGroup;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksRes;
import com.game2sky.publib.db.land.AbstractEntitySimpleOptManager;
import com.game2sky.publib.framework.dbs.orm.BaseEntity;
import com.game2sky.publib.framework.dbs.orm.CommonEntity;

import gnu.trove.map.hash.TIntObjectHashMap;

/**
 * TODO 海战排行管理器
 *
 * <AUTHOR>
 * @version v0.1 2018年9月7日 上午10:59:56  guojijun
 */
class USFBenfuRankManagerImpl extends AbstractEntitySimpleOptManager implements IUSFBenfuRankManager {

	/**海战排名分组*/
	private final TIntObjectHashMap<USFBenfuUnionRankGroup> unionRankGroupCache;

	USFBenfuRankManagerImpl() {
		this.unionRankGroupCache = new TIntObjectHashMap<>();
	}

	@Override
	public void load() {
	}

	@Override
	public boolean updateEntity(BaseEntity<?> entity) {
		return true;
	}

	@Override
	public boolean insertEntity(BaseEntity<?> entity) {
		return true;
	}

	@Override
	public boolean deleteEntity(BaseEntity<?> entity) {
		return true;
	}

	@Override
	public int getZoneId() {
		return 0;
	}

	@Override
	protected String getGroupId(BaseEntity<?> entity) {
		CommonEntity commonEntity = (CommonEntity) entity;
		return commonEntity.getEntityClassName();
	}

	@Override
	public USFBenfuUnionRankGroup getUnionRankGroup(int groupId) {
		return this.unionRankGroupCache.get(groupId);
	}

	@Override
	public void syncRanks(SSUSFSyncRanksRes ssUSFSyncRanksRes) {
		final int groupId = ssUSFSyncRanksRes.getGroupId();
		USFBenfuUnionRankGroup unionRankGroup = this.unionRankGroupCache.get(groupId);
		if (unionRankGroup == null) {
			unionRankGroup = new USFBenfuUnionRankGroup(ssUSFSyncRanksRes);
			this.unionRankGroupCache.put(groupId, unionRankGroup);
		} else {
			unionRankGroup.syncRanks(ssUSFSyncRanksRes);
		}
	}

}
