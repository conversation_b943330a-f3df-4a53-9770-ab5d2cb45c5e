package com.game2sky.prilib.benfu.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.unionSeaFight.data.USFBenfuUnionRankGroup;
import com.game2sky.prilib.benfu.logic.unionSeaFight.manager.IUSFBenfuRankManager;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksReq;
import com.game2sky.prilib.core.dict.domain.DictUnionSeaFightServerGroup;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO 
 *
 * <AUTHOR>
 * @version v0.1 2018年9月14日 下午7:28:09  guojijun
 */
class ScheduleUSFBenfuSyncRanks extends ScheduledMessage {

	public static final int PERIOD = 120000;

	private final HashMap<Integer, List<Integer>> groups;

	ScheduleUSFBenfuSyncRanks() {
		super(System.currentTimeMillis());
		this.groups = new HashMap<>();
	}

	@Override
	public void execute() {
		try {
			List<Integer> serverIds = CoreBoot.getAllServerId();
			for (int serverId : serverIds) {
				DictUnionSeaFightServerGroup dictUnionSeaFightServerGroup = DictUnionSeaFightServerGroup
					.getByServerId(serverId);
				if (dictUnionSeaFightServerGroup != null) {
					List<Integer> groupServerIds = this.groups.get(dictUnionSeaFightServerGroup.getId());
					if (groupServerIds == null) {
						groupServerIds = new ArrayList<>(5);
						this.groups.put(dictUnionSeaFightServerGroup.getId(), groupServerIds);
					}
					groupServerIds.add(serverId);
				}
			}
			if (this.groups.isEmpty()) {
				return;
			}
			for (Entry<Integer, List<Integer>> entry : this.groups.entrySet()) {
				USFBenfuUnionRankGroup rankGroup = IUSFBenfuRankManager.MANAGER.getUnionRankGroup(entry.getKey());
				long timeStamp = (rankGroup != null ? rankGroup.getTimeStamp() : 0);
				SSUSFSyncRanksReq ssUSFSyncRanksReq = new SSUSFSyncRanksReq(entry.getKey(), timeStamp,
					entry.getValue());
				String centerFlag = CrossServerManager.getDefaultServerFlag(ServerTypeEnum.QUANFU);
				CoreBoot.dispatch2Server(ssUSFSyncRanksReq, 0, entry.getValue().get(0), centerFlag);
			}
		} finally {
			this.groups.clear();
		}
	}

}
