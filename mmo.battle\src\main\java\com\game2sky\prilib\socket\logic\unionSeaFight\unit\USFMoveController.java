package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.USFShipInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictUSFMap;
import com.game2sky.prilib.core.dict.domain.DictUSFMoveTips;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFMsgSender;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFUtils;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFight;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.IUSFRouteNode;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.StringUtils;

/**
 * TODO 移动控制器
 *
 * <AUTHOR>
 * @version v0.1 2018年6月9日 下午7:10:23  guojijun
 */
public class USFMoveController {

	private final IUSFUnitMoveable moveable;

	/**战船数据*/
	private USFShipInfo shipInfo;
	/**碰撞体积*/
	private int collisionSize;
	/**最近一次计算移动的时间*/
	private long lastCalcPosTime;
	/**移动路径据点*/
	private final LinkedList<USFFortress> pathFortresses;
	/**临时使用的据点ID集合*/
	private final LinkedList<Integer> tmpPathIds;
	/**当前目标据点*/
	private USFFortress targetFortress;
	/**航线上的下一个节点*/
	private IUSFRouteNode nextNode;
	/**航线上的前一个节点*/
	private IUSFRouteNode prevNode;
	/**所在航线*/
	private USFRoute route;
	/**已经移动的距离*/
	private int moveDis;

	public USFMoveController(IUSFUnitMoveable moveable) {
		this.moveable = moveable;
		this.pathFortresses = new LinkedList<>();
		this.tmpPathIds = new LinkedList<>();
	}

	/**
	 * 进入航线
	 * 
	 * @param route
	 * @return
	 */
	public boolean enterRoute(USFRoute route, USFFortress targetFortress) {
		boolean bl = route.addNode(this.moveable, targetFortress);
		if (bl) {
			this.route = route;
		}
		return bl;
	}

	/**
	 * 离开航线
	 */
	public void leaveRoute() {
		if (this.route != null) {
			this.route.removeNode(this.moveable);
			this.moveable.getFsmController().switchState(USFUnitStateType.USF_UNIT_STATE_NORMAL);
			this.route = null;
			this.moveDis = 0;
		}
	}

	/**
	 * 重置数据
	 */
	public void reset() {
		this.pathFortresses.clear();
		this.targetFortress = null;
	}

	/**
	 * 取消移动
	 */
	public void cancelMove() {
		if (this.route == null) {
			this.targetFortress = null;
		}
		this.pathFortresses.clear();
	}
	
	/**
	 * 准备移动
	 * 
	 * @param targetFortressId 目标据点字典表ID
	 * @return
	 */
	public int readyToMove(final int targetFortressId) {
		USFight usFight = this.moveable.getCamp().getUsFight();
		USFFortress tmpFortress = usFight.getFortress(targetFortressId);
		if (tmpFortress == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位开始移动失败,目标据点不存在,unit={},targetFortressId={}", this.moveable, targetFortressId);
			}
			return ErrorCodeConstants.USF_UNIT_MOVE_TARGET_FORTRESS_NOT_EXISTS;
		}

		if (!this.pathFortresses.isEmpty()) {
			tmpFortress = this.pathFortresses.getLast();
			if (targetFortressId == tmpFortress.getDictId()) {
				return ErrorCodeConstants.USF_MOVE_TO_TARGET_FORTRESS_ALREADY;
			}
			if (targetFortressId == this.targetFortress.getDictId()) {// 目标据点就是当前的目标据点则清空路径
				this.pathFortresses.clear();
				return ErrorCodeConstants.OPERATION_SUCCESS;
			}
			boolean hasNode = false;
			for (Iterator<USFFortress> iter = this.pathFortresses.iterator(); iter.hasNext();) {
				USFFortress nodeFortress = iter.next();
				if (hasNode) {
					iter.remove();
					continue;
				}
				if (nodeFortress.getDictId() == targetFortressId) {
					hasNode = true;
				}
			}
			if (hasNode) {
				return ErrorCodeConstants.OPERATION_SUCCESS;
			}
		} else {
			if (this.targetFortress != null) {
				if (targetFortressId == this.targetFortress.getDictId()) {
					return ErrorCodeConstants.USF_MOVE_TO_TARGET_FORTRESS_ALREADY;
				}
			}
		}

		boolean needChangeTargetFortress = false;
		USFFortress curFortress;
		USFUnitStateType curStateType = this.moveable.getFsmController().getCurStateType();
		switch (curStateType) {
			case USF_UNIT_STATE_GARRISON:
				needChangeTargetFortress = true;// 在据点中目标据点可能需要改变
				curFortress = this.moveable.getCurFortress();
				if (targetFortressId == curFortress.getDictId()) {
					return ErrorCodeConstants.USF_MOVE_IN_FORTRESS_ALREADY;
				}
				break;
			case USF_UNIT_STATE_BATTLING:
			case USF_UNIT_STATE_MOVING:
			case USF_UNIT_STATE_WAITING:
				curFortress = this.moveable.getTargetFortress();
				break;
			case USF_UNIT_STATE_GHOST:
				if (AMLog.LOG_UNION.isWarnEnabled()) {
					AMLog.LOG_UNION.warn("海战单位彻底死亡不能发起移动,unit={}", this.moveable);
				}
				return ErrorCodeConstants.USF_UNIT_MOVE_STATE_GHOST;
			case USF_UNIT_STATE_REORGANIZE:
				if (AMLog.LOG_UNION.isWarnEnabled()) {
					AMLog.LOG_UNION.warn("海战单位整编中不能发起移动,unit={}", this.moveable);
				}
				return ErrorCodeConstants.USF_UNIT_MOVE_STATE_REORGANIZE;
			default:
				if (AMLog.LOG_UNION.isWarnEnabled()) {
					AMLog.LOG_UNION.warn("海战单位当前状态不能发起移动,unit={}", this.moveable);
				}
				return ErrorCodeConstants.USF_UNIT_MOVE_STATE_ERROR;
		}

		try {
			DictUSFMap.calcPaths(usFight.getMapId(), curFortress.getDictId(), targetFortressId, this.shipInfo, this.tmpPathIds);
			// 去掉当前点
			this.tmpPathIds.poll();
			if (this.tmpPathIds.isEmpty()) {
				if (AMLog.LOG_UNION.isWarnEnabled()) {
					AMLog.LOG_UNION.warn("海战单位开始移动失败,目标据点不可达,unit={},targetFortressId={}", this.moveable,
						targetFortressId);
				}
				return ErrorCodeConstants.USF_UNIT_MOVE_TARGET_FORTRESS_UNREACHABLE;
			}
			for (int fortressId : this.tmpPathIds) {
				USFFortress fortress = usFight.getFortress(fortressId);
				if (fortress == null) {
					if (AMLog.LOG_UNION.isWarnEnabled()) {
						AMLog.LOG_UNION.warn("海战单位开始移动失败,路径据点不存在,unit={},fortressId={}", this.moveable, fortressId);
					}
					return ErrorCodeConstants.USF_UNIT_MOVE_TARGET_FORTRESS_UNREACHABLE;
				}
			}
			int tmpTargetFortressId;
			if (needChangeTargetFortress) {
				tmpTargetFortressId = this.tmpPathIds.poll();
			} else {
				tmpTargetFortressId = this.tmpPathIds.peek();
			}
			USFRoute tmpRoute = curFortress.getRoute(tmpTargetFortressId);
			if (tmpRoute == null) {
				if (AMLog.LOG_UNION.isWarnEnabled()) {
					AMLog.LOG_UNION.warn("海战单位开始移动失败,第一个路径点不可达,unit={},targetFortressId={}", this.moveable,
						this.targetFortress.getDictId());
				}
				return ErrorCodeConstants.USF_UNIT_MOVE_TARGET_FORTRESS_UNREACHABLE;
			}
			if (needChangeTargetFortress) {
				this.targetFortress = usFight.getFortress(tmpTargetFortressId);
			}
			this.pathFortresses.clear();
			for (int fortressId : this.tmpPathIds) {
				USFFortress fortress = usFight.getFortress(fortressId);
				this.pathFortresses.add(fortress);
			}
			
			String tips = DictUSFMoveTips.getTips(curFortress.getDictId(), targetFortressId, this.shipInfo);
			if (!StringUtils.isEmpty(tips) && (this.moveable instanceof IUSFMsgSender)) {
				((IUSFMsgSender)this.moveable).pushPrompt(tips);
			}
			
			return ErrorCodeConstants.OPERATION_SUCCESS;
		} finally {
			this.tmpPathIds.clear();
		}
	}

	/**
	 * 刷新航线进度
	 * 
	 * @param now
	 */
	public void refreshProgress(long now) {
		AbstractUSFUnitFsmController fsmController = this.moveable.getFsmController();
		USFUnitStateType stateType = fsmController.getCurStateType();
		if (stateType != USFUnitStateType.USF_UNIT_STATE_MOVING) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位刷新进度失败,不在移动状态,unit={}", this.moveable);
			}
			return;
		}
		if (this.targetFortress == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位刷新进度失败,没有目标据点,unit={}", this.moveable);
			}
			return;
		}
		long moveTime = now - lastCalcPosTime;
		float speed = this.getSpeed() * this.route.getScale(this.moveable);
		float preMoveDis = moveTime * speed / 1000;
		int realPreMoveDis = USFUtils.floatToInt(preMoveDis);
		if (realPreMoveDis <= 0) {
			return;
		}
		this.route.checkCollideAndSwitch(this.moveable, realPreMoveDis);
		this.lastCalcPosTime = now;
	}

	/**
	 * 尝试移动
	 */
	public void tryMove() {
		if (!this.moveable.canMove()) {
			return;
		}
		if (this.targetFortress == null) {
			return;
		}
		AbstractUSFUnitFsmController fsmController = this.moveable.getFsmController();
		USFUnitStateType stateType = fsmController.getCurStateType();
		if (stateType != USFUnitStateType.USF_UNIT_STATE_GARRISON) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位尝试移动失败,不在据点中,unit={}", this.moveable);
			}
			return;
		}
		USFFortress curFortress = this.moveable.getCurFortress();
		if (curFortress == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位尝试移动失败,当前据点为空,unit={}", this.moveable);
			}
			return;
		}
		if (curFortress.getId() == this.targetFortress.getId()) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位尝试移动失败,目标据点指向当前据点,unit={},targetFortress={}", this.moveable,
					this.targetFortress.getId());
			}
			this.reset();
			return;
		}
		USFRoute route = curFortress.getRoute(this.targetFortress.getDictId());
		if (route == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位尝试移动失败,没有到达目标据点的航线,unit={},targetFortress={}", this.moveable, targetFortress);
			}
			this.reset();
			return;
		}
		boolean bl = route.checkCollide(this.moveable);
		if (bl) {
			return;
		}
		this.moveable.leaveFortress();
		bl = this.moveable.enterRoute(route);
		if (bl) {
			final long now = System.currentTimeMillis();
			curFortress.setLastUnitLeaveTime(now);
			int dis = curFortress.getCollisionSize() + USFUtils.getLeaveFortressCollisionSize();
			this.moveable.incrMoveDis(dis);
			this.moveable.getFsmController().switchState(USFUnitStateType.USF_UNIT_STATE_MOVING);
			this.lastCalcPosTime = now;
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战单位开始移动,unit={}", this.moveable);
			}
		} else {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位进入航线失败,回到出生点,unit={},route={}", this.moveable, route);
			}
			this.reset();
			this.moveable.enterFortress(this.moveable.getCamp().getBornFortress());
		}
	}

	/**
	 * 尝试继续移动
	 */
	public void tryMoveOn() {
		if (!this.moveable.canMove()) {
			return;
		}
		AbstractUSFUnitFsmController fsmController = this.moveable.getFsmController();
		USFUnitStateType stateType = fsmController.getCurStateType();
		if (stateType != USFUnitStateType.USF_UNIT_STATE_WAITING) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位尝试继续移动失败,不在等待状态,unit={}", this.moveable);
			}
			return;
		}
		boolean bl = this.route.checkCollideAndSwitch(this.moveable, 0);
		if (bl) {
			return;
		}
		fsmController.switchState(USFUnitStateType.USF_UNIT_STATE_MOVING);
		this.lastCalcPosTime = System.currentTimeMillis();
	}

	/**
	 * 切换到下一个目标据点 
	 */
	public void nextTargetFortress() {
		this.targetFortress = null;
		if (!this.pathFortresses.isEmpty()) {
			this.targetFortress = this.pathFortresses.poll();
			if (AMLog.LOG_UNION.isDebugEnabled()) {
				AMLog.LOG_UNION.debug("海战单位获取下一个目标据点,unit={},targetFortress={}", this.moveable, this.targetFortress);
			}
		}
	}

	public void incrMoveDis(int addMoveDis) {
		this.moveDis = this.moveDis + addMoveDis;
	}

	/**
	 * 获取航线进度(相对于尾部的)
	 * 
	 * @return
	 */
	public int getProgress() {
		if (this.route != null) {
			int progress = this.moveDis;
			if (this.targetFortress == this.route.getTailVertex().getFortress()) {
				progress = this.route.getLength() - this.moveDis;
				progress = (progress < 0 ? 0 : progress);
			}
			return progress;
		} else {
			USFFortress curFortres = this.moveable.getCurFortress();
			USFRoute route = curFortres.getRoute(this.targetFortress.getDictId());
			int progress = 0;
			if (this.targetFortress == route.getTailVertex().getFortress()) {
				progress = route.getLength();
			}
			return progress;
		}
	}

	/**
	 * 获取最终目标据点ID
	 * 
	 * @return
	 */
	public int getFinalTargetFortressUid() {
		if (!this.pathFortresses.isEmpty()) {
			return this.pathFortresses.getLast().getId();
		}
		if (this.targetFortress != null) {
			return this.targetFortress.getId();
		}
		return 0;
	}
	
	/**
	 * 获取路径据点Uid
	 * 
	 * @return
	 */
	public List<Integer> getPathFortressUids() {
		if (this.pathFortresses.isEmpty() && this.targetFortress == null) {
			return null;
		}
		ArrayList<Integer> paths = new ArrayList<>(this.pathFortresses.size() + 1);
		if (this.targetFortress != null) {
			paths.add(this.targetFortress.getId());
		}
		for (USFFortress fortress : this.pathFortresses) {
			paths.add(fortress.getId());
		}
		return paths;
	}

	/**
	 * 更新战船数据
	 * 
	 * @param newShipInfo
	 */
	public void updateShipInfo(USFShipInfo newShipInfo) {
		this.shipInfo = newShipInfo;
		this.collisionSize = USFUtils.floatToInt(this.shipInfo.getCollisionSize());
	}

	public int getShipId() {
		return this.shipInfo.getShipId();
	}

	public float getSpeed() {
		return this.shipInfo.getSpeed();
	}

	public USFShipInfo getShipInfo() {
		return shipInfo;
	}

	public int getCollisionSize() {
		return this.collisionSize;
	}

	public List<Integer> getSkillIds() {
		return this.shipInfo.getSkillIds();
	}

	public USFFortress getTargetFortress() {
		return targetFortress;
	}

	public IUSFRouteNode getNextNode() {
		return nextNode;
	}

	public void setNextNode(IUSFRouteNode nextNode) {
		this.nextNode = nextNode;
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("海战单位nextNode改变,unit={},nextNode={}", this.moveable, nextNode);
		}
	}

	public IUSFRouteNode getPrevNode() {
		return prevNode;
	}

	public void setPrevNode(IUSFRouteNode prevNode) {
		this.prevNode = prevNode;
		if (AMLog.LOG_UNION.isDebugEnabled()) {
			AMLog.LOG_UNION.debug("海战单位prevNode改变,unit={},prevNode={}", this.moveable, prevNode);
		}
	}

	public USFRoute getRoute() {
		return route;
	}

}
