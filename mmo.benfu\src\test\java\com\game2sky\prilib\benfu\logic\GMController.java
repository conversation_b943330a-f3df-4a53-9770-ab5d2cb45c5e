//package com.game2sky.prilib.benfu.logic.system.controller;
//
//import com.game2sky.prilib.benfu.logic.FriendTestControllerIO;
//import com.game2sky.prilib.benfu.logic.TestAsyncGMGruop;
//import com.game2sky.publib.Globals;
//import com.game2sky.publib.framework.web.base.BaseController;
//import com.game2sky.publib.socket.logic.human.AbstractHuman;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.servlet.ModelAndView;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//
///**
// * GM命令组填写数据库Controller
// *
// * <AUTHOR>
// * @version v0.1 2017年07月04日 11:13 wuyidi
// */
//@Controller
//public class GMController extends BaseController {
//	public static int THREAD_HUMANLIST_SIZE = 50;
//
//	@RequestMapping(path = "/testAddFriend")
//	public ModelAndView testAddFriend() {
//		Set<AbstractHuman> allHuman = Globals.getAllHuman();
//		List<List<AbstractHuman>> threadHumanGroup = new ArrayList<List<AbstractHuman>>();
//		int listSize = THREAD_HUMANLIST_SIZE;
//		List<AbstractHuman> currentList = null;
//		for (AbstractHuman human : allHuman) {
//			if (listSize == THREAD_HUMANLIST_SIZE) {
//				currentList = new ArrayList<AbstractHuman>();
//				threadHumanGroup.add(currentList);
//				listSize = 0;
//			}
//			currentList.add(human);
//			listSize++;
//		}
//		int threadCount = threadHumanGroup.size();
//		for (int i = 0; i < threadCount; i++) {
//			Globals.getAsyncService()
//				.createOperationAndExecuteAtOnce(new FriendTestControllerIO(threadHumanGroup.get(i)));
//		}
//		return returnString("Async friend job created.");
//	}
//
//	@RequestMapping(path = "/testGMGroup")
//	public ModelAndView testGMGroup() {
//		Set<AbstractHuman> allHuman = Globals.getAllHuman();
//		List<List<AbstractHuman>> threadHumanGroup = new ArrayList<List<AbstractHuman>>();
//		int listSize = THREAD_HUMANLIST_SIZE;
//		List<AbstractHuman> currentList = null;
//		for (AbstractHuman human : allHuman) {
//			if (listSize == THREAD_HUMANLIST_SIZE) {
//				currentList = new ArrayList<AbstractHuman>();
//				threadHumanGroup.add(currentList);
//				listSize = 0;
//			}
//			currentList.add(human);
//			listSize++;
//		}
//		int threadCount = threadHumanGroup.size();
//		for (int i = 0; i < threadCount; i++) {
//			Globals.getAsyncService().createOperationAndExecuteAtOnce(new TestAsyncGMGruop(threadHumanGroup.get(i)));
//		}
//		return returnString("Async GM group created.");
//	}
//}
