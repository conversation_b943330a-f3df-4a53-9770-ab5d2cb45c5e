package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateBeAttackedInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;

/**
 * TODO 据点被进攻中
 *
 * <AUTHOR>
 * @version v0.1 2018年6月17日 下午6:13:31  guojijun
 */
public class USFFortressFsmStateBeAttacked extends AbstractUSFUnitFsmState {

	private static final USFClientUnitStateBeAttackedInfo CLIENT_INFO = new USFClientUnitStateBeAttackedInfo();

	public USFFortressFsmStateBeAttacked(USFFortressFsmController controller) {
		super(USFUnitStateType.USF_UNIT_STATE_BE_ATTACKED, controller);
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		clientUnitStateInfo.setBeAttackedInfo(CLIENT_INFO);
	}

}
