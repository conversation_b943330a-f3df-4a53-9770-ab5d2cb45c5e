package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.game2sky.prilib.communication.game.fight.SCReplayData;
import com.game2sky.prilib.communication.game.integralArena.ArenaFightPlayerData;
import com.game2sky.prilib.communication.game.integralArena.PVPFightRecord;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerBattleResult;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFightRecord;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.dict.domain.DictUSFMap;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightHeroData;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.battle.fdefine.EnumDefine;
import com.game2sky.prilib.core.socket.logic.fight.base.AutoFight;
import com.game2sky.prilib.core.socket.logic.fight.base.RoomType;
import com.game2sky.prilib.core.socket.logic.fight.record.FightRecordType;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFight;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.Globals;
import com.game2sky.publib.constants.CommonSysCodeConstants;
import com.game2sky.publib.dict.domain.DictSysMessage;

/**
 * TODO 海战战斗基类
 *
 * <AUTHOR>
 * @version v0.1 2018年6月17日 下午2:19:21  guojijun
 */
public abstract class AbstractUSFFight
{

	protected final String fightPlayId;
	protected final IUSFUnitFightable leftFightable;
	protected final IUSFUnitFightable rightFightable;
	private long createTime;
	private AutoFight autoFight;
	private volatile USFFightRecord usfFightRecord;
	

	public AbstractUSFFight(IUSFUnitFightable leftFightable, IUSFUnitFightable rightFightable)
	{
		this.fightPlayId = UUID.randomUUID().toString();
		this.leftFightable = leftFightable;
		this.rightFightable = rightFightable;
	}

	public void start()
	{
		this.createTime = System.currentTimeMillis();
		if (leftFightable instanceof AbstractUSFUnit && rightFightable instanceof AbstractUSFUnit)
		{
			AbstractUSFUnit leftUnit = (AbstractUSFUnit) leftFightable;
			AbstractUSFUnit rightUnit = (AbstractUSFUnit) rightFightable;
			leftUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_BEGIN, leftUnit, rightUnit, this);
			rightUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_BEGIN, rightUnit, leftUnit, this);
		}
		this.leftFightable.onStartFight(this);
		this.rightFightable.onStartFight(this);
		this.leftFightable.getCamp().getUsFight().addFight(this);
		this.onStart();
	}

	protected abstract void onStart();

	/**
	 *  真实开始
	 * 
	 * @param now
	 * @return
	 */
	public boolean realStart(long now)
	{
		int delay = BaseService.getConfigValue(PriConfigKeyName.USF_FIGHT_DELAY);
		if (now < this.createTime + delay)
		{
			return false;
		}
		USFAsyncFightIo asyncFightIo = new USFAsyncFightIo(this);
		Globals.getAsyncService().createOperationAndExecuteAtOnce(asyncFightIo);
		return true;
	}

	/**
	 * 计算战斗
	 */
	void calcFight()
	{
		FFightSide left = this.leftFightable.getCurFFightSide(EnumDefine.FightCampEnum_Left);
		FFightSide right = this.rightFightable.getCurFFightSide(EnumDefine.FightCampEnum_Right);
		
		this.autoFight = new AutoFight();
		List<FFightSide> leftSides = Arrays.asList(left);
		List<FFightSide> rightSides = Arrays.asList(right);

		USFight usFight = this.leftFightable.getCamp().getUsFight();
		DictUSFMap dictUSFMap = DictUSFMap.getDictUSFMap(usFight.getMapId());
		int fightMapId = (dictUSFMap != null ? dictUSFMap.getFightMapId() : 6);

		usfFightRecord = presetUSFFightRecord();
		
		int res = this.autoFight.beginFight(leftSides, rightSides, RoomType.UNION_SEA_FIGHT, fightMapId);
		int winCamp = -1;
		if (res == 0)
		{
			winCamp = this.autoFight.setMaxRoundWinCamp();
//			int roundNum = this.autoFight.getComputeCore().engineData.roundNum;
//			int roundMax = this.autoFight.getComputeCore().engineData.roundMax;
//			// 战斗细节C：我们已知在竞技场中，若20回合后依然未分出胜负，则判定攻击一方失败，而在海战中无法区分谁主动攻击了谁。
//			// 所以当回合结束时依然无法分出胜负，海战的规则是哪一方剩余的总血量值，即HP多，则判定为胜利
//			if (roundNum >= roundMax)
//			{
//				double leftCampHp = this.autoFight.getComputeCore().getAlifeHpByCamp(EnumDefine.FightCampEnum_Left);
//				double rightCampHp = this.autoFight.getComputeCore().getAlifeHpByCamp(EnumDefine.FightCampEnum_Right);
//				// 都没有全部死亡
//				if (leftCampHp > 0 && rightCampHp > 0)
//				{
//					if (leftCampHp > rightCampHp)
//					{
//						winCamp = EnumDefine.FightCampEnum_Left;
//					} else if (leftCampHp < rightCampHp)
//					{
//						winCamp = EnumDefine.FightCampEnum_Right;
//					}
//					//设置阵容胜负放
//					this.autoFight.setRealWinCamp(winCamp);
//				}
//			}
		}

		USFFightSettleCallBack fightSettleCallBack = new USFFightSettleCallBack(this, winCamp);
		Globals
			.createAndExecuteSceneMessageCallBack(0, 0, fightSettleCallBack, this.leftFightable.getCamp().getScene());
	}

	/**
	 * 异常中断
	 */
	void interrupt()
	{
		this.leftFightable.onFightInterrupt(this);
		this.rightFightable.onFightInterrupt(this);
	}

	/**
	 * 结算
	 * 
	 * @param winCamp
	 */
	protected void settleCurrentFight(int winCamp)
	{
		IUSFUnitFightable winner;
		IUSFUnitFightable loser;
		if (winCamp == EnumDefine.FightCampEnum_Left)
		{
			winner = this.leftFightable;
			loser = this.rightFightable;
		} else
		{
			winner = this.rightFightable;
			loser = this.leftFightable;
		}
		boolean isLoserInFortress = ((USFPlayer)loser).getCurFortress() != null;
//		winner.onFightWin(this);
//		loser.onFightFailed(this);
		SendBattleResultChatMsg(winner, loser);
		settleFightEnd(winCamp, winner, loser);
		if (winner instanceof AbstractUSFUnit && loser instanceof AbstractUSFUnit)
		{
			AbstractUSFUnit winUnit = (AbstractUSFUnit) winner;
			USFPlayer loseUnit = (USFPlayer) loser;
			winUnit.triggerEvent(USFBuffTriggerType.KILL, winUnit, loseUnit);
			winUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_END, winUnit, loseUnit, this);
			if(!isLoserInFortress)
			{
				//TODO 由于福卡斯船只有在据点外被杀才起效果，因此此处暂时这么写，以后有需求，需要改动这块的代码！！！！！
				loseUnit.triggerEvent(USFBuffTriggerType.BE_KILLED, loseUnit, winUnit);				
			}
			loseUnit.triggerEvent(USFBuffTriggerType.ALL_FIGHT_END, loseUnit, winUnit, this);
		}

		if ((this.leftFightable instanceof USFPlayer) && (this.rightFightable instanceof USFPlayer))
		{
			SCUSFPlayerBattleResult scUSFPlayerBattleResult = new SCUSFPlayerBattleResult(winner.getId(), loser.getId());
			this.leftFightable.getCamp().getUsFight().broadcast(scUSFPlayerBattleResult);
		}
		USFight usFight = this.leftFightable.getCamp().getUsFight();
		// 添加战斗记录
		usFight.getFightReplayManager().addFightRecord(this);
	}

	protected abstract void settleFightEnd(int winCamp, IUSFUnitFightable winner, IUSFUnitFightable loser);

	public void SendBattleResultChatMsg(IUSFUnitFightable winner, IUSFUnitFightable loser)
	{
		String winText = String.format(GetCampColorFormat(winner.getCampType()), ((USFPlayer) winner).getDisplayInfo()
			.getNickName());
		String loseText = String.format(GetCampColorFormat(loser.getCampType()), ((USFPlayer) loser).getDisplayInfo()
			.getNickName());
		DictSysMessage dictSysMessage = DictSysMessage.cache.get(CommonSysCodeConstants.USF_BATTLE_MSG);
		String formatString = dictSysMessage != null ? dictSysMessage.getSysMsg() : "Text%s 战胜 %s";
		String content = String.format(formatString, winText, loseText);
		winner.getCamp().getUsFight().getBroadcastManager().PushSystemBattleChat(content);
	}

	private String GetCampColorFormat(USFCampType campType)
	{
		DictSysMessage dictSysMessage;
		switch (campType)
		{
			case USF_CAMP_BLUE:
				dictSysMessage = DictSysMessage.cache.get(CommonSysCodeConstants.USF_BLUE);
				return dictSysMessage != null ? dictSysMessage.getSysMsg() : "<#4dfff0>%s</color>";
			case USF_CAMP_RED:
				dictSysMessage = DictSysMessage.cache.get(CommonSysCodeConstants.USF_RED);
				return dictSysMessage != null ? dictSysMessage.getSysMsg() : "<#f43a3a>%s</color>";
			default:
				return "<#eda950>%s</color>";
		}
	}

	/**
	 * 获取对应单位的战斗数据
	 * 
	 * @param fightable
	 * @return
	 */
	public Map<Long, FFightHeroData> getHeroDatasByCamp(IUSFUnitFightable fightable)
	{
		int camp = (fightable == this.leftFightable ? EnumDefine.FightCampEnum_Left : EnumDefine.FightCampEnum_Right);
		return this.autoFight.getComputeCore().getRealHeroDatasByCamp(camp);
	}

	/**
	 * 获取战斗类型
	 * 
	 * @return
	 */
	public abstract USFFightTypeEnum geFightType();

	/**
	 * 获取相关联的据点
	 * 
	 * @return
	 */
	public abstract USFFortress getFortress();

	/**
	 * 获取战斗目标的uid
	 * 
	 * @param fightable
	 * @return
	 */
	public abstract int getTargetId(IUSFUnitFightable fightable);

	public String getFightPlayId()
	{
		return fightPlayId;
	}

	/**
	 * 构建战斗记录
	 * @return
	 */
	public PVPFightRecord creatPVPFightRecord()
	{
		PVPFightRecord record = new PVPFightRecord(FightRecordType.SEA_FIGHT.value(), autoFight.getFightId(),
			new ArenaFightPlayerData(), new ArenaFightPlayerData(), createTime);
		boolean leftWin = autoFight.getWinCamp() == EnumDefine.FightCampEnum_Left;
		record.getAttacker().setIsWin(leftWin);
		record.getDefender().setIsWin(!leftWin);
		fullRecordData(leftFightable, record.getAttacker());
		fullRecordData(rightFightable, record.getDefender());
		return record;
	}

	private void fullRecordData(IUSFUnitFightable fightable, ArenaFightPlayerData info)
	{
		info.setInformation(fightable.toPlayerDisplayInformation());
//		if (fightable instanceof USFPlayer)
//		{
//			USFPlayer usFPlayer = (USFPlayer) fightable;
//			info.setInformation(usFPlayer.getDisplayInfo());
//		} else
//		{
//			FFightSide curFFightSide = fightable.getCurFFightSide();
//			FFightRole fFightRole = curFFightSide.roles.get(0);
//			info.setInformation(fFightRole.toPlayerDisplayInformation());
//		}
	}

	/**
	 * 获取回放数据
	 * @return
	 */
	public SCReplayData getSCReplayData()
	{
		return autoFight.getScReplayData();
	}

	public IUSFUnitFightable getOtherSide(IUSFUnitFightable fightable)
	{
		if (fightable == this.leftFightable)
		{
			return this.rightFightable;
		} else if (fightable == this.rightFightable)
		{
			return this.leftFightable;
		}
		return null;
	}
	//预创建一个战斗记录
	private USFFightRecord presetUSFFightRecord() {
		USFFightRecord ret = new USFFightRecord();
		//初始化
		if(leftFightable instanceof USFPlayer){
			USFPlayer player = (USFPlayer) leftFightable;
			ret.setLeft(player.toUSFFightRecordPerson());
		}
		if(rightFightable instanceof USFPlayer){
			USFPlayer player = (USFPlayer) rightFightable;
			ret.setRight(player.toUSFFightRecordPerson());
		}
		ret.setTime(System.currentTimeMillis());
		return ret;
	}
	
	public USFFightRecord getUsfFightRecord() {
		return usfFightRecord;
	}

}
