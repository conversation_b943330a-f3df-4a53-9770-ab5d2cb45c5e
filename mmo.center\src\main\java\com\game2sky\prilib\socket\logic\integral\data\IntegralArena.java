package com.game2sky.prilib.socket.logic.integral.data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.game2sky.prilib.communication.game.integralArena.IntegralCenterAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentBase;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.communication.game.integralArena.SSRefreshGameIntegralBase;
import com.game2sky.prilib.communication.game.integralArena.SSRefreshGameIntegralRank;
import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.dict.domain.DictIntegralServer;
import com.game2sky.prilib.core.redis.RedisManager;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.redis.RedisDao;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterRedis;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnect;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnectManager;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.util.DateUtil;

/**
 * 天梯积分PVP竞技场
 * <AUTHOR>
 * @version v0.1 2017年5月16日 下午9:08:15  zhoufang
 */
public class IntegralArena {

	/** 分支 */
	private int branch;
	/** 赛季基本信息 */
	private IntegralCenterBase baseInfo = new IntegralCenterBase();
	/** 卡区信息 */
	private Map<Integer, IntegralArea> areas = new HashMap<Integer, IntegralArea>();
	/** 当前是否是新版本 */
	private boolean isNewVersion;

	public IntegralArena(int branch) {
		this.branch = branch;
	}

	/**
	 * 加载数据
	 * @param forciblyOpen  是否强制切换为新玩法
	 * @throws Exception
	 */
	public void load(boolean forciblyOpen) throws Exception {
		RedisDao dao = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
		String baseKey = IntegralCenterRedis.getBaseCommonRedisKey(branch);
		List<IntegralCenterBase> list = dao.getAllValues(baseKey, IntegralCenterBase.class);
		if (list == null || list.isEmpty()) {
			// 第一次开启天梯
			startNewWeek(false);
			checkVersion();
			AMLog.LOG_COMMON.info("Ladder loading branch data is completed, Branch = {} ,Season = {}, week = {}", branch, baseInfo.getSeason(),
				baseInfo.getWeek());
			return;
		}
		// The basic information of redis loaded ladder branches
		Collections.sort(list);
		this.baseInfo = list.get(0);
		if (forciblyOpen) {
			// Force entered new gameplay
			this.baseInfo.nextSeason(branch);
			this.isNewVersion = true;
			saveBaseInfo();
			IntegralCenterRedis.saveVersionCheckValue(branch);
			AMLog.LOG_COMMON.info("Ladder[{}]Start new gameplay", this.branch);
		} else {
			// Check time
			checkVersion();
			if (DateUtil.compareWeekWithNow(baseInfo.getWeekStartTime()) > 0) {
				AMLog.fatal("Ladder{}It was found in this launch that it had passed for a week,The start time began last week{}", branch, new Timestamp(baseInfo.getWeekStartTime()));
			}
		}
		// Initialization area information
		initArea();
		if (AMLog.LOG_COMMON.isInfoEnabled()) {
			AMLog.LOG_COMMON.info("Ladder loading branch data is completed, Branch = {} ,Season = {}, week = {}", branch, baseInfo.getSeason(),
				baseInfo.getWeek());
		}
	}

	private void initArea() {
		this.areas.clear();
		for (Entry<Integer, DictIntegralArenaArea> entry : IntegralConfigCenter.getAreaMap().entrySet()) {
//			if (IntegralAreaType.isErrorType(entry.getKey())) {
//				continue;
//			}
			IntegralArea area = new IntegralArea(branch, entry.getKey());
			this.areas.put(area.getAreaType(), area);
		}
	}

	public int getRankVersion(int areaType) {
		return areas.get(areaType).getRankVersion();
	}

	/**
	 * 玩家加入天梯赛
	 * @param players    
	 * @param isReconnect
	 */
	public void addPlayers(	int serverId, int displayServerId, List<IntegralCenterPlayerInfo> players,
							boolean isReconnect) {
		Set<Integer> refreshRankTypes = new HashSet<Integer>();
		if (players != null) {
			for (IntegralCenterPlayerInfo playerInfo : players) {
				for (IntegralCenterAreaPlayerInfo areaInfo : playerInfo.getAreaInfos()) {
					IntegralArea area = areas.get(areaInfo.getAreaType());
					boolean isChange = area.addPlayer(playerInfo.getPlayerId(), playerInfo.getServerId(),
						displayServerId, areaInfo.getIntegral(), areaInfo.getIntegralTime(),
						playerInfo.getDisplayInformation());
					if (isChange) {
						refreshRankTypes.add(areaInfo.getAreaType());
					}
				}
			}
		}
		if (isReconnect) {
			// 来自重连后本服发来的消息, 需要推送排行榜,基本信息
			refreshGameRankList(serverId, areas.keySet());

			refreshGameBaseCache(serverId, false, false);
			return;
		}
		// 非重连消息表示某个玩家加入了天梯,如果影响了排行榜,则推送刷新
		refreshGameRankList(serverId, refreshRankTypes);

	}

	/**
	 * 推送排行榜刷新至某个本服
	 * @param serverId   本服Id
	 * @param areaTypes  需要刷新的赛区类型
	 */
	public void refreshGameRankList(int serverId, Set<Integer> areaTypes) {
		if (areaTypes.isEmpty()) {
			return;
		}
		SSRefreshGameIntegralRank respRank = new SSRefreshGameIntegralRank();
		respRank.setRankList(new ArrayList<IntegralCurrentRank>());
		respRank.setGroupId(branch);
		for (Integer type : areaTypes) {
			IntegralCurrentRank rank = new IntegralCurrentRank();
			rank.setAreaType(type);
			rank.setRankList(new ArrayList<IntegralRankItem>());
			rank.getRankList().addAll(areas.get(type).getRankList());
			rank.setVersion(getRankVersion(type));
			respRank.getRankList().add(rank);
		}
		IntegralCommonService.centerToBenfu(respRank, 0L, serverId);
//		AMLog.LOG_COMMON.info("天梯推送排行榜刷新,serverId={}, branch:{}, result:{}", serverId, branch, result);

//		AMLog.LOG_INTERFACE.info("天梯推送排行榜刷新,serverId={},body={}",serverId, JsonUtils.O2S(respRank));
	}

	/**
	 * 推送基本信息刷新至某个本服
	 * @param serverId  本服Id
	 */
	public void refreshGameBaseCache(int serverId, boolean isWeekEnd, boolean forbicly) {
		SSRefreshGameIntegralBase respBase = new SSRefreshGameIntegralBase();
		IntegralCurrentBase baseProto = baseInfo.toProto();
		baseProto.setIsNewVersion(isNewVersion);
		respBase.setIsWeekEnd(isWeekEnd);
		respBase.setBaseInfo(baseProto);
		respBase.setGroupId(branch);
		respBase.setForcibly(forbicly);
		IntegralCommonService.centerToBenfu(respBase, 0L, serverId);
	}

	/**
	 * 开始新的一周
	 * @param forbiclyEnd 强制结算
	 */
	public void startNewWeek(boolean forbiclyEnd) {
		if (forbiclyEnd) {
			// 强制结算进入下一周
			this.baseInfo.nextSeason(branch);
			for (Entry<Integer, IntegralArea> entry : this.areas.entrySet()) {
				baseInfo.getLastSeasonRankCache().put(entry.getKey(), entry.getValue().getRankCache());
			}
			// 开始新的赛季
			initArea();
		} else {
			// 正常结算
			if (baseInfo.nextWeek(branch)) {
				// 记录本赛季排行榜
				baseInfo.getLastSeasonRankCache().clear();
				for (Entry<Integer, IntegralArea> entry : this.areas.entrySet()) {
					baseInfo.getLastSeasonRankCache().put(entry.getKey(), entry.getValue().getRankCache());
				}
				// 开始新的赛季
				initArea();
			} else {
				// 还是本赛季, 删除榜单重新排序
				for (IntegralArea area : this.areas.values()) {
					area.getRankList().clear();
				}
			}
		}
		saveBaseInfo();
		if (AMLog.LOG_COMMON.isInfoEnabled()) {
			AMLog.LOG_COMMON.info("天梯赛分支 = {}, 开始新的一周,当前赛季 = {}, 当前周 = {}", branch, baseInfo.getSeason(),
				baseInfo.getWeek());
		}
	}

	private void checkVersion() {
		if (!isNewVersion) {
			// 判断是否需要切换进入新版本
			DictIntegralServer dictIntegralServer = IntegralConfigCenter.getBranchMap().get(branch);
			if (dictIntegralServer.getNewVersionStartTime() != 0
				&& System.currentTimeMillis() >= dictIntegralServer.getNewVersionStartTime()) {
				this.isNewVersion = true;
				AMLog.LOG_COMMON.info("Ladder group [{}] into the new version, the opening time is[{}]", this.branch,
					dictIntegralServer.getNewVersionStartDate());
			} else {
				AMLog.LOG_COMMON.info("Ladder packet [{}] is currently the old version, the new version opening time is[{}]", this.branch,
					dictIntegralServer.getNewVersionStartDate());
			}
		}
	}

	public boolean isLastWeek() {
		int seasonWeek = IntegralConfigCenter.getSeasonWeekByBranch(branch);
		return baseInfo.getWeek() >= seasonWeek;
	}

	/**
	 * 保存基本信息
	 */
	private void saveBaseInfo() {
		try {
			RedisDao redis = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
			String baseRedisKey = IntegralCenterRedis.getBaseRedisKey(branch, baseInfo.getSeason(), baseInfo.getWeek());
			redis.valueSet(baseRedisKey, baseInfo);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("save IntegralArena ERROR with ARENAINFO = " + JsonUtils.O2S(this), e);
		}
	}

	public int getBranch() {
		return branch;
	}

	public Map<Integer, IntegralArea> getAreas() {
		return areas;
	}

	/**
	 * 周日0点重置
	 * @param forbiclyEnd 强制结算
	 */
	public void weekRestart(boolean forbiclyEnd) {
		AMLog.LOG_COMMON.info("天梯分支周末更新,branch=" + branch);
		startNewWeek(forbiclyEnd);
		// 通知连接本分支的所有游戏服刷新基本信息
		ServerConnectManager serverCenter = NetConnectCenter.getInstance().getServerCenter();
		Set<ServerConnect> connectList = new HashSet<ServerConnect>(serverCenter.getServerIdConnects().values());
		for (ServerConnect serverConnect : connectList) {
			ServerTypeEnum clientType = serverConnect.getClientType();
			if (clientType != ServerTypeEnum.BENFU) {
				continue;
			}
			if (serverConnect.getServerIds().isEmpty()) {
				AMLog.LOG_ERROR.error("serverIds is null... Flag=" + serverConnect.getClientFlag());
				continue;
			}
			// 此进程对应的所有分组
			Map<Integer, List<Integer>> conformBranchMap = conformBranchMap(serverConnect.getServerIds());
			List<Integer> serverList = conformBranchMap.get(branch);
			if (serverList == null) {
				continue;
			}
			refreshGameBaseCache(serverList.get(0), true, forbiclyEnd);
			AMLog.LOG_COMMON.info("通知本服[{}]去刷新分组{}的赛季信息,发送给服务器组{}", serverConnect.getClientFlag(), branch, serverList);
		}
	}

	Map<Integer, List<Integer>> conformBranchMap(List<Integer> serverIds) {
		// key:branch value:serverIdList
		Map<Integer, List<Integer>> branchServerMap = new HashMap<Integer, List<Integer>>();
		for (Integer serverId : serverIds) {
			int branch = IntegralConfigCenter.getIntegralBranchByServerId(serverId);
			List<Integer> list = branchServerMap.get(branch);
			if (list == null) {
				list = new ArrayList<Integer>();
				branchServerMap.put(branch, list);
			}
			list.add(serverId);
		}
		return branchServerMap;
	}

	public IntegralCenterBase getBaseInfo() {
		return baseInfo;
	}

	public boolean isNewVersion() {
		return isNewVersion;
	}

}
