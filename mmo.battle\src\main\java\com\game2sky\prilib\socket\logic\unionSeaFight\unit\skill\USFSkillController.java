package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import java.util.ArrayList;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFSkillType;
import com.game2sky.prilib.core.dict.domain.DictUsfSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFTickable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.IBattleTriggerEvent;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;


/**
 * 海战技能控制器
 *
 * <AUTHOR>
 * @version v0.1 2018年10月23日 上午2:42:15  lidong
 */
public class USFSkillController implements IUSFTickable, IUSFConsts, IBattleTriggerEvent
{
	private final AbstractUSFUnit owner;
	private List<USFSkill> skills;
	
	/** 受其他buff影响，当前单位释放的技能buff有额外加成值 ，主要是对buff的valueEffect值有影响*/
	private float extraValue = 0;
	
	/** 受其他buff影响，当前单位释放的技能buff有额外加成百分比 ，主要是对buff的valueEffect值有影响*/
	private float extraPercent = 0;

	public USFSkillController(AbstractUSFUnit owner)
	{
		this.owner = owner;
		skills = createSkills(owner.getSkillIds());
	}
	
	private List<USFSkill> createSkills(List<Integer> ids)
	{
		ArrayList<USFSkill> skills = new ArrayList<USFSkill>();
		List<Integer> tmpSkills = owner.getSkillIds();
		if (tmpSkills == null || tmpSkills.size() == 0) 
		{
			return null;
		}
		for (Integer id : tmpSkills) 
		{
			DictUsfSkill dictUsfSkill = DictUsfSkill.getDictUSFSkill(id);
			if (dictUsfSkill != null) {
				USFSkill usfSkill = SkillFactory.createSkillByType(USFSkillType.valueOf(dictUsfSkill.getSkillType()));
				usfSkill.setData(dictUsfSkill);
				usfSkill.setOwner(owner);
				skills.add(usfSkill);
			}
		}
		return skills;
	}

	/**
	 *TODO 据点升级完成以后老据点的buff撤销还没有做
	 * 准备完成，可以此时buff机制可以生效了，调用次方法。例如玩家创建完成，或者据点升级完成
	 */
	public void Ready()
	{
		if (skills != null && skills.size() != 0)
		{
			for (USFSkill skill : skills)
			{
				skill.triggerEvent(USFBuffTriggerType.FOREVER, owner, owner);
			}
		}
	}
	
	/** 为据点而写 */
	public List<USFClientBuffInfo> getShowBuffs()
	{
		if(skills == null)
		{
			return null;
		}
		ArrayList<USFClientBuffInfo> buffInfos = new ArrayList<>();
		for(USFSkill skill : skills)
		{
			List<USFBuff> usfBuffers = skill.getUsfBuffers();
			if(usfBuffers == null)
			{
				continue;
			}
			for (USFBuff buff : usfBuffers)
			{
				if (!buff.isClientSide())
				{
					buffInfos.add(buff.getClientInfo());
				}
			}
		}
		return buffInfos;
	}

	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit sourceUnit, AbstractUSFUnit targetUnit,
								Object... args)
	{
		if (skills != null && skills.size() != 0)
		{
			for (USFSkill skill : skills)
			{
				skill.triggerEvent(triggerType, sourceUnit, targetUnit,args);
			}
		}
	}
	
	
	/**
	 * 关闭所有技能
	 */
	public void closeAllSkill()
	{
		if (skills != null && skills.size() != 0)
		{
			for (USFSkill skill : skills)
			{
				skill.closeSkill();
			}
		}
	}
	
	public List<USFBuff> getAllBuffOfSkill()
	{
		List<USFBuff> buffs = new ArrayList<USFBuff>();
		if(skills != null)
		{
			for(USFSkill skill : skills)
			{
				List<USFBuff> skillBuffs = skill.getUsfBuffers();
				if(skillBuffs != null)
				{
					buffs.addAll(skillBuffs);
				}
			}
		}
		return buffs;
	}
	
	/** 替换所有技能 */
	public void replaceAllSkill(List<Integer> skillId)
	{
		closeAllSkill();
		skills = createSkills(skillId);
	}
	
	public void addSkill(USFSkill skill)
	{
		if(skills == null)
		{
			skills = new ArrayList<USFSkill>();
		}
		if(skill == null)
		{
			return;
		}
		skills.add(skill);
	}

	@Override
	public void tick(long now)
	{
		if (skills != null && skills.size() != 0)
		{
			for (USFSkill skill : skills)
			{
				if(skill.IsArriveDuration(now))
				{
					skill.doDuration();
				}
			}
		}
	}

	public AbstractUSFUnit getOwner()
	{
		return owner;
	}

	public List<USFSkill> getSkills()
	{
		return skills;
	}

	public void setSkills(List<USFSkill> skills)
	{
		this.skills = skills;
	}
	
	public void addExtraValue(int addType, float value) {
		if (addType == USF_BUFF_ADD_TYPE_FIXED) {
			extraValue += value;
		} else {
			extraPercent += value;
		}
	}
	
	public float getExtraValue() {
		return extraValue;
	}
	
	public float getExtraPercent() {
		return extraPercent;
	}

}
