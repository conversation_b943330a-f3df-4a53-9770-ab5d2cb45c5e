package com.game2sky.prilib.benfu.logic.entirerank;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerInfoChange;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankRes;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankSendSortData;
import com.game2sky.prilib.communication.game.entirerank.SSRefreshEntireRank;
import com.game2sky.prilib.core.socket.logic.entirerank.EntireRankBenfuManager;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 全服排行功能本服协议处理类.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月24日 下午5:53:44  Jet Lee
 */
@Controller
public class EntireRankBenfuController extends BaseController {

	@Autowired
	private EntireRankBenfuService service;
	
    /**
     * 本服排行榜tick
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankBenfuTick)
    public void ssEntireRankBenfuTick() {
    	EntireRankBenfuManager.getInstance().tick();
    }

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSRefreshEntireRank)
	public void ssRefreshEntireRank(SSRefreshEntireRank msg, Head head) {
		service.ssRefreshEntireRank(msg);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankPlayerRankRes)
	public void ssEntireRankPlayerRankRes(SSEntireRankPlayerRankRes msg, Head head) {
		service.ssEntireRankPlayerRankRes(msg);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankScoreRankRes)
	public void ssEntireRankScoreRankRes(SSEntireRankScoreRankRes msg, Head head) {
		service.ssEntireRankScoreRankRes(msg);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankSendSortData)
	public void ssEntireRankSendSortData(SSEntireRankSendSortData msg, Head head) {
		service.ssEntireRankSendSortData(msg);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSEntireRankPlayerInfoChange)
	public void ssEntireRankPlayerInfoChange(SSEntireRankPlayerInfoChange msg, Head head) {
		service.ssEntireRankPlayerInfoChange(msg, head);
	}
}
