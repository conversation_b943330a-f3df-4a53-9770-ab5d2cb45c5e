package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;

import com.game2sky.BattleCoreBoot;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnect;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnectManager;

import io.netty.channel.Channel;

/**
 * TODO 海战广播
 *
 * <AUTHOR>
 * @version v0.1 2018年8月23日 下午11:45:46  guojijun
 */
class USFBroadcast implements IUSFTickable
{

	private final int serverId;
	private final USFight usFight;
	private final HashSet<Long> playerIds;
	private Channel channel;

//	private ConcurrentLinkedQueue<APSHandler> receiveQueue = new ConcurrentLinkedQueue<APSHandler>();
//	private ConcurrentLinkedQueue<APSHandler> pushQueue = new ConcurrentLinkedQueue<APSHandler>();

//	private ReadWriteLock lock = new ReentrantReadWriteLock();

	USFBroadcast(int serverId, USFight usFight)
	{
		this.serverId = serverId;
		this.usFight = usFight;
		this.playerIds = new HashSet<>();
		this.bindChannel();
	}

	private void bindChannel()
	{
		ServerConnectManager serverCenter = NetConnectCenter.getInstance().getServerCenter();
		ServerConnect serverConnect = serverCenter.getServerConnect(serverId);
		if (serverConnect != null)
		{
			this.channel = serverConnect.randomChannel();
		} else
		{
			AMLog.LOG_UNION.warn("海战绑定benfu连接失败,serverId={}", this.serverId);
		}
	}

	@Override
	public void tick(long now)
	{
		if (channel == null || !channel.isActive())
		{
			if (this.playerIds.isEmpty())
			{
				return;
			}
			try
			{
//				for (long playerId : playerIds)
//				{
//					USFPlayer player = this.usFight.getPlayer(playerId);
//					if (player != null)
//					{
//						player.onQuit();
//					}
//				}
				AMLog.LOG_UNION.info("海战和本服的连接断开,玩家自动进入挂机状态,uqId={},serverId={},playerIds={}", this.usFight.getId(),
					this.serverId, playerIds);
				// 为啥要创建个临时集合,防止onPlayerQuit方法会做playerIds的删除操作
				ArrayList<Long> playerIdList = new ArrayList<>(this.playerIds);
				for (long playerId : playerIdList)
				{
					this.usFight.onPlayerQuit(serverId, playerId, "USFChannelDisconnect");
				}
			} finally
			{
				// 本服的处理是断开玩家连接并清理状态,所以这里要clear
				this.playerIds.clear();
			}
		}
//		if (receiveQueue.isEmpty())
//		{
//			return;
//		}
		
//		lock.writeLock().lock();
//		try
//		{
//			ConcurrentLinkedQueue<APSHandler> tmp = receiveQueue;
//			receiveQueue = pushQueue;
//			pushQueue = tmp;
//		} finally
//		{
//			lock.writeLock().unlock();
//		}
//		
//		while (!pushQueue.isEmpty())
//		{
//			BattleCoreBoot.pushToClients(channel, pushQueue.poll(), this.serverId, this.playerIds);
//		}

	}

	boolean addPlayer(long playerId)
	{
		boolean bl = this.playerIds.add(playerId);
		// 有玩家进来检测下连接
		if (channel == null || !channel.isActive())
		{
			this.bindChannel();
		}
		return bl;
	}

	boolean removePlayer(long playerId)
	{
		return this.playerIds.remove(playerId);
	}

	void pushMsg(APSHandler msg)
	{
		if (this.playerIds.isEmpty())
		{
			return;
		}
		BattleCoreBoot.pushToClients(channel, msg, this.serverId, this.playerIds);
//		lock.readLock().lock();
//        try
//        {
//        	receiveQueue.add(msg);
//        }
//        finally
//        {
//        	lock.readLock().unlock();
//        }
	}

	int getServerId()
	{
		return serverId;
	}

	Collection<Long> getPlayerIds()
	{
		return playerIds;
	}

	Channel getChannel()
	{
		return channel;
	}

}
