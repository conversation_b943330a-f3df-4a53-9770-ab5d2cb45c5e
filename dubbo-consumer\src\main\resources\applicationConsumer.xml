<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
http://code.alibabatech.com/schema/dubbo    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	<dubbo:application name="consumer-of-dubbo-demo" />

	<dubbo:registry address="zookeeper://127.0.0.1:2181" />

	<!-- 向注册中心订阅服务 -->
	<dubbo:reference id="sensitiveService" interface="mmo.sensitiveword.com.game2sky.inter.SensitiveWordService" />
	
</beans> 