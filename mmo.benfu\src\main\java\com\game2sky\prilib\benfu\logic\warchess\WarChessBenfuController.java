package com.game2sky.prilib.benfu.logic.warchess;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.game2sky.prilib.communication.game.warchess.*;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.WebApi;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 海域战旗本服协议处理.
 *
 * <AUTHOR>
 * @version v0.1 2019/7/16 14:15 wangby
 */
@Controller
public class WarChessBenfuController extends BaseController {

    @Autowired
    private WarChessBenfuService service;


    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessInfo)
    public void csWarChessInfo(CSWarChessInfo msg, Human human) {
        if (!SwitchConditionManager.checkState(PrivSwitchEnum.WAR_CHESS, human, PrivPSEnum.SCWarChessInfo)) {
            return;
        }

        service.csWarChessInfo(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessEnter)
    public void csWarChessEnter(CSWarChessEnter msg, Human human) {
        if (!SwitchConditionManager.checkState(PrivSwitchEnum.WAR_CHESS, human, PrivPSEnum.SCWarChessEnter)) {
            return;
        }

        service.csWarChessEnter(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessSkipChapter)
    public void csWarChessSkipChapter(CSWarChessSkipChapter msg, Human human) {
        service.csWarChessSkipChapter(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessQuit)
    public void csWarChessQuit(CSWarChessQuit msg, Human human) {
        service.csWarChessQuit(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessMoveChess)
    public void csWarChessMoveChess(CSWarChessMoveChess msg, Human human) {
        service.csWarChessMoveChess(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessOperateChess)
    public void csWarChessOperateChess(CSWarChessOperateChess msg, Human human) {
        service.csWarChessOperateChess(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessPlayEnd)
    public void csWarChessPlayEnd(CSWarChessPlayEnd msg, Human human) {
        service.csWarChessPlayEnd(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessHeroChange)
    public void csWarChessHeroChange(CSWarChessHeroChange msg, Human human) {
        if (!SwitchConditionManager.checkState(PrivSwitchEnum.WAR_CHESS, human, PrivPSEnum.SCWarChessHeroChange)) {
            return;
        }

        service.csWarChessHeroChange(msg, human);
    }

    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessSkipRound)
    public void csWarChessSkipRound(CSWarChessSkipRound msg, Human human) {
        service.csWarChessSkipRound(msg, human);
    }
    
    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessBuyHp)
    public void csWarChessBuyHp(CSWarChessBuyHp msg, Human human) {
        service.csWarChessBuyHp(msg, human);
    }
    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessGetFightHeroInfo)
    public void csWarChessGetFightHeroInfo(CSWarChessGetFightHeroInfo msg, Human human) {
    	service.CSWarChessGetFightHeroInfo(msg, human);
    }
    
    @ProtoStuffMapping(pvalue = PrivPSEnum.CSWarChessEnterGM)
    public void csWarChessEnterGM(CSWarChessEnterGM msg, Human human) {
    	service.csWarChessEnterGM(msg, human);
    }

    /**
     * 周重置GM
     *
     * @param request
     * @return
     */
    @RequestMapping(value = WebApi.BI_WAR_CHESS_BENFU_WEEK_RESET)
    public ModelAndView weekReset(HttpServletRequest request) {
        Map <String, String> paramsMap = getParamsMap(request);
        if (!paramsMap.containsKey("playerId")) {
            return returnString("缺少参数");
        }
        int playerId = Integer.valueOf(paramsMap.get("playerId"));

        return returnString("OK");
    }

}
