package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight;

import com.game2sky.publib.async.IIoOperation;

/**
 * TODO 海战异步战斗IO
 *
 * <AUTHOR>
 * @version v0.1 2018年8月27日 下午9:48:06  guojijun
 */
class USFAsyncFightIo implements IIoOperation {

	private final AbstractUSFFight fight;

	USFAsyncFightIo(AbstractUSFFight fight) {
		this.fight = fight;
	}

	@Override
	public int doStart() {
		return IIoOperation.STAGE_START_DONE;
	}

	@Override
	public int doIo() {
		this.fight.calcFight();
		return IIoOperation.STAGE_IO_DONE;
	}

	@Override
	public int doStop() {
		return IIoOperation.STAGE_STOP_DONE;
	}

}
