package com.game2sky.prilib.socket.logic.gvo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleReq;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO 大航海
 *
 * <AUTHOR>
 * @version v0.1 2020年10月20日 下午4:56:27  guojijun
 */
@Controller
public class GvoCenterController {
	@Autowired
	private GvoCenterService service;

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSGvoAllocBattleReq)
	public void ssGvoAllocBattleReq(SSGvoAllocBattleReq ssGvoAllocBattleReq) {
		service.ssGvoAllocBattleReq(ssGvoAllocBattleReq);
	}
}
