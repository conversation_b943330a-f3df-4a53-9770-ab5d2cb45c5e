package com.game2sky.prilib.socket.logic.autochess;

import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * 中心服自走棋启动.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月9日 下午3:31:47  <PERSON> Lee
 */
public class AutoChessCenterStart {

	private AutoChessCenterStart() {
	}

	public static void start() {

		// 消息处理器
		new AutoChessCenterMessageProcessor();
		AutoChessCenterManager.getInstance().init();

		// 启动定时器
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleAutoChessCenterTick(),
			ScheduleAutoChessCenterTick.PERIOD, ScheduleAutoChessCenterTick.PERIOD);

		if (AMLog.LOG_STDOUT.isInfoEnabled()) {
			AMLog.LOG_STDOUT.info("自走棋服务启动完毕!! ");
		}
	}
}