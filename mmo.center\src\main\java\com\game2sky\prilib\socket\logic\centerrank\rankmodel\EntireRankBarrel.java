package com.game2sky.prilib.socket.logic.centerrank.rankmodel;

import java.util.LinkedList;

import com.game2sky.prilib.socket.logic.integral.sort.Barrel;

/**
 * 通用全服排行榜积分桶.
 *
 * <AUTHOR>
 * @version v0.1 2020年2月27日 下午3:29:45  <PERSON>
 */
public class EntireRankBarrel extends Barrel {

	/** 同一积分的玩家Ids */
	private LinkedList<Long> playerIds = new LinkedList<Long>();

	public EntireRankBarrel(Long id) {
		this.id = id;
	}

	public void addPlayerId(Long playerId) {
		this.playerIds.addLast(playerId);
		this.haveNumber = playerIds.size();
	}

	public void removePlayerId(Long playerId) {
		this.playerIds.remove(playerId);
		this.haveNumber = playerIds.size();
	}

	public LinkedList<Long> getPlayerIds() {
		return playerIds;
	}

	/**
	 * 在链表的位置越靠前，排名越靠前
	 * @param playerId
	 * @return
	 */
	public int getRank(long playerId) {
		int indexOf = playerIds.indexOf(playerId);
		return indexOf < 0 ? playerIds.size() + 1 : indexOf + 1;
	}
}
