package com.game2sky.prilib.socket.logic.gvo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleReq;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleRes;
import com.game2sky.publib.socket.logic.server.battle.BattleServerInfo;
import com.game2sky.publib.socket.logic.server.battle.IServerSelector;

/**
 * TODO 大航海
 *
 * <AUTHOR>
 * @version v0.1 2020年10月20日 下午4:57:03  guojijun
 */
@Service
public class GvoCenterService {

	@Autowired
	private IServerSelector battleSelector;

	public void ssGvoAllocBattleReq(SSGvoAllocBattleReq ssGvoAllocBattleReq) {
		BattleServerInfo serverInfo = battleSelector.select(1);
		SSGvoAllocBattleRes ssGvoAllocBattleRes = new SSGvoAllocBattleRes(
			serverInfo != null ? serverInfo.toServerAddress() : null, 0);
		CoreBoot.dispatch2Server(ssGvoAllocBattleRes, ssGvoAllocBattleReq.getPlayerId(),
			ssGvoAllocBattleReq.getServerId(), null);
	}
}
