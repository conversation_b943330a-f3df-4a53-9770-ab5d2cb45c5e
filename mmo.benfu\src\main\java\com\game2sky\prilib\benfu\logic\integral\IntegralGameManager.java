package com.game2sky.prilib.benfu.logic.integral;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.springframework.web.servlet.ModelAndView;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentBase;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralPlayerChange;
import com.game2sky.prilib.communication.game.integralArena.SSBenfuIntegralPlayer;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.PrivConfigCenterEnum;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralManager;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.monitor.IntegralAreaMonitor;
import com.game2sky.prilib.core.socket.logic.monitor.IntegralMonitorModel;
import com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.spring.support.view.AView;
import com.game2sky.publib.framework.spring.support.view.FastJsonView;
import com.game2sky.publib.framework.web.base.Result;

/**
 * 天梯赛游戏服
 * <AUTHOR>
 * @version v0.1 2018年3月6日 下午2:31:53  zhoufang
 */
public class IntegralGameManager implements IntegralManager {

	public static IntegralGameManager instance = new IntegralGameManager();

	private IntegralGameManager() {
	}

	public static IntegralGameManager getInstance() {
		return instance;
	}

	/**
	 * 本进程下所有天梯分组<br>
	 * key: groupId  value:本进程在此分组下的服务器列表
	 * <b>进程启动后,数据不再变化,禁止修改</b><br>
	 * <b>动态刷新字典表无效,必须重启进程</b><br>
	 */
	private final Map<Integer, LinkedList<Integer>> groupServerIds = new HashMap<Integer, LinkedList<Integer>>();
	
	/** key:groupId value:分组数据 */
	public Map<Integer, IntegralGroup> groups = new HashMap<Integer, IntegralGroup>();

	/** 初始化本服天梯信息 */
	public void init() {
		// 先初始化服务器分组和赛区信息
		initGroupAndAreaInfo();
		// 从redis加载赛季和玩家信息
		IntegralGameRedis.load();
		// Determine whether to enter the new season gameplay
		directEnterNewVersion();
		// 开始
		start();
	}

	private void initGroupAndAreaInfo() {
		List<Integer> allServerId = CoreBoot.getAllServerId();
		Map<Integer, Integer> serverIdBranch = IntegralConfigCenter.getServerIdBranch();
		for (Integer serverId : allServerId) {
			Integer groupId = serverIdBranch.get(serverId);
			if (groupId == null) {
				continue;
			}
			LinkedList<Integer> serverList = this.groupServerIds.get(groupId);
			if (serverList == null) {
				serverList = new LinkedList<Integer>();
				this.groupServerIds.put(groupId, serverList);
			}
			serverList.add(serverId);
		}
		Set<Integer> groupIds = this.groupServerIds.keySet();
		for (Integer groupId : groupIds) {
			IntegralGroup group = new IntegralGroup(groupId);
			group.initArea();
			groups.put(groupId, group);
		}
	}

	private void directEnterNewVersion() {
		int forciblyOpen = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_FORCIBLY_NEW_VERSION);
		if (forciblyOpen == 0) {
			return;
		}
		// 练习赛初始积分
		int practice = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_OPEN_PINNACLE);
		// 巅峰赛最低下限
		int pinnacleLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_EXTEND_NOLIMIT_LOWLIMIT);
		try{
			for(Integer branch : this.groupServerIds.keySet()){
				if(alreadyInNewVersion(branch)){
					continue;
				}
				IntegralGroup group = this.groups.get(branch);
				List<IntegralGamePlayer> players = new ArrayList<>(group.getPlayers().values());
				for (IntegralGamePlayer player : players) {
					player.initAreaInfo(IntegralAreaType.PRACTICE.value());
					player.initAreaInfo(IntegralAreaType.PINNACLE.value());
					// Practice the competition points changes
					player.unsafeModifyIntegral(IntegralAreaType.PRACTICE.value(), practice);
					// The peak points change, the ranking needs to be maintained
					int nolimitIntegral = player.getIntegral(IntegralAreaType.NOLIMIT.value());
					long nolimitTime = player.getIntegralTime(IntegralAreaType.NOLIMIT.value());
//					int pinnacleIntegral = nolimitIntegral < pinnacleLimit ? pinnacleLimit : nolimitIntegral;
					int pinnacleIntegral = pinnacleLimit;
					player.unsafeModifyIntegral(IntegralAreaType.PINNACLE.value(), pinnacleIntegral);
					if (nolimitTime > 0) {
						player.unsafeModifyIntegralTime(IntegralAreaType.PINNACLE.value(), nolimitTime);
					}
					AMLog.LOG_COMMON.info("天梯转移--玩家[{}]的原无限制赛区积分为[{}],转移后巅峰赛区积分为[{}]", player.getPlayerId(),
						nolimitIntegral, pinnacleIntegral);
					// 保存到redis
					IntegralGameRedis.savePlayer(player);
				}
				// 保存到serverConfig
				saveBranchEnterNewVersion(branch);
			}
		}catch(Exception e){
			AMLog.LOG_ERROR.error("天梯进入新玩法时产生错误.", e);
			System.exit(1);
		}
	}
	
	public void saveBranchEnterNewVersion(int branch) {
		LinkedList<Integer> linkedList = this.groupServerIds.get(branch);
		Set<Integer> zoneIds = new java.util.HashSet<Integer>();
		String time = Long.toString(System.currentTimeMillis());
		for(Integer serverId : linkedList){
			int zoneId = Globals.getZoneId(serverId);
			if(!zoneIds.contains(zoneId)){
				// 保存到serverConfig
				Globals.updateStringValue(zoneId, PrivConfigCenterEnum.INTEGRAL_NEWVERSION_FORCLIBY.getConfigKey(),time);
				zoneIds.add(zoneId);
			}
		}
	}

	/**
	 * 某个天梯分组是否曾经进入过新玩法
	 * @param branch
	 * @return
	 */
	public boolean alreadyInNewVersion(int branch) {
		LinkedList<Integer> linkedList = this.groupServerIds.get(branch);
		int zoneId = Globals.getZoneId(linkedList.getFirst());
		long defaultValue = Globals.getLongValueByDefault(zoneId,
			PrivConfigCenterEnum.INTEGRAL_NEWVERSION_FORCLIBY.getConfigKey(), 0);
		return defaultValue > 0;
	}

	/**
	 * 天梯启动,数据已加载完成
	 */
	public void start() {
		for (Entry<Integer, IntegralGroup> entry : groups.entrySet()) {
			entry.getValue().start();
		}
	}

	/**
	 * 根据分组Id获取组信息
	 * @param groupId
	 * @return
	 */
	public IntegralGroup getGroupById(int groupId) {
		return this.groups.get(groupId);
	}

	/**
	 * 根据serverId获取组信息
	 * @param serverId
	 * @return
	 */
	public IntegralGroup getGroupByServerId(int serverId) {
		int groupId = IntegralConfigCenter.getIntegralBranchByServerId(serverId);
		return getGroupById(groupId);
	}

	public void changeGroupBaseCache(Integer groupId, IntegralBaseCache baseCache) {
		getGroupById(groupId).setBaseCache(baseCache);
	}

	/**
	 * 统计玩家基本信息的改变
	 * @param playerId
	 * @param list
	 */
	public void addPlayerInfoChange(long playerId, List<DisplayInformationType> list) {
		int serverId = getServerIdByPlayerId(playerId);
		IntegralGroup group = getGroupByServerId(serverId);
		for (DisplayInformationType type : list) {
			group.playerInfoChange(playerId, type.getType(), type.getValue());
		}
	}

	/**
	 * 获取本次要发送的玩家刷新信息
	 * @return
	 */
	public List<IntegralPlayerChange> getPlayerInfoChanges(int groupId) {
		IntegralGroup group = getGroupById(groupId);
		return group.getPlayerInfoChanges();
	}

	/**
	 * 将游戏服玩家数据推送到中心服
	 */
	public void sendPlayersToCenter() {
		// 将所有玩家信息推送给center,等待center的数据返回
		Map<Integer, List<IntegralGamePlayer>> serverPlayers = new HashMap<>();
		Map<Long, IntegralGamePlayer> allPlayers = new HashMap<Long, IntegralGamePlayer>();
		for (IntegralGroup group : groups.values()) {
			allPlayers.putAll(group.getPlayers());
		}
		for (Entry<Long, IntegralGamePlayer> entry : allPlayers.entrySet()) {
			List<IntegralGamePlayer> list = serverPlayers.get(entry.getValue().getServerId());
			if (list == null) {
				list = new ArrayList<IntegralGamePlayer>();
				serverPlayers.put(entry.getValue().getServerId(), list);
			}
			list.add(entry.getValue());
		}
		for (int serverId : CoreBoot.getAllServerId()) {
			SSBenfuIntegralPlayer info = new SSBenfuIntegralPlayer();
			info.setIsReConnect(true);
			info.setServerId(serverId);
			info.setPlayers(new ArrayList<IntegralCenterPlayerInfo>());
			int displayServerId = CoreBoot.getServerDisplayId(serverId);
			info.setDisplayServerId(displayServerId);
			List<IntegralGamePlayer> playerList = serverPlayers.get(serverId);
			if (playerList != null) {
				for (IntegralGamePlayer player : playerList) {
					info.getPlayers().add(player.toCenterProto());
				}
			}
			IntegralCommonService.benfuToCenter(info, 0, info.getServerId());
			AMLog.LOG_COMMON.info("游戏服serverId={},推送天梯玩家数量size={}", serverId, info.getPlayers().size());
		}
	}

	@Override
	public int getPlayerRank(int areaType, Long playerId, int serverId) {
		return getGroupByServerId(serverId).getPlayerRank(areaType, playerId);
	}

	/**
	 * 周重置
	 * @param groupId 分组Id
	 * @param weekStartTime 中心服传过来的周开始时间
	 * @param forbicly 是否强制重置
	 */
	public void weekReset(int groupId, long weekStartTime,boolean forbicly) {
		getGroupById(groupId).weekReset(weekStartTime,forbicly);
	}

	/**
	 * 刷新排行榜缓存
	 * @param rankList
	 */
	public void refreshRankCache(int groupId, List<IntegralCurrentRank> rankList) {
		getGroupById(groupId).refreshRankCache(rankList);
	}

	/**
	 * 刷新赛季信息缓存
	 * @param groupId
	 * @param baseInfo
	 */
	public void refreshBaseCache(int groupId, IntegralCurrentBase baseInfo) {
		getGroupById(groupId).refreshBaseCache(baseInfo);
	}

	public int getRankVersion(int groupId, int areaType) {
		Map<Integer, IntegralCurrentRank> rankCache = getGroupById(groupId).getRankCache();
		return rankCache.get(areaType).getVersion();
	}

	IntegralGamePlayer getPlayerInfo(long playerId) {
		int serverId = getServerIdByPlayerId(playerId);
		IntegralGroup group = getGroupByServerId(serverId);
		return group == null ? null : group.getPlayerInfo(playerId);
	}

	public IntegralBaseCache getBaseCache(int serverId) {
		return getGroupByServerId(serverId).getBaseCache();
	}

	@Override
	public boolean InRank(long playerId) {
		int serverId = getServerIdByPlayerId(playerId);
		IntegralGroup group = getGroupByServerId(serverId);
		if (group == null || group.getRankPlayers() == null || group.getRankPlayers().isEmpty()) {
			AMLog.LOG_COMMON.warn("check player in Integral Rank Error, serverId:{}, playerId:{}", serverId, playerId);
			return false;
		}
		return group.getRankPlayers().contains(playerId);
	}

	@Override
	public Integer getIntegral(long playerId, int areaType) {
		IntegralGamePlayer gamePlayer = getPlayerInfo(playerId);
		return gamePlayer == null ? null : gamePlayer.getIntegral(areaType);
	}

	public int changePlayerIntegral(int areaType, Long playerId, boolean isWin, int changeIntegral) {
		IntegralGamePlayer playerInfo = getPlayerInfo(playerId);
		if (playerInfo != null) {
			playerInfo.addIntegral(areaType, isWin, changeIntegral);
			return playerInfo.getIntegral(areaType);
		}
		return 0;
	}

	@Override
	public String repair(Map<Object, Object> params) {
//		// 补发新世界21-40积分奖励
//		int groupId = Integer.parseInt(params.get("groupId").toString());
//		IntegralGroup group = getGroupById(groupId);
//		if(group == null){
//			AMLog.LOG_ERROR.error("本服务器没有{}天梯分组",groupId);
//			return "no group="+groupId;
//		}
//		int success = 0;
//		// 需要发奖的玩家, 2个赛区的奖励配置
//		Map<Long, IntegralGamePlayer> players = group.getPlayers();
//		List<com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig> nolimitConfigList = IntegralConfigCenter
//			.getIntegralRewardConfig(com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst.AREA_NOLIMIT);
//		List<com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig> amendConfigList = IntegralConfigCenter
//			.getIntegralRewardConfig(com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst.AREA_AMEND);
//
//		AMLog.LOG_COMMON.info("分组[{}]开始发放天梯奖励", groupId);
//		com.game2sky.publib.socket.logic.awardSend.IAwardSendService awardSendService = CoreBoot
//			.getBean(com.game2sky.publib.socket.logic.awardSend.IAwardSendService.class);
//		com.game2sky.prilib.core.socket.logic.title.TitleService titleService = CoreBoot
//			.getBean(com.game2sky.prilib.core.socket.logic.title.TitleService.class);
//		List<IntegralGamePlayer> playerList = new ArrayList<>(players.values());
//		for (IntegralGamePlayer player : playerList) {
//			com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer.IntegralAreaInfo nolimitAreaInfo = player.getAreaInfos().get(com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst.AREA_NOLIMIT);
//			com.game2sky.prilib.benfu.logic.integral.IntegralGamePlayer.IntegralAreaInfo amendAreaInfo = player.getAreaInfos().get(com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst.AREA_AMEND);
//			int nolimitIntegral = nolimitAreaInfo == null ? 0 : nolimitAreaInfo.getIntegral();
//			int amendIntegral = amendAreaInfo == null ? 0 : amendAreaInfo.getIntegral();
//
//			// 玩家在无限制赛区可获取的奖励配置
//			com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig config_Nolimit = null;
//			for (com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig config : nolimitConfigList) {
//				if (nolimitIntegral >= config.before && nolimitIntegral <= config.after) {
//					config_Nolimit = config;
//					break;
//				}
//			}
//			// 玩家在修正赛区可获取的奖励配置
//			com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig config_Amend = null;
//			for (com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig config : amendConfigList) {
//				if (amendIntegral >= config.before && amendIntegral <= config.after) {
//					config_Amend = config;
//					break;
//				}
//			}
//			if (config_Nolimit == null && config_Amend == null) {
//				AMLog.LOG_COMMON.info("天梯积分奖励----->玩家{}无法发放积分奖励,无限制赛区本周积分{}...修正赛区本周积分{}", player.getPlayerId(),
//					nolimitIntegral, amendIntegral);
//				continue;
//			}
//			// 判断2份奖励,取得可以得到的那份奖励
//			boolean isNolimit = false;
//			if (config_Nolimit != null) {
//				if (config_Amend == null) {
//					isNolimit = true;
//				} else {
//					int value_Nolimit = config_Nolimit.getIntegralValue();
//					int value_Amend = config_Amend.getIntegralValue();
//					isNolimit = value_Nolimit >= value_Amend;
//				}
//			}
//			int integral = isNolimit ? nolimitIntegral : amendIntegral;
//			com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig realConfig = isNolimit ? config_Nolimit : config_Amend;
//			int zoneId = Globals.getZoneId(player.getServerId());
//			awardSendService.addAward(com.game2sky.prilib.core.socket.logic.awardSend.AwardSendType.INTEGRAL_AWARD, zoneId, player.getPlayerId(), realConfig.items,
//				new Object[] {}, new Object[] { integral });
//			if (realConfig.title != null) {
//				titleService.addTitle(zoneId, player.getPlayerId(), Integer.parseInt(realConfig.title));
//			}
//			AMLog.LOG_COMMON.info("天梯积分奖励----->玩家{}的奖励发送成功,无限制赛区本周积分{}...修正赛区本周积分{}", player.getPlayerId(),
//				nolimitIntegral, amendIntegral);
//			success ++;
//		}

//		return Integer.toString(success);
		return null;
	}

	@Override
	public ModelAndView queryIntegeral(String[] playerIds, int serverId, int areaType) {
		List<QueryIntegralResult> list = new ArrayList<QueryIntegralResult>();
		for (String id : playerIds) {
			QueryIntegralResult query = new QueryIntegralResult();
			Long playerId = Long.parseLong(id);
			int zoneId = Globals.getZoneId(serverId);
			query.setPlayerId(playerId);
			Integer integral = getIntegral(playerId, areaType);
			query.setIntegral(integral == null ? -9999999 : integral);
			Human human = Globals.getRealHuman(zoneId, playerId);
			if (human != null) {
				query.setName(human.getName());
				query.setFight(human.getFightPower());
				IntegralMonitorModel intergalMonitor = human.getMonitorManager().getIntergalMonitor();
				IntegralAreaMonitor areaMonitor = intergalMonitor.getAreaMonitors().get(areaType);
				if (areaMonitor != null) {
					query.setRiskLevel(areaMonitor.getIntegralRiskLevel());
					query.setBannedEndTime(areaMonitor.getIntegralBannedEndTime());
				}
			} else {
				PlayerDisplayInformation info = Globals.getPlayerDisplayInformation(playerId, zoneId);
				if (info != null) {
					query.setName(info.getNickName());
					query.setFight(info.getFightPower());
				} else {
					query.setName("Error PlayerId!!!!! No this player in server");
				}
				IntegralMonitorModel intergalMonitor = PlayerMonitorManager.loadFromDb(playerId, zoneId);
				if (intergalMonitor != null) {
					IntegralAreaMonitor areaMonitor = intergalMonitor.getAreaMonitors().get(areaType);
					if (areaMonitor != null) {
						query.setRiskLevel(areaMonitor.getIntegralRiskLevel());
						query.setBannedEndTime(areaMonitor.getIntegralBannedEndTime());
					}
				}
			}
			list.add(query);
		}
		Result result = new Result();
		result.setData(list);
		return new ModelAndView(new FastJsonView(), AView.RESULT, result);
	}

	public Map<Integer, LinkedList<Integer>> getGroupServerIds() {
		return groupServerIds;
	}

	public Map<Integer, IntegralGroup> getGroups() {
		return groups;
	}

	public int getAllPlayerSize() {
		int sum = 0;
		for (Entry<Integer, IntegralGroup> entry : groups.entrySet()) {
			sum += entry.getValue().getPlayers().size();
		}
		return sum;
	}

	public int getServerIdByPlayerId(long playerId) {
		String str = Long.toString(playerId);
		int subServerId = Integer.parseInt(str.substring(1, 5));
		return subServerId;
	}

	@Override
	public int getSeasonFightCount(long playerId, int serverId, int areaType) {
		IntegralGroup group = getGroupByServerId(serverId);
		IntegralGamePlayer gamePlayer = group.getPlayers().get(playerId);
		return gamePlayer == null ? 0 : gamePlayer.getSeasonFightCount(areaType);
	}

	@Override
	public boolean isNewVersion(int groupId) {
		IntegralGroup integralGroup = this.groups.get(groupId);
		return integralGroup.isNewVersion();
	}

}
