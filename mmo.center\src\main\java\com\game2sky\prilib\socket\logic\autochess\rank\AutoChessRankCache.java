package com.game2sky.prilib.socket.logic.autochess.rank;

import java.util.List;

import com.game2sky.prilib.communication.game.autochess.AutoChessRankPlayerInfo;

/**
 * 自走棋排行榜数据.
 *
 * <AUTHOR>
 * @version v0.1 2019年6月12日 下午4:23:41  <PERSON> Lee
 */
public class AutoChessRankCache {
	
	private List<AutoChessRankPlayerInfo> rankList = null;
	
	public List<AutoChessRankPlayerInfo> getRankList() {
		return rankList;
	}
	
	public void setRankList(List<AutoChessRankPlayerInfo> rankList) {
		this.rankList = rankList;
	}

}
