package com.game2sky.prilib.socket.logic.homeland;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.homeland.inner.SSRequestCanRobList;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncEnemyInfoFromCenter;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncHomeLandInfoToCenter;
import com.game2sky.prilib.communication.game.homeland.inner.SSSyncHomeLandInfoToCenterWhenConnect;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年4月2日 下午5:45:44  daixl
 */

@Controller
public class CenterHomelandController {

	@Autowired
	CenterHomelandService service;

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncHomeLandInfoToCenter)
	public void syncHomeLandInfoToCenter(SSSyncHomeLandInfoToCenter msg) {
		service.syncHomeLandInfoToCenter(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSRequestCanRobList)
	public void requestCanRobList(SSRequestCanRobList msg) {
		service.requestCanRobList(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncEnemyInfoFromCenter)
	public void syncEnemyInfoFromCenter(SSSyncEnemyInfoFromCenter msg) {
		service.syncEnemyInfoFromCenter(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSSyncHomeLandInfoToCenterWhenConnect)
	public void syncHomeLandInfoToCenterWhenConnect(SSSyncHomeLandInfoToCenterWhenConnect msg) {
		service.syncHomeLandInfoToCenterWhenConnect(msg);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSCleanHomeBarrel)
	public void cleanHomeBarrel() {
		service.cleanHomeBarrel();
	}
}
