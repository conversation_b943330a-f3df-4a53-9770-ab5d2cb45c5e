package com.game2sky.prilib.socket.logic.unionSeaFight;

import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.springframework.stereotype.Service;

import com.game2sky.BattleCoreBoot;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.integralArena.PVPFightRecord;
import com.game2sky.prilib.communication.game.integralArena.SCPlayerFightRecord;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUFRequestForCombatLogInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFFortressPromotion;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFGetFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFPlayerBot;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFPlayerMove;
import com.game2sky.prilib.communication.game.unionSeaFight.CSUSFSwitchPlayerMicState;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUFRequestForCombatLogInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFGetFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerBot;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerEnter;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerMove;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFSwitchPlayerMicState;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCreateReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFCreateRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterReq;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterRes;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerQuit;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientCampFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientCampInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientRouteInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFightRecord;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFShipInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictUSFPromotion;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.union.service.AbstractUnionSeaFightService;
import com.game2sky.prilib.socket.logic.common.BattleCommonUtils;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.replay.USFFightReplayManager;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayerEnterCallBack;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayerQuitCallBack;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.chat.inner.SSPushChannelChatToKuafu;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.scene.base.AbstractScene;

/**
 * TODO 工会海战
 *
 * <AUTHOR>
 * @version v0.1 2018年6月12日 下午3:42:13  guojijun
 */
@Service
public class USFightBattleService extends AbstractUnionSeaFightService implements IUSFTickable {

	private final ConcurrentHashMap<Long, USFight> usFightCache = new ConcurrentHashMap<>();
	/**工会和海战的对应关系*/
	private final ConcurrentHashMap<Long, Long> unionToFightCache = new ConcurrentHashMap<>();
	/**用于开始之前的tick*/
	private final CopyOnWriteArrayList<USFight> tickCache = new CopyOnWriteArrayList<>();

	void removeFromCache(long fightId) {
		USFight usFight = this.usFightCache.remove(fightId);
		if (usFight != null) {
			AMLog.LOG_UNION.info("海战从usFightCache中移除usFight,usFight={}", usFight);
			this.removeFromTickCache(usFight);
			List<USFCamp> camps = usFight.getCamps();
			for (USFCamp camp : camps) {
				if (this.unionToFightCache.containsKey(camp.getUnionId())) {
					this.unionToFightCache.remove(camp.getUnionId());
					AMLog.LOG_UNION.info("海战解除绑定关系,uqId={},unionId={}", fightId, camp.getUnionId());
				}
			}
		}
	}

	void removeFromTickCache(USFight usFight) {
		boolean bl = this.tickCache.remove(usFight);
		if (bl) {
			AMLog.LOG_UNION.info("海战从tickCache中移除usFight,usFight={}", usFight);
		}
	}

	@Override
	public void tick(long now) {
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			AMLog.LOG_UNION.info("Current number of naval warfare, totalCount={},unopenedCount={}", this.usFightCache.size(),
				this.tickCache.size());
		}
		if (this.tickCache.isEmpty()) {
			return;
		}
		for (USFight usFight : this.tickCache) {
			usFight.tick(now);
		}
	}

	/**
	 * 创建海战(由本服发起)
	 * 
	 * @param ssUSFCreateReq
	 */
	public void createFight(SSUSFCreateReq ssUSFCreateReq) {
		final long uqId = ssUSFCreateReq.getUqId();

		USFUnionInfo unionInfo = ssUSFCreateReq.getUnionInfo();

		SSUSFCreateRes ssUSFCreateRes = new SSUSFCreateRes(uqId, false, unionInfo.getServerId(), unionInfo.getUnionId());
		ssUSFCreateRes.setServerId(unionInfo.getServerId());
		ssUSFCreateRes.setUnionId(unionInfo.getUnionId());
		ssUSFCreateRes.setUqId(uqId);
		if (this.unionToFightCache.containsKey(unionInfo.getUnionId())) {
			AMLog.LOG_UNION.warn("该公会已经开启了海战,uqId={},unionId={}", uqId, unionInfo.getUnionId());
			ssUSFCreateRes.setSucc(true);
			BattleCoreBoot.pushToBenfu(ssUSFCreateReq.getChannel(), ssUSFCreateRes, unionInfo.getServerId());
			return;
		}
		USFight usFight = usFightCache.get(uqId);
		if (usFight == null) {
			usFight = new USFight(uqId, ssUSFCreateReq.getMapId());
			usFight.setPrepareTime(ssUSFCreateReq.getPrepareTime());
			usFight.setRunningTime(ssUSFCreateReq.getRunningTime());
			boolean succ = usFight.init();
			if (!succ) {
				AMLog.LOG_UNION.warn("海战初始化失败,uqId={},unionId={}", uqId, unionInfo.getUnionId());
				ssUSFCreateRes.setSucc(false);
				BattleCoreBoot.pushToBenfu(ssUSFCreateReq.getChannel(), ssUSFCreateRes, unionInfo.getServerId());
				return;
			}
			usFight.setService(this);
			this.usFightCache.put(uqId, usFight);
			this.tickCache.add(usFight);
			AMLog.LOG_UNION.info("创建USFight并初始化,uqId={},mapId={}", uqId, ssUSFCreateReq.getMapId());
		}
		boolean succ = usFight.syncCampInfo(unionInfo);
		if (succ) {
			this.unionToFightCache.put(unionInfo.getUnionId(), uqId);
			AMLog.LOG_UNION.info("海战建立绑定关系,uqId={},unionId={}", uqId, unionInfo.getUnionId());
		}
		ssUSFCreateRes.setSucc(succ);
		BattleCoreBoot.pushToBenfu(ssUSFCreateReq.getChannel(), ssUSFCreateRes, unionInfo.getServerId());
		usFight.start(ssUSFCreateReq.getChannel());
		
	}

	private void sendPlayerEnterRes(Channel channel, int serverId, long playerId, int errorCode) {
		SSUSFPlayerEnterRes ssUSFPlayerEnterRes = new SSUSFPlayerEnterRes();
		ssUSFPlayerEnterRes.setErrorCode(errorCode);
		BattleCoreBoot.pushToBenfu(channel, ssUSFPlayerEnterRes, serverId, playerId);
	}

	/**
	 * 玩家进入海战(在海战世界线程执行)
	 * 
	 * @param ssUSFPlayerEnterReq
	 */
	public void playerEnter(SSUSFPlayerEnterReq ssUSFPlayerEnterReq) {
		final Channel channel = ssUSFPlayerEnterReq.getChannel();
		final int serverId = ssUSFPlayerEnterReq.getServerId();
		final long playerId = ssUSFPlayerEnterReq.getPlayerId();

		Long uqId = this.unionToFightCache.get(ssUSFPlayerEnterReq.getUnionId());
		if (uqId == null) {
			AMLog.LOG_COMMON.info("玩家进入海战 失败， 找不到uqId， playerId:{}, unionId:{}", playerId,
				ssUSFPlayerEnterReq.getUnionId());
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_NOT_EXISTS);
			return;
		}
		USFight usFight = this.usFightCache.get(uqId);
		if (usFight == null) {
			AMLog.LOG_COMMON.info("玩家进入海战 失败，USFight=null， playerId:{}, unionId:{}, uqId={}", playerId,
				ssUSFPlayerEnterReq.getUnionId(), uqId);
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_NOT_EXISTS);
			return;
		}
		USFStateType stateType = usFight.getFsmController().getCurStateType();
		if (stateType == USFStateType.USF_STATE_NONE) {
			AMLog.LOG_COMMON.info("玩家进入海战 失败，stateType=USF_STATE_NONE， playerId:{}, unionId:{}, uqId={}", playerId,
				ssUSFPlayerEnterReq.getUnionId(), uqId);
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_NOT_OPEN);
			return;
		} else if (stateType == USFStateType.USF_STATE_CLOSE) {
			AMLog.LOG_COMMON.info("玩家进入海战 失败，stateType=USF_STATE_CLOSE， playerId:{}, unionId:{}, uqId={}", playerId,
				ssUSFPlayerEnterReq.getUnionId(), uqId);
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_CLOSED);
			return;
		}
		USFPlayerEnterCallBack callBack = new USFPlayerEnterCallBack();
		callBack.setService(this);
		callBack.setSsUSFPlayerEnterReq(ssUSFPlayerEnterReq);
		callBack.setUqId(uqId);
		Globals.createAndExecuteSceneMessageCallBack(serverId, playerId, callBack, usFight.getScene());
	}

	/**
	 * 玩家进入海战(在场景线程执行)
	 * 
	 * @param uqId
	 */
	public void playerEnter(long uqId, SSUSFPlayerEnterReq ssUSFPlayerEnterReq) {
		final Channel channel = ssUSFPlayerEnterReq.getChannel();
		final int serverId = ssUSFPlayerEnterReq.getServerId();
		final long playerId = ssUSFPlayerEnterReq.getPlayerId();

		USFight usFight = this.usFightCache.get(uqId);
		if (usFight == null) {
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_CLOSED);
			return;
		}
		USFStateType stateType = usFight.getFsmController().getCurStateType();
		if (stateType == USFStateType.USF_STATE_CLOSE) {
			this.sendPlayerEnterRes(channel, serverId, playerId, ErrorCodeConstants.USF_CLOSED);
			return;
		}

		USFClientInfo clientInfo = new USFClientInfo();
		clientInfo.setFightId(usFight.getId());
		clientInfo.setMapId(usFight.getMapId());
		USFClientStateInfo stateInfo = usFight.getFsmController().copyTo();
		clientInfo.setStateInfo(stateInfo);
		clientInfo.setIsViewer(false);

		List<USFRoute> routes = usFight.getRoutes();
		ArrayList<USFClientRouteInfo> routeInfos = new ArrayList<>(routes.size());
		clientInfo.setRouteInfos(routeInfos);
		for (USFRoute route : routes) {
			routeInfos.add(route.copyTo());
		}

		int unitId = 0;
		USFPlayer player = usFight.getPlayer(playerId);
		if (player != null) {
			unitId = player.getId();
			player.onEnter();
			clientInfo.setMyInfo(player.copyToMy());
		} else {
			// 没有说明是观战玩家
			clientInfo.setIsViewer(true);
			USFCamp myCamp = usFight.getCampByUnionId(ssUSFPlayerEnterReq.getUnionId());
			if (myCamp != null) {
				myCamp.addViewer(playerId);
			}
		}

		List<USFCamp> camps = usFight.getCamps();
		ArrayList<USFClientCampInfo> campInfos = new ArrayList<>(camps.size());
		clientInfo.setCampInfos(campInfos);
		LinkedList<USFClientUnitInfo> unitInfos = new LinkedList<>();
		clientInfo.setUnitInfos(unitInfos);
		for (USFCamp camp : camps) {
			if (camp.getUnionId() == ssUSFPlayerEnterReq.getUnionId()) {
				clientInfo.setMicRoomId(camp.getMicRoomId());
				clientInfo.setSelfCampType(camp.getCampType());
			}
			if (camp.getUnionId() > 0) {
				campInfos.add(camp.copyTo());
			}
			for (IUSFUnit unit : camp.getUnits()) {
				if (unitId == unit.getId()) {
					continue;
				}
				unitInfos.add(unit.copyTo());
			}
		}
		usFight.onPlayerEnter(serverId, playerId);

		Human human = BattleCommonUtils.createAndRegHuman(serverId, playerId, "USFPlayerEnter");
		human.setChannel(channel);
		usFight.getScene().addHuman(human);

		SCUSFPlayerEnter scUSFPlayerEnter = new SCUSFPlayerEnter(clientInfo);
		BattleCoreBoot.pushToBenfu(channel, scUSFPlayerEnter, serverId, playerId);
		this.sendPlayerEnterRes(channel, serverId, playerId, 0);
	}


	/**
	 * 玩家退出海战(在海战世界线程执行)
	 * 
	 * @param ssUSFPlayerQuit
	 */
	public void playerQuit(SSUSFPlayerQuit ssUSFPlayerQuit) {
		Long uqId = this.unionToFightCache.get(ssUSFPlayerQuit.getUnionId());
		if (uqId == null) {
			return;
		}
		USFight usFight = this.usFightCache.get(uqId);
		if (usFight == null) {
			return;
		}
		USFStateType stateType = usFight.getFsmController().getCurStateType();
		if (stateType == USFStateType.USF_STATE_CLOSE || stateType == USFStateType.USF_STATE_NONE) {
			return;
		}
		USFPlayerQuitCallBack callBack = new USFPlayerQuitCallBack();
		callBack.setService(this);
		callBack.setSsUSFPlayerQuit(ssUSFPlayerQuit);
		callBack.setUqId(uqId);
		Globals.createAndExecuteSceneMessageCallBack(ssUSFPlayerQuit.getServerId(), ssUSFPlayerQuit.getPlayerId(),
			callBack, usFight.getScene());
	}

	/**
	 * 玩家退出海战(在场景线程执行)
	 * 
	 * @param uqId
	 * @param ssUSFPlayerQuit
	 */
	public void playerQuit(long uqId, SSUSFPlayerQuit ssUSFPlayerQuit) {
		final int serverId = ssUSFPlayerQuit.getServerId();
		final long playerId = ssUSFPlayerQuit.getPlayerId();

		USFight usFight = this.usFightCache.get(uqId);
		if (usFight != null) {
			usFight.onPlayerQuit(serverId, playerId, "SSUSFPlayerQuit");
		}
	}

	/**
	 * 玩家发起移动
	 * 
	 * @param human
	 */
	public void playerMove(Human human, CSUSFPlayerMove csUSFPlayerMove) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		USFPlayer player = usFight.getPlayer(human.getPlayerId());
		if (player == null) {
			BattleCoreBoot.pushErrorMsgToClient(csUSFPlayerMove.getChannel(), human.getServerId(), human.getPlayerId(),
				PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_INVALID_OPERATION);
			return;
		}
		if (usFight.isRunning()) {
			if(csUSFPlayerMove.getIsDive() != 0)
			{
				if(!couldDive(player,usFight,csUSFPlayerMove))
				{
					return;
				}
			}
			int code = player.readyToMove(csUSFPlayerMove.getTargetFortressId());
			if (code == 0) {

				SCUSFPlayerMove playerMove = new SCUSFPlayerMove();
				player.pushMsg(playerMove);
				player.onManual(false);
				if(csUSFPlayerMove.getIsDive() != 0)
				{
					player.setDiveState(1);
					player.addDiveNumber(-1);
				}
			} else {
				player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, code);
			}
		} else {
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_STATE_CAN_NOT_OPERATE);
		}
	}

	/**
	 * 检测是否可以潜行
	 * @param player
	 * @param usFight
	 * @param csUSFPlayerMove
	 * @return
	 */
	public boolean couldDive(USFPlayer player,USFight usFight, CSUSFPlayerMove csUSFPlayerMove)
	{
		if(player.getCouldDiveNumber() <= 0)
		{
			//潜行点数没有不可潜行
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_DIVE_NOT_ENOUGH);
			return false;
		}
		//不等于0的时候表示潜行
		USFFortress curFortress = player.getCurFortress();
		if(curFortress == null)
		{
			//据点内才能潜行
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_DIVE_NOT_IN_FORTRESS);
			return false;
		}
		USFFortress targetFortress = usFight.getFortress(csUSFPlayerMove.getTargetFortressId());
		if (targetFortress == null)
		{
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战单位开始移动失败,目标据点不存在,playerId={},targetFortressId={}", player.getPlayerId(), csUSFPlayerMove.getTargetFortressId());
			}
			//据点不存在
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_MOVE_TARGET_FORTRESS_NOT_EXISTS);
			return false;
		}
		if(targetFortress.getCampType() != player.getCampType() && targetFortress.getType() == USFFortressType.USF_FORTRESS_BASE)
		{
			//不可潜行至对方基地
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_DIVE_CANNOT_ENEMY_BASE);
			return false;
		}
		int conType = curFortress.isConnectForPlayer(targetFortress,player);
		if(conType  == 0)
		{
			return true;
		}
		else if(conType == 1)
		{
			//只能潜行至相邻据点
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_DIVE_NOT_NEIGHBOUR_FORTRESS);
			return  false;
		}
		else if(conType == 2)
		{
			//该航线不容许通过
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerMove, ErrorCodeConstants.USF_UNIT_COUNT_PASS_ROUTE);
			return  false;
		}
		return true;
	}

	/**
	 * 玩家托管
	 * 
	 * @param human
	 */
	public void playerBot(Human human, CSUSFPlayerBot csUSFPlayerBot) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		USFPlayer player = usFight.getPlayer(human.getPlayerId());
		if (player == null) {
			BattleCoreBoot.pushErrorMsgToClient(csUSFPlayerBot.getChannel(), human.getServerId(), human.getPlayerId(),
				PrivPSEnum.SCUSFPlayerBot, ErrorCodeConstants.USF_INVALID_OPERATION);
			return;
		}
		if (usFight.isRunning()) {
			boolean isBot;
			long now = System.currentTimeMillis();
			if (player.isBot()) {
				player.finishBot(now);
				isBot = false;
			} else {
				player.startBot(now);
				isBot = true;
			}
			SCUSFPlayerBot scUSFPlayerBot = new SCUSFPlayerBot(isBot);
			player.pushMsg(scUSFPlayerBot);
		} else {
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerBot, ErrorCodeConstants.USF_STATE_CAN_NOT_OPERATE);
		}
	}

	/**
	 *  获取对战信息
	 * 
	 * @param human
	 */
	public void getFightInfo(Human human, CSUSFGetFightInfo csUSFGetFightInfo) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		List<USFClientCampFightInfo> campFightInfos = usFight.copyToFightInfos(false);
		SCUSFGetFightInfo scUSFGetFightInfo = new SCUSFGetFightInfo(campFightInfos);
		BattleCoreBoot.pushToBenfu(csUSFGetFightInfo.getChannel(), scUSFGetFightInfo, human.getServerId(),
			human.getPlayerId());
	}

	/**
	 * 同步阵容数据
	 * 
	 * @param human
	 * @param fightSides
	 */
	public void syncPlayerFormation(Human human, List<byte[]> fightSides) {
//		if (CollectionUtils.isEmpty(fightSides)) {
//			return;
//		}
//		USFScene scene = (USFScene) human.getSceneObject().getScene();
//		USFight usFight = scene.getUsFight();
//		USFPlayer player = usFight.getPlayer(human.getPlayerId());
//		if (player == null) {
//			return;
//		}
//		USFStateType curStateType = usFight.getFsmController().getCurStateType();
//		if (curStateType == USFStateType.USF_STATE_PREPARE) {
//			player.getFightController().syncFightData(fightSides);
//		} else if (curStateType == USFStateType.USF_STATE_RUNNING) {
//			USFUnitStateType curUnitStateType = player.getFsmController().getCurStateType();
//			if (curUnitStateType == USFUnitStateType.USF_UNIT_STATE_DEATH) {
//				player.getFightController().syncFightData(fightSides);
//			} else {
//				AMLog.LOG_UNION.warn("海战玩家当前状态不能修改阵容,serverId={},playerId={},curUnitStateType={}", human.getServerId(),
//					human.getPlayerId(), curUnitStateType);
//			}
//		} else {
//			AMLog.LOG_UNION.warn("海战当前状态不能修改阵容,serverId={},playerId={},curStateType={}", human.getServerId(),
//				human.getPlayerId(), curStateType);
//		}
	}

	/**
	 * 同步战船数据
	 * 
	 * @param human
	 * @param newShipInfo
	 */
	public void syncPlayerShip(Human human, USFShipInfo newShipInfo) {
//		USFScene scene = (USFScene) human.getSceneObject().getScene();
//		USFight usFight = scene.getUsFight();
//		USFPlayer player = usFight.getPlayer(human.getPlayerId());
//		if (player == null) {
//			return;
//		}
//		USFStateType curStateType = usFight.getFsmController().getCurStateType();
//		if (curStateType == USFStateType.USF_STATE_PREPARE) {
//			player.getMoveController().syncShipInfo(newShipInfo, true);
//		} else if (curStateType == USFStateType.USF_STATE_RUNNING) {
//			USFUnitStateType curUnitStateType = player.getFsmController().getCurStateType();
//			if (curUnitStateType == USFUnitStateType.USF_UNIT_STATE_DEATH) {
//				player.getMoveController().syncShipInfo(newShipInfo, true);
//			} else {
//				AMLog.LOG_UNION.warn("海战玩家当前状态不能修改出战船只,serverId={},playerId={},curUnitStateType={}",
//					human.getServerId(), human.getPlayerId(), curUnitStateType);
//			}
//		} else {
//			AMLog.LOG_UNION.warn("海战当前状态不能修改出战船只,serverId={},playerId={},curStateType={}", human.getServerId(),
//				human.getPlayerId(), curStateType);
//		}
	}

	/**
	 * 处理海战频道信息
	 * 
	 * @param msg
	 */
	@Override
	public void ssPushChannelChatToKuafu(SSPushChannelChatToKuafu msg) {
		Human human = (Human) Globals.getHumanByServerId(msg.getFromServerId(), msg.getFromId());
		if (human != null) {
			AbstractScene scene = human.getScene();
			if (scene instanceof USFScene) {
				USFScene usfScene = (USFScene) scene;
				USFBroadcastManager broadcastManager = usfScene.getUsFight().getBroadcastManager();
				broadcastManager.addChat(msg);
			}
		}
	}

	/**
	 * 改变目标玩家语音权限
	 * 
	 * @param human
	 */
	public void switchPlayerMicState(Human human, CSUSFSwitchPlayerMicState csUSFSwitchPlayerMicState) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		USFPlayer player = usFight.getPlayer(human.getPlayerId());
		if (player == null) {
			BattleCoreBoot.pushErrorMsgToClient(csUSFSwitchPlayerMicState.getChannel(), human.getServerId(),
				human.getPlayerId(), PrivPSEnum.SCUSFSwitchPlayerMicState, ErrorCodeConstants.USF_INVALID_OPERATION);
			return;
		}
		USFPlayer targetPlayer = usFight.getPlayer(csUSFSwitchPlayerMicState.getPlayerId());
		if (targetPlayer == null) {
			player.pushErrorMsg(PrivPSEnum.SCUSFSwitchPlayerMicState, ErrorCodeConstants.USF_TARGET_PLAYER_NOT_EXISTS);
			return;
		}
		if (player.getUnionRoleId() > targetPlayer.getUnionRoleId()) {
			player.pushErrorMsg(PrivPSEnum.SCUSFSwitchPlayerMicState, ErrorCodeConstants.USF_PERMISSION_DENIED);
			return;
		}
		if (targetPlayer.isCanOpenMic()) {
			targetPlayer.setCanOpenMic(false);
		} else {
			targetPlayer.setCanOpenMic(true);
		}
		targetPlayer.syncData();
		SCUSFSwitchPlayerMicState scUsfSwitchPlayerMicState = new SCUSFSwitchPlayerMicState(
			csUSFSwitchPlayerMicState.getPlayerId(), targetPlayer.isCanOpenMic());
		player.pushMsg(scUsfSwitchPlayerMicState);
	}

	public void lookFightReplay(Human human, long fightId) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		if (usFight != null) {
			USFFightReplayManager fightReplayManager = usFight.getFightReplayManager();
			fightReplayManager.sendFightReplay(human, fightId);
		}
	}

	public void getFightRecords(Human human) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		if (usFight != null) {
			USFFightReplayManager fightReplayManager = usFight.getFightReplayManager();
			List<PVPFightRecord> records = fightReplayManager.getRecords(human.getPlayerId());
			SCPlayerFightRecord sc = new SCPlayerFightRecord(records);
			BattleCoreBoot.pushToBenfu(human.getChannel(), sc, human.getServerId(), human.getPlayerId());
		}

	}

	/**
	 * 玩家发起据点升变
	 * 
	 * @param human
	 * @param csUSFFortressPromotion
	 */
	public void fortressPromotion(Human human, CSUSFFortressPromotion csUSFFortressPromotion) {
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		USFPlayer player = usFight.getPlayer(human.getPlayerId());
		if (player == null) {
			BattleCoreBoot.pushErrorMsgToClient(csUSFFortressPromotion.getChannel(), human.getServerId(),
				human.getPlayerId(), PrivPSEnum.SCUFFortressPromotionBegin, ErrorCodeConstants.USF_INVALID_OPERATION);
			return;
		}
		if (!usFight.isRunning()) {
			player.pushErrorMsg(PrivPSEnum.SCUSFPlayerBot, ErrorCodeConstants.USF_STATE_CAN_NOT_OPERATE);
			return;
		}

		if (!USFUtils.isManager(player)) {
			player.pushErrorMsg(PrivPSEnum.SCUFFortressPromotionBegin, ErrorCodeConstants.USF_PERMISSION_DENIED);
			return;
		}

		USFUnitStateType curStateType = player.getFsmController().getCurStateType();
		if (curStateType != USFUnitStateType.USF_UNIT_STATE_GARRISON) {
			player.pushErrorMsg(PrivPSEnum.SCUFFortressPromotionBegin, ErrorCodeConstants.USF_FORTRESS_NOT_GARRISON);
			return;
		}
		USFFortress curFortress = player.getCurFortress();
		if (curFortress.getId() != csUSFFortressPromotion.getFortressUid()) {
			player.pushErrorMsg(PrivPSEnum.SCUFFortressPromotionBegin,
				ErrorCodeConstants.USF_FORTRESS_NOT_GARRISON_FORTRESS);
			return;
		}
		DictUSFPromotion dictUSFPromotion = DictUSFPromotion.getDictUSFPromotion(csUSFFortressPromotion
			.getPromotionId());
		if (dictUSFPromotion == null) {
			if (AMLog.LOG_UNION.isWarnEnabled()) {
				AMLog.LOG_UNION.warn("海战据点开始升变失败,没有找到DictUSFPromotion,promotionId={}",
					csUSFFortressPromotion.getPromotionId());
			}
			player.pushErrorMsg(PrivPSEnum.SCUFFortressPromotionBegin, ErrorCodeConstants.USF_INVALID_OPERATION);
			return;
		}
		int code = curFortress.beginPromotion(dictUSFPromotion, player.getId());
		if (code > 0) {
			player.pushErrorMsg(PrivPSEnum.SCUFFortressPromotionBegin, code);
		}
		player.onManual(true);
	}

	public void csUFRequestForCombatLogInfo(Human human, CSUFRequestForCombatLogInfo csUFRequestForCombatLogInfo) {
		int type = csUFRequestForCombatLogInfo.getType();
		USFScene scene = (USFScene) human.getSceneObject().getScene();
		USFight usFight = scene.getUsFight();
		if (usFight != null) {
			USFFightReplayManager fightReplayManager = usFight.getFightReplayManager();
			List<USFFightRecord> usfFightRecordList = fightReplayManager.getUSFFightRecordList(human.getPlayerId(),
				type);
			SCUFRequestForCombatLogInfo sc = new SCUFRequestForCombatLogInfo(type, usfFightRecordList);
			BattleCoreBoot.pushToBenfu(human.getChannel(), sc, human.getServerId(), human.getPlayerId());
		}
	}
}
