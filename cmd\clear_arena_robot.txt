
#清理机器人的sql

delete from inst_equipment_data where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_currency where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_fight_record where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_player_arena where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_player_fight where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_scene_data where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_formation where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_hero where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_life where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_msea_data where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_system_settings where playerId in (select playerId from inst_player where isRobot = 1);
delete from inst_title_data where playerId in (select playerId from inst_player where isRobot = 1);

delete from inst_player where  isRobot = 1;
