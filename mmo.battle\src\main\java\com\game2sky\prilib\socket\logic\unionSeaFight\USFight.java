package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFPlayerEnter;
import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFightSettlement;
import com.game2sky.prilib.communication.game.unionSeaFight.USFCampType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientCampFightInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientCampInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientRouteInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnionInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFWinReason;
import com.game2sky.prilib.core.dict.domain.DictUSFFortress;
import com.game2sky.prilib.core.dict.domain.DictUSFFortressDetail;
import com.game2sky.prilib.core.dict.domain.DictUSFRoute;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneIdGen;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneManager;
import com.game2sky.prilib.socket.logic.unionSeaFight.camp.USFCamp;
import com.game2sky.prilib.socket.logic.unionSeaFight.replay.USFFightReplayManager;
import com.game2sky.prilib.socket.logic.unionSeaFight.route.USFRoute;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.AbstractUSFFight;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.TimeUtils;
import com.game2sky.publib.socket.logic.human.state.DisconnectReason;
import com.game2sky.publib.socket.logic.scene.base.AbstractScene;

import gnu.trove.map.hash.TIntObjectHashMap;
import gnu.trove.map.hash.TLongObjectHashMap;
import io.netty.channel.Channel;

/**
 * TODO 海战
 *
 * <AUTHOR>
 * @version v0.1 2018年6月7日 下午4:47:58  guojijun
 */
public class USFight implements IUSFTickable {

	/**唯一ID*/
	private final long id;
	/**地图ID*/
	private final int mapId;
	/**自增ID*/
	private int IdGen;
	/**是否开始了*/
	private boolean isBegin;
	/**胜利方阵营*/
	private USFCampType winCampType;
	/**胜利原因*/
	private USFWinReason winReason;
	/**所有阵营*/
	private final ArrayList<USFCamp> camps;
	/**所有航线*/
	private final ArrayList<USFRoute> routes;
	/**所有玩家*/
	private final TLongObjectHashMap<USFPlayer> players;
	/**所有据点*/
	private final TIntObjectHashMap<USFFortress> fortresses;
	/**等待开始的战斗*/
	private final LinkedList<AbstractUSFFight> fights;
	/**广播管理器*/
	private final USFBroadcastManager broadcastManager;
	/**状态管理器*/
	private final USFightFsmController fsmController;
	/**绑定的场景*/
	private USFScene scene;
	/**准备时间*/
	private int prepareTime;
	/**运行时间*/
	private int runningTime;
	/**开始时间*/
	private long beginTime;

	private USFightBattleService service;
	/** 战斗回放管理器 */
	private USFFightReplayManager fightReplayManager;

	public USFight(long id, int mapId) {
		this.id = id;
		this.mapId = mapId;
		this.IdGen = 0;
		this.isBegin = false;

		this.camps = new ArrayList<>(USFCampType.values().length);
		this.routes = new ArrayList<>(30);
		this.players = new TLongObjectHashMap<>();
		this.fortresses = new TIntObjectHashMap<>();
		this.fights = new LinkedList<>();
		this.broadcastManager = new USFBroadcastManager(this);
		this.fsmController = new USFightFsmController(this);
		this.fightReplayManager = new USFFightReplayManager();
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("[id=").append(this.id);
		builder.append(",mapId=").append(this.mapId);
		for (USFCamp camp : this.camps) {
			if (camp.getUnionId() > 0) {
				builder.append(",").append(camp.getCampType()).append("=").append(camp.getUnionId());
			}
		}
		builder.append("]");
		return builder.toString();
	}

	/**
	 * 获取唯一自增ID
	 * 
	 * @return
	 */
	public int getNextId() {
		return ++this.IdGen;
	}

	/**
	 * 获取阵营
	 * 
	 * @param campType 阵营类型
	 * @return
	 */
	public USFCamp getCamp(USFCampType campType) {
		for (USFCamp camp : this.camps) {
			if (camp.getCampType() == campType) {
				return camp;
			}
		}
		return null;
	}

	/**
	 * 获取阵营
	 * 
	 * @param campType 阵营类型
	 * @return
	 */
	public USFCamp getCamp(int campType) {
		for (USFCamp camp : this.camps) {
			if (camp.getCampType().value() == campType) {
				return camp;
			}
		}
		return null;
	}

	/**
	 * 根据公会ID获取USFCamp
	 * 
	 * @param unionId
	 * @return
	 */
	public USFCamp getCampByUnionId(long unionId) {
		for (USFCamp camp : this.camps) {
			if (camp.getUnionId() == unionId) {
				return camp;
			}
		}
		return null;
	}

	/**
	 * 添加延迟计算的战斗
	 * 
	 * @param fight
	 */
	public void addFight(AbstractUSFFight fight) {
		this.fights.add(fight);
		AMLog.LOG_UNION.debug("海战添加延迟计算的战斗,fight={}", fight.getFightPlayId());
	}

	/**'
	 * 获取玩家
	 * 
	 * @param playerId
	 * @return
	 */
	public USFPlayer getPlayer(long playerId) {
		return this.players.get(playerId);
	}

	/**
	 * 添加玩家
	 * 
	 * @param player
	 */
	public void addPlayer(USFPlayer player) {
		this.players.put(player.getPlayerId(), player);
	}

	/**
	 * 获取据点
	 * 
	 * @param fortressId 据点ID(字典表ID)
	 * @return
	 */
	public USFFortress getFortress(int fortressId) {
		return this.fortresses.get(fortressId);
	}

	@Override
	public void tick(long now) {
		this.fsmController.updateState(now);
	}

	/**
	 * 广播消息
	 * 
	 * @param msg
	 */
	public void broadcast(APSHandler msg) {
		this.broadcastManager.broadcast(msg);
	}

	/**
	 * 初始化
	 */
	public boolean init() {
		// 初始化阵营
		for (USFCampType campType : USFCampType.values()) {
			USFCamp camp = new USFCamp(campType);
			this.camps.add(camp);
			camp.setUsFight(this);
		}

		// 初始化据点
		List<DictUSFFortress> dictUSFFortresses = DictUSFFortress.getDictUSFFortresses(mapId);
		TIntObjectHashMap<USFFortress> map = new TIntObjectHashMap<>();
		for (DictUSFFortress dictUSFFortress : dictUSFFortresses) {
			USFFortressType fortressType = USFFortressType.valueOf(dictUSFFortress.getType());
			if (fortressType == null) {
				AMLog.LOG_UNION.warn("海战据点类型错误,fortressId={},type={}", dictUSFFortress.getId(),
					dictUSFFortress.getType());
				return false;
			}
			DictUSFFortressDetail dictUSFFortressDetail = DictUSFFortressDetail.getDictUSFFortressDetail(fortressType
				.value());
			if (dictUSFFortressDetail == null) {
				AMLog.LOG_UNION.warn("海战据点详情不存在,fortressId={},type={}", dictUSFFortress.getId(),
					dictUSFFortress.getType());
				return false;
			}
			USFFortress fortress = new USFFortress(this.getNextId(), dictUSFFortress, dictUSFFortressDetail);
			this.fortresses.put(fortress.getDictId(), fortress);
			// fortress.setUsableBuffs(USFBuff.createBuff(dictUSFFortress.getBuffIds()));
			USFCamp camp = this.getCamp(dictUSFFortress.getCampType());
			if (camp != null) {
				map.put(fortress.getDictId(), fortress);
				fortress.joinCamp(camp);
				if (fortressType == USFFortressType.USF_FORTRESS_BASE) {
					camp.setBornFortress(fortress);
				}
				fortress.pushFortrestBuffs(null);
			} else {
				AMLog.LOG_UNION.warn("海战据点初始阵营错误,fortressId={},campType={}", dictUSFFortress.getId(),
					dictUSFFortress.getCampType());
				return false;
			}
		}

		// 初始化航线
		List<DictUSFRoute> dictUSFRoutes = DictUSFRoute.getDictUSFRoutes(mapId);
		for (DictUSFRoute dictUSFRoute : dictUSFRoutes) {
			USFFortress headFortress = map.get(dictUSFRoute.getEndFortressId());
			USFFortress tailFortress = map.get(dictUSFRoute.getStartFortressId());
			if (headFortress == null || tailFortress == null) {
				AMLog.LOG_UNION.warn("海战航线起始据点或者结束据点错误,routeId={},startFortressId={},endFortressId={}",
					dictUSFRoute.getId(), dictUSFRoute.getStartFortressId(), dictUSFRoute.getEndFortressId());
				return false;
			}
			USFRoute route = new USFRoute(dictUSFRoute, headFortress, tailFortress);
			this.routes.add(route);
			tailFortress.addRoute(headFortress.getDictId(), route);
			if (dictUSFRoute.getIsOneWay() == 0) {// 非单行道
				headFortress.addRoute(tailFortress.getDictId(), route);
			}
		}

		for (USFCamp camp : this.camps) {
			if (camp.getUnionId() > 0 && camp.getBornFortress() == null) {
				AMLog.LOG_UNION.warn("海战阵营没有出生据点,campType={}", camp.getCampType());
				return false;
			}
		}
		return true;
	}

	/**
	 * 同步阵营数据
	 * 
	 * @param unionInfo
	 * @return true同步成功false同步失败
	 */
	public boolean syncCampInfo(USFUnionInfo unionInfo) {
		if (unionInfo.getCampType() == USFCampType.USF_CAMP_NEUTRAL) {
			return false;
		}
		USFCamp camp = this.getCamp(unionInfo.getCampType());
		if (camp.getUnionId() != 0) {
			boolean bl = camp.getUnionId() == unionInfo.getUnionId();
			if (!bl) {
				AMLog.LOG_UNION.warn("该阵营数据已经同步,curUnionId={},syncUnionId={}", camp.getUnionId(),
					unionInfo.getUnionId());
			}
			return bl;
		}
		return camp.syncCampInfo(unionInfo);
	}

	/**
	 * 启动
	 * 
	 * @param channel
	 */
	public void start(Channel channel) {
		for (USFCamp camp : this.camps) {
			if (camp.getCampType() == USFCampType.USF_CAMP_NEUTRAL) {
				continue;
			}
			// 有阵营数据没同步完
			if (camp.getUnionId() <= 0) {
				return;
			}
		}
		USFStateType stateType = this.fsmController.getCurStateType();
		if (stateType != USFStateType.USF_STATE_NONE) {
			return;
		}
		this.service.removeFromTickCache(this);
		int sceneId = SceneIdGen.createCopyId();
		this.scene = new USFScene(sceneId);
		USFSceneCloser sceneCloser = new USFSceneCloser();
		sceneCloser.addScene(this.scene);
		this.scene.setUsFight(this);

		ArrayList<AbstractScene> scenes = new ArrayList<>(1);
		scenes.add(scene);
		SceneManager sceneManager = (SceneManager) Globals.getMemoryData().getSceneManager();
		sceneManager.addManySceneToOneSceneRunner(scenes);
//		Globals.getMemoryData().getSceneManager().addActiveScene(this.scene);

		this.fsmController.switchState(USFStateType.USF_STATE_PREPARE);
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			AMLog.LOG_UNION.info("海战启动,uqId={},mapId={},camps={}", this.id, this.mapId, this.camps);
		}
	}

	/**
	 *  获取战斗数据
	 * 
	 * @param isSettlement 是否结算
	 * @return
	 */
	public List<USFClientCampFightInfo> copyToFightInfos(boolean isSettlement) {
		ArrayList<USFClientCampFightInfo> campFightInfos = new ArrayList<>(2);
		for (USFCamp camp : this.camps) {
			if (camp.getUnionId() > 0) {
				campFightInfos.add(camp.copyToFightInfo(isSettlement));
			}
		}
		return campFightInfos;
	}

	/**
	 * 当准备中
	 * 
	 * @param now
	 */
	public void onPrepare(long now) {
		this.broadcastManager.tick(now);
	}

	/**
	 * 当玩家进入
	 * 
	 * @param serverId
	 * @param playerId
	 */
	public void onPlayerEnter(int serverId, long playerId) {
		this.broadcastManager.onPlayerEnter(serverId, playerId);
	}

	/**
	 * 当玩家退出
	 * 
	 * @param serverId
	 * @param playerId
	 * @param callInterface 调用的接口
	 */
	public void onPlayerQuit(int serverId, long playerId, String callInterface) {
		this.broadcastManager.onPlayerQuit(serverId, playerId);
		USFPlayer player = this.getPlayer(playerId);
		if (player != null) {
			player.onQuit();
		} else {
			// playerId只会存在于一个阵营里,所以这么写也没问题
			for (USFCamp camp : this.camps) {
//				if (camp.getServerId() == serverId) {
					camp.removeViewer(playerId);
//				}
			}
		}
		Human human = (Human) Globals.getHumanByServerId(serverId, playerId);
		if (human != null) {
			Globals.removeHuman(human, DisconnectReason.USF_PLAYER_QUIT, callInterface);
		}
	}

	/**
	 * 是否运行中
	 * 
	 * @return
	 */
	public boolean isRunning() {
		return this.fsmController.getCurStateType() == USFStateType.USF_STATE_RUNNING;
	}

	/**
	 * 当运行中
	 * 
	 * @param now
	 */
	void onRunning(long now) {
		if (this.winCampType != null) {
			this.fsmController.switchState(USFStateType.USF_STATE_CLOSE);
			this.broadcastManager.tick(now);
			return;
		}

		this.broadcastManager.tick(now);

		for (USFCamp camp : this.camps) {
			camp.tick(now);
		}
		for (USFRoute route : this.routes) {
			route.tick(now);
		}

		if (!this.fights.isEmpty()) {
			// tick战斗开始秒算
			Iterator<AbstractUSFFight> iterator = this.fights.iterator();
			while (iterator.hasNext()) {
				AbstractUSFFight fight = iterator.next();
				if (fight.realStart(now)) {
					iterator.remove();
					AMLog.LOG_UNION.debug("海战战斗开始计算,fight={}", fight.getFightPlayId());
				}
			}
		}
	}

	/**
	 * 当进入运行状态
	 */
	void onEnterRunningState() {
		this.isBegin = true;
		final long now = System.currentTimeMillis();
		Collection<USFPlayer> coll = this.players.valueCollection();
		for (USFPlayer player : coll) {
			player.onFightStartRunning(now);
		}
		this.beginTime = now;
		// 记录开始
		SCUSFPlayerEnter scUSFPlayerEnter = createBeginViewerInfo();
		broadcastManager.getCommonMsgRecorder().addReplayMsg(scUSFPlayerEnter);
	}

	private SCUSFPlayerEnter createBeginViewerInfo() {
		USFStateType stateType = getFsmController().getCurStateType();
		if (stateType == USFStateType.USF_STATE_CLOSE) {
			return null;
		}
		USFClientInfo clientInfo = new USFClientInfo();
		clientInfo.setFightId(getId());
		clientInfo.setMapId(getMapId());
		USFClientStateInfo stateInfo = getFsmController().copyTo();
		clientInfo.setStateInfo(stateInfo);
		clientInfo.setIsViewer(false);

		List<USFRoute> routes = getRoutes();
		ArrayList<USFClientRouteInfo> routeInfos = new ArrayList<>(routes.size());
		clientInfo.setRouteInfos(routeInfos);
		for (USFRoute route : routes) {
			routeInfos.add(route.copyTo());
		}
		// 没有说明是观战玩家
		clientInfo.setIsViewer(true);
		List<USFCamp> camps = getCamps();
		ArrayList<USFClientCampInfo> campInfos = new ArrayList<>(camps.size());
		clientInfo.setCampInfos(campInfos);
		LinkedList<USFClientUnitInfo> unitInfos = new LinkedList<>();
		clientInfo.setUnitInfos(unitInfos);
		for (USFCamp camp : camps) {
			if (camp.getUnionId() > 0) {
				campInfos.add(camp.copyTo());
			}
			for (IUSFUnit unit : camp.getUnits()) {
				unitInfos.add(unit.copyTo());
			}
		}
		clientInfo.setSelfCampType(USFCampType.USF_CAMP_NEUTRAL);
		return new SCUSFPlayerEnter(clientInfo);
	}

	/**
	 * 当离开运行状态
	 */
	void onLeaveRunningState() {
		final long now = System.currentTimeMillis();
		Collection<USFPlayer> coll = this.players.valueCollection();
		for (USFPlayer player : coll) {
			player.onFightFinishRunning(now);
		}
	}

	/**
	 * 关闭
	 */
	void close() {
		if (this.isBegin) {
			if (this.winCampType == null) {// 时间到结束
				this.calcWinCampType();
			}
			List<USFClientCampFightInfo> campFightInfos = this.copyToFightInfos(true);
			// 记录结算信息
			SCUSFightSettlement scUSFightSettlement = new SCUSFightSettlement();
			scUSFightSettlement.setCampFightInfos(campFightInfos);
			broadcastManager.getCommonMsgRecorder().addReplayMsg(scUSFightSettlement);
			for (USFCamp camp : this.camps) {
				if (camp.getUnionId() > 0) {
					camp.settlement(campFightInfos, this.winReason);
				}
			}
			broadcastManager.getCommonMsgRecorder().uploadCommonMsgToCos();
		}
	}
	/**
	 * 生成cos的文件名
	 * 
	 * @return
	 */
	public String createCosKey() {
		StringBuilder sb = new StringBuilder();
		String date = TimeUtils.format(System.currentTimeMillis(), TimeUtils.YMD_FMT_STR2);
		// nhan2k3 usf 
		sb.append("server/seafight/").append(date).append("_").append(getId()).append(".replay");
		return sb.toString();
	}

	/**
	 * 关闭场景
	 */
	void closeScene() {
		this.service.removeFromCache(this.id);
		if (this.scene != null) {
			this.scene.getSceneCloser().close(0);
		}
	}

	/**
	 * 如果没有胜利方阵营,则根据其他规则判定胜负
	 */
	private void calcWinCampType() {
		this.winReason = USFWinReason.WIN_REASON_TIME_OUT;
		USFCamp buleCamp = this.getCamp(USFCampType.USF_CAMP_BLUE);
		USFCamp redCamp = this.getCamp(USFCampType.USF_CAMP_RED);
		int buleCampFortressCount = buleCamp.getFortressCount();
		int redCampFortressCount = redCamp.getFortressCount();
		if (buleCampFortressCount > redCampFortressCount) {
			this.winCampType = USFCampType.USF_CAMP_BLUE;
		} else if (buleCampFortressCount < redCampFortressCount) {
			this.winCampType = USFCampType.USF_CAMP_RED;
		} else {
			// 平局
		}
	}

	/**
	 * 外部主动触发结束
	 * 
	 * @param winCampType 胜利方阵营类型
	 */
	public void over(USFCampType winCampType, USFWinReason winReason) {
		if (this.winCampType == null) {
			this.winCampType = winCampType;
			this.winReason = winReason;
			if (AMLog.LOG_UNION.isInfoEnabled()) {
				AMLog.LOG_UNION.info("海战结束,fightId={},winer={},reason={}", this.id, this.getCamp(winCampType),
					winReason);
			}
		}
	}

	public long getId() {
		return id;
	}

	public int getMapId() {
		return mapId;
	}

	public USFightFsmController getFsmController() {
		return fsmController;
	}

	public List<USFCamp> getCamps() {
		return camps;
	}

	public List<USFRoute> getRoutes() {
		return routes;
	}

	public Collection<USFFortress> getFortresses() {
		return fortresses.valueCollection();
	}

	public USFScene getScene() {
		return scene;
	}

	void setService(USFightBattleService service) {
		this.service = service;
	}

	public USFightBattleService getService() {
		return service;
	}

	public USFCampType getWinCampType() {
		return winCampType;
	}

	public USFBroadcastManager getBroadcastManager() {
		return broadcastManager;
	}

	public USFFightReplayManager getFightReplayManager() {
		return fightReplayManager;
	}

	public int getPrepareTime() {
		return prepareTime;
	}

	public void setPrepareTime(int prepareTime) {
		this.prepareTime = prepareTime;
	}

	public int getRunningTime() {
		return runningTime;
	}

	public void setRunningTime(int runningTime) {
		this.runningTime = runningTime;
	}

	public long getBeginTime() {
		return beginTime;
	}
}
