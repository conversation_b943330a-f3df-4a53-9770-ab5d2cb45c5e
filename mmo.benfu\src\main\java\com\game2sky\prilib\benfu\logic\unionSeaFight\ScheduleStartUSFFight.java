package com.game2sky.prilib.benfu.logic.unionSeaFight;

import java.util.ArrayList;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFFightAllotReq;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFightAllotInfo;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

public class ScheduleStartUSFFight extends ScheduledMessage {


	int serverId;
	int fightTimeId;
	USFFightAllotInfo fightAllotInfo;
	
	public static final int DELAY = 1000;

	public ScheduleStartUSFFight(int serverId, int fightTimeId, USFFightAllotInfo fightAllotInfo) {
		super(System.currentTimeMillis());
		this.serverId = serverId;
		this.fightTimeId = fightTimeId;
		this.fightAllotInfo = fightAllotInfo;
	}
	
	@Override
	public void execute() {
		List<USFFightAllotInfo> list = new ArrayList<>();
		list.add(fightAllotInfo);
		
		SSUSFFightAllotReq req = new SSUSFFightAllotReq();
		req.setFightAllotInfo(list);
		req.setServerId(serverId);
		req.setFightTimeId(fightTimeId);
		
		Message message = Message.buildByZoneId(0L, serverId, req, null, null, null);
		Globals.getMsgProcessDispatcher().put(message);
	}

}
