2024-04-17 18:01:40,778 [AsyncServiceImpl-thread-3] ERROR(SendPlayerToMainSvrIO.java:44) - @SendPlayerToMainSvrIO http://127.0.0.1:8081/self/updateGamePlayerToMain_____{content={"chargeMoney":0.0,"fightPower":4617,"globalId":0,"isLogin":0,"lastLoginTime":1713338302000,"level":1,"name":"fasgsag","otherInviteCode":"947642354","plafformUid":"nhan2k6","playerId":************,"serverId":1}}
java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_202]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_202]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_202]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_202]
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246) ~[?:1.8.0_202]
	at java.io.BufferedInputStream.read(BufferedInputStream.java:265) ~[?:1.8.0_202]
	at org.apache.commons.httpclient.HttpParser.readRawLine(HttpParser.java:78) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpParser.readLine(HttpParser.java:106) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpConnection.readLine(HttpConnection.java:1116) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpMethodBase.readStatusLine(HttpMethodBase.java:1973) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpMethodBase.readResponse(HttpMethodBase.java:1735) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpMethodBase.execute(HttpMethodBase.java:1098) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpMethodDirector.executeWithRetry(HttpMethodDirector.java:398) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpMethodDirector.executeMethod(HttpMethodDirector.java:171) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:397) ~[commons-httpclient-3.1.jar!/:?]
	at org.apache.commons.httpclient.HttpClient.executeMethod(HttpClient.java:323) ~[commons-httpclient-3.1.jar!/:?]
	at com.game2sky.publib.framework.common.http.AMHttp.httpPost(AMHttp.java:187) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.common.http.AMHttp.httpPost(AMHttp.java:204) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.common.http.AMHttp.httpPost(AMHttp.java:208) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.player.SendPlayerToMainSvrIO.doIo(SendPlayerToMainSvrIO.java:41) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.async.AsyncOperation.execute(AsyncOperation.java:73) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.async.AsyncOperation$1.run(AsyncOperation.java:95) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
