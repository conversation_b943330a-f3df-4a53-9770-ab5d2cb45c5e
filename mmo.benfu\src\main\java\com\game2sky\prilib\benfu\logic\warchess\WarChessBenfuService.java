package com.game2sky.prilib.benfu.logic.warchess;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.common.HeroPropEnum;
import com.game2sky.prilib.communication.game.equipment.PropValue;
import com.game2sky.prilib.communication.game.warchess.CSWarChessBuyHp;
import com.game2sky.prilib.communication.game.warchess.CSWarChessEnter;
import com.game2sky.prilib.communication.game.warchess.CSWarChessEnterGM;
import com.game2sky.prilib.communication.game.warchess.CSWarChessGetFightHeroInfo;
import com.game2sky.prilib.communication.game.warchess.CSWarChessHeroChange;
import com.game2sky.prilib.communication.game.warchess.CSWarChessInfo;
import com.game2sky.prilib.communication.game.warchess.CSWarChessMoveChess;
import com.game2sky.prilib.communication.game.warchess.CSWarChessOperateChess;
import com.game2sky.prilib.communication.game.warchess.CSWarChessPlayEnd;
import com.game2sky.prilib.communication.game.warchess.CSWarChessQuit;
import com.game2sky.prilib.communication.game.warchess.CSWarChessSkipChapter;
import com.game2sky.prilib.communication.game.warchess.CSWarChessSkipRound;
import com.game2sky.prilib.communication.game.warchess.SCWarChessBuyHp;
import com.game2sky.prilib.communication.game.warchess.SCWarChessEnter;
import com.game2sky.prilib.communication.game.warchess.SCWarChessEnterGM;
import com.game2sky.prilib.communication.game.warchess.SCWarChessGetFightHeroInfo;
import com.game2sky.prilib.communication.game.warchess.SCWarChessHeroChange;
import com.game2sky.prilib.communication.game.warchess.SCWarChessInfo;
import com.game2sky.prilib.communication.game.warchess.SCWarChessQuit;
import com.game2sky.prilib.communication.game.warchess.SCWarChessSkipRound;
import com.game2sky.prilib.communication.game.warchess.WarChessActionEnum;
import com.game2sky.prilib.communication.game.warchess.WarChessDataClient;
import com.game2sky.prilib.communication.game.warchess.WarChessHeroGrid;
import com.game2sky.prilib.communication.game.warchess.WarChessHeroHp;
import com.game2sky.prilib.communication.game.warchess.WarChessInfo;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictWarChessChapter;
import com.game2sky.prilib.core.dict.domain.DictWarChessHeroBuyHP;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.base.CommonService;
import com.game2sky.prilib.core.socket.logic.battle.EFightUnit;
import com.game2sky.prilib.core.socket.logic.fight.base.Fight;
import com.game2sky.prilib.core.socket.logic.fight.base.IFight;
import com.game2sky.prilib.core.socket.logic.fight.manager.CommonFightManager;
import com.game2sky.prilib.core.socket.logic.hero.HeroModel;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.human.state.ActionState;
import com.game2sky.prilib.core.socket.logic.scene.unit.component.state.RoleStateManager;
import com.game2sky.prilib.core.socket.logic.scene.unit.player.ScenePlayerObject;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.prilib.core.socket.logic.warchess.IWarChessManager;
import com.game2sky.prilib.core.socket.logic.warchess.WarChessChapterWeight;
import com.game2sky.prilib.core.socket.logic.warchess.WarChessData;
import com.game2sky.publib.communication.game.struct.ItemBase;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.base.ItemBaseTools;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;

/**
 * <AUTHOR>
 * @version v0.1 2019/7/12 17:10 wangby
 */
@Service
public class WarChessBenfuService {

	private WarChessData getWarChessData(Human human) {
		IWarChessManager warChessManager = human.getWarChessManager();
		WarChessData warChessData = warChessManager.getPlayerWarChessData().getWarChessData();
		if (warChessData == null) {
			;// TODO 抛出异常
		}
		return warChessData;
	}

	/**
	 * 获取副本信息
	 *
	 * @param msg
	 * @param human
	 */
	/**
	 * 获取副本信息
	 *
	 * @param msg
	 * @param human
	 */
	public void csWarChessInfo(CSWarChessInfo msg, Human human) {
		SCWarChessInfo scWarChessInfo = new SCWarChessInfo();
		IWarChessManager warChessManager = human.getWarChessManager();
		Map<Integer, Integer> curChapter = warChessManager.getPlayerWarChessData().getCurChapter();
		List<WarChessInfo> result = new ArrayList<WarChessInfo>();
		List<WarChessInfo> trainresult = new ArrayList<WarChessInfo>();
		boolean isChange = false;
		// 取正常副本
		for (DictWarChessChapter chapter : DictWarChessChapter.getAllNormalChapter().values()) {
			WarChessInfo info = new WarChessInfo();
			info.setChapterId(chapter.getChapterId());
			Integer state = curChapter.get(chapter.getChapterId());
			if (state == null) {
				state = 0;
				curChapter.put(chapter.getChapterId(), state);
				isChange = true;
			}

			info.setState(state);
			result.add(info);
		}
		if (isChange) {
			warChessManager.getPlayerWarChessData().setCurChapter(curChapter);
			warChessManager.setModified();
		}
		// 取训练关卡
		Map<Integer, WarChessChapterWeight> curTrainChapter = warChessManager.getPlayerWarChessData()
			.getChapterWeight();
		for (WarChessChapterWeight weight : curTrainChapter.values()) {
			if (weight.getChapterId() == -1) {
				continue;
			}
			WarChessInfo info = new WarChessInfo();
			info.setChapterId(weight.getChapterId());
			info.setState(weight.getState());
			info.setIsToday(weight.isCurToday() ? 1 : 0);
			trainresult.add(info);
		}
		scWarChessInfo.setInfos(result);
		scWarChessInfo.setTrainInfos(trainresult);
		scWarChessInfo.setCurTrainTimes(warChessManager.getCurTrainTimes());
		human.push2Gateway(scWarChessInfo);
	}

	/**
	 * 进入副本
	 *
	 * @param msg
	 * @param human
	 */
	public void csWarChessEnter(CSWarChessEnter msg, Human human) {
		SCWarChessEnter scWarChessEnter = new SCWarChessEnter();
		IWarChessManager warChessManager = human.getWarChessManager();
		int needReconnect = warChessManager.isHaveNotEndWarChess();
		int chapterId = msg.getChapterId();
		CommonFightManager commonFightManager = human.getCommonFightManager();
		IFight fight = commonFightManager.getFightByPlayerId(human.getPlayerId());

		if (needReconnect > 0) {
			// 断线重连，检测玩家处于战斗状态
		} else {
			// 校验玩家是否能进入战斗状态
			RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
			boolean bool = roleStateManager.canEnter(ActionState.BATTLE, false);
			if (!bool) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
				return;
			}
		}

		// 判断是新章节 还是 训练章节
		DictWarChessChapter dict = DictWarChessChapter.getByChapterId(chapterId);
		if (dict == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
				ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
			return;
		}
		Map<Integer, Integer> curChapter = warChessManager.getPlayerWarChessData().getCurChapter();
		// 训练章节
		if (dict.getIsTrainChapter() > 0) {
			Map<Integer, WarChessChapterWeight> curTrainChapter = warChessManager.getPlayerWarChessData()
				.getChapterWeight();
			// 判断次数
			WarChessChapterWeight weight = curTrainChapter.get(-1);
			int curTimes = weight.getState();
			int maxTimes = BaseService.getConfigIntByDefault("WarChessTrainChapterTimes", 3);
			if (curTimes >= maxTimes) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_CHAPTER_TIMES_ERR);
				return;
			}
			WarChessChapterWeight cueTrain = curTrainChapter.get(chapterId);
			Integer state = cueTrain.getState();
			if (warChessManager.getPlayerWarChessData().getWarChessData() == null && !cueTrain.isCurToday()) {
				// 如果存在存在异常跨天的未完结的
				warChessManager.getPlayerWarChessData().setWarChessData(null);
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_TODAY_ERROR);
				return;
			}
			if (state == 1) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_TODAY_ALREADY);
				return;
			}
			// 判断前置关卡 是否通关
			Integer lastState = curChapter.get(dict.getIsTrainChapter());
			if (lastState == null) {
				lastState = 0;
				curChapter.put(chapterId, lastState);
			}
			if (lastState == 0) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_PRE);
				return;
			}
		}
		else if(dict.getIsDifficultyChapter()  > 0)
		{
			//判断开关
			boolean checkState = SwitchConditionManager.checkState(PrivSwitchEnum.WARCHESS_HARD_CHAPTER, human, PrivPSEnum.SCWarChessEnter);
			if (!checkState) {
				return;
			}
			// 判断前置关卡 是否通关
			Integer lastState = curChapter.get(dict.getIsDifficultyChapter());
			if (lastState == null) {
				lastState = 0;
				curChapter.put(chapterId, lastState);
			}
			if (lastState == 0) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_PRE);
				return;
			}
		}
		else
		{
			// 判断前置关卡是否通关
			DictWarChessChapter lastDict = DictWarChessChapter.getByGroupIdAndOrderNum(dict.getGroupId(),dict.getIsDifficultyChapter(),
				dict.getOrderNum() - 1);
			if (lastDict != null) {
				Integer laststate = curChapter.get(lastDict.getChapterId());
				if (laststate == null) {
					laststate = 0;
					curChapter.put(lastDict.getChapterId(), laststate);
				}
				if (laststate == 0) {
					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
						ErrorCodeConstants.WAR_CHESS_CHAPTER_NOT_OPEN);
					return;
				}
			}
			// 判断当前关卡是否通关
			Integer state = curChapter.get(chapterId);
			if (state == null) {
				state = 0;
				curChapter.put(chapterId, state);
			}
			if (state != 0) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_CHAPTER_END);
				return;
			}
		}

		WarChessDataClient data = warChessManager.getWarChessDataClient(chapterId);
		scWarChessEnter.setWarChessData(data);
		scWarChessEnter.setHeroIds(warChessManager.getPlayerWarChessData().getHeroIdsAlive());
		scWarChessEnter.setHps(warChessManager.getCurHeroHp());
		scWarChessEnter.setCurBuyHpTimes(warChessManager.getCurBuyHpTimes());
		human.push2Gateway(scWarChessEnter);

		// 重连判断
		if (needReconnect > 0) {
			if (fight != null) {
				fight.reconnect(human);
			}
		}

		// 修改状态
		warChessManager.onPlayerEnter();
		// 玩家进入战斗状态
		try {
			ScenePlayerObject scenePlayerObject = human.getSceneObject();
			//是否在战斗状态
			if(!human.isInBattle()) {
				scenePlayerObject.getRoleActionManager().enterBattle();
			}
		} catch (Exception e) {
			warChessManager.warChessQuit();
			AMLog.LOG_ERROR.error("_AbstractFightPlayer_: enter battle failed! playerId : {}, errorMessage : {}",
					human.getPlayerId(), e.getMessage());
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
		}
	}
	/**
	 * GM专用
	 * 
	 * @param msg
	 * @param human
	 */
	 public void csWarChessEnterGM(CSWarChessEnterGM msg, Human human) {
		 SCWarChessEnterGM scWarChessEnter = new SCWarChessEnterGM();
			IWarChessManager warChessManager = human.getWarChessManager();
			int needReconnect = warChessManager.isHaveNotEndWarChess();
			int chapterId = msg.getChapterId();
			CommonFightManager commonFightManager = human.getCommonFightManager();
			IFight fight = commonFightManager.getFightByPlayerId(human.getPlayerId());

			if (needReconnect > 0) {
				// 断线重连，检测玩家处于战斗状态
			} else {
				// 校验玩家是否能进入战斗状态
				RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
				boolean bool = roleStateManager.canEnter(ActionState.BATTLE, false);
				if (!bool) {
					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
						ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
					return;
				}
			}

			// 判断是新章节 还是 训练章节
			DictWarChessChapter dict = DictWarChessChapter.getByChapterId(chapterId);
			if (dict == null) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
					ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
				return;
			}
			Map<Integer, Integer> curChapter = warChessManager.getPlayerWarChessData().getCurChapter();
			// 训练章节
			if (dict.getIsTrainChapter() > 0) {
				Map<Integer, WarChessChapterWeight> curTrainChapter = warChessManager.getPlayerWarChessData()
					.getChapterWeight();
				// 判断次数
				WarChessChapterWeight weight = curTrainChapter.get(-1);
				int curTimes = weight.getState();
				int maxTimes = BaseService.getConfigIntByDefault("WarChessTrainChapterTimes", 3);
//				if (curTimes >= maxTimes) {
//					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
//						ErrorCodeConstants.WAR_CHESS_CHAPTER_TIMES_ERR);
//					return;
//				}
				WarChessChapterWeight cueTrain = curTrainChapter.get(chapterId);
				Integer state = cueTrain.getState();
				if (warChessManager.getPlayerWarChessData().getWarChessData() == null ) {
					// 如果存在存在异常跨天的未完结的
					warChessManager.getPlayerWarChessData().setWarChessData(null);
					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
						ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_TODAY_ERROR);
					return;
				}
//				if (state == 1) {
//					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
//						ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_TODAY_ALREADY);
//					return;
//				}
				// 判断前置关卡 是否通关
				Integer lastState = curChapter.get(dict.getIsTrainChapter());
				if (lastState == null) {
					lastState = 0;
					curChapter.put(chapterId, lastState);
				}
//				if (lastState == 0) {
//					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
//						ErrorCodeConstants.WAR_CHESS_TRAIN_CHAPTER_PRE);
//					return;
//				}
			} else {
				// 判断前置关卡是否通关
				DictWarChessChapter lastDict = DictWarChessChapter.getByGroupIdAndOrderNum(dict.getGroupId(),dict.getIsDifficultyChapter(),
					dict.getOrderNum() - 1);
				if (lastDict != null) {
					Integer laststate = curChapter.get(lastDict.getChapterId());
					if (laststate == null) {
						laststate = 0;
						curChapter.put(lastDict.getChapterId(), laststate);
					}
//					if (laststate == 0) {
//						human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
//							ErrorCodeConstants.WAR_CHESS_CHAPTER_NOT_OPEN);
//						return;
//					}
				}
				// 判断当前关卡是否通关
				Integer state = curChapter.get(chapterId);
				if (state == null) {
					state = 0;
					curChapter.put(chapterId, state);
				}
//				if (state != 0) {
//					human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
//						ErrorCodeConstants.WAR_CHESS_CHAPTER_END);
//					return;
//				}
			}

			WarChessDataClient data = warChessManager.getWarChessDataClient(chapterId);
			scWarChessEnter.setWarChessData(data);
			scWarChessEnter.setHeroIds(warChessManager.getPlayerWarChessData().getHeroIdsAlive());
			scWarChessEnter.setHps(warChessManager.getCurHeroHp());
			scWarChessEnter.setCurBuyHpTimes(warChessManager.getCurBuyHpTimes());
			human.push2Gateway(scWarChessEnter);

			// 重连判断
			if (needReconnect > 0) {
				if (fight != null) {
					fight.reconnect(human);
				}
			}

			// 修改状态
			warChessManager.onPlayerEnter();
			// 玩家进入战斗状态
			try {
				ScenePlayerObject scenePlayerObject = human.getSceneObject();
				//是否在战斗状态
				if(!human.isInBattle()) {
					scenePlayerObject.getRoleActionManager().enterBattle();
				}
			} catch (Exception e) {
				warChessManager.warChessQuit();
				AMLog.LOG_ERROR.error("_AbstractFightPlayer_: enter battle failed! playerId : {}, errorMessage : {}",
						human.getPlayerId(), e.getMessage());
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessEnter.getCode(),
						ErrorCodeConstants.WAR_CHESS_CHAPTER_ERROR);
			}
	  }

	/**
	 * 确认阵容
	 *
	 * @param msg
	 * @param human
	 */
	public void csWarChessHeroChange(CSWarChessHeroChange msg, Human human) {
		List<WarChessHeroGrid> grids = msg.getHeros();
		IWarChessManager warChessManager = human.getWarChessManager();
		if (grids == null || grids.size() == 0) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessHeroChange.getCode(),
				ErrorCodeConstants.WAR_CHESS_HERO_POS_ERR);
			return;
		}

		for (WarChessHeroGrid grid : grids) {
			// 校验英雄是否存在
			HeroModel model = human.getHeroManager().getHeroByHeroId(grid.getHeroId());
			if (model == null) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessHeroChange.getCode(),
					ErrorCodeConstants.WAR_CHESS_HERO_NOT_EXIST);
				return;
			}
			// 判断英雄血量
			WarChessHeroHp hp = warChessManager.getCurHeroHpByHeroId(grid.getHeroId());
			if (hp.getPer() <= 0d) {
				human.pushErrorMessageToClient(PrivPSEnum.SCWarChessHeroChange.getCode(),
					ErrorCodeConstants.WAR_CHESS_HERO_HP);
				return;
			}
		}

		boolean isOk = warChessManager.initWarChessHeroData(msg.getChapterId(), grids);
		if (!isOk) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessHeroChange.getCode(),
				ErrorCodeConstants.WAR_CHESS_HERO_POS_ERR);
			return;
		}
		// 取结果
		SCWarChessHeroChange scWarChessHeroChange = warChessManager.getSCWarChessHeroChange();
		human.push2Gateway(scWarChessHeroChange);
		// 回合轮换
		warChessManager.scWarChessRoundTurnRes();
	}

	/**
	 * 跳过当前章节
	 *
	 * @param msg
	 * @param human
	 */
	public void csWarChessSkipChapter(CSWarChessSkipChapter msg, Human human) {
		int chapterId = msg.getChapterId();
		List<Integer> heroIds = msg.getHeroIds();

		IWarChessManager warChessManager = human.getWarChessManager();
		if (heroIds == null || heroIds.size() == 0) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessSkipChapter.getCode(),
					ErrorCodeConstants.WAR_CHESS_HERO_POS_ERR);
			return;
		}

		// 检测客户端作弊
		List<Integer> heroIdList = new ArrayList<>();
		for (Integer heroId : heroIds) {
			if (!heroIdList.contains(heroId)) {
				heroIdList.add(heroId);
			}
		}
		if (heroIdList.size() < 1) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessSkipChapter.getCode(),
					ErrorCodeConstants.WAR_CHESS_HERO_POS_ERR);
			return;
		}

		// 增加英雄经验,结算
		warChessManager.warChessSkipChapter(chapterId, heroIdList);
	}

	/**
	 * 主动退出
	 *
	 * @param msg
	 * @param human
	 */
	public void csWarChessQuit(CSWarChessQuit msg, Human human) {
		IWarChessManager warChessManager = human.getWarChessManager();
		warChessManager.warChessQuitAndSettle(false,false);
		SCWarChessQuit scWarChessQuit = new SCWarChessQuit();
		human.push2Gateway(scWarChessQuit);
	}

	public void csWarChessMoveChess(CSWarChessMoveChess msg, Human human) {
		int scrPos = msg.getSrcPos();
		int targetPos = msg.getTarPos();
		List<Integer> path = msg.getWarchessGoords();

		IWarChessManager warChessManager = human.getWarChessManager();
		warChessManager.warChessMove(scrPos, targetPos, path);
	}

	public void csWarChessOperateChess(CSWarChessOperateChess msg, Human human) {
		int scrPos = msg.getSrcPos();
		int targetPos = msg.getTarPos();
		int extendPos = msg.getExtendPos();
		int skillId = msg.getSkillId();
		WarChessActionEnum actionEnum = WarChessActionEnum.valueOf(msg.getAcionType());
		if (actionEnum == null) {
			AMLog.LOG_STDOUT.info("@WarChess csWarChessOperateChess,msg.getAcionType()=" + msg.getAcionType()
									+ ",actionEnum=" + actionEnum);
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessOperateChess.getCode(),
				ErrorCodeConstants.WAR_CHESS_ACTION_TYPE_ERR);
			return;
		}

		IWarChessManager warChessManager = human.getWarChessManager();
		warChessManager.warChessAction(scrPos, targetPos, extendPos, actionEnum, skillId);
	}

	public void csWarChessPlayEnd(CSWarChessPlayEnd msg, Human human) {
		IWarChessManager warChessManager = human.getWarChessManager();
		warChessManager.csWarChessPlayEnd();
	}

	public void csWarChessSkipRound(CSWarChessSkipRound msg, Human human) {
		long playerId = human.getPlayerId();

		IWarChessManager warChessManager = human.getWarChessManager();
		SCWarChessSkipRound scWarChessSkipRound = warChessManager.warChessSkipRound(playerId);
		if (scWarChessSkipRound != null){
			human.push2Gateway(scWarChessSkipRound);
		}else{
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessSkipRound.getCode(),
					ErrorCodeConstants.WAR_CHESS_AUTH_ERR);
		}
	}

	public void csWarChessBuyHp(CSWarChessBuyHp msg, Human human) {
		int heroId = msg.getHeroId();
		IWarChessManager warChessManager = human.getWarChessManager();
		int times = warChessManager.getCurBuyHpTimes();
		// 校验是否有此英雄
		HeroModel model = human.getHeroManager().getHeroByHeroId(heroId);
		if (model == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessBuyHp.getCode(),
				ErrorCodeConstants.WAR_CHESS_HERO_NOT_EXIST);
			return;
		}
		// 校验是否是满血
		WarChessHeroHp heroHP = warChessManager.getCurHeroHpByHeroId(heroId);
		if (heroHP.getPer() >= 1.0d) {
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessBuyHp.getCode(),
				ErrorCodeConstants.WAR_CHESS_BUY_HP_MAX);
			return;
		}
		// 取消耗
		String cost = DictWarChessHeroBuyHP.getPriceByTimes(times + 1);
		if (cost == null) {
			// 最大上限
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessBuyHp.getCode(),
				ErrorCodeConstants.WAR_CHESS_BUY_HP_TIMES);
			return;
		}
		List<ItemBase> costItems = ItemBaseTools.convertFromString(cost);
		if (!CommonService.settle(PrivPSEnum.CSWarChessBuyHp, human, costItems, null)) {
			// 最大上限
			human.pushErrorMessageToClient(PrivPSEnum.SCWarChessBuyHp.getCode(),
				ErrorCodeConstants.WAR_CHESS_BUY_HP_COST);
			return;
		}
		// 加次数
		warChessManager.updateHeroHp(-1, times + 1);
		// 加血
		warChessManager.updateHeroHp(heroId, 1.0d);
		SCWarChessBuyHp sc = new SCWarChessBuyHp();
		sc.setHeroId(heroId);
		human.push2Gateway(sc);
	}
	
	private static List<HeroPropEnum> fightHeroInfoPropEnum = new ArrayList<>();
	
	static{
		fightHeroInfoPropEnum.add(HeroPropEnum.maxHp);
		fightHeroInfoPropEnum.add(HeroPropEnum.def);
		fightHeroInfoPropEnum.add(HeroPropEnum.atk);
		fightHeroInfoPropEnum.add(HeroPropEnum.cri);
		fightHeroInfoPropEnum.add(HeroPropEnum.hit);
		fightHeroInfoPropEnum.add(HeroPropEnum.anticri);
		fightHeroInfoPropEnum.add(HeroPropEnum.dodge);
	}

	public void CSWarChessGetFightHeroInfo(CSWarChessGetFightHeroInfo msg, Human human) {
		IWarChessManager warChessManager = human.getWarChessManager();
		WarChessData warChessData = warChessManager.getPlayerWarChessData().getWarChessData();
		long guid = msg.getGuid();
		if(warChessData != null){
			Fight fight = warChessData.getWarChessFightPlay().getFight();
			if(fight != null){
				EFightUnit eFightUnit = fight.getComputeCore().engineData.getEFightUnit(guid);
				if(eFightUnit != null){
					List<PropValue> pvs = new ArrayList<>();
					SCWarChessGetFightHeroInfo ret = new SCWarChessGetFightHeroInfo(guid, eFightUnit.heroId, pvs);
					for (HeroPropEnum heroPropEnum : fightHeroInfoPropEnum) {
						double value = eFightUnit.unitProp.getPropByPropId(heroPropEnum);
						pvs.add(new PropValue(heroPropEnum.value(), value));
					}
					human.push2Gateway(ret);
					return;
				}
			}
		}
		//出问题情况都报错
		human.push2Gateway(new SCWarChessGetFightHeroInfo(guid, 0, new ArrayList<PropValue>()));
	}
}
