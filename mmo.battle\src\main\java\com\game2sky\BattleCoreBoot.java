package com.game2sky;

import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.IPSEnum;
import com.game2sky.publib.communication.common.ErrorMessage;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossServerManager;

import io.netty.channel.Channel;

/**
 * TODO 战斗服CoreBoot
 *
 * <AUTHOR>
 * @version v0.1 2017-11-8 下午3:16:39  guojijun
 */
public final class BattleCoreBoot extends CoreBoot {

	private BattleCoreBoot() {
	}

	public static String getQuanfuServerFlag() {
		return CrossServerManager.getDefaultServerFlag(ServerTypeEnum.QUANFU);
	}

	/**
	 * 向中心服推送消息
	 * 
	 * @param data
	 */
	public static boolean pushToCenter(APSHandler data) {
		return pushToCenter(data, 0, 0);
	}

	/**
	 * 向中心服推送消息
	 * 
	 * @param data
	 * @param playerId
	 * @param serverId
	 */
	public static boolean pushToCenter(APSHandler data, long playerId, int serverId) {
		return dispatch2Server(data, playerId, serverId, getQuanfuServerFlag());
	}

//	/**
//	 * 获取指定serverId的Channel
//	 * 
//	 * @param serverId
//	 * @return
//	 */
//	public static Channel getBenfuChannel(int serverId) {
//		ServerConnectManager serverCenter = NetConnectCenter.getInstance().getServerCenter();
//		ServerConnect serverConnect = serverCenter.getServerConnect(serverId);
//		if (serverConnect == null) {
//			AMLog.LOG_COMMON.warn("没有找到对应的serverConnect,serverId={}", serverId);
//			return null;
//		}
//		Channel channel = serverConnect.randomChannel();
//		if (channel == null || !channel.isActive()) {
//			AMLog.LOG_COMMON.warn("没有获取到channel,serverId={}", serverId);
//			return null;
//		}
//		return channel;
//	}

	/**
	 * 向本服推送消息
	 * 
	 * @param data
	 * @param serverId
	 */
	public static void pushToBenfu(APSHandler data, int serverId) {
		pushToBenfu(null, data, serverId, 0);
	}

	/**
	 * 向本服推送消息
	 * 
	 * @param channel
	 * @param data
	 * @param serverId
	 */
	public static void pushToBenfu(Channel channel, APSHandler data, int serverId) {
		pushToBenfu(channel, data, serverId, 0);
	}

	/**
	 *  向本服指定玩家推送消息
	 * 
	 * @param data
	 * @param playerId
	 * @param serverId
	 */
	public static void pushToBenfu(APSHandler data, long playerId, int serverId) {
		pushToBenfu(null, data, serverId, playerId);
	}

	/**
	 * 向本服指定玩家推送消息
	 * 
	 * @param channel
	 * @param data
	 * @param serverId
	 * @param playerId
	 */
	public static void pushToBenfu(Channel channel, APSHandler data, int serverId, long playerId) {
		if (data == null) {
			return;
		}
		if (channel == null || !channel.isActive()) {
			channel = getBenfuChannel(serverId);
			if (channel == null) {
				AMLog.LOG_ERROR.warn("没有找到channel,serverId={},playerId={},data={}", serverId, playerId, data);
				return;
			}
		}
		Message message = Message.buildByServerId(playerId, serverId, data, null, null, null);
		channel.writeAndFlush(message);
	}

	/**
	 * 推送错误消息给玩家
	 * 
	 * @param channel
	 * @param serverId
	 * @param playerId
	 * @param psEnum
	 * @param errorCode
	 * @param arguments
	 */
	public static void pushErrorMsgToClient(Channel channel, int serverId, long playerId, IPSEnum psEnum, int errorCode,
											Object... arguments) {
		ErrorMessage data = BaseService.getErrorMessage(psEnum.getCode(), errorCode, arguments);
		pushToBenfu(channel, data, serverId, playerId);
	}

//	/**
//	 * 向本服的多个玩家推送消息(内部会产生新的玩家ID集合)
//	 * 
//	 * @param channel
//	 * @param data
//	 * @param serverId
//	 * @param playerIds
//	 */
//	public static void pushToClients(Channel channel, APSHandler data, int serverId, Collection<Long> playerIds) {
//		if (data == null || CollectionUtils.isEmpty(playerIds)) {
//			return;
//		}
//		OpDefine opDefine = AMFrameWorkBoot.OP_DEFINE_MAP.get(data.getClass());
//		if (opDefine == null) {
//			AMLog.LOG_COMMON.warn("没有找到对应的opDefine,className={}", data.getClass().getName());
//			return;
//		}
//		if (channel == null || !channel.isActive()) {
//			channel = getBenfuChannel(serverId);
//			if (channel == null) {
//				AMLog.LOG_COMMON.warn("没有找到channel,serverId={},playerIds={},data={}", serverId, playerIds, data);
//				return;
//			}
//		}
//		byte[] bs = SerializationUtil.serialize(data);
//		ArrayList<Long> list = new ArrayList<>(playerIds);
//		SSMsgForward ssMsgToBenfu = new SSMsgForward(serverId, opDefine.code(), bs, list);
//		Message message = Message.buildByServerId(0L, 0, ssMsgToBenfu, null, null, null);
//		channel.writeAndFlush(message);
//	}

}
