package com.game2sky.prilib.socket.logic.integral.ai;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.communication.game.integralArena.IntegralCenterAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 *  玩家的AI信息
 * <AUTHOR>
 * @version v0.1 2020年9月15日 下午7:51:24  zhoufang
 */
public class IntegralPlayerAIModel {
	
	private final long playerId;
//	private final int serverId;
	private final Map<Integer, AreaPlayerInfo> areaInfo = new HashMap<Integer, AreaPlayerInfo>();

	public IntegralPlayerAIModel(IntegralCenterPlayerInfo playerInfo) {
		this.playerId = playerInfo.getPlayerId();
//		this.serverId = playerInfo.getServerId();
		List<IntegralCenterAreaPlayerInfo> areaInfos = playerInfo.getAreaInfos();
		for (IntegralCenterAreaPlayerInfo areaInfo : areaInfos) {
			AreaPlayerInfo api = new AreaPlayerInfo();
			api.areaType = areaInfo.getAreaType();
			api.fightPower = areaInfo.getFightPower();
			this.areaInfo.put(api.areaType, api);
		}
	}

	class AreaPlayerInfo {
		public int areaType;
		public volatile long fightPower;
//		private LinkedList<Long> dayRobotIds = new LinkedList<Long>();
//		private long nextRefreshTime;
	}

	public void addAreaInfo(IntegralCenterPlayerInfo playerInfo) {
		List<IntegralCenterAreaPlayerInfo> areaInfos = playerInfo.getAreaInfos();
		for (IntegralCenterAreaPlayerInfo areaInfo : areaInfos) {
			if (this.areaInfo.containsKey(areaInfo.getAreaType())) {
				continue;
			}
			AreaPlayerInfo api = new AreaPlayerInfo();
			api.areaType = areaInfo.getAreaType();
			api.fightPower = areaInfo.getFightPower();
			this.areaInfo.put(api.areaType, api);
		}
	}

	public void refreshAreaFightPower(int areaType, long fightPower) {
		AreaPlayerInfo areaPlayerInfo = this.areaInfo.get(areaType);
		areaPlayerInfo.fightPower = fightPower;
		AMLog.LOG_COMMON.info("Player{}In the Ceradine{}The combat power is changed to change to{}", playerId, areaType, fightPower);
	}

	public long getFightPower(int areaType) {
		AreaPlayerInfo areaPlayerInfo = this.areaInfo.get(areaType);
		return areaPlayerInfo == null ? 0 : areaPlayerInfo.fightPower;
	}

//	public void refreshMatchedRobot(int areaType) {
//		AreaPlayerInfo areaPlayerInfo = this.areaInfo.get(areaType);
//		if(System.currentTimeMillis() > areaPlayerInfo.nextRefreshTime){
//			areaPlayerInfo.dayRobotIds.clear();
//			areaPlayerInfo.nextRefreshTime = DateUtil.getTomorrowTime(0, 0);
//		}
//	}
//
//	public void recordMatchRobotRecord(int areaType, long robotId) {
//		AreaPlayerInfo areaPlayerInfo = this.areaInfo.get(areaType);
//		areaPlayerInfo.dayRobotIds.addLast(robotId);
//	}
//
//	public boolean hasMatchedRobotId(int areaType, long robotId) {
//		AreaPlayerInfo areaPlayerInfo = this.areaInfo.get(areaType);
//		return areaPlayerInfo.dayRobotIds.contains(robotId);
//	}
}
