package com.game2sky;


import org.apache.commons.lang.StringUtils;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.embedded.EmbeddedMongoAutoConfiguration;
import org.springframework.core.env.SimpleCommandLinePropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.alibaba.fastjson.JSON;

import com.game2sky.prilib.core.socket.logic.integralCommon.thread.IntegralThreadStart;
import com.game2sky.prilib.socket.logic.autochess.AutoChessBattleStart;
import com.game2sky.prilib.socket.logic.integral.thread.IntegralBattlThreadGroup;
import com.game2sky.prilib.socket.logic.ladderwar.LadderWarBattleStart;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFBattleMessageProcessor;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.http.AMHttp;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.common.system.AMSystem;
import com.game2sky.publib.framework.constant.Errors;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.web.WebApi;

/**
 * TODO Starting the battle service process
 *
 * <AUTHOR>
 * @version v0.1 2017-11-1 ??????11:05:36  guojijun
 */
@EnableScheduling
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, //
									MongoDataAutoConfiguration.class, //
									MongoRepositoriesAutoConfiguration.class, //
									EmbeddedMongoAutoConfiguration.class, //
									MongoAutoConfiguration.class //
})
public class MainOpBattle {
	static {
		System.setProperty("Log4jContextSelector", "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
	}

//	/**Server ID*/
//	private static int zoneId;

	public static void main(String[] args) throws Exception {
		try {

			CoreBoot.SELF_PORT = 13300;
			
			// SimpleCommandLinePropertySource simpleCommandLinePropertySource = new SimpleCommandLinePropertySource(args);
			// String property = simpleCommandLinePropertySource.getProperty("netty.server.port");
			// if (StringUtils.isBlank(property)) {
			// 	CoreBoot.SELF_PORT = 13300;
			// } else {
			// 	CoreBoot.SELF_PORT = Integer.valueOf(property);
			// }
			// AMFrameWorkBoot.SELF_PORT = CoreBoot.SELF_PORT;

			CoreBoot.bootSrping(MainOpBattle.class, args);
			CoreBoot.bootLocalConfig();
	
			AMLog.stdout("init.battle.start.config...");
			// nhan2k3 CoreBoot.SELF_IP = AMSystem.getNWIP();
			CoreBoot.SELF_IP = "*************";

			CoreBoot.SELF_SERVER_TYPE = ServerTypeEnum.BATTLE;
	
			CoreBoot.boot(MainOpBattle.class, args);
	
	//		initConfig();
			
			// Start the ladder thread
			IntegralThreadStart.start(new IntegralBattlThreadGroup());
			new USFBattleMessageProcessor().startUp();
			LadderWarBattleStart.start();
			AutoChessBattleStart.start();
			
			// Connection center service
			AMLog.stdout("init.center.server ...");
			CrossServerManager.init(CoreBoot.initConfig.getCenterConfig(), true);
	
			final long now = System.currentTimeMillis();
			Globals.getScheduleService().scheduleWithFixedDelay(new ScheduledLoadReporter(now),
				ScheduledLoadReporter.SCHEDULED_DELAY, ScheduledLoadReporter.SCHEDULED_PERIOD);
			
			AMLog.stdout("init.netty.server...");
			CoreBoot.getBean(CoreBoot.class).initServer(); // Initialization and Server link
			
			AMFrameWorkBoot.START_SUCCESS = true;
			
			AMLog.stdout("#" + CoreBoot.SELF_SERVER_TYPE + " start!");
		
		} catch(Throwable t) {
			AMLog.LOG_STDOUT.error(Errors.SERVER_INIT_FAIL + "#" + CoreBoot.SELF_SERVER_TYPE + ".main", t);
			AMLog.stdout("#" + CoreBoot.SELF_SERVER_TYPE + " start error. error log info logs/stdout/stdout.log!");
		}
	}

//	private static void initConfig() throws HttpException, IOException {
//		GatewayInitConfig initConfig = wgetConfig();
//		initGatewayInitConfig(initConfig);
//	}
//
//	private static void initGatewayInitConfig(GatewayInitConfig initConfig) {
//		for (String sid : initConfig.getRedisDataSourceConfig().keySet()) {
//			zoneId = Integer.parseInt(sid);
//			break;
//		}
//		CrossServerManager.init(initConfig.getQuanfuSceneConfigs(), true);
//		
//		if(!CrossServerManager.existCrossServer(ServerTypeEnum.QUANFU)) {
//			AMLog.stdout("????????????????????????", new RuntimeException());
//			System.exit(1);
//		}
//	}
//
//	private static GatewayInitConfig wgetConfig() throws HttpException, IOException {
//		StringBuilder url = new StringBuilder(AMFrameWorkBoot.conf.getString(CoreBoot.CHANNEL_GROUP.getCenterConfigUrl()));
//		url.append(WebApi.GET_GATEWAY_INIT_CONFIG);
//		url.append("?outIp=").append(AMFrameWorkBoot.SELF_IP);
//		url.append("&port=").append(AMFrameWorkBoot.SELF_PORT);
//		String httpPost = AMHttp.httpGet(url.toString());
//		GatewayInitConfig initConfig = JSON.parseObject(httpPost, GatewayInitConfig.class);
//		return initConfig;
//	}


}
