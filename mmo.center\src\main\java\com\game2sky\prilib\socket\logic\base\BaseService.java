package com.game2sky.prilib.socket.logic.base;

import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.game.server.inner.SSMessageBenfuToBenfuPassCenter;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.protostuf.OpDefine;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年6月5日 下午4:25:30  daixl
 */
@Service
public class BaseService {

	public void sendMessageFromBenfuToBenfu(SSMessageBenfuToBenfuPassCenter ss) {
		byte[] msg = ss.getMsg();
		if (msg == null) {
			return;
		}
		int code = ss.getCode();
		OpDefine opDefine = AMFrameWorkBoot.OP_DEFINE_MAP.get(code);
		if (opDefine == null) {
			AMLog.LOG_COMMON.warn("没有找到对应的opDefine,code={}", code);
			return;
		}
		APSHandler body = (APSHandler) SerializationUtil.deserialize(msg, msg.length, opDefine.getPs().getClazz());
		CoreBoot.centerToBenfu(body, 0, ss.getServerId());
	}

}
