package com.game2sky.prilib.socket.logic.integral.match;

import java.util.ArrayList;
import java.util.List;

import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.dict.domain.DictIntegralMirror;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.KV;
import com.game2sky.publib.match.constants.MatchObjectType;
import com.game2sky.publib.match.model.MatchObject;

/**
 * 跨服赛匹配池中的玩家数据
 * <AUTHOR>
 * @version v0.1 2017年5月24日 下午3:46:57  zhoufang
 */
public class IntegralMatchPlayer extends MatchObject {

	private int areaType;

	/** 在匹配池已扩展区间 */
	private int matchExpandArea;

	/** 可匹配的积分范围  */
	private int before;
	private int after;

	private int serverId;

	// 超时的时间
	private long timeOutTime;

	private DictIntegralArenaArea dict;

	private boolean isRobot;

	private DictIntegralMirror dictMirror;
	private List<Integer> matchSeconds;

	public IntegralMatchPlayer(long guid, int integral, int serverId, boolean isRobot) {
		super(guid, integral);
		objType = MatchObjectType.PLAYER;

		this.timeOutTime = this.joinTime + 1800000L;
		this.serverId = serverId;
		this.isRobot = isRobot;
	}

	public boolean isRobot() {
		return isRobot;
	}

	/**
	 * 扩展匹配范围
	 * @param now
	 * @param expandTime
	 * @return 返回是否已到达最大匹配范围
	 */
	public boolean addExpandArea(long now, Long[] expandTime) {
		if (this.matchExpandArea >= expandTime.length) {
			return true;
		}
		long periodTime = now - this.getJoinTime();
		for (int i = this.matchExpandArea; i < expandTime.length; i++) {
			if (periodTime >= expandTime[i]) {
				this.matchExpandArea++;
				this.after += dict.getExpandIntegral();
				this.before -= dict.getExpandIntegral();
				break;
			}
		}
		return false;
	}

	public boolean canFight(IntegralMatchPlayer other) {
		int otherIntegral = other.getIntegral();
		return otherIntegral >= before && otherIntegral <= after;
	}

	public boolean isTimeout(long time) {
		return time >= timeOutTime;
	}

	public long getPlayerId() {
		return this.getGuid();
	}

	public int getAreaType() {
		return areaType;
	}

	public void setAreaType(int areaType) {
		this.areaType = areaType;
		this.dict = IntegralConfigCenter.getDictIntegralArenaArea(this.areaType);
		this.dictMirror = DictIntegralMirror.getDictMirror(areaType, this.integral);
		if (dictMirror != null) {
			int size = dictMirror.getTimeRateList().size();
			this.matchSeconds = new ArrayList<Integer>(size);
		}else {
			AMLog.LOG_COMMON.info("玩家[{}]开始在赛区[{}]匹配, 由于当前积分为[{}], 无法分配机器人",this.guid,this.areaType,this.integral);
		}
	}

	/**
	 * 能否匹配机器人
	 * @return
	 */
	public boolean canMatchRobot() {
		if (this.dictMirror != null) {
			List<KV<Integer, Integer>> timeRateList = dictMirror.getTimeRateList();
			return timeRateList != null && !timeRateList.isEmpty();
		}
		return false;
	}

	public DictIntegralMirror getDictMirror() {
		return dictMirror;
	}
	
	public List<Integer> getMatchSeconds() {
		return matchSeconds;
	}

	public int getMatchExpandArea() {
		return matchExpandArea;
	}

	public void setMatchExpandArea(int matchExpandArea) {
		this.matchExpandArea = matchExpandArea;
	}

	public int getBefore() {
		return before;
	}

	public void setBefore(int before) {
		this.before = before;
	}

	public int getAfter() {
		return after;
	}

	public void setAfter(int after) {
		this.after = after;
	}

	public void setMatchReadyTime(long matchReadyTime) {
		super.setSuccessTime(matchReadyTime);
	}

	public long getMatchReadyTime() {
		return super.getSuccessTime();
	}

	public int getServerId() {
		return serverId;
	}
}
