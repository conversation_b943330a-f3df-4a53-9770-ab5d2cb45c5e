package com.game2sky.prilib.benfu.logic.integral.task;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.integralArena.IntegralTaskCondition;
import com.game2sky.prilib.communication.game.integralArena.SSCreateIntegralRobotReq;
import com.game2sky.prilib.communication.game.integralArena.SSStartIntegralFightRes;
import com.game2sky.prilib.communication.game.player.CommonFormationType;
import com.game2sky.prilib.communication.game.player.FormationTypeEnum;
import com.game2sky.prilib.communication.game.player.PlayerFormation;
import com.game2sky.prilib.communication.game.player.PlayerInformation;
import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.commonformation.CommonFightDataModel;
import com.game2sky.prilib.core.socket.logic.fight.manager.CommonFightManager;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.integral.IntegralGameTmpData;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.player.PlayerCacheManager;
import com.game2sky.publib.Globals;
import com.game2sky.publib.async.IIoOperation;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.socket.logic.base.IdTools;

/**
 *
 * <AUTHOR>
 * @version v0.1 2019年7月30日 下午3:49:18  zhoufang
 */
public class CreateIntegralRobotTask implements IIoOperation {

	public int serverId;
	public SSCreateIntegralRobotReq msg;

	public CreateIntegralRobotTask(int serverId, SSCreateIntegralRobotReq msg) {
		this.serverId = serverId;
		this.msg = msg;
	}

	@Override
	public int doStart() {
		return IIoOperation.STAGE_START_DONE;
	}

	@Override
	public int doIo() {
		int zoneId = Globals.getZoneId(serverId);
		DictIntegralArenaArea dict = IntegralConfigCenter.getDictIntegralArenaArea(msg.getAreaType());
		PlayerFormation playerFormation = null;
		FFightSide fightSide = null;
		PlayerDisplayInformation displayInformation = null;
		long copyPlayerId = msg.getCopyPlayerId();
		try {
			Human robotHuman = (Human) Globals.getHumanByServerId(serverId, copyPlayerId);
			if (robotHuman != null) {
				// 被选中的玩家恰好在线
				displayInformation = robotHuman.getDisPlayerInformation();
				if (IntegralAreaType.isOldVersion(msg.getAreaType())) {
					// 旧天梯取当前阵容数据
					boolean withEquip = dict.getForbidEquipment() == 0;// 0表示需要计算装备属性
					// 传输战斗数据
					playerFormation = robotHuman.getFormationManager().getFormationDataModel()
						.getCurrentFormationByType(FormationTypeEnum.SOLO);
					long shipGuid = robotHuman.getShipSystemManager().getCurrentFightShipGuid();
					fightSide = robotHuman.getCommonFormationDataManager().getFFightSide(playerFormation, null,
						shipGuid, msg.getCamp(), withEquip);
				}
				if (IntegralAreaType.isNewVersion(msg.getAreaType())) {
					CommonFormationType formationType = msg.getAreaType() == IntegralAreaType.PRACTICE.value() ? CommonFormationType.INTEGRAL_PRACTICE_FORMATION
						: CommonFormationType.INTEGRAL_PINNACLE_FORMATION;
					IntegralGameTmpData tmpData = robotHuman.getIntegralManager().getTmpData();
					playerFormation = tmpData.getPlayerFormation(formationType);
					fightSide = tmpData.getFFightSide(formationType);
					fightSide.refreshCamp(msg.getCamp());
				}
			} else {
				PlayerInformation playerInformation = PlayerCacheManager.getPlayerInFormationFromRedis(zoneId,
					copyPlayerId);
				if (playerInformation == null) {
					playerInformation = PlayerCacheManager.loadPlayerInformationFromDB(serverId, copyPlayerId);
				}
				displayInformation = playerInformation.getDisPlay();
				if (IntegralAreaType.isOldVersion(msg.getAreaType())) {
					// 旧天梯取当前阵容数据
					playerFormation = playerInformation.getFormation();
					CommonFightDataModel fightModel = CommonFightManager.getFFightSideByType(copyPlayerId,
						zoneId, CommonFormationType.CURRENT_FORMATION);
					fightSide = fightModel.getFFightSide();
				}
				if (IntegralAreaType.isNewVersion(msg.getAreaType())) {
					CommonFormationType formationType = msg.getAreaType() == IntegralAreaType.PRACTICE.value() ? CommonFormationType.INTEGRAL_PRACTICE_FORMATION
						: CommonFormationType.INTEGRAL_PINNACLE_FORMATION;
					CommonFightDataModel rightData = CommonFightManager.getFFightSideByType(copyPlayerId, zoneId, formationType);
					fightSide = rightData.getFFightSide();
					fightSide.refreshCamp(msg.getCamp());
					playerFormation = fightSide.converToPlayerFormation();
				}
			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("天梯加载玩家战斗数据异常,playerId[{}],fightId[{}]", msg.getCopyPlayerId(), msg.getFightId(), e);
		}
		// 修改PLAYERID,随机角色名,隐藏公会名,随机区服Id
		SSStartIntegralFightRes resp = new SSStartIntegralFightRes();
		long robotId = IdTools.nextId();
		displayInformation.setPlayerId(robotId);
//		NickNameManager nickNameManager = Globals.getMemoryData(zoneId).getNickNameManager();
//		String nickName = nickNameManager.getRandomName(1);
//		String nickName = "机器人测试"+robotId;
		String nickName = displayInformation.getNickName();
		displayInformation.setNickName(nickName);
		displayInformation.setUnionName("");
		
		// 随机给到对方本大区中的serverId
		int randomServerId = randomServerId(msg.getOpponentServerId());
		displayInformation.setServerId(randomServerId);
		
		if (fightSide != null) {
			fightSide.changeRobot(robotId, msg.getCamp(), nickName, randomServerId);
			byte[] bytes = SerializationUtil.serialize(fightSide);
			resp.setFightData(bytes);
		}
		// 推送数据到战斗服
		resp.setIsRobot(true);
		resp.setAreaType(msg.getAreaType());
		resp.setFormation(playerFormation);
		resp.setFightId(msg.getFightId());
		resp.setDisplayInformation(displayInformation);
		resp.setIntegral(msg.getIntegral());
		// 验证玩家有没有带满装备
		resp.setEquipCheck(true);
		resp.setTaskConditions(new ArrayList<IntegralTaskCondition>());
		CoreBoot.dispatch2Server(resp, robotId, serverId, msg.getAddress().getFlag());
		return IIoOperation.STAGE_IO_DONE;
	}

	private int randomServerId(int serverId) {
		try {
			int branch = IntegralConfigCenter.getIntegralBranchByServerId(serverId);
			List<Integer> serverIdList = IntegralConfigCenter.getServerIdListByGroupId(branch);
			if (serverIdList.size() <= 1) {
				// 此分组只有一个服务器
				return serverId;
			}
			List<Integer> copyServerList = new ArrayList<Integer>(serverIdList);
			copyServerList.remove(Integer.valueOf(serverId));
			int index =ThreadLocalRandom.current().nextInt(copyServerList.size());
			return copyServerList.get(index);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("随机serverId[{}]错误", serverId, e);
			return serverId;
		}
	}

	@Override
	public int doStop() {
		return IIoOperation.STAGE_STOP_DONE;
	}

}
