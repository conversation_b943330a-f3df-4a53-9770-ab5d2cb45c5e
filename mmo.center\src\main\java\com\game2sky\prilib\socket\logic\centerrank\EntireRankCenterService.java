package com.game2sky.prilib.socket.logic.centerrank;

import org.springframework.stereotype.Service;

import com.game2sky.prilib.communication.game.entirerank.SSEntireRankClearRank;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerInfoToCenter;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerToCenter;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankReq;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Head;

/**
 * 通用全服排行中心服处理器.
 *
 * <AUTHOR> Lee
 * @version v0.1 2020年2月26日 下午3:41:35  Jet Lee
 */
@Service
public class EntireRankCenterService {
	
	public void init() {
		EntireRankCenterManager.getInstance().init();
		// 启动定时器
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleEntireRankCenterTick(),
			ScheduleEntireRankCenterTick.PERIOD, ScheduleEntireRankCenterTick.PERIOD);
	}

	public void ssEntireRankPlayerToCenter(SSEntireRankPlayerToCenter msg) {
		EntireRankCenterManager.getInstance().addPlayers(msg.getRankType() ,msg.getPlayers());
	}

	public void ssEntireRankReq(SSEntireRankReq msg, Head head) {
		EntireRankCenterManager.getInstance().sendTopRankToBenfu(head.getServerId(), msg.getRankType());
	}

	public void ssEntireRankPlayerRankReq(SSEntireRankPlayerRankReq msg) {
		EntireRankCenterManager.getInstance().sendPlayerRankToBenfu(msg);
	}

	public void ssEntireRankScoreRankReq(SSEntireRankScoreRankReq msg) {
		EntireRankCenterManager.getInstance().sendScoreRankToBenfu(msg);
	}

	public void ssEntireRankClearRank(SSEntireRankClearRank msg) {
		EntireRankCenterManager.getInstance().clearRank(msg.getRankType());
	}

	public void ssEntireRankPlayerInfoToCenter(SSEntireRankPlayerInfoToCenter msg) {
		EntireRankCenterManager.getInstance().updatePlayerInfo(msg.getPlayerChanges());
	}
}
