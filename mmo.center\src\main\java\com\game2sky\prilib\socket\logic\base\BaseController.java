package com.game2sky.prilib.socket.logic.base;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.game.server.inner.SSMessageBenfuToBenfuPassCenter;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年6月5日 下午4:25:30  daixl
 */
@Controller
public class BaseController {

	@Autowired
	BaseService baseService;

	@ProtoStuffMapping(value = PSEnum.SSMessageBenfuToBenfuPassCenter)
	public void sendMessageFromBenfuToBenfu(SSMessageBenfuToBenfuPassCenter msg) {
		baseService.sendMessageFromBenfuToBenfu(msg);
	}

}
