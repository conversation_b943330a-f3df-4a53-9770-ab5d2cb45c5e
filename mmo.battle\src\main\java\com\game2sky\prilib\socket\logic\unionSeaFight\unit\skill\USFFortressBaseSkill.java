package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import java.util.ArrayList;
import java.util.List;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffSelectType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;

/**
 * 据点基础技能
 *
 * <AUTHOR>
 * @version v0.1 2018年10月25日 上午8:18:33  lidong
 */
public class USFFortressBaseSkill extends USFSkill
{
	public USFFortressBaseSkill()
	{
		super();
		setTriggerCondition(new TriggerCondition(null));
		setSkillId(0);
		setTriggerType(USFBuffTriggerType.ENTER_TOWER);
		setCloseType(USFBuffTriggerType.FOREVER);
		setUnloadType(USFBuffTriggerType.LEAVE_TOWER);
		setSelectType(USFBuffSelectType.OPPOSITE);
	}

//	public void addBuff(USFBuff buff)
//	{
//		usfBuffers.add(buff);
//	}
//
//	public void removeBuff(USFBuff buff)
//	{
//		usfBuffers.remove(buff);
//	}
	
	@Override
	public List<USFBuff> getUsfBuffers()
	{
		return getOwner().getBuffController().getBuffs();
	}
}
