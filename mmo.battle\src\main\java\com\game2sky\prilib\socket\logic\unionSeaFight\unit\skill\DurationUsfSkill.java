package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffSelectType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.core.dict.domain.DictUsfSkill;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * 周期性技能
 *
 * <AUTHOR>
 * @version v0.1 2018年10月25日 上午9:22:12  lidong
 */
public class DurationUsfSkill extends USFSkill
{
	/** 是否激活，针对周期性技能设计 */
	protected boolean actived;
	/** 激活触发类型 */
	protected USFBuffTriggerType activeType;

	public void setData(DictUsfSkill dictUsfSkill)
	{
		super.setData(dictUsfSkill);
		activeType = USFBuffTriggerType.valueOf(dictUsfSkill.getActiveType());
		if (duration <= 0)
		{

			AMLog.LOG_ERROR.info("公会战技能配置错误 周期性技能：{} 周期时间：{} 太短", duration);
			duration = 10000;
		}
//		if(extraInfo != null && extraInfo != "")
//		{
//			duration = 3000;
//		}
		actived = false;
	}

	public void triggerEvent(USFBuffTriggerType triggerType, AbstractUSFUnit triggerUnit, AbstractUSFUnit oppositeUnit,
								Object... args)
	{
		if (triggerUnit == null)
		{
			return;
		}
		if (activeType == triggerType)
		{
//			if(!actived)
//			{
//				if(triggerUnit != null)
//				{
//					lastDurationTime = triggerUnit.getNow();
//				}
//			}
			actived = true;
			this.sourceUnit = triggerUnit;
			this.targetUnit = oppositeUnit;
			this.extraArgs = args;
		}
		 else if (this.closeType == triggerType)
		{
			actived = false;
		}
		if (actived && this.triggerType == triggerType)
		{
			this.sourceUnit = triggerUnit;
			this.targetUnit = oppositeUnit;
			this.extraArgs = args;
			doEffect(triggerUnit.getNow());
		}
	}

	public boolean isActived()
	{
		return actived;
	}

	public void setActived(boolean actived)
	{
		this.actived = actived;
	}
	
	@Override
	public void doDuration()
	{
		//  lastDurationTime = owner.getNow();
		doEffect(owner.getNow());
	}
	
	public boolean IsArriveDuration(long now)
	{
		return actived && now - lastDurationTime >= duration;
	}

}
