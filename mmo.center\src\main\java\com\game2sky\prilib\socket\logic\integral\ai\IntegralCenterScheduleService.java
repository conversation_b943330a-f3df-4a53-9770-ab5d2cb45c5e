package com.game2sky.prilib.socket.logic.integral.ai;

import com.game2sky.prilib.core.socket.logic.integralCommon.IIntegralScheduleService;
import com.game2sky.prilib.socket.logic.integral.data.IntegralSnapshotLog;
import com.game2sky.publib.log.LogHelper;

/**
 *
 * <AUTHOR>
 * @version v0.1 2020年9月12日 下午5:46:00  zhoufang
 */
public class IntegralCenterScheduleService implements IIntegralScheduleService{

	@Override
	public void doSomethingAtMidnight() {
		
		IntegralAICenter.incrementServerDay();
		
		LogHelper.logBI(new IntegralSnapshotLog());
	}
	
}
