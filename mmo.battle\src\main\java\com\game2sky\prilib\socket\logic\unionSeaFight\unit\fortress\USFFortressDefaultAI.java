package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress;

import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;

/**
 * TODO 据点默认AI
 *
 * <AUTHOR>
 * @version v0.1 2018年10月25日 下午2:41:52  guojijun
 */
class USFFortressDefaultAI implements IUSFFortressAI {

	private USFFortress fortress;

	USFFortressDefaultAI(USFFortress fortress) {
		this.fortress = fortress;
	}

	@Override
	public void tick(long now) {
		this.fortress.promotionTick(now);
	}

	@Override
	public void onAddUnit(IUSFUnitFightable unit) {
	}

	@Override
	public void onRemoveUnit(IUSFUnitFightable unit) {
	}

}
