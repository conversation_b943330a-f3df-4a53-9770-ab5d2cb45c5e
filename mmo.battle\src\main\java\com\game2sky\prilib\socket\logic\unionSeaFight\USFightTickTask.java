package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFTick;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年7月4日 下午5:51:56  guojijun
 */
public class USFightTickTask extends ScheduledMessage {

	public USFightTickTask(long createTime) {
		super(createTime);
	}

	@Override
	public void execute() {
		SSUSFTick req = new SSUSFTick();
		Message msg = Message.buildByServerId(0L, 0, req, null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
	}

}
