//package com.game2sky.publib.benfu.scene.logic.controller;
//
//import java.io.IOException;
//
//import org.apache.commons.httpclient.HttpException;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.servlet.ModelAndView;
//
//import com.game2sky.MainOpBenfu;
//import com.game2sky.publib.framework.engine.config.GatewayInitConfig;
//import com.game2sky.publib.framework.web.base.BaseController;
//
///**
// * TODO ADD FUNCTION_DESC.
// *
// * <AUTHOR>
// * @version v0.1 2017年3月31日 下午3:35:30  amyn
// */
//@Controller
//public class GatewaySelfController extends BaseController {
//
//	@RequestMapping(path="reload.scene")
//	public ModelAndView reloadScene() throws HttpException, IOException {
//		GatewayInitConfig wgetConfig = MainOpBenfu.wgetConfig();
//		MainOpBenfu.initGatewayInitConfig(wgetConfig);
//		return returnString("success");
//	}
//
//}
