package com.game2sky.prilib.socket.logic.homeland.matchv2;

import com.game2sky.prilib.socket.logic.integral.sort.Barrel;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 上午11:24:04  daixl
 */
@Deprecated
public class HomeLandCreamMatchBarrel extends Barrel {
//
//	private final static Long BASE_TIME = 1594396800000L;
//
////	private static final int COL_CAPACITY = 16;
//
//	// 暂时使用linkedList
//	private LinkedList<Long> playerIds = new LinkedList<Long>();
//
////	private HashMap<Integer, LinkedList<Long>> playerIdCol = new HashMap<Integer, LinkedList<Long>>(COL_CAPACITY, 1f);
//
//	public HomeLandCreamMatchBarrel(Long id) {
//		this.id = id;
//	}
//
//	public void addPlayerId(long playerId) {
//		this.playerIds.add(playerId);
//		this.haveNumber = playerIds.size();
//	}
//
//	public void removePlayerId(Long playerId) {
//		this.playerIds.remove(playerId);
//		this.haveNumber = playerIds.size();
//	}
//
//	public List<Long> getPlayerIds() {
//		return playerIds;
//	}
//
//	public List<Long> randomPlayerIds(Set<Long> excludeIds, int num) {
//		long now = Globals.getTimeService().now();
//		List<Long> result = new ArrayList<Long>();
//		int size = playerIds.size();
//		if (size == 0) {
//			return result;
//		}
//
//		if (num >= size) {
//			for (Long playerId : playerIds) {
//				HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
//				long nextMatchTime = info.getNextMatchTime();
//				if (nextMatchTime > now) {
//					continue;
//				}
//				if (!excludeIds.contains(playerId)) {
//					result.add(playerId);
//				}
//			}
//			return result;
//		}
//
//		ThreadLocalRandom random = ThreadLocalRandom.current();
//		for (int i = 0; i < num; i++) {
//			for (int retry = 0; retry < 5; retry++) {
//				int index = random.nextInt(size);
//				Long playerId = playerIds.get(index);
//				if (excludeIds.contains(playerId)) {
//					continue;
//				}
//				HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
//				long nextMatchTime = info.getNextMatchTime();
//				if (nextMatchTime > now) {
//					continue;
//				}
//				result.add(playerId);
//				excludeIds.add(playerId);
//				break;
//			}
//		}
//		return result;
//	}
//
//	public List<Long> randomPlayerIdsV2(Set<Long> excludeIds, int num) {
//		List<Long> result = new ArrayList<>();
//		List<Long> removeIds = new ArrayList<Long>();
//		long now = System.currentTimeMillis();
//		int maxBeRobTimes = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_MAX_BE_ROBED_TIMES);
//		Iterator<Long> iterator = playerIds.iterator();
//		while (iterator.hasNext()) {
//			Long playerId = iterator.next();
//			if (excludeIds.contains(playerId)) {
//				continue;
//			}
//			HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
//			long nextMatchTime = info.getNextMatchTime();
//			if (nextMatchTime > now) {
//				continue;
//			}
//			int beRobTimes = info.getBeRobTimes();
//			if (beRobTimes >= maxBeRobTimes) {
//				boolean checkIsSameDay = checkIsSameDay(now, info.getLastUpdateTime());
//				if (!checkIsSameDay) {
//					info.setBeRobTimes(0);
//					info.setLastUpdateTime(now);
//				} else {
//					iterator.remove();
//					removeIds.add(playerId);
//					if (removeIds.size() > 5) {
//						break;
//					}
//					continue;
//				}
//			}
//			result.add(playerId);
//			excludeIds.add(playerId);
//			iterator.remove();
//			removeIds.add(playerId);
//			if (removeIds.size() > 5) {
//				break;
//			}
//			if (result.size() >= num) {
//				break;
//			}
//		}
//
//		if (removeIds.size() > 0) {
//			long configValue = (Long) BaseService.getConfigValue(PriConfigKeyName.HOMELAND_REFRESH_PROTECT_TIME);
//			configValue = TimeUnit.SECONDS.toMillis(configValue);
//			now += configValue;
//			for (Long playerId : removeIds) {
//				HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
//				info.setNextMatchTime(now);
//				playerIds.addLast(playerId);
//			}
//		}
//		return result;
//	}
//
//	private static boolean checkIsSameDay(long time1, long time2) {
//		long peroid1 = time1 - BASE_TIME;
//		long peroid2 = time2 - BASE_TIME;
//
//		return ((peroid1 / TimeUtils.DAY) == (peroid2 / TimeUtils.DAY));
//	}
}
