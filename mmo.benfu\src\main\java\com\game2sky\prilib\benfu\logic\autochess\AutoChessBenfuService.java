package com.game2sky.prilib.benfu.logic.autochess;

import com.game2sky.prilib.core.socket.logic.autochess.listener.IAutoChessEventSource;
import com.game2sky.publib.event.EventSourceManager;

import io.netty.channel.Channel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.autochess.AutoChessMatchState;
import com.game2sky.prilib.communication.game.autochess.AutoChessPlayerFashionInfo;
import com.game2sky.prilib.communication.game.autochess.AutoChessPlayerState;
import com.game2sky.prilib.communication.game.autochess.AutoChessPlayerSyncInfo;
import com.game2sky.prilib.communication.game.autochess.AutochessExtraPackInfo;
import com.game2sky.prilib.communication.game.autochess.AutochessMapShopInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessBuyPacket;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessChooseShip;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessGetExtraPackInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessGetMapShopInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessInfo;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessPlayerEnter;
import com.game2sky.prilib.communication.game.autochess.CSAutoChessUseMap;
import com.game2sky.prilib.communication.game.autochess.CSCancelAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.CSGetAutoChessTaskReward;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessBuyPacket;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessChooseShip;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessGetExtraPackInfo;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessGetMapShopInfo;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessInfo;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessMatchSucess;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessPlayerEnter;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessPlayerQuit;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessScoreChange;
import com.game2sky.prilib.communication.game.autochess.SCAutoChessUseMap;
import com.game2sky.prilib.communication.game.autochess.SCCancelAutoChessMatch;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessBaseReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessCancelMatch;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessConnectBattle;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessCreateFight;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessMatchStateChange;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerEnterReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerEnterRes;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerOffline;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerQuit;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankRes;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerStateChange;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessReconnectReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessSettlement;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessStartFight;
import com.game2sky.prilib.communication.game.autochess.SSRefreshAutoChessRank;
import com.game2sky.prilib.communication.game.autochess.SSRefreshGameAutoChessBase;
import com.game2sky.prilib.communication.game.autochess.SSSendTaskDataToBenfu;
import com.game2sky.prilib.communication.game.struct.ItemBaseType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictAutoChessExtraHeroPack;
import com.game2sky.prilib.core.dict.domain.DictAutoChessGrade;
import com.game2sky.prilib.core.dict.domain.DictAutoChessHero;
import com.game2sky.prilib.core.dict.domain.DictAutoChessMapShop;
import com.game2sky.prilib.core.dict.domain.DictHeroFashion;
import com.game2sky.prilib.core.socket.logic.autochess.AbstractAutoChessService;
import com.game2sky.prilib.core.socket.logic.autochess.AutoChessBenfuManager;
import com.game2sky.prilib.core.socket.logic.autochess.AutoChessConstans;
import com.game2sky.prilib.core.socket.logic.autochess.AutoChessPlayerMatch;
import com.game2sky.prilib.core.socket.logic.autochess.IAutoChessHumanManager;
import com.game2sky.prilib.core.socket.logic.autochess.PlayerAutoChessModel;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.base.CommonService;
import com.game2sky.prilib.core.socket.logic.hero.HeroModel;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.human.event.INoticePlayerLoginSuccessEventListener;
import com.game2sky.prilib.core.socket.logic.human.event.INoticePlayerLoginSuccessEventSource;
import com.game2sky.prilib.core.socket.logic.human.state.ActivityState;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneCommonService;
import com.game2sky.prilib.core.socket.logic.scene.unit.component.state.RoleStateManager;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.struct.ItemBase;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossBattleTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveListener;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveSource;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventLinstener;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;
import com.game2sky.publib.framework.util.StringUtils;
import com.game2sky.publib.log.LogHelper;
import com.game2sky.publib.log.model.autochess.AutoChessEnter;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;
import com.game2sky.publib.util.DateUtil;

/**
 * 自走棋本服.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月9日 下午4:33:34  Jet Lee
 */
@Service
@EventSubscriber(eventSources = {	IClientBattleChannelInactiveSource.class, INoticePlayerLoginSuccessEventSource.class,
									ITargetServerRestartEventSource.class })
public class AutoChessBenfuService extends AbstractAutoChessService implements IClientBattleChannelInactiveListener,
									INoticePlayerLoginSuccessEventListener, ITargetServerRestartEventLinstener {

	@Override
	public void onTargetServerRestart(ServerTypeEnum serverType, Channel channel, int restartType) {
		if (serverType != ServerTypeEnum.QUANFU) {
			return;
		}
		AMLog.LOG_COMMON.info("[autochess] [link to center] [begin]");
		// 链接中心服，请求自走棋基本信息
		for (Integer zoneId : Globals.getAllZoneId()) {
			if (!SwitchConditionManager.checkMasterSwitch(PrivSwitchEnum.AUTO_CHESS, zoneId)) {
				return;
			}
		}
		SSAutoChessBaseReq req = new SSAutoChessBaseReq();
		CoreBoot.benfuToCenter(req, 0, CoreBoot.getAllServerId().get(0));
		AutoChessBenfuManager.getInstance().sendPlayersToCenter();
		AMLog.LOG_COMMON.info("[autochess] [link to center] [end]");
	}

	public void ssAutoChessCancelMatch(SSAutoChessCancelMatch msg) {
		// 取消匹配的机制做了修改，变成本服收到客户端取消匹配请求后，本服直接取消。然后通知中心服。所以此处的中心服回传消息不用处理了
//		long playerId = msg.getPlayerId();
//		int serverId = msg.getServerId();
//		int zoneId = Globals.getZoneId(serverId);
//		Human human = (Human) Globals.getHuman(zoneId, playerId);
//		int state = msg.getState();
//		if (state == CommonConstants.TRUE) {
		// 取消成功
//			cancelMatch(human);
//		} else {
//			if (human != null) {
//				human.pushErrorMessageToClient(PrivPSEnum.SCCancelAutoChessMatch.getCode(),
//					ErrorCodeConstants.AUTO_CHESS_CANCEL_MATCH_FAIL);
//				return;
//			}
//		}
	}

	public void csAutoChessInfo(CSAutoChessInfo msg, Human human) {
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS, human, PrivPSEnum.SCAutoChessInfo)) {
			return;
		}
		sendAutoChessInfo(human);
	}

	private void sendAutoChessInfo(Human human) {
		SCAutoChessInfo scMsg = new SCAutoChessInfo();
		AutoChessBenfuManager manager = AutoChessBenfuManager.getInstance();
		PlayerAutoChessModel playerAutoChess = manager.getPlayerAutoChess(human.getPlayerId(), human.getServerId());
		int grade = playerAutoChess.getGrade();
		scMsg.setGrade(grade);
		scMsg.setIsOpen(AutoChessConstans.isOpen(Globals.getTimeService().now()) ? 1 : 0);
		DictAutoChessGrade dictAutoChessGrade = DictAutoChessGrade.getDictAutoChessGrade(grade);
		int currentScore = 0;
		if (dictAutoChessGrade != null) {
			currentScore = playerAutoChess.getScore() - dictAutoChessGrade.getMinScore();
		}
		scMsg.setScore(currentScore);
		int seasonWeek = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_SEASON_WEEK);
		int week = manager.getBaseInfo().getWeek();
		scMsg.setSeasonEndTime(DateUtil.getIntervalWeek(seasonWeek - week + 1));
		long playerId = human.getPlayerId();
		long matchBeginTime = manager.getMatchBeginTime(playerId);
		scMsg.setStartMatchTime(matchBeginTime);
		scMsg.setState(manager.getMatchState(playerId));
		scMsg.setWeekFightCount(playerAutoChess.getWeekFightCount());
		scMsg.setWeekRewardCoin(playerAutoChess.getWeekRewardCoin());
		scMsg.setLastSeasonRanks(manager.getLastSeasonRanks());
		scMsg.setRankList(manager.getRankList());
		scMsg.setShipId(playerAutoChess.getShipId());
		scMsg.setRank(manager.getRank(playerId));

		int useMapId = human.getAutochessHumanManager().getUseMapId();
		scMsg.setMapId(useMapId);
		int isPackOpen = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_HERO_PACK_OPEN);
		scMsg.setIsHeroPackOpen(isPackOpen);
		human.push2Gateway(scMsg);
	}

	public void csAutoChessChooseShip(CSAutoChessChooseShip msg, Human human) {
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS, human, PrivPSEnum.SCAutoChessChooseShip)) {
			return;
		}
		int shipId = msg.getShipId();
		AutoChessBenfuManager manager = AutoChessBenfuManager.getInstance();
		PlayerAutoChessModel playerAutoChess = manager.getPlayerAutoChess(human.getPlayerId(), human.getServerId());
		if (shipId <= 0 || shipId == playerAutoChess.getShipId()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessChooseShip.getCode(), ErrorCodeConstants.SYSTEM_ERROR);
			return;
		}
		boolean own = human.getShipSystemManager().hasOwnShip(shipId);
		if (!own) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessChooseShip.getCode(),
				ErrorCodeConstants.AUTO_CHESS_NO_SHIP);
			return;
		}
		playerAutoChess.setShipId(shipId);
		manager.changeShip(playerAutoChess, shipId);
		SCAutoChessChooseShip scMsg = new SCAutoChessChooseShip(shipId);
		human.push2Gateway(scMsg);
	}

	public void csAutoChessMatch(CSAutoChessMatch msg, Human human) {
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS, human, PrivPSEnum.SCAutoChessMatch)) {
			return;
		}
		if (!CrossServerManager.isCrossServerConnected(ServerTypeEnum.QUANFU, human)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(),
				ErrorCodeConstants.AUTO_CHESS_KUAFU_NOT_CONNECT);
			return;
		}
		// 判断活动有没有开启
		boolean open = AutoChessConstans.isOpen(Globals.getTimeService().now());
		if (!open) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(), ErrorCodeConstants.SWITCH_OFF);
			return;
		}
		AutoChessMatchState matchState = AutoChessBenfuManager.getInstance().getMatchState(human.getPlayerId());
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (roleStateManager.containState(ActivityState.AUTO_CHESS_MATCHING)
			|| matchState == AutoChessMatchState.MATCH) {
			// 正在匹配中
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(),
				ErrorCodeConstants.AUTO_CHESS_MATCHING);
			return;
		}
		if (roleStateManager.containState(ActivityState.KUAFU_FIGHTING) || matchState == AutoChessMatchState.FIGHT) {
			// 正在战斗中
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(),
				ErrorCodeConstants.AUTO_CHESS_FIGHTING);
			return;
		}
		if (!human.getSceneObject().getRoleStateManager().canEnter(ActivityState.AUTO_CHESS_MATCHING)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(),
				ErrorCodeConstants.AUTO_CHESS_MATCH_STATE_ERROR);
			return;
		}
		long playerId = human.getPlayerId();
		// 通知中心服开始匹配
		AutoChessBenfuManager manager = AutoChessBenfuManager.getInstance();
		PlayerAutoChessModel playerAutoChess = manager.getPlayerAutoChess(human.getPlayerId(), human.getServerId());
		int grade = playerAutoChess.getGrade();
		int score = playerAutoChess.getScore();
		SSAutoChessMatchStateChange ssMsg = new SSAutoChessMatchStateChange(playerId, human.getServerId(), grade, score,
			AutoChessConstans.MATCH_STATE_ENTER);
		boolean success = CoreBoot.benfuToCenter(ssMsg, 0, human.getServerId());
		if (!success) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessMatch.getCode(),
				ErrorCodeConstants.AUTO_CHESS_KUAFU_NOT_CONNECT);
			return;
		}
		// 修改本服的相关状态及通知客户端
		roleStateManager.tryEnter(ActivityState.AUTO_CHESS_MATCHING);
		AutoChessBenfuManager.getInstance().playerEnterMatch(playerId);
		long matchBeginTime = AutoChessBenfuManager.getInstance().getMatchBeginTime(playerId);
		human.push2Gateway(new SCAutoChessMatch(AutoChessMatchState.MATCH, matchBeginTime));
	}

	public void csCancelAutoChessMatch(CSCancelAutoChessMatch msg, Human human) {
		AutoChessBenfuManager manager = AutoChessBenfuManager.getInstance();
		if (!CrossServerManager.isCrossServerConnected(ServerTypeEnum.QUANFU, human)) {
			manager.cancelMatch(human);
			return;
		}
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (!roleStateManager.containState(ActivityState.AUTO_CHESS_MATCHING)) {
			// 没有在匹配中，直接告诉玩家取消成功
			human.push2Gateway(new SCCancelAutoChessMatch(AutoChessMatchState.FREE));
			return;
		}

		// 通知中心服退出匹配

		PlayerAutoChessModel playerAutoChess = manager.getPlayerAutoChess(human.getPlayerId(), human.getServerId());
		int grade = playerAutoChess.getGrade();
		int score = playerAutoChess.getScore();
		SSAutoChessMatchStateChange ssMsg = new SSAutoChessMatchStateChange(human.getPlayerId(), human.getServerId(),
			grade, score, AutoChessConstans.MATCH_STATE_CANCEL);
		CoreBoot.benfuToCenter(ssMsg, 0, human.getServerId());
		// 本服先取消匹配
		manager.cancelMatch(human);
	}

	public void ssAutoChessStartFight(SSAutoChessStartFight ssAutoChessStartFight, Human human) {
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		boolean bl = roleStateManager.containState(ActivityState.AUTO_CHESS_MATCHING);
		if (!bl) {
			AMLog.LOG_COMMON.warn("@AutoChess玩家不在匹配状态,不能进入战斗,playerId={}", human.getPlayerId());
			return;
		}
		// 离开自走棋匹配状态
		AutoChessBenfuManager.getInstance().playerLeaveMatch(human.getPlayerId());
		roleStateManager.tryExit(ActivityState.AUTO_CHESS_MATCHING);

		bl = roleStateManager.tryEnter(ActivityState.KUAFU_FIGHTING);
		if (!bl) {
			AMLog.LOG_COMMON.warn("@AutoChess玩家不能进入战斗状态,playerId={}", human.getPlayerId());
			return;
		}
		ServerSocketAddress serverSocketAddress = ServerSocketAddress.parseProto(ssAutoChessStartFight.getAddress());

		long enterBattleTime = ssAutoChessStartFight.getEnterBattleTime();
		SCAutoChessMatchSucess scAutoChessMatchSucess = new SCAutoChessMatchSucess(AutoChessMatchState.FIGHT,
			enterBattleTime);
		human.push2AllGateway(scAutoChessMatchSucess);

		AutoChessBenfuManager.getInstance().playerEnterFight(human.getPlayerId(), enterBattleTime, serverSocketAddress);

		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(serverSocketAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			CrossServerManager.init(serverSocketAddress, true);
		}
		bl = CrossServerManager.isCrossServerConnected(serverSocketAddress.getServerType(),
			serverSocketAddress.getFlag());
		PlayerAutoChessModel autoChessModel = AutoChessBenfuManager.getInstance()
			.getPlayerAutoChess(human.getPlayerId(), human.getServerId());

		ArrayList<AutoChessPlayerFashionInfo> fashionInfos = new ArrayList<>(30);
		Map<Long, HeroModel> heroModelMap = human.getHeroManager().getHeroMap();
		for (HeroModel heroModel : heroModelMap.values()) {
			DictHeroFashion dictHeroFashion = DictHeroFashion.getDictHeroFashion(heroModel.getFashionId());
			if (dictHeroFashion == null || dictHeroFashion.getIsDefault() == 1) {
				continue;
			}
			fashionInfos.add(new AutoChessPlayerFashionInfo(heroModel.getHeroId(), heroModel.getFashionId()));
		}
		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		int mapId = autochessHumanManager.getUseMapId();
		Set<Integer> extraHeroIds = autochessHumanManager.getExtraHeroIds();
		String monthCardId = BaseService
				.getConfigStringByDefault(PriConfigKeyName.AUTO_CHESS_WEEKLY_TASK_MONTH_CARD_ID);
		boolean haveMonthCard = human.getRechargeManager().isHaveMonthCard(monthCardId);
		AutoChessPlayerSyncInfo playerSyncInfo = new AutoChessPlayerSyncInfo(human.getDisPlayerInformation(),
			autoChessModel.getGrade(), autoChessModel.getScore(), autoChessModel.getShipId(), mapId, fashionInfos,
			new ArrayList<Integer>(extraHeroIds), haveMonthCard);

		SSAutoChessCreateFight ssAutoChessCreateFight = new SSAutoChessCreateFight(
			ssAutoChessStartFight.getBattleGuid(), ssAutoChessStartFight.getPlayerIds(),
			ssAutoChessStartFight.getServerIds(), playerSyncInfo, enterBattleTime);
		if (bl) {
			this.ssAutoChessCreateFight(ssAutoChessCreateFight, human, serverSocketAddress);
		} else {
			AutoChessScheduleCreateFight scheduleAutoChessCreateFight = new AutoChessScheduleCreateFight(
				serverSocketAddress, ssAutoChessCreateFight);
			Globals.getScheduleService().scheduleWithFixedDelay(scheduleAutoChessCreateFight,
				AutoChessScheduleCreateFight.DELAY, AutoChessScheduleCreateFight.DELAY);
		}

		try {
			IAutoChessEventSource eventSource = EventSourceManager.getInstance()
				.getEventSource(IAutoChessEventSource.class);
			if (eventSource != null) {
				eventSource.onAutoChessEnter(human);
			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("@AutoChess triggerEnterEvent error:", e);
		}

	}

	public void ssAutoChessConnectBattle(SSAutoChessConnectBattle ssAutoChessConnectBattle, Human human) {
		boolean succ = ssAutoChessConnectBattle.getSucc();
		if (succ) {
			ServerSocketAddress serverSocketAddress = ServerSocketAddress
				.parseProto(ssAutoChessConnectBattle.getAddress());
			this.ssAutoChessCreateFight(ssAutoChessConnectBattle.getCreateFight(), human, serverSocketAddress);
		} else {
			AMLog.LOG_COMMON.warn("@AutoChess建立连接超时,退出战斗状态,playerId={}", human.getPlayerId());
			human.getSceneObject().getRoleStateManager().tryExit(ActivityState.KUAFU_FIGHTING);
			AutoChessBenfuManager.getInstance().playerLeaveFight(human.getPlayerId());
		}
	}

	private void ssAutoChessCreateFight(SSAutoChessCreateFight ssAutoChessCreateFight, Human human,
										ServerSocketAddress serverSocketAddress) {
		boolean bl = human.enterCrossBattle(CrossBattleTypeEnum.AUTO_CHESS, serverSocketAddress);
		if (bl) {
			CoreBoot.dispatch2Server(ssAutoChessCreateFight, 0, 0, serverSocketAddress.getFlag());
		} else {
			AutoChessBenfuManager.getInstance().playerLeaveFight(human.getPlayerId());
		}
	}

	public void ssAutoChessPlayerStateChange(SSAutoChessPlayerStateChange msg, Human human) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		if (!bl) {
			return;
		}
		switch (msg.getState()) {
			case AUTO_CHESS_PLAYER_STATE_DISCONNECT:
				AMLog.LOG_COMMON.info("@AutoChess本服和战斗服连接断开,断开玩家连接,playerId={}", human.getPlayerId());
				human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
				AutoChessBenfuManager.getInstance().playerLeaveFight(human.getPlayerId());
				AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
					"@Minc.close.autochess.client.battle.channel.inactive");
				break;
			case AUTO_CHESS_PLAYER_STATE_EXIT:
				AMLog.LOG_COMMON.info("@AutoChess玩家退出自走棋,playerId={}", human.getPlayerId());
				human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
				break;
			case AUTO_CHESS_PLAYER_STATE_FIGHTING:
				AMLog.LOG_COMMON.info("@AutoChess玩家进入自走棋,停止接收场景消息,playerId={}", human.getPlayerId());
				human.getSceneObject().stopReceiveSceneMsg();
				break;
			case AUTO_CHESS_PLAYER_STATE_BEGIN:
				// 2019-09-10 19:25
				// 自走棋同步数据阶段没有创建AutoChessPlayer,无法触发离线操作,固这里收到开始消息后看下玩家是否在线,不在线就再发一次离线消息
				LogHelper.logBI(new AutoChessEnter(human, msg.getBattleGuid()));
				if (human.isOnline()) {
					AMLog.LOG_COMMON.info("@AutoChess玩家开始自走棋,停止接收场景消息,playerId={}", human.getPlayerId());
					human.getSceneObject().stopReceiveSceneMsg();
				} else {
					SSAutoChessPlayerOffline ssAutoChessPlayerOffline = new SSAutoChessPlayerOffline(
						human.getPlayerId(), human.getServerId());
					CoreBoot.dispatch2Server(ssAutoChessPlayerOffline, 0, 0,
						CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId()));
				}
				break;
		}
	}

	public void csAutoChessPlayerQuit(Human human) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		if (bl) {
			String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
			if (!StringUtils.isEmpty(serverFlag)) {
				SSAutoChessPlayerQuit ssAutoChessPlayerQuit = new SSAutoChessPlayerQuit(human.getPlayerId(),
					human.getServerId());
				CoreBoot.dispatch2Server(ssAutoChessPlayerQuit, human.getPlayerId(), human.getServerId(), serverFlag);
			} else {
				AMLog.LOG_COMMON.warn("@AutoChess玩家退出自走棋没有找到serverFlag,playerId={}", human.getPlayerId());
			}
			human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		}
		human.push2Gateway(new SCAutoChessPlayerQuit());
	}

	public void ssRefreshGameAutoChessBase(SSRefreshGameAutoChessBase msg) {
		if (msg.getIsWeekEnd()) {
			AutoChessBenfuManager.getInstance().reset();
		}
		AutoChessBenfuManager.getInstance().refreshBaseCache(msg);
	}

	@Override
	public void onClientBattleChannelInactive(AbstractHuman abstractHuman) {
		Human human = (Human) abstractHuman;
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		if (!isCrossBattle) {
			return;
		}
		SSAutoChessPlayerStateChange ssAutoChessPlayerStateChange = new SSAutoChessPlayerStateChange(
			AutoChessPlayerState.AUTO_CHESS_PLAYER_STATE_DISCONNECT, 0);
		Message msg = Message.buildByServerId(human.getPlayerId(), human.getServerId(), ssAutoChessPlayerStateChange,
			null, null, null);
		Globals.getMsgProcessDispatcher().put(msg);
	}

	@Override
	public void onNoticePlayerLoginSuccess(Human human) {
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		if (!isCrossBattle) {
			return;
		}
		String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
		if (StringUtils.isEmpty(serverFlag)) {
			human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		} else {
			SSAutoChessReconnectReq ssAutoChessReconnectReq = new SSAutoChessReconnectReq(human.getPlayerId(),
				human.getServerId());
			boolean succ = CoreBoot.dispatch2Server(ssAutoChessReconnectReq, 0, 0, serverFlag);
			if (!succ) {
				AMLog.LOG_COMMON.warn("@AutoChess发送重连消息失败,playerId={},serverFlag={}", human.getPlayerId(), serverFlag);
				human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
			}
		}
	}

	public void csAutoChessPlayerEnter(CSAutoChessPlayerEnter msg, Human human) {
		if (human.getSceneObject().getTeamId() > 0) {
			AMLog.LOG_COMMON.warn("@AutoChess组队状态下不能进入自走棋,playerId={}", human.getPlayerId());
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_HAS_TEAM);
			return;
		}
		boolean bl = SceneCommonService.canBeginRaid(PrivPSEnum.SCAutoChessPlayerEnter, human);
		if (!bl) {
			return;
		}
		AutoChessPlayerMatch playerMatch = AutoChessBenfuManager.getInstance().getPlayerMatch(human.getPlayerId());
		if (playerMatch == null) {
			AMLog.LOG_COMMON.warn("@AutoChess没有参加战斗,playerId={}", human.getPlayerId());
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NO_FIGHT);
			return;
		}
		ServerSocketAddress socketAddress = playerMatch.getServerSocketAddress();
		if (socketAddress == null) {
			AMLog.LOG_COMMON.warn("@AutoChess没有找到连接的战斗服,playerId={}", human.getPlayerId());
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NO_FIGHT);
			return;
		}
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance()
			.getClientConnectStatus(socketAddress.getFlag());
		if (connectStatus != ClientConnectStatus.CONNECTED) {
			AMLog.LOG_COMMON.warn("@AutoChess和战斗服的连接断开,开始重新建立,playerId={},serverFlag={}", human.getPlayerId(),
				socketAddress.getFlag());
			CrossServerManager.init(socketAddress, true);
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NO_FIGHT);
			return;
		}

		boolean succ = human.enterCrossBattle(CrossBattleTypeEnum.AUTO_CHESS, socketAddress);
		if (!succ) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_STATE_ERROR);
			return;
		}
		SSAutoChessPlayerEnterReq ssAutoChessPlayerEnterReq = new SSAutoChessPlayerEnterReq(human.getPlayerId(),
			human.getServerId());
		succ = CoreBoot.dispatch2Server(ssAutoChessPlayerEnterReq, 0, 0, socketAddress.getFlag());
		if (!succ) {
			AMLog.LOG_COMMON.warn("@AutoChess发送SSAutoChessPlayerEnterReq失败,playerId={},serverFlag={}",
				human.getPlayerId(), socketAddress.getFlag());
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(),
				ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NO_FIGHT);
			human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		}
	}

	public void ssAutoChessPlayerEnterRes(SSAutoChessPlayerEnterRes msg, Human human) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		if (bl) {
			int errorCode = msg.getErrorCode();
			if (errorCode > 0) {
				human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessPlayerEnter.getCode(), errorCode);
				if (errorCode != ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NOT_BEGIN) {// 等待战斗开始,所以不能清理状态
					human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
				}
				AMLog.LOG_COMMON.warn("@AutoChess玩家进入自走棋失败,playerId={},errorCode={}", human.getPlayerId(), errorCode);
				if (errorCode == ErrorCodeConstants.AUTO_CHESS_PLAYER_ENTER_NO_FIGHT) {
					AutoChessBenfuManager.getInstance().playerLeaveFight(human.getPlayerId());
				}
			} else {
				String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
				AMLog.LOG_COMMON.info("@AutoChess玩家进入自走棋战斗,停止接收场景消息,playerId={},serverFlag={}", human.getPlayerId(),
					serverFlag);
				human.getSceneObject().stopReceiveSceneMsg();
				human.push2Gateway(new SCAutoChessPlayerEnter());
			}
		} else {
			AMLog.LOG_COMMON.warn("@AutoChess玩家收到SSAutoChessPlayerEnterRes,但是已经不在自走棋状态,playerId={}",
				human.getPlayerId());
		}
	}

	public void ssAutoChessSettlement(SSAutoChessSettlement msg) {
		AutoChessBenfuManager manager = AutoChessBenfuManager.getInstance();
		long playerId = msg.getPlayerId();
		manager.playerLeaveFight(playerId);
		int serverId = msg.getServerId();
		int beforeGrade = 0;
		int beforeScore = 0;
		int afterGrade = 0;
		int afterScore = 0;
		int changeScore = msg.getChangeScore();
		PlayerAutoChessModel playerAutoChess = manager.getPlayerAutoChess(playerId, serverId);
		beforeGrade = playerAutoChess.getGrade();
		beforeScore = playerAutoChess.getScore();
		manager.changePlayerAutoChessScore(msg);
		int zoneId = CoreBoot.getZoneId(serverId);
		Human human = (Human) Globals.getHuman(zoneId, playerId);
		if (human == null) {
			return;
		}
		if (human.isOnline() && msg.getInGame()) {
			playerAutoChess = manager.getPlayerAutoChess(playerId, serverId);
			afterGrade = playerAutoChess.getGrade();
			afterScore = playerAutoChess.getScore();
			SCAutoChessScoreChange scMsg = new SCAutoChessScoreChange(beforeGrade, beforeScore, afterGrade, afterScore,
				changeScore, msg.getReportInfos());
			human.push2Gateway(scMsg);
		}
		if (!msg.getInGame()) {
			human.exitCrossBattle(CrossBattleTypeEnum.AUTO_CHESS);
		}
	}

	public void ssRefreshAutoChessRank(SSRefreshAutoChessRank msg) {
		AutoChessBenfuManager.getInstance().refreshRank(msg.getRankList());
	}

	public void csAutoChessGetMapShopInfo(CSAutoChessGetMapShopInfo msg, Human human) {
		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		List<AutochessMapShopInfo> mapInfos = new ArrayList<>();
		List<DictAutoChessMapShop> valueCollection = DictAutoChessMapShop.getList();
		for (DictAutoChessMapShop dictAutoChessMapShop : valueCollection) {
			int mapId = dictAutoChessMapShop.getMapId();
			boolean isActived = autochessHumanManager.isMapIdActived(mapId);
			mapInfos.add(new AutochessMapShopInfo(mapId, isActived));
		}
		int useMapId = autochessHumanManager.getUseMapId();
		human.push2AllGateway(new SCAutoChessGetMapShopInfo(mapInfos, useMapId));
	}

	public void csAutoChessUseMap(CSAutoChessUseMap msg, Human human) {

		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		int mapId = msg.getMapId();

		DictAutoChessMapShop dictAutoChessMapShop = DictAutoChessMapShop.getDictAutoChessMapShop(mapId);
		if (dictAutoChessMapShop == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessUseMap.getCode(), ErrorCodeConstants.SYSTEM_ERROR);
			return;
		}
		autochessHumanManager.useMapId(mapId);
		SCAutoChessUseMap sc = new SCAutoChessUseMap();
		human.push2Gateway(sc);
	}

	public void csAutoChessGetExtraPackInfo(CSAutoChessGetExtraPackInfo msg, Human human) {
		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		List<DictAutoChessExtraHeroPack> valueCollection = DictAutoChessExtraHeroPack.getList();
		List<AutochessExtraPackInfo> packetInfos = new ArrayList<>();

		Set<Integer> extraHeroIds = autochessHumanManager.getExtraHeroIds();
		for (DictAutoChessExtraHeroPack dictAutoChessExtraHeroPack : valueCollection) {
			int packetId = dictAutoChessExtraHeroPack.getPacketId();
			boolean isActived = autochessHumanManager.isPacketActived(packetId);
			int worth = getWorth(extraHeroIds, dictAutoChessExtraHeroPack);
			packetInfos.add(new AutochessExtraPackInfo(packetId, isActived,
				new ItemBase(0, ItemBaseType.DIAMOND.value(), worth, 0, false)));
		}
		human.push2AllGateway(new SCAutoChessGetExtraPackInfo(packetInfos, new ArrayList<Integer>(extraHeroIds)));
	}

	private int getWorth(Set<Integer> extraHeroIds, DictAutoChessExtraHeroPack dictAutoChessExtraHeroPack) {
		int worth = 0;
		List<Integer> heroIdList = dictAutoChessExtraHeroPack.getHeroIdList();
		for (Integer heroId : heroIdList) {
			if (!extraHeroIds.contains(heroId)) {
				DictAutoChessHero dictAutoChessHero = DictAutoChessHero.getDictById(heroId);
				if (dictAutoChessHero != null) {
					worth += dictAutoChessHero.getWorth();
				}
			}
		}
		return worth;
	}

	public void csAutoChessBuyPacket(CSAutoChessBuyPacket msg, Human human) {
		int packetId = msg.getPacketId();
		DictAutoChessExtraHeroPack dictAutoChessExtraHeroPack = DictAutoChessExtraHeroPack
			.getDictAutoChessExtraHeroPack(packetId);
		if (dictAutoChessExtraHeroPack == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessBuyPacket.getCode(), ErrorCodeConstants.SYSTEM_ERROR);
			return;
		}
		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		Set<Integer> extraHeroIds = autochessHumanManager.getExtraHeroIds();
		int worth = getWorth(extraHeroIds, dictAutoChessExtraHeroPack);
		ItemBase cost = new ItemBase(0, ItemBaseType.DIAMOND.value(), worth, 0, false);
		boolean settle = CommonService.settle(PrivPSEnum.SCAutoChessBuyPacket, human, cost, null);
		if (!settle) {
			human.pushErrorMessageToClient(PrivPSEnum.SCAutoChessBuyPacket.getCode(),
				ErrorCodeConstants.RESOURCE_NOT_ENOUGH);
			return;
		}
		autochessHumanManager.addPacketId(packetId);
		List<Integer> heroIdList = dictAutoChessExtraHeroPack.getHeroIdList();
		for (Integer heroId : heroIdList) {
			autochessHumanManager.activeHeroId(heroId);
		}
		human.push2AllGateway(new SCAutoChessBuyPacket(packetId));
	}

	public void getAutoChessWeeklyTaskList(Human human) {
		boolean checkState = SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS_WEEKLY_TASK, human,
			PrivPSEnum.SCGetAutoChessWeeklyTaskList);
		if (!checkState) {
			return;
		}

		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		autochessHumanManager.pushWeeklyTaskListToHuman();

	}

	public void getAutoChessTaskReward(CSGetAutoChessTaskReward msg, Human human) {
		boolean checkState = SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS_WEEKLY_TASK, human,
			PrivPSEnum.SCGetAutoChessTaskReward);
		if (!checkState) {
			return;
		}

		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		autochessHumanManager.getTaskReward(msg.getTaskId());
	}

	public void sendTaskDataToBenfu(SSSendTaskDataToBenfu msg, Human human) {
		boolean checkState = SwitchConditionManager.checkState(PrivSwitchEnum.AUTO_CHESS_WEEKLY_TASK, human);
		if (!checkState) {
			return;
		}

		IAutoChessHumanManager autochessHumanManager = human.getAutochessHumanManager();
		autochessHumanManager.updateTaskData(msg);
	}

	public void ssAutoChessPlayerRankRes(SSAutoChessPlayerRankRes msg) {
		AutoChessBenfuManager.getInstance().refreshPlayerRank(msg);
	}

}
