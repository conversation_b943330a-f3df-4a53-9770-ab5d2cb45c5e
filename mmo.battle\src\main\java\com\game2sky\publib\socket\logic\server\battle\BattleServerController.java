package com.game2sky.publib.socket.logic.server.battle;

import io.netty.channel.Channel;
import io.netty.util.Attribute;

import org.springframework.stereotype.Controller;

import com.game2sky.CoreBoot;
import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.common.SGRegist;
import com.game2sky.publib.event.EventSourceManager;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.AMAttributeKey;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnect;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO 战斗服控制器
 *
 * <AUTHOR>
 * @version v0.1 2017-11-28 下午6:56:14  guojijiun
 */
@Controller
public class BattleServerController {

	@ProtoStuffMapping(value = PSEnum.SSBattleServerLoadReportResp)
	public void ssBattleServerLoadReportResp() {
	}
	
	@ProtoStuffMapping(PSEnum.SGRegist)
	public void sgRegist(Channel channel, SGRegist sg, Head head, Message message) {
//		java.net.SocketAddress remoteAddress = channel.remoteAddress();
//		ServerSocketAddress socketAddress = sceneClientCenter.getServerSocketAddress(remoteAddress);
		Attribute<String> attr = channel.attr(AMAttributeKey.SCENE_FLAG);
		attr.set(sg.getSceneFlag());
//		sceneClientCenter.add(channel, socketAddress);
		AMLog.LOG_STDOUT.info(
			" [SGRegist] " + AMFrameWorkBoot.SELF_FLAG + " -> " + channel.toString() + " [" + sg.getSceneFlag() + "]");
		
		try {
			checkServerStartTime(channel, sg.getSceneFlag(), sg.getServerType(), sg.getServerStartTime());
		} catch(Exception e) {
			AMLog.LOG_ERROR.error("检测服务器时间出错，server="+sg.getSceneFlag()+", startTime="+sg.getServerStartTime()+", "+channel, e);
		}
	}
	
	/**
	 * 检测服务器时间，以校验目标服务器是否存在重启
	 * 
	 * @param channel
	 * @param startTime
	 */
	private void checkServerStartTime(Channel channel, String sceneFlag, ServerTypeEnum serverType, long startTime) {
		ClientConnect clientConnect = NetConnectCenter.getInstance().getClientCenter().getClientConnect(channel);
		if(clientConnect != null) {
			ServerSocketAddress serverSocketAddress = clientConnect.getServerSocketAddress();
			if(serverSocketAddress != null) {
				//该服务器的启动时间
				int restartType = 0;
				if(serverSocketAddress.getStartTime() == 0) {
					//第一次连接上该服， 什么也不用做
					restartType = 0;
					AMLog.LOG_COMMON.info("{} 注册后，发现是第一次连接到目标服务器{}", CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag());
				} else if(serverSocketAddress.getStartTime() == startTime) {
					//目标服务器未重启， 之前的网络断开或Channel中断导致的啥也不用做
					restartType = 1;
					AMLog.LOG_COMMON.info("{} 注册目标服务器{}， startTime一致，为网络断开", CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag());
				} else {
					restartType = 2;
					AMLog.LOG_COMMON.info("{} 注册后，发现是目标服务器{}有重启，断开时间:{}", 
						CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag(), (startTime - serverSocketAddress.getStartTime()));
					//目标服务器肯定是中心服
					ITargetServerRestartEventSource eventSource = EventSourceManager.getInstance().getEventSource(
						ITargetServerRestartEventSource.class);
					if (eventSource != null) {
						eventSource.onTargetServerRestart(serverType, channel, restartType);
					}
				}
				serverSocketAddress.setStartTime(startTime);
			}
		}
	}
}
