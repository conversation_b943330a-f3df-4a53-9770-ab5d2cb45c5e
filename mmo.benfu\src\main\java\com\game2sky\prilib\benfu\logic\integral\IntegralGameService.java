package com.game2sky.prilib.benfu.logic.integral;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.benfu.logic.integral.task.CreateIntegralRobotTask;
import com.game2sky.prilib.benfu.logic.integral.task.ScheduleCreateIntegralRobot;
import com.game2sky.prilib.benfu.logic.integral.task.ScheduleStartIntegralFight;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.integralArena.CSCancelIntegralMatch;
import com.game2sky.prilib.communication.game.integralArena.CSIntegralArenaMatch;
import com.game2sky.prilib.communication.game.integralArena.CSIntegralChat;
import com.game2sky.prilib.communication.game.integralArena.CSIntegralFightReady;
import com.game2sky.prilib.communication.game.integralArena.CSPlayerIntegralArena;
import com.game2sky.prilib.communication.game.integralArena.IntegralAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCurrentRank;
import com.game2sky.prilib.communication.game.integralArena.IntegralPlayerChange;
import com.game2sky.prilib.communication.game.integralArena.IntegralPlayerRewardInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralRankItem;
import com.game2sky.prilib.communication.game.integralArena.IntegralReward;
import com.game2sky.prilib.communication.game.integralArena.IntegralState;
import com.game2sky.prilib.communication.game.integralArena.SCCancelIntegralMatch;
import com.game2sky.prilib.communication.game.integralArena.SCIntegralArenaMatch;
import com.game2sky.prilib.communication.game.integralArena.SCIntegralChat;
import com.game2sky.prilib.communication.game.integralArena.SCIntegralStartNewSeason;
import com.game2sky.prilib.communication.game.integralArena.SCOpenIntegralPinnacle;
import com.game2sky.prilib.communication.game.integralArena.SCPlayerIntegralArena;
import com.game2sky.prilib.communication.game.integralArena.SSChangePlayerIntegral;
import com.game2sky.prilib.communication.game.integralArena.SSConnectCenterIntegralEvent;
import com.game2sky.prilib.communication.game.integralArena.SSCreateIntegralRobotReq;
import com.game2sky.prilib.communication.game.integralArena.SSDisconnectIntegralEvent;
import com.game2sky.prilib.communication.game.integralArena.SSFightMonitorInfo;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralArenaRankReq;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralBannedPlayer;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralChat;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralFightResult;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralMatchStateChange;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralMatcherReady;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralRankReward;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralWeekReset;
import com.game2sky.prilib.communication.game.integralArena.SSKuafuStateChange;
import com.game2sky.prilib.communication.game.integralArena.SSPlayerInfoChange;
import com.game2sky.prilib.communication.game.integralArena.SSRefreshGameIntegralBase;
import com.game2sky.prilib.communication.game.integralArena.SSRefreshGameIntegralRank;
import com.game2sky.prilib.communication.game.integralArena.SSStartIntegralFightReq;
import com.game2sky.prilib.communication.game.integralArena.SSStartIntegralFightRes;
import com.game2sky.prilib.communication.game.player.CommonFormationData;
import com.game2sky.prilib.communication.game.player.CommonFormationType;
import com.game2sky.prilib.communication.game.player.FormationItem;
import com.game2sky.prilib.communication.game.player.FormationTypeEnum;
import com.game2sky.prilib.communication.game.player.PlayerFormation;
import com.game2sky.prilib.communication.game.rank.RankInfo;
import com.game2sky.prilib.communication.game.rank.RankType;
import com.game2sky.prilib.communication.game.rank.SCGetRankData;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.constants.IPrivRedPointType;
import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.dict.domain.DictIntegralChat;
import com.game2sky.prilib.core.socket.logic.awardSend.AwardSendType;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.equipment.EquipmentModel;
import com.game2sky.prilib.core.socket.logic.formation.FormationDataModel;
import com.game2sky.prilib.core.socket.logic.formation.check.FormationCheckUtil;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.human.state.ActivityState;
import com.game2sky.prilib.core.socket.logic.human.temporary.DuplicateScene;
import com.game2sky.prilib.core.socket.logic.integralCommon.AbstractIntegralService;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.integralCommon.event.IIntegralArenaEventSource;
import com.game2sky.prilib.core.socket.logic.integralCommon.event.IIntegralFightEventSource;
import com.game2sky.prilib.core.socket.logic.monitor.PlayerMonitorManager;
import com.game2sky.prilib.core.socket.logic.rank.RankFunction;
import com.game2sky.prilib.core.socket.logic.scene.unit.component.state.RoleStateManager;
import com.game2sky.prilib.core.socket.logic.switchCondition.PrivSwitchEnum;
import com.game2sky.prilib.core.socket.logic.title.TitleService;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.game.player.DisplayInformationType;
import com.game2sky.publib.event.EventSourceManager;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossBattleTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveListener;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveSource;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.web.base.Result;
import com.game2sky.publib.log.LogHelper;
import com.game2sky.publib.socket.logic.awardSend.IAwardSendService;
import com.game2sky.publib.socket.logic.awardSend.IAwardSendType;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.mail.MailService;
import com.game2sky.publib.socket.logic.mail.model.MailDictIdEnum;
import com.game2sky.publib.socket.logic.switchCondition.SwitchConditionManager;

/**
 * 本服天梯协议
 * <AUTHOR>
 * @version v0.1 2017年11月13日 下午8:45:23  zhoufang
 */
@Service
@EventSubscriber(eventSources = { IClientBattleChannelInactiveSource.class })
public class IntegralGameService extends AbstractIntegralService implements IClientBattleChannelInactiveListener {

	@Autowired
	TitleService titleService;
	@Autowired
	private IAwardSendService awardSendService;

	public static int getRealAreaType(int branch, int areaType) {
		IntegralGroup group = IntegralGameManager.getInstance().getGroupById(branch);
		if (group.isNewVersion()) {
			// 新版天梯
			if (!IntegralAreaType.isNewVersion(areaType)) {
				// 默认进入练习赛
				return IntegralAreaType.NewDefault().value();
			}
			return areaType;
		}
		// 旧版天梯
		if (!IntegralAreaType.isOldVersion(areaType)) {
			// 默认进入无限制赛
			return IntegralAreaType.oldDefault().value();
		}
		return areaType;
	}

	@Override
	public void csPlayerIntegralArena(CSPlayerIntegralArena msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.INTEGRAL_ARENA, human, PrivPSEnum.SCPlayerIntegralArena)) {
			return;
		}
		
		int integralServerClose = Globals.getIntValueByDefault(zoneId, "IntegralServerClose", 0);
		if(integralServerClose ==1 ) {
			// 天梯关闭
			int areaType = IntegralAreaType.PRACTICE.value();
			if(msg.getType() == IntegralAreaType.PINNACLE.value()) {
				areaType = msg.getType();
			}
			SCPlayerIntegralArena resp = new SCPlayerIntegralArena();
			resp.setSeason(1);
			resp.setWeek(1);
			resp.setSeasonEndTime(1607529600000L);
			resp.setIsOpen(0);
			Long[] expandTime = IntegralConfigCenter.getAreaExpandTime(areaType);
			resp.setLongestWaitTime(expandTime[expandTime.length - 1]);
			IntegralAreaPlayerInfo playerAreaProto = new IntegralAreaPlayerInfo();
			playerAreaProto.setRank(-1);
			playerAreaProto.setState(IntegralState.FREE);
			resp.setPlayerInfo(playerAreaProto);
			resp.setAreaType(areaType);
			resp.setPinnacleIsOpen(false);
			human.push2Gateway(resp);
			return;
		}
		
		
		// 玩家所属分组
		int branch = IntegralConfigCenter.getIntegralBranchByServerId(human.getServerId());
		// 显示正确的赛区
		int areaType = IntegralGameService.getRealAreaType(branch, msg.getType());

		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		// 赛季基本信息
		IntegralBaseCache baseCache = gameManager.getBaseCache(human.getServerId());
		// 分组信息
		IntegralGroup group = gameManager.getGroupById(branch);
		if (group == null) {
			AMLog.LOG_ERROR.error("玩家ID={},serverId={}不属于本机的天梯分组[{}]", human.getPlayerId());
			return;
		}
		// 判断是否是新赛季第一次进入
		if ((areaType == IntegralAreaType.oldDefault().value() && !group.haveAreaData(human.getPlayerId(), areaType))
			|| (areaType == IntegralAreaType.NewDefault().value() && !group.haveAreaData(human.getPlayerId(), areaType))) {
			human.push2Gateway(new SCIntegralStartNewSeason());
		}

		// 赛区积分信息
		IntegralAreaPlayerInfo playerAreaProto = group.toPlayerAreaProto(human.getPlayerId(), human.getServerId(),
			areaType);
		// 是否开启了巅峰赛
		boolean pinnacleIsOpen = group.haveAreaData(human.getPlayerId(), IntegralAreaType.PINNACLE.value());

		if (playerAreaProto == null) {
			playerAreaProto = new IntegralAreaPlayerInfo();
			playerAreaProto.setRank(-1);
			playerAreaProto.setState(IntegralState.FREE);
		}

		SCPlayerIntegralArena resp = new SCPlayerIntegralArena();
		resp.setSeason(baseCache.getSeason());
		resp.setWeek(baseCache.getWeek());
		resp.setSeasonEndTime(baseCache.getSeasonEndTime());
		resp.setIsOpen(IntegralConfigCenter.isOpen(areaType) ? 1 : 0);
		Long[] expandTime = IntegralConfigCenter.getAreaExpandTime(areaType);
		resp.setLongestWaitTime(expandTime[expandTime.length - 1]);
		resp.setPlayerInfo(playerAreaProto);
		resp.setAreaType(areaType);
		resp.setPinnacleIsOpen(pinnacleIsOpen);
		CoreBoot.push2Gateway(resp, head.getPid(), head.getServerId(), msg.getChannel());
		// 小红点
		if (human.getIntegralManager().canReceiveReward()) {
			human.getRedPointManager().pushRedPointToClient(IPrivRedPointType.INTEGRAL_TASK, 0);
		}
	}

	@Override
	public void csIntegralArenaMatch(CSIntegralArenaMatch msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.INTEGRAL_ARENA, human, PrivPSEnum.SCIntegralArenaMatch)) {
			return;
		}
		// 玩家所属分组
		int branch = IntegralConfigCenter.getIntegralBranchByServerId(human.getServerId());
		// 显示正确的赛区
		int areaType = IntegralGameService.getRealAreaType(branch, msg.getAreaType());
		int bannedMinute = human.getMonitorManager().getLastMinute(areaType);
		if (bannedMinute > 0) {
			// 天梯赛被封禁中
			human.pushErrorMessageToClientByArguments(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_BANNED_MATCH, bannedMinute);
			return;
		}

		if (!CrossServerManager.isCrossServerConnected(ServerTypeEnum.QUANFU, human)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_KUAFU_NOT_CONNECT);
			return;
		}

		FormationDataModel formationDataModel = human.getFormationManager().getFormationDataModel();
		PlayerFormation playerFormation = formationDataModel.getCurrentFormationByType(FormationTypeEnum.SOLO);
		// 检测阵容
		if (!FormationCheckUtil.checkIntegerlFormation(areaType, playerFormation)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_FORMATION_NOQUALITY);
			return;
		}
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (roleStateManager.containState(ActivityState.KUAFU_MATCHING)) {
			// 正在匹配中
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_MATCH_MATCHING);
			return;
		}
		if (roleStateManager.containState(ActivityState.KUAFU_FIGHTING)) {
			// 正在战斗中
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_FIGHTING);
			return;
		}
		if (!human.getSceneObject().getRoleStateManager().canEnter(ActivityState.KUAFU_MATCHING)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_MATCH_STATE_ERROR);
			return;
		}
		IntegralGamePlayer playerInfo = IntegralGameManager.getInstance().getPlayerInfo(human.getPlayerId());
		if (!playerInfo.haveAreaData(areaType)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralArenaMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_AREA_PINNACLE_CLOSED);
			return;
		}

		// 进入跨服匹配状态
		roleStateManager.tryEnter(ActivityState.KUAFU_MATCHING);
		IntegralCommonService.playerEnterMatch(human.getPlayerId(), areaType);
		human.push2Gateway(new SCIntegralArenaMatch(areaType, System.currentTimeMillis()));

		// 通知全服开始进行匹配
		SSIntegralMatchStateChange resp = new SSIntegralMatchStateChange();
		resp.setAreaType(areaType);
		resp.setPlayerId(human.getPlayerId());
		resp.setIntegral(playerInfo.getIntegral(areaType));
		resp.setAction(IntegralConst.MATCH_STATE_ENTER);

		CoreBoot.dispatch2Server(resp, human.getPlayerId(), human.getServerId(),
			CoreBoot.getServerFlag(ServerTypeEnum.QUANFU, human.getPlayerId()));

	}

	@Override
	public void csCancelIntegralMatch(CSCancelIntegralMatch msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		AbstractHuman human = Globals.getHuman(zoneId, head.getPid());
		if (!SwitchConditionManager.checkState(PrivSwitchEnum.INTEGRAL_ARENA, human, PrivPSEnum.SCCancelIntegralMatch)) {
			return;
		}

//		if (!CrossServerManager.isCrossServerConnected(ServerTypeEnum.QUANFU, human)) {
//			human.pushErrorMessageToClient(PrivPSEnum.SCCancelIntegralMatch.getCode(),
//				ErrorCodeConstants.INTEGRAL_KUAFU_NOT_CONNECT);
//			return;
//		}
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (!roleStateManager.containState(ActivityState.KUAFU_MATCHING)) {
			// 没有在匹配中
			human.pushErrorMessageToClient(PrivPSEnum.SCCancelIntegralMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_CANCEL_MATCH_NOTHING);
			return;
		}
		if (!IntegralCommonService.inMatching(msg.getAreaType(), human.getPlayerId())) {
			// 此赛区没有在匹配中
			human.pushErrorMessageToClient(PrivPSEnum.SCCancelIntegralMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_CANCEL_MATCH_NOTHING);
			return;
		}
		if (IntegralCommonService.isPreparing(human.getPlayerId())) {
			// In preparation, the matching cannot be canceled
			human.pushErrorMessageToClient(PrivPSEnum.SCCancelIntegralMatch.getCode(),
				ErrorCodeConstants.INTEGRAL_CANCEL_MATCH_READY);
			return;
		}

		// 离开跨服匹配状态
		roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
		IntegralCommonService.playerLeaveMatch(human.getPlayerId());

		// 通知全服退出匹配
		IntegralGamePlayer playerInfo = IntegralGameManager.getInstance().getPlayerInfo(human.getPlayerId());
		SSIntegralMatchStateChange resp = new SSIntegralMatchStateChange();
		resp.setAreaType(msg.getAreaType());
		resp.setPlayerId(human.getPlayerId());
		resp.setIntegral(playerInfo.getIntegral(msg.getAreaType()));
		resp.setAction(IntegralConst.MATCH_STATE_CANCEL);

		CoreBoot.dispatch2Server(resp, human.getPlayerId(), human.getServerId(),
			CoreBoot.getServerFlag(ServerTypeEnum.QUANFU, human.getPlayerId()));

		human.push2Gateway(new SCCancelIntegralMatch(msg.getAreaType()));
		return;
	}

	@Override
	public void csIntegralFightReady(CSIntegralFightReady msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (!roleStateManager.containState(ActivityState.KUAFU_MATCHING)) {
			// 没有在匹配中
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralFightReady.getCode(),
				ErrorCodeConstants.INTEGRAL_CANCEL_MATCH_NOTHING);
			return;
		}
		// 转发中心服
		IntegralCommonService.benfuToCenter(new SSIntegralMatcherReady(human.getPlayerId()), head.getPid(),
			head.getServerId());
	}

//	@Override
//	public void ssPlayerFormationDataReq(SSPlayerFormationDataReq msg, Head head) {
//		Human human = (Human) Globals.getHuman(head.getSid(), head.getPid());
//		if (!SwitchConditionManager.checkState(PrivSwitchEnum.INTEGRAL_ARENA, human)) {
//			// 等级不够
//			human.pushErrorMessageToClient(PrivPSEnum.SCPlayerIntegralArena.getCode(),
//				CommonErrorCodeConstants.OUT_OF_LEVEL);
//			return;
//		}
//		// 回复中心服
//		APSHandler resp = new SSPlayerFormationDataRes(msg.getAreaType(), human.getDisPlayerInformation());
//		CoreBoot.dispatch2Server(resp, head.getPid(), head.getSid(),
//			CoreBoot.getServerFlag(ServerTypeEnum.QUANFU, human.getPlayerId()));
//	}

	@Override
	public void ssStartIntegralFightReq(SSStartIntegralFightReq msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		int areaType = msg.getAreaType();
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (msg.getAddress() == null) {
			// 分配战斗服失败,退出匹配状态
			roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
			IntegralCommonService.playerLeaveMatch(human.getPlayerId());
			human.push2Gateway(new SCCancelIntegralMatch(areaType));
			return;
		}
		// 分配成功,准备连接战斗服
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(msg.getAddress());
		if (IntegralWaitFightCenter.isTimeout(msg.getFightId())) {
			// 此战斗重连超时, 战斗已被强制结束, 战斗服已经通知了本服退出状态
			roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
			IntegralCommonService.playerLeaveMatch(human.getPlayerId());
			human.push2Gateway(new SCCancelIntegralMatch(areaType));
			AMLog.LOG_COMMON.info("战斗服serverAddress={},连接超时,战斗id={},playerId={} 已被强制结束", serverAddress.getFlag(),
				msg.getFightId(), human.getPlayerId());
			return;
		}
		AMLog.LOG_COMMON.info("开始连接战斗服serverAddress[{}],human[{}],fightId[{}]", serverAddress.getFlag(),
			human.getPlayerId(), msg.getFightId());
		// 是否正在创建连接
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(
			serverAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			// 未建立连接，则立即建立连接
			CrossServerManager.init(serverAddress, true);
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info(
					"开始连接战斗服, IntegralGameService.ssStartIntegralFightReq:: 本服与[{}]创建连接,human[{}],fightId[{}]",
					serverAddress.getFlag(), human.getPlayerId(), msg.getFightId());
			}
		} else {
			if (roleStateManager.containState(ActivityState.KUAFU_FIGHTING)) {
				AMLog.LOG_COMMON.info("@ssStartIntegralFightReq已经在天梯战斗中,playerId={}",human.getPlayerId());
				return;
			}
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info(
					"IntegralGameService.ssStartIntegralFightReq:: 本服与[{}]的连接的连接状态为[{}],human[{}],fightId[{}]",
					serverAddress.getFlag(), connectStatus, human.getPlayerId(), msg.getFightId());
			}
			// 检测本服是否连接上了战斗服（跨服）
			if (CrossServerManager.isCrossServerConnected(ServerTypeEnum.BATTLE, human)) {
				// 为玩家绑定该serverFlag
				if (CrossServerManager.initPlayerBindCrossServer(human, serverAddress)) {
					if (AMLog.LOG_COMMON.isInfoEnabled()) {
						AMLog.LOG_COMMON.info(
							"IntegralGameService.ssStartIntegralFightReq:: 本服与[{}]的连接成功,状态为[{}],human[{}],fightId[{}]",
							serverAddress.getFlag(), connectStatus, human.getPlayerId(), msg.getFightId());
					}
					IntegralWaitFightCenter.delete(msg.getFightId());
					// 绑定成功,加载战斗数据
					PlayerFormation playerFormation = null;
					byte[] bytes = null;
					try {
						DictIntegralArenaArea dict = IntegralConfigCenter.getDictIntegralArenaArea(msg.getAreaType());
						boolean withEquip = dict.getForbidEquipment() == 0;// 0表示需要计算装备属性
						// 获取天梯阵容
						if (IntegralAreaType.isOldVersion(areaType)) {
							// 旧版取当前阵容
							playerFormation = human.getFormationManager().getFormationDataModel()
								.getCurrentFormationByType(FormationTypeEnum.SOLO);
						} else if (areaType == IntegralAreaType.PRACTICE.value()) {
							// 练习赛区
							CommonFormationData formationData = human.getCommonFormationDataManager()
								.getCommonFormationData(CommonFormationType.INTEGRAL_PRACTICE_FORMATION);
							playerFormation = formationData.getPlayerFormation();
						} else if (areaType == IntegralAreaType.PINNACLE.value()) {
							// 巅峰赛区
							CommonFormationData formationData = human.getCommonFormationDataManager()
								.getCommonFormationData(CommonFormationType.INTEGRAL_PINNACLE_FORMATION);
							playerFormation = formationData.getPlayerFormation();
						}
						// 检查阵容的合法性
						if (!FormationCheckUtil.checkIntegerlFormation(msg.getAreaType(), playerFormation)) {
							human.pushErrorMessageToClient(PSEnum.ErrorMessage.getCode(),
								ErrorCodeConstants.INTEGRAL_FORMATION_NOQUALITY);
							return;
						}
						long shipGuid = human.getShipSystemManager().getCurrentFightShipGuid();
						// 获取战斗数据
						FFightSide fightSide = null;
						if (areaType == IntegralAreaType.PRACTICE.value()) {
							// 练习赛区只取部分玩法的战斗数据
							fightSide = human.getCommonFormationDataManager().getFFightSide(
								CommonFormationType.INTEGRAL_PRACTICE_FORMATION, msg.getCamp(), false, null);
						} else {
							fightSide = human.getCommonFormationDataManager().getFFightSide(playerFormation, null,
								shipGuid, msg.getCamp(), withEquip);
						}
						if (fightSide != null) {
							bytes = SerializationUtil.serialize(fightSide);
						}
					} catch (Exception e) {
						AMLog.LOG_ERROR.error("天梯记载玩家战斗数据异常,playerId[{}],fightId[{}]", human.getPlayerId(),
							msg.getFightId(), e);
					} finally {
						// 退出匹配状态
						roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
						IntegralCommonService.playerLeaveMatch(human.getPlayerId());
						human.push2Gateway(new SCCancelIntegralMatch(msg.getAreaType()));
					}
					if (bytes == null) {
						human.pushErrorMessageToClient(PSEnum.ErrorMessage.getCode(),
							ErrorCodeConstants.INTEGRAL_INIT_FIGHTDATE);
						return;
					}
					if (!roleStateManager.canEnter(ActivityState.KUAFU_FIGHTING)) {
						human.pushErrorMessageToClient(PSEnum.ErrorMessage.getCode(),
							ErrorCodeConstants.INTEGRAL_FIGHTING);
						return;
					}
					// 进入跨服战斗状态
					CrossServerManager.setPlayerCrossBattle(human, CrossBattleTypeEnum.INTEGRAL_PVP_FIGHT);
					roleStateManager.tryEnter(ActivityState.KUAFU_FIGHTING);
					IntegralCommonService.playerEnterFight(human.getPlayerId(), msg.getAreaType());
					// 记录战斗次数
					IntegralGamePlayer playerInfo = IntegralGameManager.getInstance()
						.getPlayerInfo(human.getPlayerId());
					playerInfo.addFightCount(areaType);
					IntegralGameRedis.savePlayer(playerInfo);

					// 推送数据到战斗服
					SSStartIntegralFightRes resp = new SSStartIntegralFightRes();
					resp.setAreaType(msg.getAreaType());
					resp.setFightData(bytes);
					resp.setFormation(playerFormation);
					resp.setFightId(msg.getFightId());
					resp.setDisplayInformation(human.getDisPlayerInformation());
					int integral = playerInfo.getIntegral(areaType);
					resp.setIntegral(integral);
					resp.setIsRobot(false);
					// 验证玩家有没有带满装备
					resp.setEquipCheck(true);
					// 今天已获得的天梯奖励次数
					int dayFightRewardCount = human.getIntegralManager().getDayFightRewardCount();
					resp.setDayRewardCount(dayFightRewardCount);
					
					for (FormationItem item : playerFormation.getFormationItems()) {
						List<EquipmentModel> heroEquips = human.getEquipmentManager().getHeroEquips(item.getHeroGuid());
						if (heroEquips.size() < 4) {
							resp.setEquipCheck(false);
							break;
						}
					}
					resp.setTaskConditions(human.getIntegralManager().toTaskServerProtos(areaType));
					CoreBoot.dispatch2Server(resp, head.getPid(), head.getServerId(), serverAddress.getFlag());
					return;
				}
			}
		}
		// 异步消息
		ScheduleStartIntegralFight scheduleMsg = new ScheduleStartIntegralFight(msg, head);
		Globals.getScheduleService().scheduleOnce(scheduleMsg, 1000);
	}

	@Override
	public void ssIntegralWeekReset(SSIntegralWeekReset msg, Head head) {
//		IntegralGameManager.getInstance().weekReset();
	}

	@Override
	public void ssIntegralFightResult(SSIntegralFightResult msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		Human human = (Human) Globals.getHuman(zoneId, msg.getPlayerId());
		int areaType = msg.getAreaType();
		// 本服玩家天梯信息
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		if (!IntegralConfigCenter.isOpen(areaType)) {
			// 天梯已经关闭
			AMLog.LOG_COMMON.error("天梯已经关闭,不再处理战斗结果.. playerId={}, isWin={}, change={} ", msg.getPlayerId(),
				msg.getIsWin(), msg.getIntegralChange());
			return;
		}
		int serverId = human != null ? human.getServerId() : gameManager.getServerIdByPlayerId(msg.getPlayerId());
		// 玩家所属分组
		IntegralGroup group = gameManager.getGroupByServerId(serverId);
		IntegralGamePlayer playerInfo = group.getPlayerInfo(msg.getPlayerId());
		// 处理玩家积分
		playerInfo.addIntegral(areaType, msg.getIsWin(), msg.getIntegralChange());
		// 当前赛区积分
		int nowIntegral = playerInfo.getIntegral(areaType);
		// 是否需要开启巅峰赛
		if (areaType == IntegralAreaType.PRACTICE.value()
			&& !playerInfo.haveAreaData(IntegralAreaType.PINNACLE.value())) {
			// 练习赛达到一定分数开启巅峰赛
			int needIntegral = BaseService.getConfigIntByDefault(PriConfigKeyName.INTEGRAL_OPEN_PINNACLE);
			if (nowIntegral > needIntegral) {
				// 开启巅峰赛
				playerInfo.initAreaInfo(IntegralAreaType.PINNACLE.value());
				// 推送中心服
				group.sendPlayerAreaToCenter(playerInfo, IntegralAreaType.PINNACLE.value());
				// 保存数据
				IntegralGameRedis.savePlayer(playerInfo);
				// 通知客户端
				if (human != null) {
					human.push2Gateway(new SCOpenIntegralPinnacle());
				}
			}

		}
		if (human == null) {
			LogHelper.logBI(new IntegralFightLog(msg.getPlayerId(), zoneId, msg.getAreaType()));
			AMLog.LOG_ERROR.error("天梯结束后玩家不在内存, 任务无法处理,丢失一份战斗监控数据,msg=" + JsonUtils.O2S(msg));
			return;
		}
		if (msg.getIsWin()) {
			// 战斗胜利
			IIntegralArenaEventSource eventSource = EventSourceManager.getInstance().getEventSource(
				IIntegralArenaEventSource.class);
			if (eventSource != null) {
				eventSource.onIntegralArenaWin(human, 0);
			}
		}
		// 发送玩家线程,处理天梯任务和监控
		SSFightMonitorInfo monitorInfo = new SSFightMonitorInfo(msg.getIsWin(), msg.getIntegralChange(),
			msg.getFightParams(), nowIntegral, msg.getOpponentPlayerId(), msg.getTaskConditions(), msg.getAreaType());
		Message monitorMsg = Message.buildByZoneId(human.getPlayerId(), human.getZoneId(), monitorInfo, null, null,
			null);
		Globals.getMsgProcessDispatcher().put(monitorMsg);
		// 记录BI
		LogHelper.logBI(new IntegralFightLog(human, msg.getAreaType()));
	}

	@Override
	public void ssIntegralArenaRankReq(SSIntegralArenaRankReq msg, Head head) {
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		int groupId = msg.getGroupId(); // 分组Id
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		if (msg.getSendCenter()) {
			// 要转发中心服
			int rankVersion = gameManager.getRankVersion(groupId, msg.getAreaType());
			List<IntegralPlayerChange> infoChanges = gameManager.getPlayerInfoChanges(groupId);
			msg = new SSIntegralArenaRankReq(groupId, msg.getAreaType(), rankVersion, infoChanges, false);
			IntegralCommonService.benfuToCenter(msg, 0L, head.getServerId());
			return;
		}
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		int type = msg.getAreaType();
		IntegralGroup group = gameManager.getGroupById(groupId);
		List<IntegralRankItem> rankList = group.getRankList(type);
		SCGetRankData res = new SCGetRankData();
		res.setRankType(type);
		res.setList(new ArrayList<RankInfo>());
		if (rankList != null) {
			for (int i = 0; i < rankList.size(); i++) {
				if (i >= IntegralConst.RANK_SHOW_MAX) {
					break;
				}
				IntegralRankItem rankItem = rankList.get(i);
				RankInfo rankInfo = new RankInfo();
				rankInfo.setRank(i + 1);
				RankFunction.rankInfoInit(rankInfo, rankItem.getInformation());
				List<Long> list = new ArrayList<Long>();
				list.add((long) rankItem.getIntegral());
				rankInfo.setValues(list);
				if (rankInfo.getPid() == human.getPlayerId()) {
					// 自己信息
					res.setMyRankInfo(rankInfo);
				}
				rankInfo.setDisplayServerId(rankItem.getDisplayServerId());
				res.getList().add(rankInfo);
			}
			if (res.getMyRankInfo() == null) {
				// 本人不在排行榜上
				RankInfo myInfo = new RankInfo();
				myInfo.setRank(-1);
			}
		}
		IntegralBaseCache baseCache = gameManager.getBaseCache(human.getServerId());
		int season = baseCache.getSeason();
		if (type == RankType.INTEGRAL_AMEND_LAST.value() || type == RankType.INTEGRAL_NOLIMIT_LAST.value()
				|| type == RankType.INTEGRAL_PRACTICE_LAST.value() || type == RankType.INTEGRAL_PINNACLE_LAST.value()) {
			season--;
		}
		List<String> params = new ArrayList<>(2);
		params.add(Integer.toString(season));
		params.add(Integer.toString(baseCache.getWeek()));
		res.setEachParams(params);
		human.push2Gateway(res);
	}

	@Override
	public void ssKuafuStateChange(SSKuafuStateChange msg, Head head) {
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
		}
		Human human = (Human) Globals.getHuman(zoneId, head.getPid());
		if (human == null) {
			return;
		}
		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
		if (msg.getState() == IntegralConst.KUAFU_STATE_ENTER_MATCH) {
//			// 进入匹配
//			roleStateManager.tryEnter(ActivityState.KUAFU_MATCHING);
//			if (AMLog.LOG_COMMON.isInfoEnabled()) {
//				AMLog.LOG_COMMON.info("玩家:" + human.getName() + ",进入跨服匹配,当前状态:"
//										+ roleStateManager.getCurActivityStates());
//			}
		} else if (msg.getState() == IntegralConst.KUAFU_STATE_LEAVE_MATCH) {
			// 离开匹配
			roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
			IntegralCommonService.playerLeaveMatch(human.getPlayerId());
			human.push2Gateway(new SCCancelIntegralMatch(msg.getAreaType()));
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("玩家:" + human.getName() + ",离开跨服匹配,当前状态:"
										+ roleStateManager.getCurActivityStates());
			}
		} else if (msg.getState() == IntegralConst.KUAFU_STATE_ENTER_FIGHT) {
			// 进入战斗
			CrossServerManager.setPlayerCrossBattle(human, CrossBattleTypeEnum.INTEGRAL_PVP_FIGHT);
			roleStateManager.tryEnter(ActivityState.KUAFU_FIGHTING);
			IntegralCommonService.playerEnterFight(human.getPlayerId(), msg.getAreaType());
			human.getTemporaryManager().enterDuplicateScene(DuplicateScene.INTEGRAL_FIGHT, true);
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("玩家:" + human.getName() + ",进入跨服战斗,当前状态:"
										+ roleStateManager.getCurActivityStates());
			}
		} else if (msg.getState() == IntegralConst.NORMAL_LEAVE_FIGHT
					|| msg.getState() == IntegralConst.GAME_SERVER_INACTIVE
					|| msg.getState() == IntegralConst.OTHER_GAME_SERVER_INACTIVE) {
			// 离开战斗
			CrossServerManager.setPlayerCrossBattle(human, CrossBattleTypeEnum.NONE);
			roleStateManager.tryExit(ActivityState.KUAFU_FIGHTING);
			IntegralCommonService.playerLeaveFight(human.getPlayerId());
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("玩家:" + human.getName() + ",离开跨服战斗,当前状态:"
										+ roleStateManager.getCurActivityStates());
			}
			// 移除战斗服的绑定channel
			CrossServerManager.removePlayerBind(human.getPlayerId(), ServerTypeEnum.BATTLE);
			human.getTemporaryManager().leaveDuplicateScene(true);

			// TODO 本服和战斗服断连的临时处理:有一方本服断开,双方都踢下线
			if (msg.getState() == IntegralConst.GAME_SERVER_INACTIVE) {
				// 玩家本服未宕机,但是断开了和战斗服连接
				AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
					"@Minc.close.gameServer.inactive");
			}
			if (msg.getState() == IntegralConst.OTHER_GAME_SERVER_INACTIVE) {
				// 玩家本人正常,但是对方本服和战斗服断连
				AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
					"@Minc.close.other.gameServer.inactive");
			}
		} else if (msg.getState() == IntegralConst.KUAFU_STATE_ENTER_PERPARING) {
			IntegralCommonService.playerEnterPreparing(human.getPlayerId());
		}
	}

//	@Override
//	public void ssIntegralArenaRankRes(SSIntegralArenaRankRes msg, Head head) {
//		SCGetRankData res = new SCGetRankData();
//		res.setRankType(msg.getType());
//		res.setList(new ArrayList<RankInfo>());
//		List<IntegralArenaPlayer> rankPlayers = msg.getPlayers();
//		if (rankPlayers != null) {
//			for (int i = 0; i < rankPlayers.size(); i++) {
//				IntegralArenaPlayer player = rankPlayers.get(i);
//				RankInfo rankInfo = new RankInfo();
//				rankInfo.setRank(player.getRank());
//				rankInfo.setPid(player.getInformation().getPlayerId());
//				rankInfo.setSid(player.getInformation().getZoneId());
//				rankInfo.setName(player.getInformation().getNickName());
//				rankInfo.setPlayerHead(player.getInformation().getIcon());
//				rankInfo.setUnionName(player.getInformation().getUnionName());
//				rankInfo.setLevel(player.getInformation().getLevel());
//				List<Long> list = new ArrayList<Long>();
//				list.add((long) player.getIntegral());
//				rankInfo.setValues(list);
//				// 自己信息
//				if (player.getInformation().getPlayerId() == head.getPid()) {
//					res.setMyRankInfo(rankInfo);
//				}
//				res.getList().add(rankInfo);
//			}
//		}
//		List<String> params = new ArrayList<>(2);
//		params.add(Integer.toString(msg.getSeason()));
//		params.add(Integer.toString(msg.getWeek()));
//		res.setEachParams(params);
//		CoreBoot.push2Gateway(res, head.getPid(), head.getSid(), msg.getChannel());
//	}

	@Override
	public void ssIntegralRankReward(SSIntegralRankReward msg, Head head) {
		IntegralReward reward = msg.getRewards();
		MailDictIdEnum mailDictIdEnum = MailDictIdEnum.indexOf(reward.getMailId());
		IAwardSendType awardSendType = null;
		switch (mailDictIdEnum) {
			case IntegralArenaReward:
				awardSendType = AwardSendType.INTEGRAL_AWARD;
				break;
			case IntegralNoLimitRankReward:
				awardSendType = AwardSendType.IntegralNoLimitRankReward;
				break;
			case IntegralAmendRankReward:
				awardSendType = AwardSendType.IntegralAmendRankReward;
				break;
		}
//		Long[] array = new Long[reward.getPlayerIds().size()];
//		for (int i = 0; i < reward.getPlayerIds().size(); i++) {
//			long playerId = reward.getPlayerIds().get(i);
//			array[i] = playerId;
//		}
//		if (!reward.getIbs().isEmpty()) {
//			MailService.sendMail(mailEnum, reward.getIbs(), head.getSid(), array);
//		}
		Integer titleId = null;
		if (StringUtils.isNotEmpty(reward.getTitle())) {
			titleId = Integer.parseInt(reward.getTitle());
		}
//		if (titleId != null) {
//			for (Long playerId : array) {
//				titleService.addTitle(head.getSid(), playerId, titleId);
//			}
//		}
		int zoneId = head.getZoneId();
		if (head.getServerId() != 0) {
			zoneId = Globals.getZoneId(head.getServerId());
			;
		}
		for (IntegralPlayerRewardInfo playerInfo : reward.getPlayerInfos()) {
			awardSendService.addAward(awardSendType, zoneId, playerInfo.getPlayerId(), reward.getIbs(),
				new Object[] {}, new Object[] { playerInfo.getRank() });
			if (titleId != null) {
				titleService.addTitle(zoneId, playerInfo.getPlayerId(), titleId);
			}
		}

	}

//	@Override
//	public Result getIntegralArenaRank(Map<String, String> paramsMap) {
//		Result jsonResult = new Result();
//		jsonResult.setCode(ErrorCodeConstants.INTEGRAL_KUAFU_NOT_CONNECT);
//		return jsonResult;
//	}

	// ----------应对中心服未能连接后的消息--------------------

//	public void csCancelIntegralMatch(CSCancelIntegralMatch msg, Head head) {
//		Human human = (Human) Globals.getHuman(head.getSid(), head.getPid());
//		if (human == null) {
//			AMLog.LOG_STDOUT.info("本服特殊处理取消匹配，处理消息CSCancelIntegralMatch失败，找不到Human." + head.getPid());
//			// 强制断开连接
//			AMFrameWorkBoot.closeChannel(msg.getChannel(), AMFrameWorkBoot.i18n("SYS_010010"),
//				"处理CSCancelIntegralMatch失败，无Human." + head.getPid());
//			return;
//		}
//		if (CrossServerManager.isCrossServerConnected(ServerTypeEnum.KUAFU, human)) {
//			AMLog.LOG_COMMON
//				.error("本服特殊处理取消匹配， 发现跨服/中心服已启动， 直接扔给它，本服不处理   msg {}, the code is {}", msg, head.getCode());
//			CoreBoot.benfuToKuafu(msg, head.getPid(), head.getSid());
//			return;
//		}
//		AMLog.LOG_COMMON.error("本服特殊处理取消匹配， 发现跨服/中心服未启动，直接在本服处理该消息,   msg {}, the code is {}, human={}", msg,
//			head.getCode(), human);
//		RoleStateManager roleStateManager = human.getSceneObject().getRoleStateManager();
//		roleStateManager.tryExit(ActivityState.KUAFU_MATCHING);
//		// 通知客户端
//		CoreBoot.push2Gateway(new SCCancelIntegralMatch(1), head.getPid(), head.getSid(), msg.getChannel());
//		//
//		sendIntegralArenaOnError(msg.getType(), head.getSid(), head.getPid(), msg.getChannel());
//	}

//	private void sendIntegralArenaOnError(int areaType, int zoneId, long playerId, Channel channel) {
//		SCPlayerIntegralArena resp = new SCPlayerIntegralArena();
//		resp.setAreaType(areaType);
//		resp.setIsOpen(0);
//
//		IntegralArenaPlayer playerInfo = new IntegralArenaPlayer();
//		playerInfo.setState(1);
//		resp.setPlayerInfo(playerInfo);
//
//		CoreBoot.push2Gateway(resp, playerId, zoneId, channel);
//	}

	@Override
	public void SSRemoveIntegralFight(com.game2sky.prilib.communication.game.integralArena.SSRemoveIntegralFight msg,
										Head head) {
	}

	@Override
	public void ssConnectCenterIntegralEvent(SSConnectCenterIntegralEvent msg, Head head) {
		IntegralGameManager.getInstance().sendPlayersToCenter();
	}

	@Override
	public void ssRefreshGameIntegralRank(SSRefreshGameIntegralRank msg, Head head) {
		List<IntegralCurrentRank> rankList = msg.getRankList();
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		gameManager.refreshRankCache(msg.getGroupId(), rankList);
	}

	@Override
	public void ssRefreshGameIntegralBase(SSRefreshGameIntegralBase msg, Head head) {
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		if (msg.getIsWeekEnd()) {
			gameManager.weekReset(msg.getGroupId(), msg.getBaseInfo().getWeekStartTime(),msg.getForcibly());
		}
		gameManager.refreshBaseCache(msg.getGroupId(), msg.getBaseInfo());
	}

	@Override
	public Result getIntegralArenaRank(Map<String, String> paramsMap) {

		return null;
	}

	@Override
	public void ssDisconnectIntegralEvent(SSDisconnectIntegralEvent msg, Head head) {
	}

	@Override
	public void ssPlayerInfoChange(SSPlayerInfoChange msg, Head head) {
		List<DisplayInformationType> list = msg.getValues();
		IntegralGameManager.getInstance().addPlayerInfoChange(head.getPid(), list);
	}

	@Override
	public void onClientBattleChannelInactive(AbstractHuman abstractHuman) {
		// 去玩家线程
		Human human = (Human) abstractHuman;
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.INTEGRAL_PVP_FIGHT);
		if (isCrossBattle) {
			SSKuafuStateChange quit = new SSKuafuStateChange(IntegralConst.GAME_SERVER_INACTIVE, 0);
			Message msg = Message.buildByZoneId(abstractHuman.getPlayerId(), abstractHuman.getZoneId(), quit, null,
				null, null);
			Globals.getMsgProcessDispatcher().put(msg);
		}
	}

	@Override
	public void ssChangePlayerIntegral(SSChangePlayerIntegral msg, Head head) {
		IntegralGameManager gameManager = IntegralGameManager.getInstance();
		Long playerId = msg.getPlayerId();
		IntegralGamePlayer playerInfo = gameManager.getPlayerInfo(playerId);
		if (playerInfo == null) {
			AMLog.LOG_ERROR.error("Can't modify player integral. Because error playerId=" + playerId);
			return;
		}
		int zoneId = Globals.getZoneId(playerInfo.getServerId());
		int areaType = msg.getAreaType();
		boolean newVersion = gameManager.getGroupByServerId(playerInfo.getServerId()).isNewVersion();

		if (newVersion && !IntegralAreaType.isNewVersion(msg.getAreaType())) {
			AMLog.LOG_ERROR.error("Can't modify player integral. now is NewVersion. but areaType=" + areaType);
			return;
		}
		if (!newVersion && !IntegralAreaType.isOldVersion(msg.getAreaType())) {
			AMLog.LOG_ERROR.error("Can't modify player integral. now is oldVersion. but areaType=" + areaType);
			return;
		}
		if (msg.getType() == IntegralConst.CHANGE_DEDUCT) {
			// 减少积分
			int decuct = msg.getIntegral();
			if (decuct == -1) {
				// 恢复到初始积分
				int myIntegral = gameManager.getIntegral(msg.getPlayerId(), areaType);
				DictIntegralArenaArea arenaArea = IntegralConfigCenter.getAreaMap().get(areaType);
				int initIntegral = arenaArea.getInitialIntegral();
				if (myIntegral <= initIntegral) {
					AMLog.LOG_COMMON.info("玩家ID={}请求恢复初始积分,当前赛区{}积分为{},少于初始积分{},不执行操作", msg.getPlayerId(), areaType,
						myIntegral, initIntegral);
					return;
				}
				decuct = myIntegral - initIntegral;
			}
			int playerIntegral = IntegralGameManager.getInstance().changePlayerIntegral(areaType, msg.getPlayerId(),
				false, decuct);
			AMLog.LOG_COMMON.info("玩家ID={}请求扣除积分{},当前赛区{}剩余积分为{}", msg.getPlayerId(), decuct, areaType, playerIntegral);
			// 发送邮件
			MailService.sendMail(MailDictIdEnum.BI_MODIFY_INTEGRAL, zoneId, playerId);
		} else if (msg.getType() == IntegralConst.CHANGE_ADD) {
			// 增加积分
			int playerIntegral = IntegralGameManager.getInstance().changePlayerIntegral(areaType, msg.getPlayerId(),
				true, msg.getIntegral());
			AMLog.LOG_COMMON.info("玩家ID={}请求增加积分{},当前赛区{}剩余积分为{}", msg.getPlayerId(), msg.getIntegral(), areaType,
				playerIntegral);
		} else if (msg.getType() == IntegralConst.CHANGE_SET) {
			int haveIntegral = playerInfo.getIntegral(areaType);
			if (haveIntegral == msg.getIntegral()) {
				return;
			}
			boolean isWin = msg.getIntegral() > haveIntegral;
			int changeIntegral = Math.abs(msg.getIntegral() - haveIntegral);
			playerInfo.addIntegral(areaType, isWin, changeIntegral);
			AMLog.LOG_COMMON.info("玩家ID={}设置积分{},当前赛区{}的实时积分为{}", msg.getPlayerId(), msg.getIntegral(), areaType,
				playerInfo.getIntegral(areaType));
		}
	}

	@Override
	public void ssIntegralBannedPlayer(SSIntegralBannedPlayer msg, Head head) {
		Human human = (Human) Globals.getHumanByServerId(head.getServerId(), head.getPid());
		PlayerMonitorManager monitorManager = human.getMonitorManager();
		if (msg.getIsBanned()) {
			monitorManager.bannedIntegral(msg.getAreaType(), msg.getMinute());
			AMLog.LOG_COMMON.info("接收BI后台消息,成功封禁在线玩家{}的天梯赛区{}, 时间:{}分钟", human.getPlayerId(), msg.getAreaType(),
				msg.getMinute());
		} else {
			monitorManager.unBannedIntegral(msg.getAreaType());
			AMLog.LOG_COMMON.info("接收BI后台消息,成功解禁在线玩家{}的天梯赛区{}", human.getPlayerId(), msg.getAreaType());
		}
	}

	@Override
	public void ssFightMonitorInfo(SSFightMonitorInfo msg, Head head) {
		Human human = (Human) Globals.getHuman(head.getZoneId(), head.getPid());
		if (human == null) {
			AMLog.LOG_ERROR.error("human={} is null!", head.getPid());
			return;
		}
		// 刷新天梯任务进度
		human.getIntegralManager().refreshTaskCondition(msg.getTaskConditions());
		// 完成天梯战斗事件
		IIntegralFightEventSource eventSource = EventSourceManager.getInstance().getEventSource(
			IIntegralFightEventSource.class);
		if (eventSource != null) {
			eventSource.onFinishIntegralFight(human, msg.getAreaType(), msg.getIsWin());
		}
		// 记录监控数据
		int needMonitor = BaseService.getConfigIntByDefault(PriConfigKeyName.MONTIOR_INTEGRAL_ISOPEN);
		if (needMonitor != 0) {
			// 开启了天梯监控
			human.getMonitorManager().monitorIntegral(msg);
		}
	}

	@Override
	public void ssCreateIntegralRobotReq(SSCreateIntegralRobotReq msg, Head head) {
		if (msg.getAddress() == null) {
			return;
		}
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(msg.getAddress());
		if (IntegralWaitFightCenter.isTimeout(msg.getFightId())) {
			// 此战斗重连超时, 战斗已被强制结束, 战斗服已经通知了本服退出状态
			AMLog.LOG_COMMON.info("战斗服serverAddress={},连接超时,战斗id={},robotPlayerId={} 已被强制结束", serverAddress.getFlag(),
				msg.getFightId(), msg.getCopyPlayerId());
			return;
		}
		// 是否正在创建连接
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(
			serverAddress.getFlag());
		if (connectStatus == ClientConnectStatus.NONE_CONNECT) {
			CrossServerManager.init(serverAddress, true);
			AMLog.LOG_COMMON.info(
				"开始连接战斗服, IntegralGameService.SSCreateIntegralRobotReq:: 本服与[{}]创建连接,robotPlayerId[{}],fightId[{}]",
				serverAddress.getFlag(), msg.getCopyPlayerId(), msg.getFightId());
			// 异步消息
			ScheduleCreateIntegralRobot scheduleMsg = new ScheduleCreateIntegralRobot(msg, head);
			Globals.getScheduleService().scheduleOnce(scheduleMsg, 1000);
		} else {
			AMLog.LOG_COMMON.info("战斗服[{}]连接成功,准备加载战斗数据. robotPlayerId[{}],fightId[{}]", serverAddress.getFlag(),
				msg.getCopyPlayerId(), msg.getFightId());
			CreateIntegralRobotTask task = new CreateIntegralRobotTask(head.getServerId(), msg);
			Globals.getAsyncService().createOperationAndExecuteAtOnce(task);
		}
	}
	
	
	@Override
	public void CSIntegralChat(CSIntegralChat msg, Head head) {
		Human human = (Human) Globals.getHuman(head.getZoneId(), head.getPid());
		if (human == null) {
			return;
		}
		if(!human.isOnKuafuFight()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralChat.getCode(), ErrorCodeConstants.INTEGRAL_KUAFU_NOT_CONNECT);
			return;
		}
		DictIntegralChat randomChat = DictIntegralChat.randomChat(msg.getMood());
		if(randomChat == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCIntegralChat.getCode(), ErrorCodeConstants.CONFIG_ERROR);
			return;
		}
		CoreBoot.benfuToBattle(new SSIntegralChat(randomChat.getChatId(), human.getPlayerId()), human.getPlayerId(), human.getZoneId());
		human.push2Gateway(new SCIntegralChat(randomChat.getChatId(), human.getPlayerId()));
	}
	
	
	
}
