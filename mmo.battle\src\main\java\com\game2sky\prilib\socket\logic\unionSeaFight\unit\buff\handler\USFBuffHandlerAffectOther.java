package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler;

import gnu.trove.map.hash.TIntObjectHashMap;

import com.game2sky.prilib.core.dict.domain.DictUSFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.framework.util.KV;
import com.game2sky.publib.framework.util.StringUtils;

/**
 * 影响其他buff的valueEffect效果.
 *
 * <AUTHOR>
 * @version v0.1 2019年8月10日 下午3:12:29  Jet Lee
 */
public class USFBuffHandlerAffectOther implements IUSFBuffHandler {

	private static final USFBuffHandlerAffectOther instance = new USFBuffHandlerAffectOther();

	private USFBuffHandlerAffectOther() {
	}

	public static USFBuffHandlerAffectOther getInstance() {
		return instance;
	}

	@Override
	public void load(USFBuff usfBuff) {
		AbstractUSFUnit ownerUnit = usfBuff.getOwnerUnit();
		boolean isPlayer = ownerUnit instanceof USFPlayer;
		if (!isPlayer) {
			return;
		}
		USFPlayer player = (USFPlayer) ownerUnit;
		int buffId = usfBuff.getClientInfo().getBuffId();
		DictUSFBuff dictUSFBuff = DictUSFBuff.getDictUSFBuff(buffId);
		String params = dictUSFBuff.getParams();
		if (StringUtils.isEmpty(params)) {
			// 参数错误
			return;
		}
		String[] split = params.split(";");
		if (split.length != 2) {
			// 参数错误
			return;
		}
		String[] buffList = split[0].split(",");
		if (buffList.length <= 0) {
			// 参数错误
			return;
		}
		KV<Integer, Float> kv = new KV<Integer, Float>();
		String[] array = split[1].split(",");
		if (array.length != 2) {
			return;
		}
		kv.setK(Integer.parseInt(array[0]));
		kv.setV(Float.parseFloat(array[1]));
		TIntObjectHashMap<KV<Integer, Float>> value = new TIntObjectHashMap<KV<Integer, Float>>();
		for (int i = 0; i < buffList.length; i++) {
			int tmpBuffId = Integer.parseInt(buffList[i]);
			value.put(tmpBuffId, kv);
		}
		player.getFightController().addAffect(buffId, value);
	}

	@Override
	public void handler(USFBuff usfBuff) {
	}

	@Override
	public void unload(USFBuff usfBuff) {
		AbstractUSFUnit ownerUnit = usfBuff.getOwnerUnit();
		boolean isPlayer = ownerUnit instanceof USFPlayer;
		if (!isPlayer) {
			return;
		}
		USFPlayer player = (USFPlayer) ownerUnit;
		int buffId = usfBuff.getClientInfo().getBuffId();
		player.getFightController().removeAffect(buffId);
	}

}
