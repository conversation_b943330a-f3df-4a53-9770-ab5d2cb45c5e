package com.game2sky.prilib.socket.logic.integral.match;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.integralArena.SCCancelIntegralMatch;
import com.game2sky.prilib.communication.game.integralArena.SCIntegralFightReady;
import com.game2sky.prilib.communication.game.integralArena.SSCreateIntegralRobotReq;
import com.game2sky.prilib.communication.game.integralArena.SSIntegralMatchStateChange;
import com.game2sky.prilib.communication.game.integralArena.SSKuafuStateChange;
import com.game2sky.prilib.communication.game.integralArena.SSStartIntegralFightReq;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictErrorMessage;
import com.game2sky.prilib.core.socket.logic.battle.fdefine.EnumDefine;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralConst;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.socket.logic.integral.match.IntegralMatchReadyQueue.MatchReady;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.common.ErrorMessage;
import com.game2sky.publib.communication.game.struct.ServerAddress;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.match.constants.MatchType;
import com.game2sky.publib.socket.logic.base.IdTools;
import com.game2sky.publib.socket.logic.server.battle.BattleServerInfo;
import com.game2sky.publib.socket.logic.server.battle.IServerSelector;

/**
 * 跨服赛等待准备队列
 * <AUTHOR>
 * @version v0.1 2017年10月24日 下午7:51:20  zhoufang
 */
public class IntegralMatchReadyQueue {

	private static final IntegralMatchReadyQueue instance = new IntegralMatchReadyQueue();

	private IntegralMatchReadyQueue() {
	}

	public static IntegralMatchReadyQueue getInstance() {
		return instance;
	}

	private LinkedList<MatchReady> readyList = new LinkedList<MatchReady>();
	private Map<Long, MatchReady> readyMap = new HashMap<Long, MatchReady>();

	/**
	 * 加入准备队列
	 * @param branch
	 * @param areaType 
	 * @param attackerId
	 * @param attackZoneId
	 * @param defenderId
	 * @param defendZoneId
	 */
	public void addMatchReady(int branch, int areaType, long attackerId, int attackServerId, long defenderId,
								int defendServerId, boolean attackIsRobot, boolean defendIsRobot, int attackIntegral,
								int defendIntegral) {
		Matcher attacker = new Matcher(attackerId, attackServerId, attackIsRobot, attackIntegral);
		Matcher defender = new Matcher(defenderId, defendServerId, defendIsRobot, defendIntegral);
		MatchReady matchReady = new MatchReady(branch, areaType, attacker, defender);
		this.readyList.addLast(matchReady);
//		Map<Long, MatchReady> hashMap = this.readyMap.get(areaType);
//		if (hashMap == null) {
//			hashMap = new HashMap<Long, MatchReady>();
//			this.readyMap.put(areaType, hashMap);
//		}
		this.readyMap.put(attackerId, matchReady);
		this.readyMap.put(defenderId, matchReady);
	}

	/** 检测状态 */
	public void execute() {
		long now = System.currentTimeMillis();
		Iterator<MatchReady> iterator = this.readyList.iterator();
		while (iterator.hasNext()) {
			MatchReady matchReady = iterator.next();
			if (matchReady.isReady()) {
				AMLog.LOG_COMMON.info("Both parties of the ladder {} and {} are ready and have started to fight.", matchReady.attacker.playerId,
					matchReady.defender.playerId);
				iterator.remove();
				removeMatchReady(matchReady);
				continue;
			}
			if (!IntegralConfigCenter.isOpen(matchReady.areaType)) {
				// 天梯赛已经关闭,全部踢出队列
				AMLog.LOG_COMMON.info("Both parties of the ladder {} and {} are not prepared, the ladder is closed, kicking out of the preparation queue.", matchReady.attacker.playerId,
					matchReady.defender.playerId);
				iterator.remove();
				removeMatchReady(matchReady);
				matchReady.quitMatching();
				continue;
			}
			if (matchReady.isTimeout(now)) {
				AMLog.LOG_COMMON.info("Both parties of the ladder {} and {} are not all prepared, and they are out of time. Start the automatic preparation for forcibly entering the battle.", matchReady.attacker.playerId,
					matchReady.defender.playerId);
				iterator.remove();
//				removeMatchReady(matchReady);
//				matchReady.destory();
				matchReady.ready(matchReady.attacker.playerId);
				matchReady.ready(matchReady.defender.playerId);
				continue;
			}
			AMLog.LOG_COMMON.info("Both parties of the ladder {} and {} have not yet been prepared, and have not yet been overtime, continue to wait. Matching successful start time{}", matchReady.attacker.playerId,
				matchReady.defender.playerId,matchReady.time);
		}
	}

	private void removeMatchReady(MatchReady matchReady) {
		this.readyMap.remove(matchReady.attacker.playerId);
		this.readyMap.remove(matchReady.defender.playerId);
	}

	/**
	 * 获取此玩家的挑战者Id
	 * @param playerId
	 * @return
	 */
	public long getChallengerId(long playerId) {
		MatchReady matchReady = this.readyMap.get(playerId);
		if (matchReady != null) {
			if (matchReady.attacker.playerId == playerId) {
				return matchReady.defender.playerId;
			}
			return matchReady.attacker.playerId;
		}
		return 0;
	}

	/**
	 * 玩家准备
	 * @param areaType
	 * @param playerId
	 */
	public void playerReady(long playerId) {
		MatchReady matchReady = this.readyMap.get(playerId);
		if (matchReady == null) {
			return;
		}
		matchReady.ready(playerId);
	}

	class Matcher {
		public long playerId;
		public int serverId;
		public boolean isReady;
		public boolean isRobot;
		public int integral;

		public Matcher(long playerId, int serverId, boolean isRobot, int integral) {
			this.playerId = playerId;
			this.serverId = serverId;
			this.isRobot = isRobot;
			this.integral = integral;
		}
	}

	class MatchReady {
		private int branch;
		private int areaType;
		private Matcher attacker;
		private Matcher defender;
		private long time;
		private IServerSelector serverSelector;

		public MatchReady(int branch, int areaType, Matcher attacker, Matcher defender) {
			this.branch = branch;
			this.areaType = areaType;
			this.time = System.currentTimeMillis();
			this.attacker = attacker;
			this.defender = defender;
		}

		public void quitMatching() {
			if (!attacker.isRobot) {
				quitMatchingState(attacker.playerId, attacker.serverId);
			}
			if (!defender.isRobot) {
				quitMatchingState(defender.playerId, defender.serverId);
			}
		}

		public IServerSelector getServerSelector() {
			if (serverSelector == null) {
				serverSelector = AMFrameWorkBoot.getBean(IServerSelector.class);
			}
			return serverSelector;
		}

		public void ready(long playerId) {
			if (attacker.playerId == playerId) {
				if (attacker.isReady) {
					return;
				}
				attacker.isReady = true;
				// 通知双方刷新界面
				if (!attacker.isRobot) {
					CoreBoot.dispatch2Server(new SCIntegralFightReady(areaType, 1), attacker.playerId,
						attacker.serverId, null);
				}
				if (!defender.isRobot) {
					CoreBoot.dispatch2Server(new SCIntegralFightReady(areaType, 2), defender.playerId,
						defender.serverId, null);
					// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.ready attacker.playerId={}, attacker.serverId={}", attacker.playerId, attacker.serverId);
				}
			} else {
				if (defender.isReady) {
					return;
				}
				defender.isReady = true;
				// 通知双方刷新界面
				if (!attacker.isRobot) {
					CoreBoot.dispatch2Server(new SCIntegralFightReady(areaType, 2), attacker.playerId,
						attacker.serverId, null);
				}
				if (!defender.isRobot) {
					CoreBoot.dispatch2Server(new SCIntegralFightReady(areaType, 1), defender.playerId,
						defender.serverId, null);
					// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.ready defender.playerId={}, defender.serverId={}", defender.playerId, defender.serverId);
				}
			}
			if (isReady()) {
				// 双方都准备了,移出队列
				removeMatchReady(this);
				if (readyList.contains(this)) {
					readyList.remove(this);
				}

				// 先退出匹配池
				if (!attacker.isRobot) {
					cancelMatchPool(attacker.playerId, attacker.serverId);
				}
				if (!defender.isRobot) {
					cancelMatchPool(defender.playerId, defender.serverId);
				}

				// 分配战斗服
				ServerAddress serverAddress = null;
				BattleServerInfo battleServerInfo = this.getServerSelector().select(
					MatchType.INTEGRAL_ARENA.getMaxNum());
				if (battleServerInfo == null) {
					// 暂时没有可分配的战斗服
					DictErrorMessage dictErrorMessage = DictErrorMessage
						.getErrorMessage(ErrorCodeConstants.INTEGRAL_BATTLE_SERVER_FULL);
					if (dictErrorMessage != null) {
						ErrorMessage errorMessage = new ErrorMessage(dictErrorMessage.getErrorCode(),
							dictErrorMessage.getErrorMsg(), dictErrorMessage.getErrorCode(),
							dictErrorMessage.getErrorType());
						if (!attacker.isRobot) {
							CoreBoot.dispatch2Server(errorMessage, attacker.playerId, attacker.serverId, null);
							// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.errorMessage attacker.playerId={}, attacker.serverId={}", attacker.playerId, attacker.serverId);
						}
						if (!defender.isRobot) {
							CoreBoot.dispatch2Server(errorMessage, defender.playerId, defender.serverId, null);
							// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.errorMessage defender.playerId={}, defender.serverId={}", defender.playerId, defender.serverId);
						}
					}
				} else {
					battleServerInfo.place(MatchType.INTEGRAL_ARENA.getMaxNum(), TimeUnit.SECONDS, 5);
					serverAddress = battleServerInfo.toServerAddress();
				}

				// AMLog.LOG_MONITER.info("nhan2k3: ready battleServerInfo={}, serverAddress={}", battleServerInfo, serverAddress);

				long fightId = IdTools.nextId();
				// nhan2k3 integral Notify each server
				if (attacker.isRobot) {
					CoreBoot.dispatch2Server(new SSCreateIntegralRobotReq(areaType, EnumDefine.FightCampEnum_Left,
						fightId, serverAddress, attacker.playerId, attacker.integral,defender.serverId), 0, attacker.serverId, null);
				} else {
					CoreBoot.dispatch2Server(new SSStartIntegralFightReq(areaType, EnumDefine.FightCampEnum_Left, fightId, serverAddress),
						attacker.playerId, attacker.serverId, null);
					// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.isReady attacker.playerId={}, attacker.serverId={}", attacker.playerId, attacker.serverId);

				}
				if (defender.isRobot) {
					CoreBoot.dispatch2Server(new SSCreateIntegralRobotReq(areaType, EnumDefine.FightCampEnum_Right,
						fightId, serverAddress, defender.playerId, defender.integral,attacker.serverId), 0, defender.serverId, null);
				} else {
					CoreBoot.dispatch2Server(new SSStartIntegralFightReq(areaType, EnumDefine.FightCampEnum_Right, fightId, serverAddress),
						defender.playerId, defender.serverId, null);
					// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.isReady defender.playerId={}, defender.serverId={}", defender.playerId, defender.serverId);
				}

			}
		}

		/**
		 * 通知匹配线程从匹配池移除玩家
		 * @param playerId
		 * @param serverId
		 */
		private void cancelMatchPool(long playerId, int serverId) {
			SSIntegralMatchStateChange res = new SSIntegralMatchStateChange();
			res.setAreaType(areaType);
			res.setPlayerId(playerId);
			res.setAction(IntegralConst.MATCH_STATE_CANCEL);
			Message msg = Message.buildByServerId(playerId, serverId, res, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
		}

		/**
		 * 通知匹配线程,玩家继续匹配
		 * @param playerId
		 * @param serverId
		 */
		private void continueMatch(long playerId, int serverId) {
			SSIntegralMatchStateChange res = new SSIntegralMatchStateChange();
			res.setAreaType(areaType);
			res.setPlayerId(playerId);
			res.setAction(IntegralConst.MATCH_STATE_CONTINUE);
			Message msg = Message.buildByServerId(playerId, serverId, res, null, null, null);
			Globals.getMsgProcessDispatcher().put(msg);
		}

		/**
		 * 通知玩家本服,退出跨服匹配状态
		 * @param playerId
		 * @param serverId
		 */
		private void quitMatchingState(long playerId, int serverId) {
			SSKuafuStateChange quit = new SSKuafuStateChange(IntegralConst.KUAFU_STATE_LEAVE_MATCH, areaType);
			IntegralCommonService.centerToBenfu(quit, playerId, serverId);
		}

		public boolean isReady() {
			return attacker.isReady && defender.isReady;
		}

		public boolean isTimeout(long now) {
			return now >= time + IntegralConst.MATCH_SUCCESS_READY - 5000L;
		}
		
		public void destory(Matcher matcher) {
			if (matcher.isReady) {
				// 继续匹配
				if(!matcher.isRobot){
					continueMatch(matcher.playerId, matcher.serverId);
				}
				return;
			} 
			// 退出匹配
			if (!matcher.isRobot) {
				cancelMatchPool(matcher.playerId, matcher.serverId);
				quitMatchingState(matcher.playerId, matcher.serverId);
				CoreBoot.dispatch2Server(new SCCancelIntegralMatch(areaType), matcher.playerId, matcher.serverId,
					null);
				// AMLog.LOG_MONITER.info("nhan2k3: MatchReady.destory matcher.playerId={}, matcher.serverId={}", matcher.playerId, matcher.serverId);
			}
		}

		public void destory() {
			// 继续或者退出匹配
			destory(attacker);
			destory(defender);
		}
	}

}
