package com.game2sky.prilib.benfu.logic.autochess;

import com.game2sky.prilib.core.socket.logic.autochess.AutoChessBenfuManager;
import com.game2sky.publib.Globals;

/**
 * 自走棋本服启动.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月9日 下午3:22:32  Jet Lee
 */
public class AutoChessBenfuStart {

	private AutoChessBenfuStart() {
	}

	public static void start() {
		new AutoChessBenfuMessageProcessor();
		AutoChessBenfuManager.getInstance().load();
		
		// 启动定时器
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduleAutoChessBenfuTick(),
			ScheduleAutoChessBenfuTick.DELAY, ScheduleAutoChessBenfuTick.PERIOD);
	}

}
