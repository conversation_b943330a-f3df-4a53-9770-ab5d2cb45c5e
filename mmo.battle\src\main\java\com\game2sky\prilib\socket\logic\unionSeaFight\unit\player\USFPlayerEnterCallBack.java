package com.game2sky.prilib.socket.logic.unionSeaFight.unit.player;

import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFPlayerEnterReq;
import com.game2sky.prilib.socket.logic.unionSeaFight.USFightBattleService;
import com.game2sky.publib.communication.common.ISysCallBack;

/**
 * TODO 玩家进入海战回调
 *
 * <AUTHOR>
 * @version v0.1 2018年7月30日 下午4:07:52  guojijun
 */
public class USFPlayerEnterCallBack implements ISysCallBack {

	private USFightBattleService service;
	private long uqId;
	private SSUSFPlayerEnterReq ssUSFPlayerEnterReq;

	@Override
	public void apply() {
		this.service.playerEnter(uqId, ssUSFPlayerEnterReq);
	}

	public USFightBattleService getService() {
		return service;
	}

	public void setService(USFightBattleService service) {
		this.service = service;
	}

	public long getUqId() {
		return uqId;
	}

	public void setUqId(long uqId) {
		this.uqId = uqId;
	}

	public SSUSFPlayerEnterReq getSsUSFPlayerEnterReq() {
		return ssUSFPlayerEnterReq;
	}

	public void setSsUSFPlayerEnterReq(SSUSFPlayerEnterReq ssUSFPlayerEnterReq) {
		this.ssUSFPlayerEnterReq = ssUSFPlayerEnterReq;
	}

}
