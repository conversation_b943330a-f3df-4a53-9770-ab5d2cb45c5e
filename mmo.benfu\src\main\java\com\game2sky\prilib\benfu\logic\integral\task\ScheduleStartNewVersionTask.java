package com.game2sky.prilib.benfu.logic.integral.task;

import java.util.LinkedList;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.game2sky.prilib.benfu.logic.integral.IntegralGameManager;
import com.game2sky.prilib.communication.game.integralArena.SCIntegralStartNewVersion;
import com.game2sky.publib.Globals;
import com.game2sky.publib.schedule.WheelScheduledTask;
import com.game2sky.publib.socket.logic.human.AbstractHuman;

/**
 * 天梯定时任务-推送新版本玩法开启
 * <AUTHOR>
 * @version v0.1 2020年4月15日 下午11:54:42  zhoufang
 */
public class ScheduleStartNewVersionTask extends WheelScheduledTask{
	
	/** 天梯分组 */
	private int branch;
	private SCIntegralStartNewVersion resp = new SCIntegralStartNewVersion();

	public ScheduleStartNewVersionTask(long delay, TimeUnit unit,int branch) {
		super(delay, unit);
		this.branch = branch;
	}

	@Override
	public void execute() {
		// 通知本服所有玩家开启了天梯新版本玩法
		LinkedList<Integer> list = IntegralGameManager.getInstance().getGroupServerIds().get(branch);
		Set<AbstractHuman> allHuman = Globals.getAllHuman();
		for(AbstractHuman human : allHuman){
			if(!list.contains(human.getServerId())){
				continue;
			}
			human.push2Gateway(resp);
		}
	}

	
}
