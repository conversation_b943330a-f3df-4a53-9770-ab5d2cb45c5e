package com.game2sky.prilib.socket.logic.homeland.matchv3;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.game2sky.prilib.communication.game.homeland.EnemyChangeInfo;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.homeland.center.HomelandBaseInfo;
import com.game2sky.publib.Globals;

/**
 *
 * <AUTHOR>
 * @version v0.1 2020年5月28日 上午9:28:44  daixl
 */
public class CenterHomelandGlobalManager {

	private static Map<Long, HomelandBaseInfo> centerHomeLandMap = new HashMap<Long, HomelandBaseInfo>();

	private static HomeLandCreamMatchSort sort = new HomeLandCreamMatchSort();

	public static void addHomeLandBaseInfo(HomelandBaseInfo info) {
		centerHomeLandMap.put(info.getPlayerId(), info);
	}

	public static HomelandBaseInfo getByPlayerId(long playerId) {
		return centerHomeLandMap.get(playerId);
	}

	public static HomeLandCreamMatchSort getSort() {
		return sort;
	}

	public static void getCanGet(EnemyChangeInfo enemyInfo) {
		long playerId = enemyInfo.getPid();
		HomelandBaseInfo info = centerHomeLandMap.get(playerId);
		if (info == null) {
			return;
		}
		int canRob = info.getCanRob();
		int protect = info.getProtect();

//		AMLog.LOG_COMMON.info(
//			"_ROB_BUFF_TEST_ : playerId : {}, primary can be robbed : {}, finally can be robbed : {}", playerId,
//			canRob, canRob - protect);	

//		canRob -= protect;
//		if (canRob <= 0) {
//			return 0;
//		}
		enemyInfo.setCanGet(canRob);
		enemyInfo.setProtect(protect);
	}

	public static void changeBarrel(long playerId, int oldCream, int newCream, boolean isStart) {
		sort.deleteElement(playerId, oldCream);
		if (isStart) {
			sort.addElement(playerId, newCream);
		} else {
			long now = Globals.getTimeService().now();
			HomelandBaseInfo info = getByPlayerId(playerId);
			long nextMatchTime = info.getNextMatchTime();
			if (nextMatchTime <= now) {
				sort.addElementFirst(playerId, newCream);
			} else {
				long configValue = (Long) BaseService.getConfigValue(PriConfigKeyName.HOMELAND_REFRESH_PROTECT_TIME);
				configValue = TimeUnit.SECONDS.toMillis(configValue);
				now += configValue;
				info.setNextMatchTime(now);
				sort.addElementLast(playerId, newCream);
			}
		}
	}

	public static Collection<HomelandBaseInfo> getAllHomeLand() {
		return Collections.unmodifiableCollection(centerHomeLandMap.values());
	}
}
