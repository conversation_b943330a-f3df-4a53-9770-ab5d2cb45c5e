package com.game2sky.prilib.socket.logic.autochess;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessBaseReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessMatchStateChange;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerToCenter;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessWeekReset;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 自走棋中心服协议处理.
 *
 * <AUTHOR>
 * @version v0.1 2019年5月9日 下午4:32:43  Jet Lee
 */
@Controller
public class AutoChessCenterController extends BaseController {

    @Autowired
    private AutoChessCenterService service;

    /**
     * 中心服自走棋匹配tick
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessCenterTick)
    public void ssAutoChessCenterTick() {
        service.tick();
    }
    
    /**
     * 中心服自走棋匹配tick
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessMatchStateChange)
    public void ssAutoChessMatchStateChange(SSAutoChessMatchStateChange msg, Head head) {
    	service.ssAutoChessMatchStateChange(msg);
    }
    
    /**
     * 中心服自走棋周末重置
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessWeekReset)
    public void ssAutoChessWeekReset(SSAutoChessWeekReset msg, Head head) {
    	service.ssAutoChessWeekReset(msg);
    }
    
    /**
     * 本服向中心服请求自走棋基本信息
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessBaseReq)
    public void ssAutoChessBaseReq(SSAutoChessBaseReq msg, Head head) {
    	service.ssAutoChessBaseReq(msg, head);
    }
    
    /**
     * 本服向中心服推送玩家自走棋数据
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessPlayerToCenter)
    public void ssAutoChessPlayerToCenter(SSAutoChessPlayerToCenter msg, Head head) {
    	service.ssAutoChessPlayerToCenter(msg, head);
    }
    
    /**
     * 请求大多数玩家排名
     */
    @ProtoStuffMapping(pvalue = PrivPSEnum.SSAutoChessPlayerRankReq)
    public void ssAutoChessPlayerToCenter(SSAutoChessPlayerRankReq msg, Head head) {
    	service.ssAutoChessPlayerRankReq(msg, head);
    }

    
}
