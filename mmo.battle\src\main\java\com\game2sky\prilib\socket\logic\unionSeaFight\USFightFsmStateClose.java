package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.sql.Timestamp;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateCloseInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * TODO 海战关闭状态
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 上午11:37:12  guojijun
 */
public class USFightFsmStateClose extends AbstractUSFightFsmState {

	private static final USFClientStateCloseInfo CLIENT_INFO = new USFClientStateCloseInfo();

	private long timeout;
	private boolean closeScene;

	public USFightFsmStateClose(USFightFsmController controller) {
		super(USFStateType.USF_STATE_CLOSE, controller);
	}

	@Override
	public void enter() {
		USFightFsmController fsmController = this.getController();
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			AMLog.LOG_UNION.info("海战关闭,usFight={}", fsmController.getUsFight());
		}
		try {
			fsmController.getUsFight().close();
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("海战close报错", e);
		}
		this.closeScene = false;
		this.timeout = System.currentTimeMillis();
		int delay = BaseService.getConfigValue(PriConfigKeyName.USF_CLOSE_SCENE_DELAY);
		this.timeout = this.timeout + delay;
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			AMLog.LOG_UNION.info("海战进入关闭阶段,usfight={},关闭场景time={}", fsmController.getUsFight(),
				new Timestamp(this.timeout));
		}
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
		if (now < this.timeout) {
			return;
		}
		if (this.closeScene) {
			return;
		}
		this.closeScene = true;
		USFightFsmController fsmController = this.getController();
		fsmController.getUsFight().closeScene();
	}

	@Override
	protected void copyTo(USFClientStateInfo clientStateInfo) {
		clientStateInfo.setCloseInfo(CLIENT_INFO);
	}
}
