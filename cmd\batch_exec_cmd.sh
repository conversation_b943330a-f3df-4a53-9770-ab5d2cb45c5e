#!/bin/bash
#参数1(平台类型,例如gw) 参数2(http接口,例如/manager/getDictVersion)

declare -A groups
groups=(["andr"]="*************" ["gw"]="***********" ["yh"]="************" ["ios"]="************" ["xf"]="*************")

group=$1

if [ ! ${group} ]
then
	echo "请输入要执行的大区";exit
fi

group_ip=${groups[${group}]}

if [ ! ${group_ip} ]
then
	echo "大区不存在,请检查配置文件";exit
fi

echo "执行大区group=${group},main_db_ip=${group_ip}"

cmd=$2
if [ ! ${cmd} ]
then
        echo "请输入要执行的操作";exit
fi
echo "要执行的操作cmd=${cmd}"

read -p "确定执行此操作(y/n):" input_op
case ${input_op} in
y|Y)
;;
n|N) exit
;;
*) echo "输入错误";exit
;;
esac

sql="use main_server;select a.internalIp from ConfigServerZone a where a.serverId>1000 and a.serverId<9000 order by a.serverId"
ips=$(mysql -uroot -pop123456A -h${group_ip} --default-character-set=utf8 -e "${sql}"|sed '1d'|sort|uniq)
for ip in ${ips[@]}
do
	url="http://${ip}:22306${cmd}"
	#echo ${url}
	echo -e "${ip}-----\c"
	curl ${url}
	echo ""
	sleep 1s
done
