package com.game2sky.prilib.socket.logic.unionSeaFight.unit;

import gnu.trove.map.hash.TIntObjectHashMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.unionSeaFight.SCUSFUnitProChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitIntProChange;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitProType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightHeroData;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightRole;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSailor;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;
import com.game2sky.prilib.core.socket.logic.battle.fdata.FightUnitHps;
import com.game2sky.prilib.socket.logic.unionSeaFight.IUSFConsts;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight.AbstractUSFFight;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.framework.util.KV;

/**
 * TODO 战斗控制器
 *
 * <AUTHOR>
 * @version v0.1 2018年6月17日 下午4:09:41  guojijun
 */
public class USFFightController implements IUSFConsts {

	private final IUSFUnitFightable fightable;
	/**原始数据*/
	private FFightSide originalFightSide;
	/**战斗数据*/
	private FFightSide fightSide;
	/**当前血量*/
	private final FightUnitHps fightUnitHps;
	/**是否战斗中*/
	private boolean onFight;
	/**血量百分比*/
	private int hp;
	/**当前连击数 */
	private int killCount;
	/**击杀总数*/
	private int killCountTotal;
	/**最大连击数*/
	private int maxKillCount;
	/**死亡次数*/
	private int deathCount;
	/**助攻总次数*/
	private int assistCountTotal;
	/**攻击过我的单位*/
	private HashMap<IUSFUnitFightable, Long> attackedUnits;
	
	/** 
	 * 影响其他buff的valueEffect效果
	 * 第一层key为buffId,是哪个buff产生的这个影响效果，方便卸载
	 * 第二层key为buffId,是对哪个buff有影响效果。
	 *  */
	TIntObjectHashMap<TIntObjectHashMap<KV<Integer, Float>>> affectMap;

	public USFFightController(IUSFUnitFightable fightable) {
		this.fightable = fightable;
		this.fightUnitHps = new FightUnitHps();
		this.onFight = false;
		this.hp = 100;
		this.attackedUnits = new HashMap<>();
		this.affectMap = new TIntObjectHashMap<TIntObjectHashMap<KV<Integer, Float>>>();
	}

	/**
	 * 获取当前战斗数据
	 * 
	 * @param fightCamp 战斗内阵营
	 * @return
	 */
	public FFightSide getCurFFightSide(int fightCamp) {
		FFightRole originalFightRole = originalFightSide.roles.get(0);
		FFightRole fightRole = fightSide.roles.get(0);
		final int sailorsSize = originalFightRole.sailors.size();
		for (int sailorIndex = 0; sailorIndex < sailorsSize; sailorIndex++) {
			FFightSailor originalFightSailor = originalFightRole.sailors.get(sailorIndex);
			FFightSailor fightSailor = fightRole.sailors.get(sailorIndex);
			fightSailor.copyFromBaseProp(originalFightSailor);
			if (CollectionUtils.isNotEmpty(originalFightSailor.fightBuffIds)) {
				fightSailor.fightBuffIds = new ArrayList<>(originalFightSailor.fightBuffIds);
				fightSailor.assisFightBuffIdLevels = new ArrayList<>(originalFightSailor.assisFightBuffIdLevels);
			}
		}		
		this.fightable.refreshProps(fightSide);
		fightSide.refreshCamp(fightCamp);
		this.fightUnitHps.setHpToFightSide(fightSide);
		return fightSide;
	}

	/**
	 * 当开始战斗
	 */
	public void onStartFight() {
		this.onFight = true;
	}

	/**
	 * 当战斗胜利
	 * 
	 * @param fight
	 */
	public void onWin(AbstractUSFFight fight) {
		this.onFight = false;
		Map<Long, FFightHeroData> heroDataMap = fight.getHeroDatasByCamp(this.fightable);
		this.fightUnitHps.updateFormFightHeroData(heroDataMap);
		this.killCount++;
		this.killCountTotal++;
		double hpTmp = this.fightUnitHps.getHpRate(fightSide);
		this.hp = (int) (hpTmp * 100);
		IUSFUnitFightable other = fight.getOtherSide(this.fightable);
		if (other != null) {
			this.attackedUnits.put(other, System.currentTimeMillis());
		}
		this.syncData();
	}
	
	public boolean changeHpByBuff(boolean isPercert,double factor)
	{
		if(onFight)
		{
			return false;
		}
		double hpTmp = this.fightUnitHps.getHpRate(fightSide);
		if(hp <= 0 || factor > 0 && hpTmp >= 1 || factor <0 && hpTmp <= 0.01)
		{
			return false;
		}
		int lastHp = this.hp;
		Map<Long, Double> emenyHpMap = fightUnitHps.getEmenyHpMap();
		if(isPercert)
		{
			for(Long guid : emenyHpMap.keySet())
			{
				 Double hp = emenyHpMap.get(guid);
				 if(hp <= 0)
				 {
					 continue;
				 }
				 hp = Math.min(1, Math.max(factor + hp,0.01));
				 emenyHpMap.put(guid, hp);
			}
			this.hp = (int)(this.fightUnitHps.getHpRate(fightSide) * 100);
		}
		else 
		{
			double nowHps = 0;
			double total = 0;
			for (FFightRole fFightRole : fightSide.roles) {
				for (FFightSailor fFightSailor : fFightRole.sailors) {
					long uid = fFightSailor.uid;
					if(emenyHpMap.get(uid) <= 0)
					{
						 continue;
					}
					double tmpHp = fFightSailor.maxHp * emenyHpMap.get(uid) + factor;
					double percent = tmpHp / fFightSailor.maxHp;
					
					percent = Math.min(1, Math.max(percent,0.01));
					emenyHpMap.put(uid, percent);
					total += fFightSailor.maxHp;
					nowHps += tmpHp;
				}
			}
			this.hp = (int)(nowHps / total * 100);
		}
		if(lastHp != this.hp)
		{
			USFClientUnitIntProChange hpChange = new USFClientUnitIntProChange(this.fightable.getId(),
				USFUnitProType.USF_UNIT_PRO_HP, this.hp);
			ArrayList<USFClientUnitIntProChange> intProChanges = new ArrayList<>(1);
			intProChanges.add(hpChange);
			SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
			scUSFUnitProChange.setIntProChanges(intProChanges);
			this.fightable.getCamp().getUsFight().broadcast(scUSFUnitProChange);
			return true;
		}
		return true;
	}

	/**
	 * 当战斗失败
	 * 
	 * @param fight
	 */
	public void onFailed(AbstractUSFFight fight) {
		this.onFight = false;
		this.hp = 0;
		if (this.killCount > this.maxKillCount) {
			this.maxKillCount = this.killCount;
		}
		this.killCount = 0;
		this.deathCount++;
		if (!this.attackedUnits.isEmpty()) {
			try {
				final long now = System.currentTimeMillis();
				final int dely = BaseService.getConfigValue(PriConfigKeyName.USF_PLAYER_ASSIST_DELAY);
				for (Entry<IUSFUnitFightable, Long> entry : this.attackedUnits.entrySet()) {
					long tmp = now - entry.getValue();
					if (tmp < dely) {
						entry.getKey().onFightAssist(this.fightable);
					}
				}
			} finally {
				this.attackedUnits.clear();
			}
		}
		this.syncData();
	}

	/**
	 * 当战斗中断
	 */
	public void onInterrupt() {
		this.onFight = false;
	}

	/**
	 * 当助攻
	 * 
	 * @param fight
	 */
	public void onFightAssist(IUSFUnitFightable loser) {
		this.assistCountTotal++;
	}

	/**
	 * 给客户端同步战斗相关数据
	 */
	public void syncData() {
		USFClientUnitIntProChange hpChange = new USFClientUnitIntProChange(this.fightable.getId(),
			USFUnitProType.USF_UNIT_PRO_HP, this.hp);
		USFClientUnitIntProChange killCountChange = new USFClientUnitIntProChange(this.fightable.getId(),
			USFUnitProType.USF_UNIT_PRO_KILL_COUNT, this.killCount);
		ArrayList<USFClientUnitIntProChange> intProChanges = new ArrayList<>(2);
		intProChanges.add(hpChange);
		intProChanges.add(killCountChange);
		SCUSFUnitProChange scUSFUnitProChange = new SCUSFUnitProChange();
		scUSFUnitProChange.setIntProChanges(intProChanges);
		this.fightable.getCamp().getUsFight().broadcast(scUSFUnitProChange);
	}

	/**
	 * 更新阵容数据
	 * 
	 * @param data
	 */
	public void updateFightData(byte[] data) {
		this.fightUnitHps.clearHps();
		this.hp = 100;
		this.originalFightSide = SerializationUtil.deserialize(data, data.length, FFightSide.class);
		this.fightSide = SerializationUtil.deserialize(data, data.length, FFightSide.class);
	}
	
	public int getMaxKillCount() {
		return Math.max(this.maxKillCount, this.killCount);
	}
	
	public IUSFUnitFightable getFightable() {
		return fightable;
	}

	public boolean isOnFight() {
		return onFight;
	}

	public int getHp() {
		return hp;
	}

	public int getKillCount() {
		return killCount;
	}

	public int getKillCountTotal() {
		return killCountTotal;
	}

	public int getDeathCount() {
		return deathCount;
	}

	public int getAssistCountTotal() {
		return assistCountTotal;
	}
	
	public void addAffect(int buffId, TIntObjectHashMap<KV<Integer, Float>> value) {
		affectMap.put(buffId, value);
	}
	
	public void removeAffect(int buffId) {
		affectMap.remove(buffId);
	}
	
	public TIntObjectHashMap<TIntObjectHashMap<KV<Integer, Float>>> getAffectMap() {
		return affectMap;
	}

}
