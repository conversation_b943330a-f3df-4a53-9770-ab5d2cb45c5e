package com.game2sky.prilib.socket.logic.homeland.matchv3;

import java.util.concurrent.ThreadLocalRandom;

import com.game2sky.prilib.socket.logic.integral.sort.BarrelSort;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年7月3日 上午11:24:58  daixl
 */
public class HomeLandCreamMatchSort extends BarrelSort<HomeLandCreamMatchBarrel> {

	@Override
	public HomeLandCreamMatchBarrel createInstance(Long id) {
		return new HomeLandCreamMatchBarrel(id);
	}

	private int getGroupId(int cream) {
		return cream / 10;
	}

	public void addElement(long playerId, int cream) {
		int groupId = getGroupId(cream);
		HomeLandCreamMatchBarrel addElement = addElement((long) groupId);
		addElement.addPlayerId(playerId);
	}

	public void addElementFirst(long playerId, int cream) {
		int groupId = getGroupId(cream);
		HomeLandCreamMatchBarrel addElement = addElement((long) groupId);
		addElement.addPlayerIdFirst(playerId);
	}

	public void addElementLast(long playerId, int cream) {
		int groupId = getGroupId(cream);
		HomeLandCreamMatchBarrel addElement = addElement((long) groupId);
		addElement.addPlayerIdLast(playerId);
	}

	public void deleteElement(long playerId, int cream) {
		int groupId = getGroupId(cream);
		HomeLandCreamMatchBarrel barrel = getBarrel(groupId);
		if (barrel == null) {
			return;
		}
		barrel.removePlayerId(playerId);
	}

	public HomeLandCreamMatchBarrel getBarrel(int groupId) {
		HomeLandCreamMatchBarrel barrel = getBarrel((long) groupId);
		return barrel;
	}

	public int getBarrelIndex(int cream) {
		int barrelId = getGroupId(cream);
		int index = 0;
		for (; index < barrelList.size(); index++) {
			HomeLandCreamMatchBarrel barrel = barrelList.get(index);
			if (barrel.getId() >= barrelId) {
				return index;
			}
		}
		return (index - 1);
	}

	public HomeLandCreamMatchBarrel randomBarrel(int index) {
		int size = barrelList.size();
		ThreadLocalRandom random = ThreadLocalRandom.current();
		int nextInt = random.nextInt(size - index);
		index += nextInt;
		return barrelList.get(index);
	}

	public int getMaxSize() {
		return barrelList.size();
	}
}
