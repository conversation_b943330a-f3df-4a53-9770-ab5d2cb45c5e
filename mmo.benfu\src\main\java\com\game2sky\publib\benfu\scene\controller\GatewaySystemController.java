package com.game2sky.publib.benfu.scene.controller;

import io.netty.channel.Channel;
import io.netty.util.Attribute;

import org.springframework.stereotype.Controller;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.homeland.inner.SSHomeLandConnectCenterSuccess;
import com.game2sky.prilib.communication.game.integralArena.SSConnectCenterIntegralEvent;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.common.SGRegist;
import com.game2sky.publib.event.EventSourceManager;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.AMAttributeKey;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnect;
import com.game2sky.publib.framework.netty.support.handler.net.event.ITargetServerRestartEventSource;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

@Controller
public class GatewaySystemController {

	@ProtoStuffMapping(PSEnum.SGRegist)
	public void sgRegist(Channel channel, SGRegist sg, Head head, Message message) {
//		java.net.SocketAddress remoteAddress = channel.remoteAddress();
//		ServerSocketAddress socketAddress = sceneClientCenter.getServerSocketAddress(remoteAddress);
		Attribute<String> attr = channel.attr(AMAttributeKey.SCENE_FLAG);
		attr.set(sg.getSceneFlag());
//		sceneClientCenter.add(channel, socketAddress);
		AMLog.LOG_STDOUT.info(
			" [SGRegist] " + AMFrameWorkBoot.SELF_FLAG + " -> " + channel.toString() + " [" + sg.getSceneFlag() + "]");
		
		try {
			checkServerStartTime(channel, sg.getServerType(), sg.getServerStartTime());
		} catch(Exception e) {
			AMLog.LOG_ERROR.error("检测服务器时间出错，server="+sg.getSceneFlag()+", startTime="+sg.getServerStartTime()+", "+channel, e);
		}
		/**
		 * 开始发消息
		 */
		Globals.getScheduleService().scheduleWithFixedDelay(new ScheduledServerHeartBeatReq(channel), ScheduledServerHeartBeatReq.PERIOD, ScheduledServerHeartBeatReq.PERIOD);
		
		if(AMFrameWorkBoot.isBenFu() && sg.getServerType() == ServerTypeEnum.QUANFU){
			// 本服成功连接全服
			// 推送本服天梯玩家数据
			SSConnectCenterIntegralEvent integralEvent = new SSConnectCenterIntegralEvent();
			Message integralMsg = Message.buildByZoneId(0L, 0, integralEvent, null, null, null);
			Globals.getMsgProcessDispatcher().put(integralMsg);

			// 推送家园数据
			SSHomeLandConnectCenterSuccess homeLandConnectCenterSuccess = new SSHomeLandConnectCenterSuccess();
			Message homelandMsg = Message.buildByZoneId(0L, 0, homeLandConnectCenterSuccess, null, null, null);
			Globals.getMsgProcessDispatcher().put(homelandMsg);
		}
		
	}
	
	/**
	 * 检测服务器时间，以校验目标服务器是否存在重启
	 * 
	 * @param channel
	 * @param startTime
	 */
	private void checkServerStartTime(Channel channel, ServerTypeEnum serverType, long startTime) {
		ClientConnect clientConnect = NetConnectCenter.getInstance().getClientCenter().getClientConnect(channel);
		if(clientConnect != null) {
			ServerSocketAddress serverSocketAddress = clientConnect.getServerSocketAddress();
			if(serverSocketAddress != null) {
				int restartType = 0;
				//该服务器的启动时间
				if(serverSocketAddress.getStartTime() == 0) {
					//第一次连接上该服， 什么也不用做
					restartType = 0;
					AMLog.LOG_COMMON.info("{} 注册后，发现是第一次连接到目标服务器{}", CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag());
				} else if(serverSocketAddress.getStartTime() == startTime) {
					//目标服务器未重启， 之前的网络断开或Channel中断导致的啥也不用做
					restartType = 1;
					AMLog.LOG_COMMON.info("{} 注册目标服务器{}， startTime一致", CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag());
				} else {
					restartType = 2;
					AMLog.LOG_COMMON.info("{} 注册后，发现是目标服务器{}有重启，断开时间:{}", 
						CoreBoot.SELF_SERVER_TYPE, serverSocketAddress.getFlag(), (startTime - serverSocketAddress.getStartTime()));
				}
				serverSocketAddress.setStartTime(startTime);
				//目标服务器肯定是中心服或战斗服
				ITargetServerRestartEventSource eventSource = EventSourceManager.getInstance().getEventSource(
					ITargetServerRestartEventSource.class);
				if (eventSource != null) {
					eventSource.onTargetServerRestart(serverType, channel, restartType);
				}
			}
		}
	}


}
