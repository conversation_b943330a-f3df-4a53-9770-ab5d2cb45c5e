package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff;

import java.util.List;

import com.game2sky.prilib.core.socket.logic.battle.fdata.FFightSide;

/**
 * TODO 海战BUFF接口
 *
 * <AUTHOR>
 * @version v0.1 2018年10月17日 下午3:15:39  guojijun
 */
public interface IUSFBuff {

	/**
	 * 获取持有的BUFF
	 * 
	 * @return
	 */
	List<USFBuff> getBuffs();

//	/**
//	 * 添加BUFF
//	 * 
//	 * @param addBuffs
//	 */
//	void addBuff(List<USFBuff> addBuffs);
	/**
	 * 添加BUFF
	 * 
	 * @param addBuff
	 */
	void addBuff(USFBuff addBuff);

//	/**
//	 * 移除BUFF
//	 * 
//	 * @param removeBuffs
//	 */
//	void removeBuff(List<USFBuff> removeBuffs);

	/**
	 * 刷新属性
	 * 
	 * @param fightSide
	 */
	void refreshProps(FFightSide fightSide);

	void removeBuff(USFBuff removeBuff);
}
