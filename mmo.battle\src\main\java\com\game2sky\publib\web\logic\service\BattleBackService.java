package com.game2sky.publib.web.logic.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.game2sky.prilib.socket.logic.integral.IntegralArenaFightManager;
import com.game2sky.prilib.socket.logic.integral.IntegralPVPFight;
import com.game2sky.publib.framework.web.ErrorCode;
import com.game2sky.publib.framework.web.base.Result;


/**
 * 战斗服专属后台
 *
 * <AUTHOR>
 */
@Service
public class BattleBackService {
	
	
	/**
	 * 获取天梯的战斗对象
	 * @param paramsMap
	 * @return
	 */
	public Result getServerInfo(Map<String, String> paramsMap) {
		Result jsonResult = new Result();
		JSONObject data = new JSONObject();
		int fightNum = 0;
		List<IntegralPVPFight> integralPVPList = IntegralArenaFightManager.getAllIntegralPVPFight();
		if(integralPVPList != null && !integralPVPList.isEmpty()) {
			fightNum = fightNum + integralPVPList.size();
			data.put("integralFightNum", integralPVPList.size());
		} else {
			data.put("integralFightNum", 0);
		}
		int playerNum = 0;
		Map<Long, Long> integralPlayerIdMap = IntegralArenaFightManager.getAllPlayerFightId();
		if(integralPlayerIdMap != null && !integralPlayerIdMap.isEmpty()) {
			playerNum = playerNum + integralPlayerIdMap.size();
			data.put("integralPlayerNum", integralPlayerIdMap.size());
		} else {
			data.put("integralPlayerNum", 0);
		}
		
		data.put("unionSeaFightNum", 0);
		data.put("unionSeaPlayerNum", 0);
		
		data.put("fightNum", fightNum);
		data.put("playerNum", playerNum);
		jsonResult.setData(data);
		jsonResult.setCode(ErrorCode.SUCCESS);
		return jsonResult;
	}
	
	

	/**
	 * 获取天梯的战斗对象
	 * @param paramsMap
	 * @return
	 */
	public Result getIntegralPVPFightInfo(Map<String, String> paramsMap) {
		Result jsonResult = new Result();
		List<IntegralPVPFight> integralPVPList = IntegralArenaFightManager.getAllIntegralPVPFight();
		if(integralPVPList != null && !integralPVPList.isEmpty()) {
			JSONArray integralList = new JSONArray(integralPVPList.size());
			for (IntegralPVPFight pvp : integralPVPList) {
				if(pvp == null)
					continue;
				JSONObject integralData = new JSONObject();
				integralData.put("id", pvp.getId());
				integralData.put("fightId", pvp.getFight().getFightId());
				integralList.add(integralData);
			}
			jsonResult.setData(integralList);
		}
		jsonResult.setCode(ErrorCode.SUCCESS);
		return jsonResult;
	}
	
	
	/**
	 * 获取天梯的玩家
	 * @param paramsMap
	 * @return
	 */
	public Result getIntegralPlayerids(Map<String, String> paramsMap) {
		Result jsonResult = new Result();
		
		Map<Long, Long> integralPlayerIdMap = IntegralArenaFightManager.getAllPlayerFightId();
		if(integralPlayerIdMap != null && !integralPlayerIdMap.isEmpty()) {
			Map<Long, Object> figthPlayerData = new HashMap<>();
			for (Map.Entry<Long, Long> me : integralPlayerIdMap.entrySet()) {
				if(me == null || me.getKey() == null || me.getValue() == null)
					continue;
				
				Object playerId = figthPlayerData.get(me.getValue());//FightID
				if(playerId == null) {
					figthPlayerData.put(me.getValue(), me.getKey());
				} else {
					figthPlayerData.put(me.getValue(), playerId+","+me.getKey());
				}
			}
			jsonResult.setData(figthPlayerData);
		}
		jsonResult.setCode(ErrorCode.SUCCESS);
		return jsonResult;
	}
	
	
	
	
}
