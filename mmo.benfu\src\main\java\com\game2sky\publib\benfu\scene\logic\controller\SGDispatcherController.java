package com.game2sky.publib.benfu.scene.logic.controller;

import io.netty.channel.Channel;

import java.util.Map;

import org.springframework.stereotype.Controller;

import com.game2sky.CoreBoot;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.SerializationUtil;
import com.game2sky.publib.communication.common.SGDispatcherMessage;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.netty.support.handler.Client;
import com.game2sky.publib.framework.netty.support.handler.ClientManager;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnect;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectManager;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

/**
 * TODO 通过gateway转发消息或广播消息
 *
 * <AUTHOR>
 * @version v0.1 2017年2月27日 下午5:25:40  amyn
 */
@Controller
public class SGDispatcherController {

	@ProtoStuffMapping(PSEnum.SGDispatcherMessage)
	public void sgDispatcherMessage(SGDispatcherMessage cs, Head head) {
		int zoneId = cs.getZoneId();
		int serverId = cs.getServerId();
		long playerId = cs.getPlayerId();
		int code = cs.getCode();
		int type = cs.getType();
		byte[] message = cs.getMessage();
		String sceneFlag = cs.getSceneFlag();

//		Channel oldSceneChannel = null;
//		boolean special = false;
//		if (code == PSEnum.SSTransferSceneRes.getCode()) {
//			special = true;
//			SSTransferSceneRes ss = SerializationUtil.deserialize(message, message == null ? 0 : message.length,
//				SSTransferSceneRes.class);
//			String bindSceneFlag = ss.getBindSceneFlag();
//			Client client = ClientManager.getClient(head.getPid());
//			if (bindSceneFlag.startsWith(ServerTypeEnum.BENFU.toString().toLowerCase())) {
//				Channel clientChannel = client.getChannel();
//				if (clientChannel != null) {
//					Attribute<Channel> attr = clientChannel.attr(AMAttributeKey.CURRENT_KUAFU_BIND);
//					oldSceneChannel = attr.get();
//					attr.set(null);
//				}
//			} else {
//				NetConnect netConnect = sceneClientCenter.getNetConnect(bindSceneFlag);
//				if (netConnect != null && client != null) {
//					Channel sceneChannel = netConnect.randomChannel();
//					Channel clientChannel = client.getChannel();
//					if (clientChannel != null && sceneChannel != null) {
//						Attribute<Channel> attr = clientChannel.attr(AMAttributeKey.CURRENT_KUAFU_BIND);
//						oldSceneChannel = attr.get();
//						attr.set(sceneChannel);
//					}
//				}
//			}
//		}

		switch (type) {
//			case SGDispatcherMessage.TO_SCENE_BY_PLAYER:
//				Client client = ClientManager.getClient(playerId);
//				if (client != null) {
//					Channel channel = client.getChannel();
//					if (channel != null) {
//						Channel sceneClient = null;
//						if (special) {
//							sceneClient = oldSceneChannel;
//						} else {
//							Attribute<Channel> attr = channel.attr(AMAttributeKey.CURRENT_KUAFU_BIND);
//							sceneClient = attr.get();
//						}
//						if (sceneClient == null) {
//							APSHandler deserialize = (APSHandler) SerializationUtil.deserialize(message,
//								message == null ? 0 : message.length,
//								AMFrameWorkBoot.OP_DEFINE_MAP.get(code).getPs().getClazz());
//							Message build = Message.build(playerId, zoneId, deserialize, null, null, null);
//							Globals.getMsgProcessDispatcher().put(build);
//						} else {
//							Message buildByte = Message.buildByte(playerId, zoneId, message, code, null, null, null);
//							sceneClientCenter.push2Scene(buildByte, sceneClient);
//						}
//					}
//				}
//				break;
//			case SGDispatcherMessage.TO_ALL_SERVER:
//				Set<ServerSocketAddress> sas = new HashSet<ServerSocketAddress>();
//				Map<java.net.SocketAddress, ServerSocketAddress> map = sceneClientCenter.getMap();
//				for (Map.Entry<java.net.SocketAddress, ServerSocketAddress> entry : map.entrySet()) {
//					ServerSocketAddress value = entry.getValue();
//					sas.add(value);
//				}
				// 给benfu发一遍，再给跨服，全服发
//				APSHandler deserialize1 = (APSHandler) SerializationUtil.deserialize(message, message == null ? 0
//					: message.length, AMFrameWorkBoot.OP_DEFINE_MAP.get(code).getPs().getClazz());
//				Message build1 = Message.buildByZoneId(playerId, zoneId, deserialize1, null, null, null);
//				Globals.getMsgProcessDispatcher().put(build1);
//
//				ClientConnectManager clientCenter = NetConnectCenter.getInstance().getClientCenter();
//				ConcurrentHashMap<String, ClientConnect> clientConnects = clientCenter.getClientConnects();
//				for (ClientConnect clientConnect : clientConnects.values()) {
//					Channel channel = clientConnect.randomChannel();
//					if (channel != null) {
//						Message buildByte3 = Message.buildByte(playerId, zoneId, message, code, null, null, null);
//						channel.writeAndFlush(buildByte3);
//					}
//				}
//				break;
			case SGDispatcherMessage.TO_CLIENT_BY_SERVERID: // 推到指定serverId的所有Client
				Map<Channel, Client> clientsByServerId = ClientManager.getClientsByServerId(serverId);
				if (clientsByServerId != null) {
					for (Map.Entry<Channel, Client> entry : clientsByServerId.entrySet()) {
						Client next = entry.getValue();
						Channel channel = next.getChannel();
						if (channel != null) {
							Message buildByte3 = Message.buildByte(next.getPlayerId(), next.getServerId(), message,
								code, null, null, null);
							channel.writeAndFlush(buildByte3);
						}
					}
				}
				break;
			case SGDispatcherMessage.TO_CLIENT_BY_ZONEID: // 推到指定zoneId的所有Client
				Map<Channel, Client> clientsByZoneId = ClientManager.getClientsByZoneId(zoneId);
				if (clientsByZoneId != null) {
					for (Map.Entry<Channel, Client> entry : clientsByZoneId.entrySet()) {
						Client next = entry.getValue();
						Channel channel = next.getChannel();
						if (channel != null) {
							Message buildByte3 = Message.buildByte(next.getPlayerId(), next.getServerId(), message,
								code, null, null, null);
							channel.writeAndFlush(buildByte3);
						}
					}
				}
				break;
			case SGDispatcherMessage.TO_SCENE_BY_SCENE_FLAG: // 推到指定的Scene进程
				ClientConnectManager clientCenter = NetConnectCenter.getInstance().getClientCenter();
				ClientConnect clientConnect = clientCenter.getClientConnect(sceneFlag);
				if (clientConnect != null) {
					Message buildByte3 = Message.buildByte(playerId, serverId, message, code, null, null, null);
					Channel channel = clientConnect.randomChannel();
					if (channel != null) {
						channel.writeAndFlush(buildByte3);
					}
				} else if (sceneFlag.equals(CoreBoot.SELF_FLAG)) {
					APSHandler deserialize = (APSHandler) SerializationUtil.deserialize(message, message == null ? 0
						: message.length, AMFrameWorkBoot.OP_DEFINE_MAP.get(code).getPs().getClazz());
					Message build = Message.buildByZoneId(playerId, zoneId, deserialize, null, null, null);
					Globals.getMsgProcessDispatcher().put(build);
				}
				break;
			default:
				break;
		}

	}

}
