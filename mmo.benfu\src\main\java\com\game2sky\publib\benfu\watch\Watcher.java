package com.game2sky.publib.benfu.watch;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.game2sky.CoreBoot;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.thread.message.IMessageProcessor;
import com.game2sky.publib.socket.logic.scene.base.SceneProcessorGroup;
import com.game2sky.publib.watch.CoreWatcher;

public class Watcher {
	private static Runtime _rt;

	public static void start() {
		_rt = Runtime.getRuntime();
		Integer integer = CoreBoot.conf.getInteger("watcher.tick.time");
		ScheduledExecutorService newScheduledThreadPool = Executors.newScheduledThreadPool(1);
		newScheduledThreadPool.scheduleAtFixedRate(new TickFunc(), 0, 5000, TimeUnit.MILLISECONDS);
		newScheduledThreadPool.schedule(new NoLogFunc(), integer, TimeUnit.MILLISECONDS);
	}

	private static long getMemory() {
		return _rt.totalMemory() - _rt.freeMemory();
	}

	private static void onTick() {
		
		log("memory,now:" + CoreWatcher.getByteStr(getMemory())+" total:"+CoreWatcher.getByteStr(_rt.totalMemory()));
		log(CoreWatcher.count());
		
		// 输出一次
		IMessageProcessor<Message> sceneGroup = Globals.getMsgProcessDispatcher().getSceneMsgProcessor();
		if (sceneGroup != null) {
			((SceneProcessorGroup) sceneGroup).scheduler.needTrace = true;
		}
	}

	public static void log(String str) {
		AMLog.LOG_MONITER.info(str);
	}

	// count

	/** socket接收 */
	public static void addSocketReceive(int mark, int len) {

	}

	/** socket推送 */
	public static void addSocketSend(int mark, int len) {

	}

	private static class TickFunc implements Runnable {
		@Override
		public void run() {
			onTick();
		}
	}
	
	private static class NoLogFunc implements Runnable {
		@Override
		public void run() {
			log(CoreWatcher.countNoLogReceive());
			log(CoreWatcher.countNoLogSend());
		}
	}
	
}
