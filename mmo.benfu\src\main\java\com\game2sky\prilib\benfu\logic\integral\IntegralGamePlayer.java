package com.game2sky.prilib.benfu.logic.integral;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.integralArena.IntegralAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterAreaPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.IntegralCenterPlayerInfo;
import com.game2sky.prilib.communication.game.integralArena.SSPlayerIntegralChange;
import com.game2sky.prilib.core.dict.domain.DictIntegralArenaArea;
import com.game2sky.prilib.core.socket.logic.integralCommon.IntegralCommonService;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralConfigCenter;
import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralRewardConfig;
import com.game2sky.publib.Globals;

/**
 * 
 * <AUTHOR>
 * @version v0.1 2018年3月6日 下午2:31:25  zhoufang
 */
public class IntegralGamePlayer {

	/** 玩家Id */
	private long playerId;
	/** 服务器Id */
	private int serverId;
	/** 玩家卡区信息 */
	private Map<Integer, IntegralAreaInfo> areaInfos = new HashMap<Integer, IntegralAreaInfo>();

	public IntegralGamePlayer() {
	}
	
	public IntegralGamePlayer(long playerId, int serverId) {
		this.playerId = playerId;
		this.serverId = serverId;
	}
	
	public void initAreaInfo(int areaType){
		DictIntegralArenaArea dict = IntegralConfigCenter.getDictIntegralArenaArea(areaType);
		IntegralAreaInfo areaInfo = new IntegralAreaInfo();
		areaInfo.areaType = areaType;
		areaInfo.integral = dict.getInitialIntegral();
		areaInfo.integralTime = System.currentTimeMillis();
		this.areaInfos.put(areaType, areaInfo);
	}

	/**
	 * 修改积分
	 * @param areaType
	 * @param isWin
	 * @param integralChange
	 */
	public void addIntegral(int areaType, boolean isWin, int integralChange) {
		if(integralChange == 0){
			return;
		}
		integralChange = Math.abs(integralChange);
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		int old = areaInfo.integral;
		long oldTime = areaInfo.integralTime;
		if (isWin) {
			areaInfo.integral += integralChange;
		} else {
			areaInfo.integral -= integralChange;
			if (areaInfo.integral < 0) {
				areaInfo.integral = 0;
			}
		}
		areaInfo.integralTime = System.currentTimeMillis();

		SSPlayerIntegralChange res = new SSPlayerIntegralChange();
		res.setPlayerId(playerId);
		res.setAreaType(areaType);
		res.setOld(old);
		res.setOldTime(oldTime);
		res.setNow(areaInfo.integral);
		res.setNowTime(areaInfo.integralTime);
		res.setDisplayServerId(CoreBoot.getServerDisplayId(serverId));
		int zoneId = CoreBoot.getZoneId(serverId);
		res.setInformation(Globals.getPlayerDisplayInformation(playerId, zoneId));
		IntegralCommonService.benfuToCenter(res, playerId, serverId);
		
		IntegralGameRedis.savePlayer(this);
	}

	/**
	 * 重置玩家的每周挑战次数
	 * @return  是否需要落地
	 */
	public boolean clearWeekFightCount() {
		boolean needSave = false;
		for (IntegralAreaInfo area : areaInfos.values()) {
			if (area.weekFightCount > 0) {
				area.weekFightCount = 0;
				needSave = true;
			}
		}
		return needSave;
	}

	/**
	 * 获取玩家在某赛区可以得到的积分奖励
	 * @param areaType 赛区类型
	 * @return
	 */
	public IntegralRewardConfig getIntegralRewardConfig(int areaType) {
		List<IntegralRewardConfig> configList = IntegralConfigCenter.getIntegralRewardConfig(areaType);
		if (configList == null) {
			return null;
		}
		IntegralAreaInfo areaInfo = this.areaInfos.get(areaType);
		if(areaInfo == null){
			return null;
		}
		for (IntegralRewardConfig config : configList) {
			if (areaInfo.weekFightCount < config.matchCount) {
				continue;
			}
			if (areaInfo.integral >= config.before && areaInfo.integral <= config.after) {
				return config;
			}
		}
		return null;
	}
	
	/**
	 * 是否有某赛区的信息
	 * @param areaType
	 * @return
	 */
	public boolean haveAreaData(int areaType){
		return areaInfos.containsKey(areaType);
	}

	public int getIntegral(int areaType) {
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		if(areaInfo == null){
			return 0;
		}
		return areaInfo.integral;
	}
	
	public long getIntegralTime(int areaType) {
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		if(areaInfo == null){
			return 0;
		}
		return areaInfo.integralTime;
	}

	public int getWeekFightCount(int areaType) {
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		if(areaInfo == null){
			return 0;
		}
		return areaInfo.weekFightCount;
	}
	
	public int getSeasonFightCount(int areaType){
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		if(areaInfo == null){
			return 0;
		}
		return areaInfo.seasonFightCount;
	}

	IntegralCenterPlayerInfo toCenterProto() {
		IntegralCenterPlayerInfo player = new IntegralCenterPlayerInfo();
		player.setPlayerId(playerId);
		player.setServerId(serverId);
		int zoneId = Globals.getZoneId(serverId);
		player.setDisplayInformation(Globals.getPlayerDisplayInformation(playerId, zoneId));
		player.setAreaInfos(new ArrayList<IntegralCenterAreaPlayerInfo>());
		for (IntegralAreaInfo area : areaInfos.values()) {
			IntegralCenterAreaPlayerInfo areaInfo = area.toCenterAreaInfo();
			player.getAreaInfos().add(areaInfo);
		}
		return player;
	}
	
	IntegralCenterPlayerInfo toCenterProtoWithAreaType(int areaType) {
		IntegralCenterPlayerInfo player = new IntegralCenterPlayerInfo();
		player.setPlayerId(playerId);
		player.setServerId(serverId);
		int zoneId = Globals.getZoneId(serverId);
		player.setDisplayInformation(Globals.getPlayerDisplayInformation(playerId, zoneId));
		player.setAreaInfos(new ArrayList<IntegralCenterAreaPlayerInfo>());
		IntegralAreaInfo area = areaInfos.get(areaType);
		IntegralCenterAreaPlayerInfo areaInfo = area.toCenterAreaInfo();
		player.getAreaInfos().add(areaInfo);
		return player;
	}

	IntegralAreaPlayerInfo toAreaInfo(int areaType) {
		IntegralAreaPlayerInfo info = new IntegralAreaPlayerInfo();
		IntegralAreaInfo area = this.areaInfos.get(areaType);
		info.setIntegral(area.integral);
		info.setWeekFightCount(area.weekFightCount);
		info.setSeasonFightCount(area.seasonFightCount);
		return info;
	}
	
	public void addFightCount(int areaType) {
		IntegralAreaInfo areaInfo = areaInfos.get(areaType);
		if(areaInfo!=null){
			areaInfo.weekFightCount ++;
			areaInfo.seasonFightCount ++;
		}
	}
	
	
	class IntegralAreaInfo {
		/** 卡区类型 */
		private int areaType;
		/** 玩家积分 */
		private int integral;
		/** 到达此积分的时间 */
		private long integralTime;
		/** 每周参赛场次 */
		private int weekFightCount;
		/** 每赛季参赛场次  */
		private int seasonFightCount;
		
		public IntegralCenterAreaPlayerInfo toCenterAreaInfo(){
			IntegralCenterAreaPlayerInfo areaInfo = new IntegralCenterAreaPlayerInfo();
			areaInfo.setAreaType(areaType);
			areaInfo.setIntegral(integral);
			areaInfo.setIntegralTime(integralTime);
			return areaInfo;
		}
		
		public int getAreaType() {
			return areaType;
		}
		public void setAreaType(int areaType) {
			this.areaType = areaType;
		}
		public int getIntegral() {
			return integral;
		}
		public void setIntegral(int integral) {
			this.integral = integral;
		}
		public long getIntegralTime() {
			return integralTime;
		}
		public void setIntegralTime(long integralTime) {
			this.integralTime = integralTime;
		}
		public int getWeekFightCount() {
			return weekFightCount;
		}
		public void setWeekFightCount(int weekFightCount) {
			this.weekFightCount = weekFightCount;
		}
		public int getSeasonFightCount() {
			return seasonFightCount;
		}
		public void setSeasonFightCount(int seasonFightCount) {
			this.seasonFightCount = seasonFightCount;
		}
		
	}
	
	public long getPlayerId() {
		return playerId;
	}

	public void setPlayerId(long playerId) {
		this.playerId = playerId;
	}

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

	public Map<Integer, IntegralAreaInfo> getAreaInfos() {
		return areaInfos;
	}
	
	public void setAreaInfos(Map<Integer, IntegralAreaInfo> areaInfos) {
		this.areaInfos = areaInfos;
	}
	
	/**
	 * 修改玩家积分
	 * 禁止调用,此接口不会上报中心服
	 * @param areaType
	 * @param integral
	 */
	public void unsafeModifyIntegral(int areaType,int integral){
		IntegralAreaInfo areaInfo = this.areaInfos.get(areaType);
		areaInfo.integral = integral;
	}
	
	/**
	 * 修改玩家积分时间
	 * 禁止调用,此接口不会上报中心服
	 * @param areaType
	 * @param integral
	 */
	public void unsafeModifyIntegralTime(int areaType,long integralTime){
		IntegralAreaInfo areaInfo = this.areaInfos.get(areaType);
		areaInfo.integralTime= integralTime;
	}
}
