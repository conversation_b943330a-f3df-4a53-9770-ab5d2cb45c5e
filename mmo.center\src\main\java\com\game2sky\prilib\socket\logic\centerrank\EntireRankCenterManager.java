package com.game2sky.prilib.socket.logic.centerrank;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerChange;
import com.game2sky.prilib.communication.game.entirerank.EntireRankPlayerInfo;
import com.game2sky.prilib.communication.game.entirerank.EntireRankType;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankPlayerRankReq;
import com.game2sky.prilib.communication.game.entirerank.SSEntireRankScoreRankReq;
import com.game2sky.prilib.socket.logic.centerrank.rankmodel.EntireRankCenterModel;
import com.game2sky.publib.framework.common.log.AMLog;


/**
 * 通用全服排行榜功能管理器.
 * 目前只有一个榜单在用，所以用的单线程处理
 *
 * <AUTHOR>
 * @version v0.1 2020年2月26日 下午3:43:43  Jet Lee
 */
public class EntireRankCenterManager {
	
	private static EntireRankCenterManager instance = new EntireRankCenterManager();

	private EntireRankCenterManager() {
	}

	public static EntireRankCenterManager getInstance() {
		return instance;
	}
	
	private Map<EntireRankType, EntireRankCenterModel> rankMap = new ConcurrentHashMap<EntireRankType, EntireRankCenterModel>();
	
	public void init() {
		for (EntireRankType rankType : EntireRankType.values()) {
			EntireRankCenterModel rankModel = new EntireRankCenterModel(rankType);
			rankModel.init();
			rankMap.put(rankType, rankModel);
		}
	}

	public void tick() {
		for (EntireRankCenterModel rankModel : rankMap.values()) {
			try {
				rankModel.tick();
			} catch (Exception e) {
				AMLog.LOG_ERROR.error("[ERankCenter.tick] [error] [rankType: + " + rankModel.getRankType() + "]", e);
			}
		}
	}

	public void addPlayers(EntireRankType rankType, List<EntireRankPlayerInfo> players) {
		EntireRankCenterModel rankModel = rankMap.get(rankType);
		rankModel.addPlayers(players);
	}

	public void sendTopRankToBenfu(int serverId, EntireRankType rankType) {
		EntireRankCenterModel rankModel = rankMap.get(rankType);
		rankModel.sendTopRankToBenfu(serverId);
	}

	public void sendPlayerRankToBenfu(SSEntireRankPlayerRankReq msg) {
		EntireRankCenterModel rankModel = rankMap.get(msg.getRankType());
		rankModel.sendPlayerRankToBenfu(msg.getPlayerId(), msg.getServerIds());
	}

	public void sendScoreRankToBenfu(SSEntireRankScoreRankReq msg) {
		EntireRankCenterModel rankModel = rankMap.get(msg.getRankType());
		rankModel.sendScoreRankToBenfu(msg);
	}

	public void clearRank(EntireRankType rankType) {
		EntireRankCenterModel rankModel = rankMap.get(rankType);
		rankModel.clear();
	}

	public void updatePlayerInfo(List<EntireRankPlayerChange> playerChanges) {
		for (EntireRankCenterModel rankModel : rankMap.values()) {
			rankModel.updatePlayerInfo(playerChanges);
		}
	}

}
