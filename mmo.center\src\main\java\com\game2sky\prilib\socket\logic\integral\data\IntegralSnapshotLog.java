package com.game2sky.prilib.socket.logic.integral.data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.core.socket.logic.integralCommon.config.IntegralAreaType;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterManager;
import com.game2sky.publib.Globals;
import com.game2sky.publib.log.CustomLog;
import com.game2sky.publib.log.CustomLogPrefixType;

/**
 * 天梯每日快照
 * <AUTHOR>
 * @version v0.1 2020年10月13日 下午4:48:46  zhoufang
 */
public class IntegralSnapshotLog extends CustomLog{
	
	private List<BranchAreaInfo> branchList = new ArrayList<BranchAreaInfo>();
	
	public IntegralSnapshotLog() {
		this.prefix = CustomLogPrefixType.IntegralSnapshotLog.getPrefix();
		this.time = Globals.getTimeService().now();
		init();
	}
	
	public void init() {
		Map<Integer, IntegralArena> integralArenas = IntegralCenterManager.getInstance().getIntegralArenas();
		for(IntegralArena arena : integralArenas.values()) {
			int branch = arena.getBranch();
			Map<Integer, IntegralArea> areas = arena.getAreas();
			for(IntegralArea area : areas.values()) {
				int areaType = area.getAreaType();
				if(!IntegralAreaType.isNewVersion(areaType)) {
					continue;
				}
				BranchAreaInfo branchInfo = new BranchAreaInfo(branch,areaType);
				branchInfo.setPlayers(area.getSort().getAllPlayers());
				branchList.add(branchInfo);
			}
		}
	}
	
	public List<BranchAreaInfo> getBranchList() {
		return branchList;
	}
	
	public void setBranchList(List<BranchAreaInfo> branchList) {
		this.branchList = branchList;
	}
}


class BranchAreaInfo{
	private String branch;
	private String areaType;
	private Map<String,String> players = null;
	public BranchAreaInfo(){}
	public BranchAreaInfo(int branch, int areaType) {
		this.branch = Integer.toString(branch);
		this.areaType = Integer.toString(areaType);
	}
	public String getBranch() {
		return branch;
	}
	public void setBranch(String branch) {
		this.branch = branch;
	}
	public String getAreaType() {
		return areaType;
	}
	public void setAreaType(String areaType) {
		this.areaType = areaType;
	}
	public Map<String, String> getPlayers() {
		return players;
	}
	public void setPlayers(Map<String, String> players) {
		this.players = players;
	}
	
}