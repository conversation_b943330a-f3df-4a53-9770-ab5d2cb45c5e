package com.game2sky.prilib.socket.logic.unionSeaFight.unit.skill;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.publib.framework.common.log.AMLog;

/**
 * 接触额外条件
 *
 * <AUTHOR>
 * @version v0.1 2018年10月22日 下午5:31:27  lidong
 */
public class TriggerCondition
{
	public enum PropType
	{
		FORTREST_TYPE("ft"),
		UNIT_TYPE("ut"),
		;
	    private final String text;
	    
	    private PropType(String value) {
	        this.text = value;
	        Tools.map.put(value, this);
	    }

	    public String text() {
	        return text;
	    }

	    public static PropType parse(String value) {
	        return Tools.map.get(value);
	    }

	    private static class Tools {
	        private static final Map<String,PropType> map = new HashMap<String,PropType>();
	    }
	}
	
	//计算方式
	public enum CalculateMethod
	{
		EQUAL("="),	//相等
		GREATER(">"),//大于
		LESS("<"),//小于
		NOT_EQUAL("!="),//不相等
		GE(">="),//大于等于
		LE("<="),//小于等于
		;
	    private final String text;
	    
	    private CalculateMethod(String value) {
	        this.text = value;
	        getList().add(this);
	    }

	    public String getText() {
	        return text;
	    }
		
		public static CalculateMethod parse(String value) {
			for(CalculateMethod cm : getList())
			{
				if(cm.text.equalsIgnoreCase(value))
				{
					return cm;
				}
			}
	        return EQUAL;
	    }

	    private static List<CalculateMethod> list;
	    public static List<CalculateMethod> getList()
	    {
	    	if(list == null)
	    	{
	    		list = new ArrayList<TriggerCondition.CalculateMethod>();
	    	}
	    	return list;
	    }
	}
	
	private boolean hasCondition = false;
	private CalculateMethod method;
	private PropType propType;
	private int rightValue;
	
	public TriggerCondition(String text)
	{
		if(text == null)
		{
			hasCondition = false;
			return;
		}
		text = text.trim();
		if(text.isEmpty())
		{
			hasCondition = false;
			return;
		}
		Parse(text);
	}
	
	private void Parse(String text)
	{
		List<CalculateMethod> list = CalculateMethod.getList();
		int index = -1;
		for(int i = list.size() - 1; i >= 0; --i)
		{
			method = list.get(i);
			index = text.indexOf(method.getText());
			if(index != -1)
			{
				break;
			}
		}
		if(index <= 0)
		{
			hasCondition = false;
			return;
		}
		String left = text.substring(0,index);
		
		propType = PropType.parse(left);
		if(propType == null)
		{
			hasCondition = false;
			return;
		}
		
		String right = text.substring(index + method.getText().length());
		try
		{
			rightValue = Integer.parseInt(right);
		} catch (Exception e)
		{
			AMLog.LOG_ERROR.info("触发条件错误右值{}无法转换成数字", right);
			hasCondition = false;
			return;
		}
		hasCondition = true;
	}
	
	public boolean meetCondition(USFBuffTriggerType triggerType,AbstractUSFUnit sourceUnit, AbstractUSFUnit targetUnit,
									Object... args)
	{
		if(!hasCondition)
		{
			return true;
		}
		int leftValue = getLeftValue(propType,triggerType,sourceUnit,targetUnit,args);
		return meet(method,leftValue,rightValue);
	}
	
	
	public int getLeftValue(PropType propType,USFBuffTriggerType triggerType,AbstractUSFUnit sourceUnit, AbstractUSFUnit targetUnit,
							Object... args)
	{
		switch (propType)
		{
			case FORTREST_TYPE:
				if(args != null && args.length > 0 && args[0] instanceof USFFortress)
				{
					USFFortress fortress = (USFFortress)args[0];
					return fortress.getType().value();
				}
				else {
					return -1;
				}
			case UNIT_TYPE:
				if(targetUnit != null )
				{
					return  targetUnit.getUnitType().value();
				}
				return -1;
			default:
				break;
		}
		return 0;
	}
	
	private boolean meet(CalculateMethod method,int left,int right)
	{
		switch (method)
		{
			case EQUAL:
				return left == right;
			case GREATER:
				return left > right;
			case LESS:
				return left < right;
			case NOT_EQUAL:
				return left != right;
			case GE:
				return left >= right;
			case LE:
				return left <= right;
			default:
				return false;
		}
	}
	
}
