package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.sql.Timestamp;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateRunningInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.TimeUtils;

/**
 * TODO 海战运行状态
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 上午11:37:12  guojijun
 */
public class USFightFsmStateRunning extends AbstractUSFightFsmState {

	private USFClientStateRunningInfo clientInfo;
	private long timeout;

	public USFightFsmStateRunning(USFightFsmController controller) {
		super(USFStateType.USF_STATE_RUNNING, controller);
	}

	@Override
	public void enter() {
		USFightFsmController fsmController = this.getController();
		USFight usFight = fsmController.getUsFight();
		this.timeout = System.currentTimeMillis();
		this.timeout = this.timeout + TimeUtils.SECOND * usFight.getRunningTime();
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			USFightFsmController controller = this.getController();
			AMLog.LOG_UNION.info("海战进入运行阶段,usFight={},关闭时间time={}", controller.getUsFight(),
				new Timestamp(this.timeout));
		}
		usFight.onEnterRunningState();
	}

	@Override
	public void leave() {
		USFightFsmController fsmController = this.getController();
		fsmController.getUsFight().onLeaveRunningState();
	}

	@Override
	public void tick(long now) {
		USFightFsmController fsmController = this.getController();
		USFight usFight = fsmController.getUsFight();
		if (now < this.timeout) {
			usFight.onRunning(now);
		} else {
			fsmController.switchState(USFStateType.USF_STATE_CLOSE);
		}
	}

	@Override
	protected void copyTo(USFClientStateInfo clientStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientStateRunningInfo();
		}
		this.clientInfo.setEndTime(this.timeout);
		clientStateInfo.setRunningInfo(this.clientInfo);
	}

}
