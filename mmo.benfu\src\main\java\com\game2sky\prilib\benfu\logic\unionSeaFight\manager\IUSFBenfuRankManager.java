package com.game2sky.prilib.benfu.logic.unionSeaFight.manager;

import com.game2sky.prilib.benfu.logic.unionSeaFight.data.USFBenfuUnionRankGroup;
import com.game2sky.prilib.communication.game.unionSeaFight.SSUSFSyncRanksRes;

/**
 * TODO 海战排行管理器接口
 *
 * <AUTHOR>
 * @version v0.1 2018年9月7日 下午4:08:32  guojijun
 */
public interface IUSFBenfuRankManager {

	IUSFBenfuRankManager MANAGER = new USFBenfuRankManagerImpl();

	/**
	 * 获取排行榜分组
	 * 
	 * @param groupId
	 * @return
	 */
	USFBenfuUnionRankGroup getUnionRankGroup(int groupId);

	/**
	 * 同步排行榜
	 * 
	 * @param ssUSFSyncRanksRes
	 */
	void syncRanks(SSUSFSyncRanksRes ssUSFSyncRanksRes);
}
