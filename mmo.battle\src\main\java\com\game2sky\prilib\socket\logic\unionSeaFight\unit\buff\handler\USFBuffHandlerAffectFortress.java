package com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.handler;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.game2sky.prilib.communication.game.unionSeaFight.USFBuffTriggerType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientBuffProInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.core.dict.domain.DictUSFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitFightable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuff;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.buff.USFBuffFactory;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;


/**
 * 影响据点的效果.
 * 会让据点产生的buff受到影响
 *
 * <AUTHOR>
 * @version v0.1 2019年8月10日 下午3:12:29  Jet Lee
 */
public class USFBuffHandlerAffectFortress implements IUSFBuffHandler {
	
	private static final USFBuffHandlerAffectFortress instance = new USFBuffHandlerAffectFortress();

	private USFBuffHandlerAffectFortress() {
	}

	public static USFBuffHandlerAffectFortress getInstance() {
		return instance;
	}
	
	@Override
	public void load(USFBuff usfBuff) {
		AbstractUSFUnit ownerUnit = usfBuff.getOwnerUnit();
		boolean isFortress = ownerUnit instanceof USFFortress;
		if (!isFortress) {
			return;
		}
		USFFortress fortress = (USFFortress) ownerUnit;
		DictUSFBuff dictUSFBuff = DictUSFBuff.getDictUSFBuff(usfBuff.getClientInfo().getBuffId());
		String params = dictUSFBuff.getParams();
		String[] split = params.split(",");
		if (split.length != 2) {
			return;
		}
		fortress.getSkillController().addExtraValue(Integer.parseInt(split[0]), Float.parseFloat(split[1]));
		doTrigger(usfBuff, fortress, true);
	}
	
	@Override
	public void handler(USFBuff usfBuff) {}

	@Override
	public void unload(USFBuff usfBuff) {
		AbstractUSFUnit ownerUnit = usfBuff.getOwnerUnit();
		boolean isFortress = ownerUnit instanceof USFFortress;
		if (!isFortress) {
			return;
		}
		USFFortress fortress = (USFFortress) ownerUnit;
		DictUSFBuff dictUSFBuff = DictUSFBuff.getDictUSFBuff(usfBuff.getClientInfo().getBuffId());
		String params = dictUSFBuff.getParams();
		String[] split = params.split(",");
		if (split.length != 2) {
			return;
		}
		fortress.getSkillController().addExtraValue(Integer.parseInt(split[0]), -Float.parseFloat(split[1]));
		// 重新触发据点里的玩家离开据点，进入据点的事件
		doTrigger(usfBuff, fortress, false);
	}

	private void doTrigger(USFBuff usfBuff, USFFortress fortress, boolean isLoad) {
		for (IUSFUnitFightable defender : fortress.getDefenders()) {
			AbstractUSFUnit unit = (AbstractUSFUnit) defender;
			if (unit == usfBuff.getSourceUnit()) {
				// 如果当前防守者为buff发起者，则不进行事件触发，否则会造成死循环
				if (isLoad) {
					reloadBuffProps(fortress, unit);
				}
				continue;
			}
			unit.triggerEvent(USFBuffTriggerType.LEAVE_TOWER,unit, fortress);
			fortress.triggerEvent(USFBuffTriggerType.ENTER_TOWER, fortress, unit);
			unit.triggerEvent(USFBuffTriggerType.ENTER_TOWER, unit, fortress);
			if(fortress.getType() == USFFortressType.USF_FORTRESS_NORMAL)
			{
				unit.triggerEvent(USFBuffTriggerType.ENTER_NORMAL_TOWER, unit, fortress);
			}
		}
	}

	private void reloadBuffProps(USFFortress fortress, AbstractUSFUnit unit) {
		// 只有加载此buff的时候，才需要检测
		List<USFBuff> buffs = unit.getBuffs();
		List<USFBuff> fortressBuffs = fortress.getSkillController().getAllBuffOfSkill();
		for (USFBuff tmpBuff : buffs) {
			if (tmpBuff.getSourceUnit() != fortress) {
				// buff的安装源不是当前据点，则略过
				continue;
			}
			for (USFBuff fortressBuff : fortressBuffs) {
				if (fortressBuff.getClientInfo().getBuffId() != tmpBuff.getClientInfo().getBuffId()) {
					break;
				}
				List<USFClientBuffProInfo> proInfos = fortressBuff.getProInfos();
				if (CollectionUtils.isEmpty(proInfos)) {
					break;
				}
				List<USFClientBuffProInfo> copyProInfos = USFBuff.copyProInfos(proInfos);
				USFBuffFactory.addExtraProps(fortress, copyProInfos);
				tmpBuff.getClientInfo().setProInfos(copyProInfos);
			}
		}
	}

}
