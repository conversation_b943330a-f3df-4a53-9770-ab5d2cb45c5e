package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateDeathInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;

/**
 * TODO 单位死亡(不在据点里,也不在航线上)
 *
 * <AUTHOR>
 * @version v0.1 2018年6月9日 下午5:00:44  guojijun
 */
public class USFUnitFsmStateDeath extends AbstractUSFUnitFsmState {

	/**复活时间*/
	private long resTime;
	private USFClientUnitStateDeathInfo clientInfo;

	public USFUnitFsmStateDeath(AbstractUSFUnitFsmController controller) {
		super(USFUnitStateType.USF_UNIT_STATE_DEATH, controller);
	}

	public void die(int delay) {
		if (this.controller.getCurStateType() == USFUnitStateType.USF_UNIT_STATE_DEATH) {
			return;
		}
		this.resTime = System.currentTimeMillis() + delay;
		this.controller.switchState(USFUnitStateType.USF_UNIT_STATE_DEATH);
	}

	@Override
	public void enter() {
	}

	@Override
	public void leave() {
		this.resTime = 0;
	}

	@Override
	public void tick(long now) {
		if (this.resTime <= now) {
			IUSFUnit unit = this.getUnit();
			unit.onRes();
		}
	}

	public long getResTime() {
		return resTime;
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateDeathInfo();
		}
		this.clientInfo.setResTime(this.resTime);
		clientUnitStateInfo.setDeathInfo(clientInfo);
	}

}
