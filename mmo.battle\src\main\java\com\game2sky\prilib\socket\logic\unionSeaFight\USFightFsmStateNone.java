package com.game2sky.prilib.socket.logic.unionSeaFight;

import java.sql.Timestamp;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFStateType;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.util.TimeUtils;

/**
 * TODO 无状态
 *
 * <AUTHOR>
 * @version v0.1 2018年6月20日 下午8:03:57  guojijun
 */
public class USFightFsmStateNone extends AbstractUSFightFsmState {

	private long timeout;

	public USFightFsmStateNone(USFightFsmController controller) {
		super(USFStateType.USF_STATE_NONE, controller);
	}

	@Override
	public void enter() {
		this.timeout = System.currentTimeMillis();
		this.timeout = this.timeout + TimeUtils.MIN * 2;
		if (AMLog.LOG_UNION.isInfoEnabled()) {
			USFightFsmController controller = this.getController();
			AMLog.LOG_UNION.info("海战进入初始阶段,usfight={},超时移除time={}", controller.getUsFight(),
				new Timestamp(this.timeout));
		}
	}

	@Override
	public void leave() {
	}

	@Override
	public void tick(long now) {
		if (now < this.timeout) {
			return;
		}
		this.controller.switchState(USFStateType.USF_STATE_CLOSE);
	}

	@Override
	protected void copyTo(USFClientStateInfo clientStateInfo) {
	}

}
