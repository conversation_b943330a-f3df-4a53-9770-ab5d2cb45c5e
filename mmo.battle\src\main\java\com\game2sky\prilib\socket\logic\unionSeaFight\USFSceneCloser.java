package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.core.socket.logic.scene.unit.player.ScenePlayerObject;
import com.game2sky.publib.Globals;
import com.game2sky.publib.socket.logic.human.AbstractHuman;
import com.game2sky.publib.socket.logic.human.state.DisconnectReason;
import com.game2sky.publib.socket.logic.scene.base.AbstractScene;
import com.game2sky.publib.socket.logic.scene.base.AbstractSceneCloser;
import com.game2sky.publib.socket.logic.scene.unit.SceneObject;

/**
 * TODO 海战场景关闭器
 *
 * <AUTHOR>
 * @version v0.1 2018年7月4日 下午3:20:15  guojijun
 */
public class USFSceneCloser extends AbstractSceneCloser {

	@Override
	protected void onPlayerAdd(AbstractHuman human) {
	}

	@Override
	protected void onPlayerLeave(AbstractHuman human, AbstractScene targetScene) {
	}

	@Override
	protected boolean canClose() {
		return false;
	}

	@Override
	protected void onClose(SceneObject sceneObject) {
		ScenePlayerObject playerObject = (ScenePlayerObject) sceneObject;
		Globals.removeHuman(playerObject.getHuman(), DisconnectReason.LOGGOUT, "USFight.Close");
	}

}
