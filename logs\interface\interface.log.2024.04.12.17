2024-04-12 17:42:58,130 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [50/2/52/SSActivityTeamBossStart/0] - {"body":{"activityId":0},"head":{"len":2,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:42:58,326 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [25/2/27/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:42:59,339 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:00,042 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [39/0/39/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:00,298 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85608,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:05,910 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:19,302 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:20,908 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:28,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:35,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:39,318 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:43,408 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:50,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:57,791 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [8/2/10/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:43:58,246 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMailList/************] - {"body":{"mails":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:58,347 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [49/0/49/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:43:58,347 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:58,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:43:58,580 [LoginProcessorGroup-thread-1] INFO (BaseService.java:110) - [0/770/770/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:43:58,632 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:58,634 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0x49e77b67, L:/127.0.0.1:12306 - R:/127.0.0.1:61426]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"FaZHMt0fRmCTQW4d5ErTTA==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:59,305 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:43:59,813 [LadderWarBenfu] INFO (BaseService.java:110) - [2/0/2/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:43:59,814 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:59,853 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [29/0/29/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:43:59,854 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:59,854 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [21/0/21/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:43:59,856 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCScenePreEnter/************] - {"body":{"pos":{"x":-1.156,"y":10.897,"z":104.305},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:43:59,898 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712918639886,"serverTime":1712918639896},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:00,011 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [10/0/10/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:44:02,403 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [17/2/19/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:02,506 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,510 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,511 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [4/0/4/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,522 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [6/0/6/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,524 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,525 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [18/0/18/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,529 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [17/0/17/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,533 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [8/0/8/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,535 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [12/0/12/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,536 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [12/0/12/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,536 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,536 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,536 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [12/0/12/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,538 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,539 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,540 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [17/0/17/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:44:02,540 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,540 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,540 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [33/0/33/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:44:02,540 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,569 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,571 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712918642554&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,571 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,575 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,578 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [6/0/6/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,580 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [9/0/9/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,583 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [11/0/11/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,592 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [11/0/11/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,595 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [20/0/20/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,601 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [33/96/129/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:02,601 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,604 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [27/0/27/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,606 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [31/0/31/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,611 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [32/0/32/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,612 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [36/0/36/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,615 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [36/0/36/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,618 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [39/0/39/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,618 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,618 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,619 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,621 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [25/0/25/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,622 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [27/0/27/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":181},"head":{"len":5,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,623 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [28/0/28/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,624 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [29/0/29/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,625 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:02,811 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [2/2/4/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:44:02,814 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:44:03,933 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:03,935 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [36/0/36/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:03,936 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:03,936 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [38/0/38/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:03,938 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [38/0/38/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:03,939 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:03,940 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:03,940 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [40/1/41/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:04,241 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:04,242 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [13/0/13/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:04,293 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [39/0/39/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:04,293 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:04,308 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:04,308 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [3/0/3/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:04,345 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:44:04,345 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:05,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:13,086 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [45/0/45/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:13,086 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:44:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:19,320 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:44:20,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:28,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:31,474 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [19/0/19/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:31,474 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:44:35,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:39,300 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:44:43,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:45,558 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [36/0/36/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:44:45,558 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:44:50,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:58,311 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:44:58,311 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [13/0/13/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:44:58,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:44:59,324 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:45:00,030 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [30/0/30/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:45:03,913 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [13/0/13/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:45:03,913 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:45:05,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:13,408 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:19,338 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:45:20,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:28,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:29,571 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [10/0/10/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:45:29,571 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:45:35,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:39,311 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:45:43,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:48,428 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [45/0/45/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:45:48,428 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:45:50,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:58,320 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [22/0/22/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:45:58,320 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:45:58,408 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:45:59,332 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:46:00,042 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [41/0/41/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:46:05,908 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:06,776 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [12/0/12/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:46:06,776 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:46:09,742 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [50/2/52/CSGm/************] - {"body":{"text":"addhero 113001"},"head":{"len":16,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:46:09,742 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":0,"msg":"Connect to the server disconnected, please try again","opcode":0,"type":3},"head":{"len":60,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:46:09,842 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [44/0/44/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":3},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:46:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:19,343 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:46:20,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:28,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:35,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:39,301 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:46:43,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:50,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:58,306 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [8/0/8/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:46:58,306 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:46:58,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:46:59,317 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:47:00,026 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [26/0/26/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:47:05,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:19,305 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:47:20,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:28,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:35,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:39,305 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:47:41,037 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [0/1/1/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:47:41,325 [LoginProcessorGroup-thread-2] INFO (BaseService.java:110) - [0/277/277/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":1,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:47:41,355 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0xe9cf1254, L:/127.0.0.1:12306 - R:/127.0.0.1:61562]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"kb/RqkFpQiyVFniEifmb4Q==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:42,386 [LadderWarBenfu] INFO (BaseService.java:110) - [0/0/0/CSLadderWarBaseInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:42,386 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCLadderWarBaseInfo/************] - {"body":{"raceEndTime":0,"raceStartTime":0,"seasonEndTime":0,"seasonStartTime":0},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:42,424 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [38/0/38/CSGetInvitePartner/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:42,424 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [38/0/38/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:42,424 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetInvitePartner/************] - {"body":{"invite":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:42,424 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-3.3960001,"y":0.0,"z":105.397},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:42,462 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712918862454,"serverTime":1712918862461},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:43,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,156 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [1/0/1/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:44,208 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,208 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,209 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [1/0/1/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,216 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,218 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,219 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,220 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,220 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,220 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,220 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712918864208&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,220 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,221 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [32/13/45/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:44,222 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,222 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,222 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,222 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":53},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,223 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,224 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [2/0/2/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,224 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:44,260 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [44/0/44/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:47:44,413 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [21/0/21/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:47:44,413 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:47:45,333 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [31/0/31/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,333 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,334 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [31/1/32/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,334 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [32/0/32/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,334 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [32/0/32/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,334 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,334 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,334 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,487 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [34/0/34/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,487 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,487 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [24/0/24/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,487 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,487 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [13/0/13/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,487 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:45,523 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [49/0/49/CSAutoMatch/************] - {"body":{"flag":0,"targetId":0,"teamId":0,"type":2},"head":{"len":8,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:45,523 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAutoMatch/************] - {"body":{"flag":2,"targetId":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:50,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:52,247 [AbstractWorldProcessorGroup-thread-1] INFO (BaseService.java:110) - [43/0/43/CSGetApplyContacters/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:52,247 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetApplyContacters/************] - {"body":{"contacterApplys":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:54,133 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [14/0/14/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:54,133 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [13/0/13/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:54,133 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:54,133 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:47:54,286 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [8/0/8/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:47:54,286 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:47:58,317 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [19/0/19/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:47:58,317 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:47:58,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:47:59,329 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:48:00,040 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [40/0/40/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:48:05,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:08,192 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [24/0/24/CSGetHeroItemDoubleInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:08,192 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetHeroItemDoubleInfo/************] - {"body":{"endTime":0,"isOpen":false},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:12,739 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [38/0/38/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:12,739 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:48:13,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:19,344 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:48:20,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:24,810 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [49/0/49/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:24,810 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [38/0/38/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:24,810 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:24,810 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:26,758 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [8/0/8/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:26,758 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:48:28,138 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [34/0/34/CSGetDiamondLimit/************] - {"body":{"playerId":************},"head":{"len":7,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:28,138 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetDiamondLimit/************] - {"body":{"limitCount":5000,"playerId":************},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:28,139 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [26/0/26/CSGetShopMallStruct/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:28,140 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetShopMallStruct/************] - {"body":{"tabInfos":[{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":0,"shopTypeName":"????"},{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":1,"shopTypeName":"????"},{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":19,"shopTypeName":"????"}]},"head":{"len":66,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:28,241 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [40/0/40/CSGetLimitList/************] - {"body":{"pageId":1,"playerId":************,"shopType":0},"head":{"len":11,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:28,241 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetLimitList/************] - {"body":{"limitList":[],"playerId":************,"shopType":0},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:28,293 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [28/1/29/CSGetShopItemList/************] - {"body":{"pageId":1,"shopType":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:28,298 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetShopItemList/************] - {"body":{"maxRefreshTimes":0,"nextRefreshTime":0,"pageId":1,"refreshTimes":0,"shopGoods":[{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":280,"type":1}],"discountPrice":[280],"discountRate":10000,"goodIndex":1,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215011,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":10,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":225001,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":11,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":225002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":20,"type":1}],"discountPrice":[20],"discountRate":10000,"goodIndex":12,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214012,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":13,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215006,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":300,"type":1}],"discountPrice":[300],"discountRate":10000,"goodIndex":14,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214010,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1}],"discountPrice":[200],"discountRate":10000,"goodIndex":15,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214008,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":600,"type":1}],"discountPrice":[600],"discountRate":10000,"goodIndex":16,"limitType":"0,0,3,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214050,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":500,"type":1}],"discountPrice":[500],"discountRate":10000,"goodIndex":24,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":217006,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":25,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":90,"type":1}],"discountPrice":[90],"discountRate":10000,"goodIndex":26,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800004,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":29,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800007,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1}],"discountPrice":[200],"discountRate":10000,"goodIndex":35,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215015,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":10,"type":1}],"discountPrice":[10],"discountRate":10000,"goodIndex":36,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":212014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":37,"limitType":"0,0,1,15,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":213014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":300,"type":1}],"discountPrice":[300],"discountRate":10000,"goodIndex":38,"limitType":"0,0,1,10,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":39,"limitType":"0,0,1,2,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":511201,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":40,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":514031,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":42,"limitType":"0,0,1,30,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":216002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":280,"type":1}],"discountPrice":[280],"discountRate":10000,"goodIndex":48,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":218011,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1}],"shopType":0},"head":{"len":1619,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:28,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:34,829 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [25/0/25/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:34,829 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:34,829 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [14/0/14/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:34,829 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:35,908 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:39,327 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:48:43,408 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:45,154 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [16/0/16/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:45,154 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:48:46,940 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [36/0/36/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:46,940 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [36/0/36/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:46,940 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:46,940 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:48:50,063 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [1/0/1/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:308","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:48:50,063 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:308","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:48:50,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:58,349 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [51/0/51/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:48:58,349 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:48:58,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:48:59,306 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:49:00,014 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [12/0/12/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:49:02,139 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [43/0/43/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:02,139 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:49:05,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:13,408 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:19,335 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:49:20,503 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [26/0/26/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:20,503 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:49:20,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:28,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:35,021 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [24/0/24/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:35,021 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:49:35,906 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:39,299 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:49:43,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:49,585 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [34/0/34/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:49,585 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [23/0/23/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:49,585 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:49,585 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:50,908 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:51,689 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPlayerPlotStart/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:51,689 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [11/3/14/CSPlayerPlotStart/************] - {"body":{"plotId":9},"head":{"len":2,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:57,359 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [21/3/24/CSPlayerPlotEnd/************] - {"body":{"isSkip":true,"plotId":9},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:57,359 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPlayerPlotEnd/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:57,507 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [45/0/45/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:57,507 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:57,509 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCEnterMainStoryRaid/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:57,513 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [45/4/49/CSEnterMainStoryRaid/************] - {"body":{"targetId":10010,"taskId":10010},"head":{"len":6,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:57,515 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCEnterRunMapRaid/************] - {"body":{"raidData":{"boxs":[],"deathMonsterId":0,"monsters":[100104]},"runMapId":3},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:57,729 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712918997708,"serverTime":1712918997729},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:57,783 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRunMapRaidEnterFight/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:49:57,784 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [32/20/52/CSRunMapRaidEnterFight/************] - {"body":{"emenyGroupId":100104},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:58,344 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:49:58,344 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:49:58,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:49:58,990 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCFightLoadStatus/************] - {"body":{"progresses":[{"isOver":1,"playerUid":-1,"progress":100.0},{"isOver":1,"playerUid":************,"progress":100.0}]},"head":{"len":44,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:49:58,990 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [44/0/44/CSFightLoadStatus/************] - {"body":{"progress":{"isOver":1,"playerUid":************,"progress":100.0}},"head":{"len":20,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:49:59,304 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:00,011 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [10/0/10/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:00,014 [MainMessageProcessor] INFO (BaseService.java:110) - [0/1/1/SGDispatcherMessage/0] - {"body":{"code":101,"message":"CkrlvLrogIXmjJHmiJjov5jmnIkxMOWIhumSn+W8gOWQr++8jOivt+WQhOS9jeiIuemVv+WBmuWlveaImOaWl+eahOWHhuWkh++8gRACGAAgAA==","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":94,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:00,014 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":3006,"message":"CmEQABpOVGV4dOW8uuiAheaMkeaImOi/mOaciTEw5YiG6ZKf5byA5ZCv77yM6K+35ZCE5L2N6Ii56ZW/5YGa5aW95oiY5paX55qE5YeG5aSH77yBIMrnto/tMSgAMAA6AEAAEAAYAiAB","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":118,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:00,023 [ChatReceiveThread] INFO (BaseService.java:110) - [0/0/0/SSPushChannelChat/0] - {"body":{"chatFrom":"SYSTEM","fromId":0,"msg":{"content":"Text??????10??????????????????","contentType":"TEXT","customContacterChatInfo":{},"flag":0,"fromId":0,"isHorn":false,"sendTime":1712919000010},"toChatChannel":"CHAT_CHANNEL_SYSTEM","toId":0},"head":{"len":103,"pid":0,"serverId":1,"zoneId":1}}
2024-04-12 17:50:05,908 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:08,436 [IO_LOOP-6-2] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCFightAutoSetting/************] - {"body":{"camp":0,"isAuto":0,"playerUid":************},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:50:08,444 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [42/10/52/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:12,919 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [16/0/16/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499917841363633152}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:13,935 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [4/0/4/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:17,309 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [44/0/44/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499853796715529216}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:18,333 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [33/0/33/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:19,329 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:20,887 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [26/0/26/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499853796740695040}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:20,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:22,109 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [19/0/19/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:26,709 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [20/0/20/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:28,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:31,348 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [2/0/2/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:35,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:36,000 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [29/2/31/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:39,343 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:43,407 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:45,699 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [4/0/4/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:50,198 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [43/0/43/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499917841363633152}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:50,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:51,225 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [46/0/46/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:54,698 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [23/0/23/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499853796715529216}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:55,722 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [14/0/14/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:58,300 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [2/0/2/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:50:58,300 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:58,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:50:58,636 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [15/0/15/CSFightUnitActionDone/************] - {"body":{"doneHero":{"camp":0,"uid":1499853796740695040}},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:50:59,310 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:50:59,860 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [35/0/35/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:00,017 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [17/0/17/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:51:04,503 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [24/0/24/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:05,907 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:51:09,150 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [20/0/20/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:13,406 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:51:14,058 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [50/0/50/CSNextActionReady/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:37,668 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [5/1/6/SSActivityTeamBossStart/0] - {"body":{"activityId":0},"head":{"len":2,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:51:37,868 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [9/2/11/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:51:38,880 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:51:39,856 [QueueProcessorGroup-thread-1] INFO (BaseService.java:110) - [9/3/12/CSPlayerLogin/0] - {"body":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":0,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0},"head":{"len":467,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:51:39,858 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85608,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:51:40,292 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMailList/************] - {"body":{"mails":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,551 [LoginProcessorGroup-thread-1] INFO (BaseService.java:110) - [1/679/680/SSPlayerLoginReq/0] - {"body":{"clientMsg":{"appVersion":"1.15.0.302225","authCode":"1","channelGroup":"andr","channelId":"ANDTEST","dataVersoin":"0","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceInfo":{"cpuCoreNumber":"20","cpuFrequency":"2688","cpuMode":"12th Gen Intel(R) Core(TM) i7-12700H","deviceId":"78e4fa172982b6d5fecb0836fef2ef411c99b258","deviceName":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","deviceType":"Vector GP76 12UGSO (Micro-Star International Co., Ltd.)","gpuApi":"Direct3D11","gpuMode":"Emulated GPU running OpenGL ES 3.0","idfa":"78e4fa172982b6d5fecb0836fef2ef411c99b258","isPhone":1,"memorySize":32472,"resolution":"1532x874","shaderLevel":"30","supportASTCCompress":0,"supportedRenderTargetCount":4,"system":"Windows 10  (10.0.0) 64bit","useMultiThreadRendering":1},"globalId":1,"isNewCreate":0,"loginType":1,"platformUid":"nhan2k4","playerType":1,"resVersoin":"0","sdkUserId":"nhan2k4","serverId":1,"usePlayerId":0}},"head":{"len":470,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:51:40,585 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,589 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPlayerLogin/************] - {"body":{"channelKey":"[id: 0x5e32415a, L:/127.0.0.1:12306 - R:/127.0.0.1:61726]","clientIp":"127.0.0.1","isFirstCreated":false,"playerId":************,"sessionKey":"17MLHyY7Qxmz4FqghsMMtw==","socialUrl":"http://127.0.0.1:8080/social"},"head":{"len":127,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,633 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [35/0/35/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:40,634 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-3.396,"y":0.0,"z":105.397},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,674 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712919100642,"serverTime":1712919100673},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:40,789 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [18/2/20/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:40,841 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRefreshFirstRechargeState/************] - {"body":{"state":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,845 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCKVInfo/************] - {"body":{"entrys":[{"key":123,"value":"false"}]},"head":{"len":11,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,848 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [5/0/5/SCChannelShowSetting/************] - {"body":{"open":[true,true,true,true,true,false,false]},"head":{"len":14,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,850 [WORLD_TEAM_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [7/0/7/SSSceneTeamStateChange/************] - {"body":{"name":"nhan2k4","type":1},"head":{"len":11,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:51:40,863 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [8/0/8/SCGetRecentContacters/************] - {"body":{"contacterSetting":{"blackMaxLimit":50,"enemyMaxLimit":50,"friendCount":0,"friendMaxLimit":100,"group":[{"group":"CONTACT_GROUP_FRIEND","groupName":"????"},{"group":"CONTACT_GROUP_GROUP2","groupName":"???"},{"group":"CONTACT_GROUP_GROUP3","groupName":"???"},{"group":"CONTACT_GROUP_GROUP4","groupName":"???"},{"group":"CONTACT_GROUP_BLACK","groupName":"???"},{"group":"CONTACT_GROUP_EMENY","groupName":"???"},{"group":"CONTACT_GROUP_KUAFU","groupName":"????"}],"kuafuFriendCount":0,"kuafuFriendMaxLimit":20,"mode":"CONTACT_MODE_RECEIVE_ALL","offlineReply":false,"offlineReplyContent":"?????????????????","role":0,"selfState":"CONTACT_STATE_ONLINE"},"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":223,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,865 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [22/0/22/SCGetContacters/************] - {"body":{"friends":[],"systemContacters":[{"contactType":"CONTACT_SYSTEM_MSG","playerName":"????"},{"contactType":"CONTACT_SYSTEM_HELPER","playerName":"????"}]},"head":{"len":36,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,868 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [23/0/23/SCGetChatGroup/************] - {"body":{"chatGroups":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,871 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [26/0/26/SCGetGagInfo/************] - {"body":{"gagInfo":{"customGagInfo":{},"endGagTime":0,"gagTimes":0}},"head":{"len":8,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,876 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [9/0/9/SCRedPoingList/************] - {"body":{"redPointList":[{"num":0,"pushToServer":0,"type":1088},{"num":0,"pushToServer":0,"type":2},{"num":0,"pushToServer":0,"type":3},{"num":0,"pushToServer":0,"type":1027},{"num":0,"pushToServer":0,"type":4},{"num":0,"params":[],"pushToServer":0,"type":5},{"num":0,"params":[],"pushToServer":0,"type":6},{"num":0,"pushToServer":0,"type":7},{"num":0,"pushToServer":0,"type":8},{"num":0,"pushToServer":0,"type":10},{"num":0,"pushToServer":0,"type":1098},{"num":0,"pushToServer":0,"type":1100},{"num":0,"pushToServer":0,"type":1038},{"num":0,"pushToServer":0,"type":1102},{"num":0,"pushToServer":0,"type":1039},{"num":0,"pushToServer":0,"type":1103},{"num":0,"pushToServer":0,"type":1040},{"num":0,"pushToServer":0,"type":1104},{"num":0,"pushToServer":0,"type":1105},{"num":0,"pushToServer":0,"type":1051},{"num":0,"pushToServer":0,"type":1115},{"num":0,"pushToServer":0,"type":1117},{"num":0,"pushToServer":0,"type":1001},{"num":1,"pushToServer":0,"type":1004},{"num":1,"pushToServer":0,"type":1077},{"num":0,"pushToServer":0,"type":1083},{"num":0,"pushToServer":0,"type":1084},{"num":0,"pushToServer":0,"type":1086},{"num":0,"pushToServer":0,"type":1087}]},"head":{"len":253,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,878 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCSyncSceneInfo/************] - {"body":{"killPlayerCount":0,"openSceneMapIds":[8003,8004,8005,8006,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017,3001,1007,8007,63,8002,8001,54,39,38,3,1],"privateSceneMapIds":[1,3,4,5,6,7,8,9,12,21,22,23,24,25,26,27,28,29,30,31,32,34,38,39,40,43,44,45,46,48,49,50,51,52,53,54,55,56,63,64,65,66,68,69,70,71,72,73,74,75,76,77,78,79,80,81,85,89,91,92,93,94,95,97,98,100,102,103,105,106,107,108,109,110,112,113,115,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,138,139,140,141,142,143,144,1007,3001,4001,5001,8001,8002,8003,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8017],"serverCount":1,"serverIndex":0,"visitedSceneMapIds":[3]},"head":{"len":364,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":2,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":3,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":78,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":0,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919165799007232,"itemId":213002,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":1,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499867852692259840,"itemId":212001,"itemNumber":28,"itemType":1001,"tradeTime":0}},{"index":2,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883969,"itemId":212002,"itemNumber":5,"itemType":1001,"tradeTime":0}},{"index":3,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499868247036527616,"itemId":712001,"itemNumber":2,"itemType":1001,"tradeTime":0}},{"index":4,"item":{"bind":true,"cdTime":0,"expiredTime":0,"itemGuid":1499919326268883968,"itemId":712004,"itemNumber":1,"itemType":1001,"tradeTime":0}},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":274,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1001,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [16/0/16/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1002,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":11,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15}],"totalSize":16}},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,882 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1003,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [14/0/14/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17},{"index":18},{"index":19},{"index":20},{"index":21},{"index":22},{"index":23},{"index":24},{"index":25},{"index":26},{"index":27},{"index":28},{"index":29}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":30},{"index":31},{"index":32},{"index":33},{"index":34},{"index":35},{"index":36},{"index":37},{"index":38},{"index":39},{"index":40},{"index":41},{"index":42},{"index":43},{"index":44},{"index":45},{"index":46},{"index":47},{"index":48},{"index":49},{"index":50},{"index":51},{"index":52},{"index":53},{"index":54},{"index":55},{"index":56},{"index":57},{"index":58},{"index":59}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":60},{"index":61},{"index":62},{"index":63},{"index":64},{"index":65},{"index":66},{"index":67},{"index":68},{"index":69},{"index":70},{"index":71},{"index":72},{"index":73},{"index":74},{"index":75},{"index":76},{"index":77},{"index":78},{"index":79},{"index":80},{"index":81},{"index":82},{"index":83},{"index":84},{"index":85},{"index":86},{"index":87},{"index":88},{"index":89}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":90},{"index":91},{"index":92},{"index":93},{"index":94},{"index":95},{"index":96},{"index":97},{"index":98},{"index":99},{"index":100},{"index":101},{"index":102},{"index":103},{"index":104},{"index":105},{"index":106},{"index":107},{"index":108},{"index":109},{"index":110},{"index":111},{"index":112},{"index":113},{"index":114},{"index":115},{"index":116},{"index":117},{"index":118},{"index":119}],"totalSize":150}},"head":{"len":128,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1004,"cells":[{"index":120},{"index":121},{"index":122},{"index":123},{"index":124},{"index":125},{"index":126},{"index":127},{"index":128},{"index":129},{"index":130},{"index":131},{"index":132},{"index":133},{"index":134},{"index":135},{"index":136},{"index":137},{"index":138},{"index":139},{"index":140},{"index":141},{"index":142},{"index":143},{"index":144},{"index":145},{"index":146},{"index":147},{"index":148},{"index":149}],"totalSize":150}},"head":{"len":151,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,883 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [15/0/15/SCUpdateContainer/************] - {"body":{"updateData":{"bagType":1005,"cells":[{"index":0},{"index":1},{"index":2},{"index":3},{"index":4},{"index":5},{"index":6},{"index":7},{"index":8},{"index":9},{"index":10},{"index":11},{"index":12},{"index":13},{"index":14},{"index":15},{"index":16},{"index":17}],"totalSize":18}},"head":{"len":79,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,898 [WORLD_MAIL_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [33/0/33/SSDiffServerMail/************] - {"body":{"receivedList":[]},"head":{"len":0,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:51:40,930 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCurrency/************] - {"body":{"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1012},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":83600,"type":5},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":1200,"type":1901}]},"head":{"len":48,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,930 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCKVInfo/************] - {"body":{"entrys":[{"key":1,"value":"7"},{"key":3,"value":"https://t.yunchanggame.com/notice/4/new_android_game_1.php?t=1712919100888&channel=ANDTEST"},{"key":4,"value":"true"},{"key":5,"value":"false"},{"key":124,"value":"0"},{"key":101,"value":"on"},{"key":102,"value":"0"},{"key":103,"value":"true"},{"key":104,"value":"0"},{"key":105,"value":"0"},{"key":113,"value":"0"},{"key":115,"value":"0"},{"key":122,"value":"0"},{"key":109,"value":"0"},{"key":116,"value":"0"},{"key":108,"value":"0"},{"key":107,"value":"0"},{"key":7,"value":"false"},{"key":8,"value":"false"},{"key":111,"value":"0"},{"key":117,"value":"0"},{"key":112,"value":"off"},{"key":114,"value":"0"},{"key":120,"value":"0"},{"key":121,"value":"0"},{"key":125,"value":"0"},{"key":126,"value":"0"},{"key":127,"value":"0"},{"key":128,"value":"0"}]},"head":{"len":314,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,930 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCKVInfo/************] - {"body":{"entrys":[{"key":106,"value":"0"}]},"head":{"len":7,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,932 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetMyUnion/************] - {"body":{"playerUnion":{"joinTime":0,"playerId":************,"roleId":0,"unionContribution":0,"unionId":0}},"head":{"len":17,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,935 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [3/0/3/SCLifeInfo/************] - {"body":{"lifeSkills":[{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":4}],"level":1,"skillType":1},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":106,"type":4}],"level":1,"skillType":2},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":112,"type":4}],"level":1,"skillType":3},{"exp":0,"items":[{"bind":false,"expireTime":0,"guid":0,"id":410001,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":118,"type":4}],"level":1,"skillType":4},{"exp":0,"items":[],"level":1,"skillType":5},{"exp":0,"items":[],"level":1,"skillType":6},{"exp":0,"items":[],"level":1,"skillType":7},{"exp":0,"items":[],"level":1,"skillType":8},{"exp":0,"items":[],"level":1,"skillType":10}]},"head":{"len":196,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,936 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [6/0/6/SCPlayerPlotHistory/************] - {"body":{"plotIds":[1,4,9]},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,937 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [7/0/7/SCTaskChapterInfo/************] - {"body":{"chapterDesc":"????????","chapterId":1,"chapterReward":{"bind":true,"expireTime":0,"guid":0,"id":212001,"num":20,"type":1001},"chapterSchedule":80},"head":{"len":47,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,938 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [18/99/117/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:40,944 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [7/0/7/SCGetTaskList/************] - {"body":{"taskInfo":[{"acceptTime":1712763481091,"allStep":0,"canGetTime":0,"currentStep":0,"failTrigger":"0","getCondition":"","getType":"1,0,0,0","giveUpType":1,"goals":[{"acceptTime":1712763481091,"current":"0","param1":"3","param2":"1","paramList":["109","206"],"state":1,"targetDesc":"Defeat Morgan the Axeman","targetId":10010,"targetType":4,"townRunMapId":0,"trustClient":0}],"handOverType":"1,0,106,0,1","isLoop":0,"loopId":0,"rewards":[],"state":3,"taskDesc":"????????????????","taskId":10010,"taskName":"????","timeControlType":0,"type":1}]},"head":{"len":191,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,946 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [13/0/13/SCTitleInfo/************] - {"body":{"curTitleId":-1,"isShow":0,"titles":[{"current":0,"deadTime":-1,"isOwn":0,"titleId":1,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":2,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":3,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":4,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":5,"total":1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":6,"total":1},{"current":1,"deadTime":-1,"isOwn":0,"titleId":7,"total":5},{"current":1,"deadTime":-1,"isOwn":0,"titleId":8,"total":15},{"current":0,"deadTime":-1,"isOwn":0,"titleId":9,"total":50},{"current":0,"deadTime":-1,"isOwn":0,"titleId":10,"total":100},{"current":0,"deadTime":-1,"isOwn":0,"titleId":11,"total":1},{"current":5,"deadTime":-1,"isOwn":0,"titleId":12,"total":12},{"current":5,"deadTime":-1,"isOwn":0,"titleId":13,"total":20},{"current":5,"deadTime":-1,"isOwn":0,"titleId":14,"total":30},{"current":5,"deadTime":-1,"isOwn":0,"titleId":15,"total":40},{"current":0,"deadTime":1344,"isOwn":0,"titleId":16,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":17,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":18,"total":-1},{"current":0,"deadTime":1344,"isOwn":0,"titleId":19,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":20,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":21,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":22,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":23,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":24,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":25,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":26,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":27,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":28,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":29,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":30,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":31,"total":10},{"current":0,"deadTime":720,"isOwn":0,"titleId":32,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":33,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":34,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":35,"total":-1},{"current":0,"deadTime":720,"isOwn":0,"titleId":36,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":37,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":38,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":39,"total":-1},{"current":0,"deadTime":672,"isOwn":0,"titleId":40,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":41,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":42,"total":5},{"current":0,"deadTime":-1,"isOwn":0,"titleId":43,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":44,"total":10},{"current":0,"deadTime":-1,"isOwn":0,"titleId":45,"total":15},{"current":0,"deadTime":720,"isOwn":0,"titleId":46,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":47,"total":1000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":48,"total":2000000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":49,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":50,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":51,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":52,"total":-1},{"current":0,"deadTime":-1,"isOwn":0,"titleId":53,"total":10000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":54,"total":20000},{"current":0,"deadTime":-1,"isOwn":0,"titleId":55,"total":30000}]},"head":{"len":1237,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,953 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [18/0/18/SCGetStatisticsInfo/************] - {"body":{"cycleCycle":2,"cycleInfo":{"infos":[]},"gameId":"KOP","loadingTime":650,"open":true,"playerLevelMin":0,"playerVipLevelMin":0,"refreshInterval":10,"saveMaximum":1000,"singleInfo":{"infos":[]},"singleTimeThreshold":20,"submitMinCycle":60,"submitNumLimit":1000},"head":{"len":32,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,955 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [21/0/21/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,956 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,960 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [24/0/24/SCGetFormationInfo/************] - {"body":{"currentformationInfoIndex":0,"formationInfoList":[{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[{"b":2,"f":92001,"h":1499853796715529216,"hi":112001},{"b":4,"f":91001,"h":1499853796740695040,"hi":111001},{"b":6,"f":92002,"h":1499917841363633152,"hi":112002}]},{"autoSkillReleaseType":0,"captainPos":0,"formationId":1,"formationItems":[]}],"levels":[{"exp":0,"formationId":1,"level":0}],"openedFormationIds":[1]},"head":{"len":94,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,960 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [28/0/28/SCPushSecretPlaceFightState/************] - {"body":{"isOpen":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,961 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [28/0/28/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,963 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [28/0/28/SCMoraleSyncData/************] - {"body":{"data":{"costItem":{"bind":false,"expireTime":0,"guid":0,"id":0,"num":0,"type":5},"curMorale":100,"maxMorale":100,"proDecr":0}},"head":{"len":22,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,964 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [31/0/31/SCRedPoingList/************] - {"body":{"redPointList":[{"num":1,"pushToServer":0,"type":1074}]},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,964 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [31/0/31/SCGetAllShipInfo/************] - {"body":{"infos":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,966 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [32/0/32/SCSystemSettings/************] - {"body":{"values":[{"key":1,"value":"9"},{"key":2,"value":"0"},{"key":3},{"key":4,"value":"0"},{"key":5,"value":"1"},{"key":6,"value":"1"}]},"head":{"len":39,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,967 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [33/0/33/SCOnlineTimeInfo/************] - {"body":{"rewardId":1,"time":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,968 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [34/0/34/SCGetAllExistSceneBuff/************] - {"body":{"sceneBuffList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,969 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [35/0/35/SCPushOpenCelebration/************] - {"body":{"endTime":0,"isOpen":0,"staetTime":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:40,970 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [32/0/32/SCSceneEnter/************] - {"body":{"lineId":1,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:41,197 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [42/2/44/SSUpdateInviteInfo/************] - {"body":{"myInviteCode":"947642354","otherInviteCode":"947642354","playerDatas":"947642354"},"head":{"len":33,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:51:41,198 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":1,"msg":"????","opcode":22007,"type":1},"head":{"len":22,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:51:42,016 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCompletedGuides/************] - {"body":{"guideIDs":[1,5],"guideMarks":[]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,017 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [7/0/7/CSGetCompletedGuides/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,068 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetSwitchStates/************] - {"body":{"states":[{"forceSwitch":1,"name":"Achievement","state":1},{"forceSwitch":1,"name":"Activity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"ActivityRaid","state":0,"taskName":"?????3???\"????\"?????????"},{"forceSwitch":1,"name":"Arena","state":0},{"forceSwitch":1,"name":"AutoChess","state":0},{"forceSwitch":1,"name":"AutoChessWeeklyTask","state":0},{"forceSwitch":1,"name":"AutoClosure","state":1},{"forceSwitch":1,"name":"AutoFight","state":0},{"forceSwitch":1,"name":"Bag","state":1},{"forceSwitch":1,"name":"Bless","state":0},{"forceSwitch":1,"name":"BloodyFight","state":0},{"forceSwitch":1,"name":"Bot","state":0},{"forceSwitch":1,"name":"BotPick","state":0},{"forceSwitch":1,"name":"BountyHunt","state":0},{"forceSwitch":1,"name":"BountyHuntBoss","state":0},{"forceSwitch":1,"name":"CatchAction","state":0},{"forceSwitch":1,"name":"Communication","state":1},{"forceSwitch":1,"name":"DailyActivity","state":0,"taskName":"?????2???\"?????\"?????????"},{"forceSwitch":1,"name":"DailyTask","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Deal","state":0},{"forceSwitch":0,"name":"DirtyHttpCheck","state":0},{"forceSwitch":1,"name":"DoubleSpeed","state":0},{"forceSwitch":1,"name":"DressingRoom","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"EquipLuck","state":0},{"forceSwitch":1,"name":"EquipPunch","state":1},{"forceSwitch":1,"name":"EquipTab","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"Expedition","state":0},{"forceSwitch":1,"name":"ExploreSystem","state":1},{"forceSwitch":1,"name":"FaceToFace","state":0},{"forceSwitch":1,"name":"FashionEquip","state":0,"taskName":"?????6???\"?????\"?????????"},{"forceSwitch":1,"name":"Field","state":0,"taskName":"?????1???\"??????\"?????????"},{"forceSwitch":1,"name":"FifthLocation","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"Formation","state":1},{"forceSwitch":1,"name":"Formations","state":0,"taskName":"?????4???\"?????\"?????????"},{"forceSwitch":1,"name":"FourthLocation","state":0,"taskName":"?????3???\"?????\"?????????"},{"forceSwitch":1,"name":"GemQuickCombine","state":1},{"forceSwitch":1,"name":"Gvo","state":0},{"forceSwitch":1,"name":"GvoPlace","state":0},{"forceSwitch":1,"name":"HEISHIShopOpen","state":0,"taskName":"????\"??????\"?????????"},{"forceSwitch":1,"name":"HeroActionEmoji","state":1},{"forceSwitch":1,"name":"HeroAssist","state":0},{"forceSwitch":1,"name":"HeroPromote","state":0},{"forceSwitch":1,"name":"HomeLand","state":0},{"forceSwitch":1,"name":"Informaiton","state":1},{"forceSwitch":1,"name":"IntegralArena","state":0},{"forceSwitch":1,"name":"ItemQuickCombine","state":1},{"forceSwitch":1,"name":"LadderTopWar","state":0},{"forceSwitch":1,"name":"LoginQueue","state":1},{"forceSwitch":1,"name":"MakeMedicine","state":0,"taskName":"?????14???\"??????????\"?????????"},{"forceSwitch":1,"name":"Mall","state":1},{"forceSwitch":1,"name":"MonthCard","state":1},{"forceSwitch":1,"name":"NavigationPass","state":0},{"forceSwitch":0,"name":"NewLadderTask","state":0},{"forceSwitch":1,"name":"OpenCelebration","state":0},{"forceSwitch":1,"name":"OpenTeamRobot","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"Partner","state":0},{"forceSwitch":1,"name":"PersonalTaskSweepAccumulative","state":1},{"forceSwitch":1,"name":"PlayerCollection","state":0},{"forceSwitch":0,"name":"PlayerReturnRecharge","state":0},{"forceSwitch":1,"name":"PlayerShop","state":0},{"forceSwitch":1,"name":"Puzzle","state":1},{"forceSwitch":1,"name":"Raid","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"RaidSweep","state":0,"taskName":"?????8???\"?????\"?????????"},{"forceSwitch":1,"name":"Rank","state":1},{"forceSwitch":1,"name":"RankSwitch","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"Recruit","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":0,"name":"ResetHeroExp","state":0},{"forceSwitch":0,"name":"ResourceBack","state":0},{"forceSwitch":1,"name":"ReturnExpBuff","state":1},{"forceSwitch":1,"name":"RoadToTheStrong","state":0},{"forceSwitch":1,"name":"SailLog","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"SecretPlaceFight","state":0},{"forceSwitch":1,"name":"ShipLevelUp","state":0},{"forceSwitch":1,"name":"ShipSystem","state":0,"taskName":"?????29???\"????\"?????????"},{"forceSwitch":1,"name":"ShipTradeSystem","state":0,"taskName":"????????"},{"forceSwitch":1,"name":"SkillOpenSwitch","state":1},{"forceSwitch":1,"name":"SkillSimulate","state":1},{"forceSwitch":1,"name":"SkipFight","state":1},{"forceSwitch":1,"name":"Society","state":1},{"forceSwitch":1,"name":"SoulCard","state":0},{"forceSwitch":1,"name":"SpringCook","state":0},{"forceSwitch":1,"name":"StrongGuide","state":1},{"forceSwitch":1,"name":"SystemMarket","state":0},{"forceSwitch":1,"name":"Task","state":1},{"forceSwitch":1,"name":"Team","state":0,"taskName":"?????2??5????????????"},{"forceSwitch":0,"name":"TeamBossActivity","state":0},{"forceSwitch":1,"name":"Title","state":1},{"forceSwitch":1,"name":"Train","state":0},{"forceSwitch":1,"name":"TurnTableActivity","state":1},{"forceSwitch":1,"name":"Union","state":0,"taskName":"?????6???\"????\"?????????"},{"forceSwitch":1,"name":"UnionSeaDeclareFight","state":1},{"forceSwitch":1,"name":"UnionSeaFight","state":1},{"forceSwitch":1,"name":"UpRank","state":0,"taskName":"?????2???\"??????\"?????????"},{"forceSwitch":1,"name":"WarChess","state":0},{"forceSwitch":1,"name":"WarChessAccelerate","state":0},{"forceSwitch":1,"name":"WarChessHardChapter","state":0},{"forceSwitch":0,"name":"WarChessSkip","state":0},{"forceSwitch":1,"name":"Welfare","state":0},{"forceSwitch":1,"name":"WelfareHot","state":0},{"forceSwitch":1,"name":"WorldPersonBoss","state":0},{"forceSwitch":0,"name":"WorldTeamBoss","state":0,"taskName":"????????????"},{"forceSwitch":1,"name":"YearActivity","state":1}]},"head":{"len":3603,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,069 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [47/1/48/CSGetSwitchStates/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,070 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [49/0/49/CSRequestNauticaladventure/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,070 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCRequestNauticaladventure/************] - {"body":{"adventureEndTime":0,"adventureIsOpen":false,"challengeEndTime":0,"challengeIsOpen":false,"currentChapterId":0},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,071 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [50/0/50/CSGetCommonFormation/************] - {"body":{"formationType":[10,11]},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,071 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetCommonFormation/************] - {"body":{"commonFormations":[],"isOnFight":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,119 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,119 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [47/0/47/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,120 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [36/0/36/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,121 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:42,121 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [26/0/26/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:42,122 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:51:45,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:51:50,918 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [2/0/2/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:51:50,918 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:51:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:51:58,898 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:00,008 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [6/0/6/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:00,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:04,992 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [26/0/26/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:04,992 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:52:07,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:18,883 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:22,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:23,759 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [40/0/40/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:23,759 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:52:30,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:30,482 [ChatReceiveThread] INFO (BaseService.java:110) - [0/3/3/CSSendChannelChat/************] - {"body":{"msg":{"content":"Text<link=\"2&IId=213002\"><#FF0900>[Intermediate Refined Stone]</color></link>","contentType":"TEXT","flag":0,"fromId":0,"isHorn":true,"sendTime":1712919150461},"toChatChannel":0},"head":{"len":98,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:30,515 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/ErrorMessage/************] - {"body":{"code":27020,"msg":"??????????","opcode":3006,"type":1},"head":{"len":41,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:52:30,516 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [32/4/36/SSSendChannelChatCheckCostReq/************] - {"body":{"chatChannel":"CHAT_CHANNEL_WORLD","chatInfo":{"content":"Text<link=\"2&IId=213002\"><#FF0900>[Intermediate Refined Stone]</color></link>","contentType":"TEXT","flag":0,"fromId":0,"isHorn":true,"sendTime":1712919150461},"sendTimes":0},"head":{"len":100,"pid":************,"serverId":0,"zoneId":1}}
2024-04-12 17:52:35,073 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [33/5/38/CSPointStep/************] - {"body":{"args":[{"key":"TradePermitPage"}],"pointType":"TownPageAds"},"head":{"len":32,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:35,119 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [10/0/10/CSPaymentShopList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:35,122 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCPaymentShopList/************] - {"body":{"shops":[{"firstReward":0,"freeDiamond":0,"icon":"cz-xkz","lastDay":0,"moneyType":"CNY","monthCardType":"TRADE","needMoney":30.0,"payDiamond":300,"paymentDesc":"?????300?????30??????100??","paymentId":"1","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-zzq","lastDay":0,"moneyType":"CNY","monthCardType":"AUTOCHESS","needMoney":30.0,"payDiamond":300,"paymentDesc":"???????????","paymentId":"10","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-jy","lastDay":0,"moneyType":"CNY","monthCardType":"HOMELAND","needMoney":68.0,"payDiamond":680,"paymentDesc":"?????????","paymentId":"11","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":60,"freeDiamond":0,"icon":"cz-1","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":6.0,"payDiamond":60,"paymentDesc":"??6????60??","paymentId":"2","paymentName":"60??","paymentType":"once","receiveFirstReward":false},{"firstReward":300,"freeDiamond":0,"icon":"cz-2","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":30.0,"payDiamond":300,"paymentDesc":"??30????300??","paymentId":"3","paymentName":"300??","paymentType":"once","receiveFirstReward":false},{"firstReward":680,"freeDiamond":70,"icon":"cz-3","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":68.0,"payDiamond":680,"paymentDesc":"??68????680??","paymentId":"4","paymentName":"680??","paymentType":"once","receiveFirstReward":false},{"firstReward":1280,"freeDiamond":150,"icon":"cz-4","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":128.0,"payDiamond":1280,"paymentDesc":"??128????1280??","paymentId":"5","paymentName":"1280??","paymentType":"once","receiveFirstReward":false},{"firstReward":3280,"freeDiamond":580,"icon":"cz-5","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":328.0,"payDiamond":3280,"paymentDesc":"??328????3280??","paymentId":"6","paymentName":"3280??","paymentType":"once","receiveFirstReward":false},{"firstReward":6480,"freeDiamond":1608,"icon":"cz-6","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":648.0,"payDiamond":6480,"paymentDesc":"??648????6480??","paymentId":"7","paymentName":"6480??","paymentType":"once","receiveFirstReward":false},{"firstReward":10000,"freeDiamond":3000,"icon":"cz-7","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":1000.0,"payDiamond":10000,"paymentDesc":"??1000????10000??","paymentId":"8","paymentName":"10000??","paymentType":"once","receiveFirstReward":false}]},"head":{"len":944,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:35,631 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [52/1/53/CSGetMonthCardActivity/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:35,636 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMonthCardActivity/************] - {"body":{"mcActivitys":[{"diamond":300,"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":3},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1},{"bind":false,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001}],"leftDay":0,"needMoney":30.0,"paymentId":"1","signCount":3,"status":"NEED_PAY"}]},"head":{"len":68,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:37,890 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [32/0/32/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:52:37,890 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:37,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:38,901 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:39,104 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [17/0/17/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:39,104 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:39,104 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [6/0/6/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:39,104 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:40,534 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [20/0/20/CSPointStep/************] - {"body":{"args":[{"key":"RechargeWelfare"}],"pointType":"TownPageAds"},"head":{"len":32,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:40,585 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [37/0/37/CSAccumulativeRecharge/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:40,588 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAccumulativeRecharge/************] - {"body":{"stalls":[{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":460009,"num":1,"type":1200},{"bind":false,"expireTime":0,"guid":0,"id":212002,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50000,"type":5}],"needDiamond":1000,"stallId":1,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":214005,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":213001,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214008,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":451001,"num":20,"type":1001}],"needDiamond":3000,"stallId":2,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":112008,"num":1,"type":1100},{"bind":false,"expireTime":0,"guid":0,"id":214011,"num":5,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":213002,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50000,"type":5}],"needDiamond":5000,"stallId":3,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":215005,"num":20,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":213007,"num":5,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":212004,"num":2,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":452201,"num":15,"type":1001}],"needDiamond":10000,"stallId":4,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":215011,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214001,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214008,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214012,"num":10,"type":1001}],"needDiamond":15000,"stallId":5,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":113008,"num":1,"type":1100},{"bind":false,"expireTime":0,"guid":0,"id":214007,"num":5,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214011,"num":5,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214002,"num":10,"type":1001}],"needDiamond":20000,"stallId":6,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":215005,"num":30,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":213004,"num":2,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100000,"type":5}],"needDiamond":30000,"stallId":7,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":215007,"num":10,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":1100105,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":1100205,"num":1,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":1100305,"num":1,"type":1001}],"needDiamond":40000,"stallId":8,"state":1},{"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":215005,"num":50,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214003,"num":2,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":214011,"num":5,"type":1001},{"bind":false,"expireTime":0,"guid":0,"id":215001,"num":10,"type":1001}],"needDiamond":50000,"stallId":9,"state":1}],"totalRecharge":0},"head":{"len":696,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:42,119 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [2/0/2/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:42,119 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:52:42,580 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [23/2/25/CSGetGiftActivityInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:42,583 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCGetGiftActivityInfo/************] - {"body":{"gifts":[{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":1,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":1,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215007,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":225002,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":1,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511303,"num":1,"type":1001}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511304,"num":5,"type":1001}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":210020,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":210018,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":210019,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":3}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216002,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":1},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200000,"type":5}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":2,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":452201,"num":30,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":3888,"type":3}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":3,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":720001,"num":120,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":30,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":3}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":3,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":200,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214014,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215002,"num":50,"type":1001}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":1,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":60,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":1}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":1,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511169,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215004,"num":8,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":3}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":2,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215011,"num":35,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":1}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":2,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216002,"num":260,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214014,"num":5,"type":1001}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480004,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":1,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215011,"num":60,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":1}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":1,"discount":100,"endTime":-1,"giftId":9990001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb_6","labelType":0,"limitNum":5,"personalBuyNum":0,"price":1000.0,"realPrice":1000.0,"refreshType":3,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":0,"num":500000,"type":4},{"bind":true,"expireTime":0,"guid":0,"id":225001,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":3}],"serverBuyNum":0,"serverLimitNum":5},{"buyNumLimitType":1,"costType":1,"discount":100,"endTime":-1,"giftId":9990002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb_13","labelType":0,"limitNum":5,"personalBuyNum":0,"price":1000.0,"realPrice":1000.0,"refreshType":3,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215001,"num":50,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":225002,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":3}],"serverBuyNum":0,"serverLimitNum":5}]},"head":{"len":2122,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:43,293 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [13/0/13/CSGetGiftActivityInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:43,294 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetGiftActivityInfo/************] - {"body":{"gifts":[{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":1,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":1,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215007,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":225002,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":1,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511303,"num":1,"type":1001}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":680003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-baiyin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":68.0,"realPrice":68.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511304,"num":5,"type":1001}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":210020,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":210018,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":210019,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":3}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":3,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216002,"num":40,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":1},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200000,"type":5}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":1280003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-huangjin","labelType":0,"limitNum":2,"personalBuyNum":0,"price":128.0,"realPrice":128.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":452201,"num":30,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":3888,"type":3}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":3,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":720001,"num":120,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":30,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":3}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":3,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":200,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214014,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215002,"num":50,"type":1001}],"serverBuyNum":0,"serverLimitNum":3},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":3280003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-zuanshi","labelType":0,"limitNum":1,"personalBuyNum":0,"price":328.0,"realPrice":328.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":60,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":1}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":1,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":511169,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215004,"num":8,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":3}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":2,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215011,"num":35,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":215005,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":20,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":2000,"type":1}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480003,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":2,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":2,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":216002,"num":260,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214003,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":214014,"num":5,"type":1001}],"serverBuyNum":0,"serverLimitNum":2},{"buyNumLimitType":1,"costType":0,"discount":100,"endTime":-1,"giftId":6480004,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb-ziyu","labelType":0,"limitNum":1,"personalBuyNum":0,"price":648.0,"realPrice":648.0,"refreshType":0,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215011,"num":60,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":216001,"num":10,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":1000,"type":1}],"serverBuyNum":0,"serverLimitNum":1},{"buyNumLimitType":1,"costType":1,"discount":100,"endTime":-1,"giftId":9990001,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb_6","labelType":0,"limitNum":5,"personalBuyNum":0,"price":1000.0,"realPrice":1000.0,"refreshType":3,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":0,"num":500000,"type":4},{"bind":true,"expireTime":0,"guid":0,"id":225001,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":3}],"serverBuyNum":0,"serverLimitNum":5},{"buyNumLimitType":1,"costType":1,"discount":100,"endTime":-1,"giftId":9990002,"giftName":"??????","giftPageType":0,"giftType":2,"icon":"lb_13","labelType":0,"limitNum":5,"personalBuyNum":0,"price":1000.0,"realPrice":1000.0,"refreshType":3,"rewards":[{"bind":true,"expireTime":0,"guid":0,"id":215001,"num":50,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":225002,"num":5,"type":1001},{"bind":true,"expireTime":0,"guid":0,"id":0,"num":200,"type":3}],"serverBuyNum":0,"serverLimitNum":5}]},"head":{"len":2122,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:43,904 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [35/0/35/CSPaymentShopList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:43,904 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPaymentShopList/************] - {"body":{"shops":[{"firstReward":0,"freeDiamond":0,"icon":"cz-xkz","lastDay":0,"moneyType":"CNY","monthCardType":"TRADE","needMoney":30.0,"payDiamond":300,"paymentDesc":"?????300?????30??????100??","paymentId":"1","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-zzq","lastDay":0,"moneyType":"CNY","monthCardType":"AUTOCHESS","needMoney":30.0,"payDiamond":300,"paymentDesc":"???????????","paymentId":"10","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-jy","lastDay":0,"moneyType":"CNY","monthCardType":"HOMELAND","needMoney":68.0,"payDiamond":680,"paymentDesc":"?????????","paymentId":"11","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":60,"freeDiamond":0,"icon":"cz-1","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":6.0,"payDiamond":60,"paymentDesc":"??6????60??","paymentId":"2","paymentName":"60??","paymentType":"once","receiveFirstReward":false},{"firstReward":300,"freeDiamond":0,"icon":"cz-2","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":30.0,"payDiamond":300,"paymentDesc":"??30????300??","paymentId":"3","paymentName":"300??","paymentType":"once","receiveFirstReward":false},{"firstReward":680,"freeDiamond":70,"icon":"cz-3","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":68.0,"payDiamond":680,"paymentDesc":"??68????680??","paymentId":"4","paymentName":"680??","paymentType":"once","receiveFirstReward":false},{"firstReward":1280,"freeDiamond":150,"icon":"cz-4","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":128.0,"payDiamond":1280,"paymentDesc":"??128????1280??","paymentId":"5","paymentName":"1280??","paymentType":"once","receiveFirstReward":false},{"firstReward":3280,"freeDiamond":580,"icon":"cz-5","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":328.0,"payDiamond":3280,"paymentDesc":"??328????3280??","paymentId":"6","paymentName":"3280??","paymentType":"once","receiveFirstReward":false},{"firstReward":6480,"freeDiamond":1608,"icon":"cz-6","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":648.0,"payDiamond":6480,"paymentDesc":"??648????6480??","paymentId":"7","paymentName":"6480??","paymentType":"once","receiveFirstReward":false},{"firstReward":10000,"freeDiamond":3000,"icon":"cz-7","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":1000.0,"payDiamond":10000,"paymentDesc":"??1000????10000??","paymentId":"8","paymentName":"10000??","paymentType":"once","receiveFirstReward":false}]},"head":{"len":944,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:45,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:45,794 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [30/0/30/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:45,794 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [30/0/30/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:45,794 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:45,794 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:47,224 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [9/0/9/CSPointStep/************] - {"body":{"args":[{"key":"FirstCharge"}],"pointType":"TownPageAds"},"head":{"len":28,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:49,881 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [18/0/18/CSPointStep/************] - {"body":{"args":[{"key":"TradePermitPage"}],"pointType":"TownPageAds"},"head":{"len":32,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:49,881 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [18/0/18/CSPaymentShopList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:49,881 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPaymentShopList/************] - {"body":{"shops":[{"firstReward":0,"freeDiamond":0,"icon":"cz-xkz","lastDay":0,"moneyType":"CNY","monthCardType":"TRADE","needMoney":30.0,"payDiamond":300,"paymentDesc":"?????300?????30??????100??","paymentId":"1","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-zzq","lastDay":0,"moneyType":"CNY","monthCardType":"AUTOCHESS","needMoney":30.0,"payDiamond":300,"paymentDesc":"???????????","paymentId":"10","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":0,"freeDiamond":0,"icon":"cz-jy","lastDay":0,"moneyType":"CNY","monthCardType":"HOMELAND","needMoney":68.0,"payDiamond":680,"paymentDesc":"?????????","paymentId":"11","paymentName":"?????","paymentType":"month","receiveFirstReward":false},{"firstReward":60,"freeDiamond":0,"icon":"cz-1","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":6.0,"payDiamond":60,"paymentDesc":"??6????60??","paymentId":"2","paymentName":"60??","paymentType":"once","receiveFirstReward":false},{"firstReward":300,"freeDiamond":0,"icon":"cz-2","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":30.0,"payDiamond":300,"paymentDesc":"??30????300??","paymentId":"3","paymentName":"300??","paymentType":"once","receiveFirstReward":false},{"firstReward":680,"freeDiamond":70,"icon":"cz-3","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":68.0,"payDiamond":680,"paymentDesc":"??68????680??","paymentId":"4","paymentName":"680??","paymentType":"once","receiveFirstReward":false},{"firstReward":1280,"freeDiamond":150,"icon":"cz-4","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":128.0,"payDiamond":1280,"paymentDesc":"??128????1280??","paymentId":"5","paymentName":"1280??","paymentType":"once","receiveFirstReward":false},{"firstReward":3280,"freeDiamond":580,"icon":"cz-5","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":328.0,"payDiamond":3280,"paymentDesc":"??328????3280??","paymentId":"6","paymentName":"3280??","paymentType":"once","receiveFirstReward":false},{"firstReward":6480,"freeDiamond":1608,"icon":"cz-6","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":648.0,"payDiamond":6480,"paymentDesc":"??648????6480??","paymentId":"7","paymentName":"6480??","paymentType":"once","receiveFirstReward":false},{"firstReward":10000,"freeDiamond":3000,"icon":"cz-7","lastDay":0,"moneyType":"CNY","monthCardType":"None","needMoney":1000.0,"payDiamond":10000,"paymentDesc":"??1000????10000??","paymentId":"8","paymentName":"10000??","paymentType":"once","receiveFirstReward":false}]},"head":{"len":944,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:50,446 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [52/0/52/CSGetMonthCardActivity/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:50,446 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetMonthCardActivity/************] - {"body":{"mcActivitys":[{"diamond":300,"itembases":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":3},{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1},{"bind":false,"expireTime":0,"guid":0,"id":452201,"num":10,"type":1001}],"leftDay":0,"needMoney":30.0,"paymentId":"1","signCount":3,"status":"NEED_PAY"}]},"head":{"len":68,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:51,928 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [37/0/37/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:51,928 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [37/0/37/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:51,928 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:51,929 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:52,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:52:55,562 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [34/2/36/CSQueryLineInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:55,563 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCQueryLineInfo/************] - {"body":{"infos":[{"lineId":1,"lineName":"1?","numberOfPeople":1,"state":"NORMAL"},{"lineId":2,"lineName":"2?","numberOfPeople":0,"state":"NORMAL"},{"lineId":3,"lineName":"3?","numberOfPeople":0,"state":"NORMAL"},{"lineId":4,"lineName":"4?","numberOfPeople":0,"state":"NORMAL"},{"lineId":5,"lineName":"5?","numberOfPeople":0,"state":"NORMAL"}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:52:58,015 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [39/1/40/CSTransferLine/************] - {"body":{"lineId":2},"head":{"len":2,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:58,880 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:52:59,186 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [51/0/51/CSQueryLineInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:52:59,186 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCQueryLineInfo/************] - {"body":{"infos":[{"lineId":1,"lineName":"1?","numberOfPeople":1,"state":"NORMAL"},{"lineId":2,"lineName":"2?","numberOfPeople":1,"state":"NORMAL"},{"lineId":3,"lineName":"3?","numberOfPeople":0,"state":"NORMAL"},{"lineId":4,"lineName":"4?","numberOfPeople":0,"state":"NORMAL"},{"lineId":5,"lineName":"5?","numberOfPeople":0,"state":"NORMAL"}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:00,041 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [40/0/40/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:53:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:00,515 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [24/0/24/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:00,515 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:53:01,283 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,285 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCTransferLine/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,334 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [11/0/11/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,334 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-3.396,"y":0.0,"z":105.397},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,375 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712919181364,"serverTime":1712919181375},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,489 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [50/0/50/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,540 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [18/0/18/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,541 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneEnter/************] - {"body":{"lineId":2,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,591 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [16/0/16/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,591 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [16/0/16/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,591 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [5/0/5/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:01,591 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,592 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:01,592 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:03,993 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [31/0/31/CSQueryLineInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:03,993 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCQueryLineInfo/************] - {"body":{"infos":[{"lineId":1,"lineName":"1?","numberOfPeople":0,"state":"NORMAL"},{"lineId":2,"lineName":"2?","numberOfPeople":1,"state":"NORMAL"},{"lineId":3,"lineName":"3?","numberOfPeople":0,"state":"NORMAL"},{"lineId":4,"lineName":"4?","numberOfPeople":0,"state":"NORMAL"},{"lineId":5,"lineName":"5?","numberOfPeople":0,"state":"NORMAL"}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:05,218 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [22/0/22/CSTransferLine/************] - {"body":{"lineId":4},"head":{"len":2,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:07,418 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [32/0/32/CSQueryLineInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:07,418 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCQueryLineInfo/************] - {"body":{"infos":[{"lineId":1,"lineName":"1?","numberOfPeople":0,"state":"NORMAL"},{"lineId":2,"lineName":"2?","numberOfPeople":1,"state":"NORMAL"},{"lineId":3,"lineName":"3?","numberOfPeople":0,"state":"NORMAL"},{"lineId":4,"lineName":"4?","numberOfPeople":1,"state":"NORMAL"},{"lineId":5,"lineName":"5?","numberOfPeople":0,"state":"NORMAL"}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:07,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,494 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCDissolveTeam/************] - {"body":{"result":1},"head":{"len":2,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,494 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCTransferLine/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,545 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [33/0/33/CSScenePreEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,545 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCScenePreEnter/************] - {"body":{"pos":{"x":-3.396,"y":0.0,"z":105.397},"sceneMapId":3},"head":{"len":19,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,588 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSyncTime/************] - {"body":{"clientTime":1712919188555,"serverTime":1712919188588},"head":{"len":14,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,698 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [45/0/45/CSSceneInit/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,750 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [34/0/34/CSSceneEnter/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,750 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneEnter/************] - {"body":{"lineId":4,"result":1,"sceneId":3},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,853 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [41/0/41/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,853 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [41/0/41/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,853 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,853 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [30/0/30/CSHaveUnionTask/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:08,853 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:08,854 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCHaveUnionTask/************] - {"body":{"curStage":0,"hasGetUnionTaskTimes":0,"taskMaxNum":0},"head":{"len":6,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:11,258 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [37/0/37/CSQueryLineInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:11,258 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCQueryLineInfo/************] - {"body":{"infos":[{"lineId":1,"lineName":"1?","numberOfPeople":0,"state":"NORMAL"},{"lineId":2,"lineName":"2?","numberOfPeople":0,"state":"NORMAL"},{"lineId":3,"lineName":"3?","numberOfPeople":0,"state":"NORMAL"},{"lineId":4,"lineName":"4?","numberOfPeople":1,"state":"NORMAL"},{"lineId":5,"lineName":"5?","numberOfPeople":0,"state":"NORMAL"}]},"head":{"len":70,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:15,501 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [37/0/37/CSGetDiamondLimit/************] - {"body":{"playerId":************},"head":{"len":7,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:15,502 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetDiamondLimit/************] - {"body":{"limitCount":5000,"playerId":************},"head":{"len":10,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:15,502 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [27/1/28/CSGetShopMallStruct/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:15,504 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetShopMallStruct/************] - {"body":{"tabInfos":[{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":0,"shopTypeName":"????"},{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":1,"shopTypeName":"????"},{"shopPageIds":[1],"shopPageNames":[""],"shopTypeId":19,"shopTypeName":"????"}]},"head":{"len":66,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:15,551 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [2/0/2/CSGetLimitList/************] - {"body":{"pageId":1,"playerId":************,"shopType":0},"head":{"len":11,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:15,552 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetLimitList/************] - {"body":{"limitList":[],"playerId":************,"shopType":0},"head":{"len":9,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:15,606 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [21/3/24/CSGetShopItemList/************] - {"body":{"pageId":1,"shopType":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:15,611 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCGetShopItemList/************] - {"body":{"maxRefreshTimes":0,"nextRefreshTime":0,"pageId":1,"refreshTimes":0,"shopGoods":[{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":280,"type":1}],"discountPrice":[280],"discountRate":10000,"goodIndex":1,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215011,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":10,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":225001,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":11,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":225002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":20,"type":1}],"discountPrice":[20],"discountRate":10000,"goodIndex":12,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214012,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":13,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215006,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":300,"type":1}],"discountPrice":[300],"discountRate":10000,"goodIndex":14,"limitType":"0,0,1,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214010,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1}],"discountPrice":[200],"discountRate":10000,"goodIndex":15,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214008,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":600,"type":1}],"discountPrice":[600],"discountRate":10000,"goodIndex":16,"limitType":"0,0,3,1,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214050,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":500,"type":1}],"discountPrice":[500],"discountRate":10000,"goodIndex":24,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":217006,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":150,"type":1}],"discountPrice":[150],"discountRate":10000,"goodIndex":25,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":90,"type":1}],"discountPrice":[90],"discountRate":10000,"goodIndex":26,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800004,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":29,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":800007,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":200,"type":1}],"discountPrice":[200],"discountRate":10000,"goodIndex":35,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":215015,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":10,"type":1}],"discountPrice":[10],"discountRate":10000,"goodIndex":36,"limitType":"0,0,1,20,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":212014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":37,"limitType":"0,0,1,15,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":213014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":300,"type":1}],"discountPrice":[300],"discountRate":10000,"goodIndex":38,"limitType":"0,0,1,10,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":214014,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":39,"limitType":"0,0,1,2,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":511201,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":100,"type":1}],"discountPrice":[100],"discountRate":10000,"goodIndex":40,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":514031,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":50,"type":1}],"discountPrice":[50],"discountRate":10000,"goodIndex":42,"limitType":"0,0,1,30,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":216002,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1},{"banned":0,"canGiveAway":0,"curBuyTimes":-1,"currency":[{"bind":false,"expireTime":0,"guid":0,"id":0,"num":280,"type":1}],"discountPrice":[280],"discountRate":10000,"goodIndex":48,"limitType":"0,0,0,0,0","maxCountPerTrade":999,"modelResource":"","preJudge":"","shopItem":[{"bind":false,"expireTime":0,"guid":0,"id":218011,"num":1,"type":1001}],"shopType":0,"sortId":0,"unlockLevel":1}],"shopType":0},"head":{"len":1619,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:18,160 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCPrisonInfo/************] - {"body":{"isInPrison":0,"prisonSurplusTime":0},"head":{"len":4,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:18,160 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/0/40/CSPrisonInfo/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:18,161 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [40/1/41/CSAcceptedBountyHuntPersonList/************] - {"body":{},"head":{"len":0,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:18,161 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCAcceptedBountyHuntPersonList/************] - {"body":{"personList":[]},"head":{"len":0,"pid":************,"serverId":1,"zoneId":0}}
2024-04-12 17:53:18,828 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [15/0/15/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:18,828 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:53:18,905 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:53:22,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:30,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:37,895 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [37/0/37/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:53:37,895 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:53:37,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:38,905 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:53:45,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:53:56,289 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [20/0/20/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:53:56,289 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:53:58,876 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:54:00,038 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [38/0/38/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:54:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:07,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:14,680 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [35/0/35/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:54:14,680 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:54:15,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:18,899 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:54:22,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:30,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:33,071 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [48/0/48/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:54:33,072 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:54:37,904 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [46/0/46/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:54:37,904 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:54:37,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:38,864 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:54:45,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:47,121 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [36/0/36/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:54:47,121 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:54:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:54:58,874 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:55:00,037 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [37/0/37/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:55:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:05,871 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [51/0/51/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:55:05,871 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:55:07,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:18,908 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:55:20,294 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [47/0/47/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:55:20,294 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:55:22,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:30,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:34,339 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [35/0/35/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:55:34,339 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:55:37,950 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:55:37,950 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [71/1/72/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:55:37,981 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:38,905 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:55:45,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:48,396 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [1/1/2/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:55:48,396 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:55:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:55:58,864 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:56:00,024 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [23/0/23/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:56:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:06,809 [SceneProcessorGroup-thread-2] INFO (BaseService.java:110) - [28/0/28/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:56:06,809 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:56:07,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:18,887 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:56:20,853 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [6/0/6/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:56:20,853 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [1/0/1/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:56:22,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:30,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:37,894 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [36/0/36/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:56:37,894 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:56:37,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:38,905 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:56:39,239 [SceneProcessorGroup-thread-6] INFO (BaseService.java:110) - [20/0/20/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:56:39,239 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:56:45,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:52,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:56:53,299 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [26/0/26/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:56:53,299 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:56:58,894 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:57:00,005 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [5/0/5/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:57:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:07,344 [SceneProcessorGroup-thread-5] INFO (BaseService.java:110) - [14/0/14/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:57:07,344 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:57:07,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:18,863 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:57:21,403 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [12/0/12/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:57:21,403 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:57:22,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:30,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:37,870 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [13/0/13/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:57:37,870 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:57:37,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:38,880 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:57:39,796 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [28/0/28/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:57:39,796 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:57:45,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:57:58,189 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [45/0/45/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:57:58,189 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:57:58,891 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:58:00,005 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [4/0/4/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:58:00,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:07,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:12,243 [SceneProcessorGroup-thread-1] INFO (BaseService.java:110) - [35/0/35/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:58:12,243 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:58:15,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:18,864 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:58:22,964 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:30,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:30,616 [SceneProcessorGroup-thread-4] INFO (BaseService.java:110) - [38/0/38/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:58:30,616 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:58:37,881 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:58:37,881 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [23/0/23/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:58:37,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:38,890 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:58:45,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:48,967 [SceneProcessorGroup-thread-3] INFO (BaseService.java:110) - [3/0/3/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:58:48,967 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:58:52,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:58:58,862 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:00,025 [TEAM_BOSS_THREAD_MSG-thread-1] INFO (BaseService.java:110) - [25/0/25/SSRefreshTeamBossRank/0] - {"body":{},"head":{"len":0,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:00,025 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":101,"message":"CknlvLrogIXmjJHmiJjov5jmnIkx5YiG6ZKf5byA5ZCv77yM6K+35ZCE5L2N6Ii56ZW/5YGa5aW95oiY5paX55qE5YeG5aSH77yBEAIYACAA","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":93,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:00,026 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":3006,"message":"CmAQABpNVGV4dOW8uuiAheaMkeaImOi/mOaciTHliIbpkp/lvIDlkK/vvIzor7flkITkvY3oiLnplb/lgZrlpb3miJjmlpfnmoTlh4blpIfvvIEgp+LXj+0xKAAwADoAQAAQABgCIAE=","playerId":0,"serverId":1,"type":1,"zoneId":0},"head":{"len":117,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:00,030 [ChatReceiveThread] INFO (BaseService.java:110) - [0/1/1/SSPushChannelChat/0] - {"body":{"chatFrom":"SYSTEM","fromId":0,"msg":{"content":"Text??????1??????????????????","contentType":"TEXT","customContacterChatInfo":{},"flag":0,"fromId":0,"isHorn":false,"sendTime":1712919540007},"toChatChannel":"CHAT_CHANNEL_SYSTEM","toId":0},"head":{"len":102,"pid":0,"serverId":1,"zoneId":1}}
2024-04-12 17:59:00,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:07,358 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [19/0/19/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:59:07,358 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:59:07,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:15,464 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:18,892 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:22,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:25,720 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [2/0/2/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:59:25,720 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:310","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:59:30,466 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:37,901 [WORLD_UNION_MSG-thread-1] INFO (BaseService.java:110) - [44/0/44/SSBenfuUSDFScheduleMessage/0] - {"body":{"groupId":1,"zoneId":1},"head":{"len":4,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:59:37,901 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":85601,"message":"CAE=","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":40,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:37,965 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:38,861 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
2024-04-12 17:59:39,821 [SceneProcessorGroup-thread-7] INFO (BaseService.java:110) - [46/0/46/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:59:39,821 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:59:45,465 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":1,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:52,966 [IntegralMainThread] INFO (BaseService.java:110) - [0/0/0/SSIntegralArenaRankReq/0] - {"body":{"areaType":2,"groupId":1,"sendCenter":true,"version":0},"head":{"len":8,"pid":0,"serverId":1,"zoneId":0}}
2024-04-12 17:59:58,163 [SceneProcessorGroup-thread-8] INFO (BaseService.java:110) - [9/0/9/CSSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":72,"pid":************,"serverId":1,"zoneId":1}}
2024-04-12 17:59:58,163 [IO_LOOP-6-1] INFO (ClientEncodeHandler.java:73) - [0/0/0/SCSceneTriggerEvent/************] - {"body":{"events":[{"eventArgs":"playerId:************|heroSceneActionId:311","eventName":"ON_SE_PlayActionAnime","guid":************}]},"head":{"len":77,"pid":0,"serverId":0,"zoneId":0}}
2024-04-12 17:59:58,874 [MainMessageProcessor] INFO (BaseService.java:110) - [0/0/0/SGDispatcherMessage/0] - {"body":{"code":78329,"message":"CAEQAQ==","playerId":0,"sceneFlag":"quanfu:127.0.0.1:12308","serverId":0,"type":3,"zoneId":1},"head":{"len":42,"pid":0,"serverId":0,"zoneId":1}}
