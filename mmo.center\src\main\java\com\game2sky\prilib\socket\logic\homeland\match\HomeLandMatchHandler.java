package com.game2sky.prilib.socket.logic.homeland.match;


/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2020年6月1日 下午9:29:51  daixl
 */
@Deprecated
public class HomeLandMatchHandler {
//
//	private static TLongLongHashMap matchPlayers = new TLongLongHashMap();
//
//	public static boolean checkCanMatch(long playerId, long now) {
//		long value = matchPlayers.get(playerId);
//		if (value > now) {
//			return false;
//		}
//		return true;
//	}
//
//	public static List<Long> match(SSRequestCanRobList msg, int times, long now) {
////		// 将玩家加入正在匹配列表，防止玩家多次匹配，会在匹配结束以后，删除这个记录
////		matchPlayers.put(msg.getPlayerId(), now + 10 * 1000);
////
////		List<Long> result = new ArrayList<Long>();
////		List<Long> excludeList = new ArrayList<Long>();
////		List<Long> enemyIds = msg.getEnemyIds();
////		if(enemyIds != null){
////			excludeList.addAll(enemyIds);
////		}
////		List<Long> friendIds = msg.getFriendIds();
////		if(friendIds != null){
////			excludeList.addAll(friendIds);
////		}
////		excludeList.add(msg.getPlayerId());
////		
////		int warValue = msg.getWarValue();
////		int robLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_ROB_WV_LIMIT);
////		int upperLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_MAX_WAR_VAL);
////		int lowerLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_MIN_WAR_VAL);
////		int upperWarValue = warValue + robLimit;
////		int lowerWarValue = warValue - robLimit;
////
////		if (upperWarValue > upperLimit) {
////			upperWarValue = upperLimit;
////		}
////		if (lowerWarValue < lowerLimit) {
////			lowerWarValue = lowerLimit;
////		}
////
////		int playerLevel = msg.getPlayerLevel();
////		int levelLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_ROB_PLAYERLEVEL_LIMIT);
////		int upperPlayerLevel = playerLevel + levelLimit;
////		int lowerPlayerLevel = playerLevel - levelLimit;
////
////		int lobbyLevel = msg.getLobbyLevel();
////		int lobbyLimit = BaseService.getConfigIntByDefault(PriConfigKeyName.HOMELAND_ROB_LOBBYLEVEL_LIMIT);
////		int upperlobbyLevel = lobbyLevel + lobbyLimit;
////		int lowerlobbyLevel = lobbyLevel - lobbyLimit;
////
////		HomeLandMatchSort sort = CenterHomelandGlobalManager.getSort();
////		TreeMap<Long, HashSet<Long>> byWarValueRegion = sort
////			.getByWarValueRegion(upperWarValue, lowerWarValue, warValue);
////		//FIXME
////		AMLog.LOG_COMMON.info("HomeLandMatchHandler#match: playerId : {}, result : {}", msg.getPlayerId(), JsonUtils.O2S(byWarValueRegion));
////		Iterator<Entry<Long, HashSet<Long>>> iterator = byWarValueRegion.entrySet().iterator();
////		while (iterator.hasNext()) {
////			int leftTimes = times;
////			Entry<Long, HashSet<Long>> next = iterator.next();
////			HashSet<Long> value = next.getValue();
////
////			value.removeAll(excludeList);
////
////			Iterator<Long> setIterator = value.iterator();
////			List<Long> getPlayerIds = new ArrayList<Long>();
////			while (setIterator.hasNext()) {
////				long playerId = setIterator.next();
////				HomelandBaseInfo info = CenterHomelandGlobalManager.getByPlayerId(playerId);
////				if (info == null) {
////					continue;
////				}
////				int enemyLevel = info.getLevel();
////				if (enemyLevel > upperPlayerLevel || enemyLevel < lowerPlayerLevel) {
////					continue;
////				}
////
////				int enemyLobbyLevel = info.getLobbyLevel();
////				if (enemyLobbyLevel > upperlobbyLevel || enemyLobbyLevel < lowerlobbyLevel) {
////					continue;
////				}
////
////				long nextMatchTime = info.getNextMatchTime();
////				if (nextMatchTime > now) {
////					continue;
////				}
////
////				int canGet = CenterHomelandGlobalManager.getCanGet(playerId, warValue);
////				if (canGet <= 0) {
////					continue;
////				}
////				getPlayerIds.add(playerId);
////			}
////
////			result.addAll(randomPlayerIds(getPlayerIds, leftTimes));
////			int size = result.size();
////			if (size >= times) {
////				break;
////			} else {
////				leftTimes = times - size;
////			}
////		}
////
////		matchPlayers.remove(msg.getPlayerId());
////		return result;
//		return null;
//	}
//
////	@SuppressWarnings("unchecked")
////	private static List<Long> randomPlayerIds(List<Long> list, int times) {
////
////		if (list == null || list.isEmpty()) {
////			return Collections.EMPTY_LIST;
////		}
////
////		if (list.size() <= times) {
////			return list;
////		}
////
////		List<Long> result = new ArrayList<Long>();
////		ThreadLocalRandom current = ThreadLocalRandom.current();
////		for (int index = 0; index < times; index++) {
////			int size = list.size();
////			int nextInt = current.nextInt(size);
////			result.add(list.get(nextInt));
////			list.remove(nextInt);
////		}
////		return result;
////	}
}
