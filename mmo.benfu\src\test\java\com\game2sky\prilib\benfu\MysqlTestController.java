package com.game2sky.prilib.benfu;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.FutureTask;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.dbcp.BasicDataSource;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.Globals;
import com.game2sky.publib.dbs.dao.InstPlayerDao;
import com.game2sky.publib.dbs.model.InstPlayer;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * Mysql数据库压测.
 *
 * <AUTHOR> Lee
 * @version v0.1 2017年7月4日 下午8:42:23  Jet Lee
 */
@Controller
public class MysqlTestController extends BaseController {

//	@RequestMapping(path = "/createAllHumans")
//	public ModelAndView createAllHumans() {
//		long begin = System.currentTimeMillis();
//		AMLog.LOG_COMMON.info("[mysql_pressure_test] [createAllHumans] [begin]");
//		for (Integer zoneId : Globals.getAllZoneId()) {
//			InstPlayerDao instPlayerDao = Globals.getDbsDataSource().getBenfuDao(InstPlayerDao.class, zoneId);
//			for (int i = 10001; i <= 20000; i++) {
//				InstPlayer instPlayer = createPlayer(0, i, zoneId, ("名称" + i), 0, (i + ""));
//				instPlayer.setChannelId("tencent");
//				instPlayerDao.save(instPlayer);
//			}
//		}
//		AMLog.LOG_COMMON.info("[mysql_pressure_test] [createAllHumans] [end] [cost: "
//								+ (System.currentTimeMillis() - begin) + "]");
//		return returnString("[mysql_pressure_test] [createAllHumans] [success]");
//	}
//
//	private static InstPlayer createPlayer(int isRobot, long playerId, int zoneId, String name, int gender,
//											String platformUid) {
//
//		InstPlayer instPlayer = new InstPlayer();
//		instPlayer.setPlayerId(playerId);
//		instPlayer.setZoneId(zoneId);
//		instPlayer.setNickName(name);
//		instPlayer.setGender(gender);
//		instPlayer.setIsRobot(isRobot);
//		instPlayer.setDiamond(0L);
//		instPlayer.setFreeDiamond(0L);
//		instPlayer.setPayDiamond(0L);
//		instPlayer.setLevel(1);
//		instPlayer.setExp(0L);
//		instPlayer.setHeadIcon("1");
//		instPlayer.setLastLoginTime(System.currentTimeMillis());
//		instPlayer.setClientVersion("");
//		instPlayer.setPlatformUid(platformUid);
//		instPlayer.setChargeDiamond(0L);
//		instPlayer.setChargeMoney(0.0);
//		instPlayer.setChatGroupSettings("");
//		instPlayer.setEndGagTime(0l);
//		instPlayer.setEndGagWatchTime(0l);
//		instPlayer.setFightPower(0l);
//		instPlayer.setForbidDealing("");
//		instPlayer.setGagPromiseMoney(0);
//		instPlayer.setGagTimes(0);
//		instPlayer.setLastLogoutTime(0l);
//		instPlayer.setVip(0);
//		return instPlayer;
//	}
//
//	@RequestMapping(path = "/loadAllHuman/{ip},{threadNum},{initialSize},{maxActive},{minPlayerId},{maxPlayerId}")
//	public void loadAllHuman(@PathVariable String ip, @PathVariable int threadNum, @PathVariable int initialSize,
//								@PathVariable int maxActive, @PathVariable int minPlayerId,
//								@PathVariable int maxPlayerId, HttpServletRequest request) {
//		ExecutorService _ioPool = Executors.newFixedThreadPool(threadNum);
//		BasicDataSource dataSource = new BasicDataSource();
//		dataSource.setInitialSize(initialSize);
//		dataSource.setMaxActive(maxActive);
//		dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
//		// *************;
//		dataSource.setUrl("jdbc:mysql://" + ip
//							+ ":3306/player_s1?useUnicode=true&amp;characterEncoding=UTF-8&amp;autoReconnect=true");
//		dataSource.setUsername("root");
//		dataSource.setPassword("op123456A");
//		final JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
//		List<FutureTask<Integer>> futureTasks = new ArrayList<FutureTask<Integer>>();
//		long begin = System.currentTimeMillis();
//		for (final Integer zoneId : Globals.getAllZoneId()) {
////			Set<Integer> set = new HashSet<Integer>();
//			for (int i = minPlayerId; i <= maxPlayerId; i++) {
////				int randomInt = RandomUtil.randomInt(minPlayerId, maxPlayerId);
////				while (set.contains(randomInt)) {
////					randomInt = RandomUtil.randomInt(minPlayerId, maxPlayerId);
////				}
////				set.add(randomInt);
//				final long playerId = i;
//				FutureTask<Integer> future = new FutureTask<Integer>(new Callable<Integer>() {
//					@Override
//					public Integer call() throws Exception {
//						long beginTime = System.currentTimeMillis();
////						Human human = new Human(zoneId, playerId, null, null);
////						Globals.regHuman(human);
////						human.load();
//						String sql = "select * from inst_player where playerId = " + playerId;
//						InstPlayer instPlayer = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<InstPlayer>(
//							InstPlayer.class));
//						AMLog.LOG_COMMON.info("[mysql_pressure_test] [human_load] [playerId: "
//												+ playerId
//												+ "][playerId: "
//												+ instPlayer.getNickName()
//												+ "][beginTime "
//												+ DateFormatUtils
//													.format(new Date(beginTime), "yyyy-MM-dd HH:mm:ss:SSS")
//												+ "][cost: " + (System.currentTimeMillis() - beginTime) + "ms]");
//
//						return 1;
//					}
//				});
//				_ioPool.submit(future);
//				futureTasks.add(future);
//			}
//		}
//
//		Iterator<FutureTask<Integer>> iterator = futureTasks.iterator();
//		while (iterator.hasNext()) {
//			FutureTask<Integer> next = iterator.next();
//			try {
//				if (next.get() == 1) {
//					iterator.remove();
//				}
//			} catch (InterruptedException | ExecutionException e) {
//			}
//		}
//
//		AMLog.LOG_COMMON.info("[mysql_pressure_test] [loadAllHuman] [end] [cost: "
//								+ (System.currentTimeMillis() - begin) + "]");
//	}
//
//	@RequestMapping(path = "/loadAllHumanFromDBS/{threadNum},{minPlayerId},{maxPlayerId}")
//	public ModelAndView loadAllHumanFromDBS(@PathVariable int threadNum, @PathVariable int minPlayerId,
//											@PathVariable int maxPlayerId, HttpServletRequest request) {
//		ExecutorService _ioPool = Executors.newFixedThreadPool(threadNum);
//		List<FutureTask<Integer>> futureTasks = new ArrayList<FutureTask<Integer>>();
//		long begin = System.currentTimeMillis();
//		for (final Integer zoneId : Globals.getAllZoneId()) {
////			Set<Integer> set = new HashSet<Integer>();
//			for (int i = minPlayerId; i <= maxPlayerId; i++) {
////				int randomInt = RandomUtil.randomInt(minPlayerId, maxPlayerId);
////				while (set.contains(randomInt)) {
////					randomInt = RandomUtil.randomInt(minPlayerId, maxPlayerId);
////				}
////				set.add(randomInt);
//				final long playerId = i;
//				FutureTask<Integer> future = new FutureTask<Integer>(new Callable<Integer>() {
//					@Override
//					public Integer call() throws Exception {
//						long beginTime = System.currentTimeMillis();
//						Human human = new Human(zoneId, playerId, null, null);
////						Globals.regHuman(human);
////						human.firstLoad();
//						human.load();
//						AMLog.LOG_COMMON.info("[mysql_pressure_test] [human_load] [playerId: "
//												+ playerId
//												+ "] [beginTime "
//												+ DateFormatUtils
//													.format(new Date(beginTime), "yyyy-MM-dd HH:mm:ss:SSS")
//												+ "] [cost: " + (System.currentTimeMillis() - beginTime) + "ms]");
//
//						return 1;
//					}
//				});
//				_ioPool.submit(future);
//				futureTasks.add(future);
//			}
//		}
//
//		Iterator<FutureTask<Integer>> iterator = futureTasks.iterator();
//		while (iterator.hasNext()) {
//			FutureTask<Integer> next = iterator.next();
//			try {
//				if (next.get() == 1) {
//					iterator.remove();
//				}
//			} catch (InterruptedException | ExecutionException e) {
//			}
//		}
//
//		AMLog.LOG_COMMON.info("[mysql_pressure_test] [loadAllHuman] [end] [threadNum: " + threadNum + "][cost: "
//								+ (System.currentTimeMillis() - begin) + "]");
//		return returnString("[mysql_pressure_test] [loadAllHumanFromDBS] [success] [threadNum: " + threadNum
//							+ "][cost: " + (System.currentTimeMillis() - begin) + "ms]");
//	}
}
