package com.game2sky.prilib.benfu.logic.gvo;

import java.util.ArrayList;
import java.util.Collection;

import org.springframework.stereotype.Service;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.gvo.GvoBattlePlayerInfo;
import com.game2sky.prilib.communication.game.gvo.GvoBattleShipInfo;
import com.game2sky.prilib.communication.game.gvo.SCGvoQuit;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleReq;
import com.game2sky.prilib.communication.game.gvo.SSGvoAllocBattleRes;
import com.game2sky.prilib.communication.game.gvo.SSGvoBattleChannelInactive;
import com.game2sky.prilib.communication.game.gvo.SSGvoEnterBattleReq;
import com.game2sky.prilib.communication.game.gvo.SSGvoEnterBattleRes;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.constants.ErrorCodeConstants;
import com.game2sky.prilib.core.dict.domain.DictSceneArea;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.core.socket.logic.gvo.IGvoManager;
import com.game2sky.prilib.core.socket.logic.gvo.afight.Actor;
import com.game2sky.prilib.core.socket.logic.gvo.afight.IAFightConstants;
import com.game2sky.prilib.core.socket.logic.gvo.model.GvoShipModel;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.prilib.core.socket.logic.scene.base.SceneCommonService;
import com.game2sky.prilib.core.socket.logic.scene.unit.player.ScenePlayerObject;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.game.scene.SCTransferScene;
import com.game2sky.publib.communication.game.struct.FPoint3;
import com.game2sky.publib.communication.game.struct.ServerAddress;
import com.game2sky.publib.event.EventSubscriber;
import com.game2sky.publib.framework.boot.AMFrameWorkBoot;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.framework.crossServer.CrossBattleTypeEnum;
import com.game2sky.publib.framework.crossServer.CrossServerManager;
import com.game2sky.publib.framework.engine.config.ServerSocketAddress;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.client.ClientConnectStatus;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveListener;
import com.game2sky.publib.framework.netty.support.handler.net.event.IClientBattleChannelInactiveSource;
import com.game2sky.publib.socket.logic.human.AbstractHuman;

/**
 * TODO 大航海
 *
 * <AUTHOR>
 * @version v0.1 2020年10月20日 下午4:57:03  guojijun
 */
@Service
@EventSubscriber(eventSources = { IClientBattleChannelInactiveSource.class })
public class GvoBenfuService implements IAFightConstants, IClientBattleChannelInactiveListener {

	public void ssGvoAllocBattleRes(SSGvoAllocBattleRes ssGvoAllocBattleRes, Human human) {
		if (human.isCrossBattle(CrossBattleTypeEnum.GVO)) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(),
				ErrorCodeConstants.GVO_ENTER_BATTLE_STATE_ERROR);
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle已经在跨服场景中,playerId={}", human.getPlayerId());
			}
			return;
		}
		ServerAddress address = ssGvoAllocBattleRes.getAddress();
		if (address == null) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(), ErrorCodeConstants.DROP_MSG);
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle没有分配到战斗服,playerId={}", human.getPlayerId());
			}
			return;
		}
		if (!human.isOnline()) {
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("@GvoBattle玩家已经下线,playerId={},address={}", human.getPlayerId(), address);
			}
			return;
		}
		if (ssGvoAllocBattleRes.getExecTimes() >= GvoScheduleEnterBattle.RETRY_MAX_COUNT) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(), ErrorCodeConstants.DROP_MSG);
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle建立连接超时,playerId={},address={}", human.getPlayerId(), address);
			}
			return;
		}
		ServerSocketAddress serverAddress = ServerSocketAddress.parseProto(address);
		ClientConnectStatus connectStatus = NetConnectCenter.getInstance().getClientConnectStatus(address.getFlag());
		if (connectStatus != ClientConnectStatus.CONNECTED) {
			CrossServerManager.init(serverAddress, true);
		}
		boolean bl = CrossServerManager.isCrossServerConnected(serverAddress.getServerType(), serverAddress.getFlag());
		if (!bl) {
			GvoScheduleEnterBattle gvoScheduleEnterBattle = new GvoScheduleEnterBattle();
			gvoScheduleEnterBattle.setServerId(human.getServerId());
			gvoScheduleEnterBattle.setPlayerId(human.getPlayerId());
			gvoScheduleEnterBattle.setSsGvoAllocBattleRes(ssGvoAllocBattleRes);
			Globals.getScheduleService().scheduleOnce(gvoScheduleEnterBattle, GvoScheduleEnterBattle.DELAY);
			return;
		}
		bl = human.enterCrossBattle(CrossBattleTypeEnum.GVO, serverAddress);
		if (!bl) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(),
				ErrorCodeConstants.GVO_ENTER_BATTLE_STATE_ERROR);
			return;
		}
		SSGvoEnterBattleReq ssGvoEnterBattleReq = new SSGvoEnterBattleReq();
		ssGvoEnterBattleReq.setServerId(human.getServerId());
		ssGvoEnterBattleReq.setPlayerId(human.getPlayerId());
		ssGvoEnterBattleReq.setSceneMapId(PVP_SCENE_MAP_ID);

		ScenePlayerObject playerObject = human.getSceneObject();
		GvoBattlePlayerInfo playerInfo = new GvoBattlePlayerInfo();
		playerInfo.setRedNameId(playerObject.getRedNameId());
		playerInfo.setNickname(playerObject.getName());
		playerInfo.setTitleId(playerObject.getTitleId());
		playerInfo.setToSeaShipId(human.getGvoManager().getToSeaShipId());
		Collection<GvoShipModel> shipModels = human.getGvoManager().getModel().getShipModels();
		ArrayList<GvoBattleShipInfo> shipInfos = new ArrayList<GvoBattleShipInfo>(shipModels.size());
		for (GvoShipModel shipModel : shipModels) {
			GvoBattleShipInfo shipInfo = new GvoBattleShipInfo();
			shipInfos.add(shipInfo);
			shipInfo.setShipId(shipModel.getShipId());
			shipInfo.setShipGuid(shipModel.getShipGuid());
			shipInfo.setHp(shipModel.getHp());
			shipInfo.setAtk(shipModel.fetchAtk());
			shipInfo.setDef(shipModel.fetchDef());
			shipInfo.setMaxHp(shipModel.fetchHp());
			shipInfo.setMoveSpeed(shipModel.fetchMoveSpeed());
			shipInfo.setMaxEnergy(shipModel.fetchMaxEnergy());
		}
		playerInfo.setShipInfos(shipInfos);
		ssGvoEnterBattleReq.setPlayerInfo(playerInfo);

		bl = CoreBoot.dispatch2Server(ssGvoEnterBattleReq, 0, 0, serverAddress.getFlag());
		if (!bl) {
			human.exitCrossBattle(CrossBattleTypeEnum.GVO);
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(), ErrorCodeConstants.DROP_MSG);
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle发送SSGvoEnterBattleReq失败,playerId={},address={}", human.getPlayerId(),
					address);
			}
		}
	}

	public void ssGvoEnterBattleRes(SSGvoEnterBattleRes ssGvoEnterBattleRes, Human human) {
		boolean bl = human.isCrossBattle(CrossBattleTypeEnum.GVO);
		if (!bl) {
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle收到SSGvoEnterBattleRes消息,但是已经不再跨服状态,playerId={}", human.getPlayerId());
			}
			return;
		}
		int errorCode = ssGvoEnterBattleRes.getErrorCode();
		if (errorCode > 0) {
			human.exitCrossBattle(CrossBattleTypeEnum.GVO);
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(), errorCode);
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle进入跨服场景失败,playerId={},errorCode={}", human.getPlayerId(), errorCode);
			}
		} else {
			human.getSceneObject().stopReceiveSceneMsg();
			human.push2Gateway(new SCTransferScene());
//			human.push2Gateway(new SCGvoEnterBattle());
			String serverFlag = CoreBoot.getServerFlag(ServerTypeEnum.BATTLE, human.getPlayerId());
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("@GvoBattle进入跨服场景,playerId={},serverFlag={}", human.getPlayerId(), serverFlag);
			}
		}
	}

	@Override
	public void onClientBattleChannelInactive(AbstractHuman abstractHuman) {
		Human human = (Human) abstractHuman;
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.GVO);
		if (isCrossBattle) {
			Message message = Message.buildByServerId(human.getPlayerId(), human.getServerId(),
				new SSGvoBattleChannelInactive(), null, null, null);
			Globals.getMsgProcessDispatcher().put(message);
		}
	}

	public void ssGvoBattleChannelInactive(Human human) {
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.GVO);
		if (isCrossBattle) {
			human.exitCrossBattle(CrossBattleTypeEnum.GVO);
			AMFrameWorkBoot.closeChannel(human.getChannel(), AMFrameWorkBoot.i18n("SYS_010016"),
				"@Minc.close.GvoBattle.client.battle.channel.inactive");
			if (AMLog.LOG_COMMON.isWarnEnabled()) {
				AMLog.LOG_COMMON.warn("@GvoBattle本服和战斗服连接断开,断开玩家连接,playerId={}", human.getPlayerId());
			}
		}
	}

	public void csGvoEnterBattle(Human human) {
		IGvoManager gvoManager = human.getGvoManager();
		Actor actor = gvoManager.getActor();
		if (actor != null && actor.isDead()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(), ErrorCodeConstants.GVO_PLAYER_DEAD);
			return;
		}
		if (gvoManager.isInBattle()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoEnterBattle.getCode(),
				ErrorCodeConstants.GVO_PLAYER_OUT_BATTLE_CAN_ACTION);
			return;
		}
		SSGvoAllocBattleReq ssGvoAllocBattleReq = new SSGvoAllocBattleReq(human.getServerId(), human.getPlayerId());
		CoreBoot.benfuToCenter(ssGvoAllocBattleReq, 0, 0);
	}

	public void csGvoQuit(Human human) {
		IGvoManager gvoManager = human.getGvoManager();
		Actor actor = gvoManager.getActor();
		if (actor != null && actor.isDead()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoQuit.getCode(), ErrorCodeConstants.GVO_PLAYER_DEAD);
			return;
		}

		if (gvoManager.isInBattle()) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoQuit.getCode(),
				ErrorCodeConstants.GVO_PLAYER_OUT_BATTLE_CAN_ACTION);
			return;
		}

		ScenePlayerObject playerObject = human.getSceneObject();
		int sceneAreaId = BaseService.getConfigValue(PriConfigKeyName.GVO_QUIT_SCENE_AREA_ID);
		DictSceneArea dictSceneArea = DictSceneArea.getDictSceneArea(sceneAreaId);
		FPoint3 pos = SceneCommonService.randomPos(dictSceneArea);
		int code = playerObject.getController().doTransferBySceneMapIdImmediately(dictSceneArea.getSceneId(), pos,
			playerObject.getDir(), new SCGvoQuit());
		if (code > 0) {
			human.pushErrorMessageToClient(PrivPSEnum.SCGvoQuit.getCode(), code);
		} else {
			gvoManager.onQuitGvo();
		}
	}

	public void ssGvoBattlePlayerQuit(Human human) {
		boolean isCrossBattle = human.isCrossBattle(CrossBattleTypeEnum.GVO);
		if (isCrossBattle) {
			human.exitCrossBattle(CrossBattleTypeEnum.GVO);
			if (AMLog.LOG_COMMON.isInfoEnabled()) {
				AMLog.LOG_COMMON.info("@GvoBattle玩家退出,playerId={}", human.getPlayerId());
			}
		}
		human.push2Gateway(new SCGvoQuit());
	}

}
