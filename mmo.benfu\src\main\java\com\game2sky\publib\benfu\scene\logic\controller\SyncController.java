package com.game2sky.publib.benfu.scene.logic.controller;

import org.springframework.stereotype.Controller;

import com.game2sky.publib.communication.PSEnum;
import com.game2sky.publib.communication.game.scene.CSSceneSync;
import com.game2sky.publib.framework.netty.support.handler.Client;
import com.game2sky.publib.framework.netty.support.handler.ClientManager;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;

import io.netty.channel.Channel;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2017年3月9日 下午4:13:52  amyn
 */
@Controller
public class SyncController {

	@ProtoStuffMapping(PSEnum.CSSceneSync)
	public void csSceneSync(CSSceneSync cs,  Channel channel) {
		//Client client = ClientManager.getClient(channel);
		//ClientManager.enterBack(client);
	}
	
}

