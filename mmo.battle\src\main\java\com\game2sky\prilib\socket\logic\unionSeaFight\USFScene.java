package com.game2sky.prilib.socket.logic.unionSeaFight;

import com.game2sky.prilib.core.socket.logic.scene.base.AbstractCustomScene;
import com.game2sky.prilib.socket.logic.common.CommonSceneController;
import com.game2sky.publib.framework.boot.ServerTypeEnum;

/**
 * TODO 海战绑定场景
 *
 * <AUTHOR>
 * @version v0.1 2018年6月18日 下午10:17:27  guojijun
 */
public class USFScene extends AbstractCustomScene implements IUSFConsts {

	private USFight usFight;

	public USFScene(int sceneId) {
		super(ServerTypeEnum.BATTLE.name(), sceneId, USF_SCENE_MAP_ID, new CommonSceneController());
	}

	@Override
	protected void init0() {
	}

	@Override
	public void tick0() {
		super.tick0();
		long now = System.currentTimeMillis();
		this.usFight.tick(now);
	}

	public USFight getUsFight() {
		return usFight;
	}

	void setUsFight(USFight usFight) {
		this.usFight = usFight;
	}

}
