package com.game2sky.prilib.socket.logic.unionSeaFight.unit.fight;

import com.game2sky.prilib.core.socket.logic.battle.fdefine.EnumDefine;
import com.game2sky.publib.communication.common.ISysCallBack;

/**
 * TODO 海战战斗结算
 *
 * <AUTHOR>
 * @version v0.1 2018年8月27日 下午9:52:10  guojijun
 */
class USFFightSettleCallBack implements ISysCallBack {

	private final AbstractUSFFight fight;
	private final int winCamp;

	USFFightSettleCallBack(AbstractUSFFight fight, int winCamp) {
		this.fight = fight;
		this.winCamp = winCamp;
	}

	@Override
	public void apply() {
		if (this.winCamp == EnumDefine.FightCampEnum_Left || this.winCamp == EnumDefine.FightCampEnum_Right) {
			this.fight.settleCurrentFight(winCamp);
		} else {
			this.fight.interrupt();
		}
	}

}
