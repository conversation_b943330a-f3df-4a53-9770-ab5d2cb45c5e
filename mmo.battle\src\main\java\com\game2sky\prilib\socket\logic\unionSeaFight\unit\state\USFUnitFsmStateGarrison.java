package com.game2sky.prilib.socket.logic.unionSeaFight.unit.state;

import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateGarrisonInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFClientUnitStateInfo;
import com.game2sky.prilib.communication.game.unionSeaFight.USFFortressType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitStateType;
import com.game2sky.prilib.communication.game.unionSeaFight.USFUnitType;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmController;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.AbstractUSFUnitFsmState;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnit;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.IUSFUnitMoveable;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.fortress.USFFortress;
import com.game2sky.prilib.socket.logic.unionSeaFight.unit.player.USFPlayer;
import com.game2sky.publib.util.RandomUtil;

/**
 * TODO 单位驻守状态(肯定在据点中)
 *
 * <AUTHOR>
 * @version v0.1 2018年6月14日 下午8:46:32  guojijun
 */
public class USFUnitFsmStateGarrison extends AbstractUSFUnitFsmState {

	private USFClientUnitStateGarrisonInfo clientInfo;
	/**进入时间*/
	private long enterTime;
	/**等待时间*/
	private int waitTime;

	public USFUnitFsmStateGarrison(AbstractUSFUnitFsmController controller) {
		super(USFUnitStateType.USF_UNIT_STATE_GARRISON, controller);
	}

	private void calcWaitTime() {
		IUSFUnit unit = this.getUnit();
		USFFortress curFortress = unit.getCurFortress();
		int[] times = BaseService.getConfigValue(PriConfigKeyName.USF_UNIT_GARRISON_WAIT_TIME_RANGE);
		int minTime = BaseService.getConfigValue(PriConfigKeyName.USF_UNIT_GARRISON_WAIT_MIN_TIME);
		if (curFortress.getType() == USFFortressType.USF_FORTRESS_BASE) {
			this.waitTime = times[2];
		} else {
			this.waitTime = RandomUtil.randomInt(times[0], times[1]);
		}
		this.waitTime = Math.max(this.waitTime, minTime);
	}

	/**
	 * 当战斗进入运行状态
	 */
	@Override
	public void onFightEnterRunning(long now) {
		this.enterTime = now;
		this.calcWaitTime();
	}

	@Override
	public void enter() {
		this.enterTime = System.currentTimeMillis();
		this.calcWaitTime();
		IUSFUnit unit = this.getUnit();
		if (unit instanceof IUSFUnitMoveable) {
			IUSFUnitMoveable moveable = (IUSFUnitMoveable) unit;
			moveable.nextTargetFortress();
		}
	}

	@Override
	public void leave() {
		this.enterTime = 0;
		this.waitTime = 0;
	}

	@Override
	public void tick(long now) {
		IUSFUnit unit = this.getUnit();
		if (unit instanceof IUSFUnitMoveable) {
			USFFortress curFortress = unit.getCurFortress();
			int realWaitTime = this.waitTime;
			int interval = BaseService.getConfigValue(PriConfigKeyName.USF_UNIT_LEAVE_FORTRESS_INTERVAL);
			if (unit.getUnitType() == USFUnitType.USF_UNIT_PLAYER) {
				USFPlayer player = (USFPlayer) unit;
				if (player.isInGame()) {
					realWaitTime = BaseService.getConfigValue(PriConfigKeyName.USF_UNIT_GARRISON_WAIT_MIN_TIME);
					interval = 0;
				}
			}
			long tmp = now - curFortress.getLastUnitLeaveTime();
			if (tmp < interval) {
				return;
			}
			tmp = now - this.enterTime;
			if (tmp < realWaitTime) {
				return;
			}
			IUSFUnitMoveable moveable = (IUSFUnitMoveable) unit;
			moveable.tryMove();
		}
	}

	@Override
	protected void copyTo(USFClientUnitStateInfo clientUnitStateInfo) {
		if (this.clientInfo == null) {
			this.clientInfo = new USFClientUnitStateGarrisonInfo();
		}
		IUSFUnit unit = this.getUnit();
		USFFortress curFortress = unit.getCurFortress();
		this.clientInfo.setFortressUid(curFortress.getId());
		clientUnitStateInfo.setGarrisonInfo(clientInfo);
	}

}
