package com.game2sky.prilib.socket.logic.autochess;

import gnu.trove.set.hash.TIntHashSet;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import com.game2sky.CoreBoot;
import com.game2sky.prilib.communication.game.autochess.AutoChessBaseInfo;
import com.game2sky.prilib.communication.game.autochess.AutoChessRankPlayerInfo;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankReq;
import com.game2sky.prilib.communication.game.autochess.SSAutoChessPlayerRankRes;
import com.game2sky.prilib.communication.game.autochess.SSRefreshAutoChessRank;
import com.game2sky.prilib.communication.game.autochess.SSRefreshGameAutoChessBase;
import com.game2sky.prilib.config.PriConfigKeyName;
import com.game2sky.prilib.core.redis.RedisManager;
import com.game2sky.prilib.core.socket.logic.base.BaseService;
import com.game2sky.prilib.redis.RedisDao;
import com.game2sky.prilib.socket.logic.autochess.match.AutoChessMatchHandler;
import com.game2sky.prilib.socket.logic.autochess.match.AutoChessMatchSort;
import com.game2sky.prilib.socket.logic.autochess.rank.AutoChessRankBarrel;
import com.game2sky.prilib.socket.logic.autochess.rank.AutoChessRankCache;
import com.game2sky.prilib.socket.logic.autochess.rank.AutoChessRankSort;
import com.game2sky.publib.Globals;
import com.game2sky.publib.communication.APSHandler;
import com.game2sky.publib.communication.game.player.PlayerDisplayInformation;
import com.game2sky.publib.constants.RedisKeyConstantsPub;
import com.game2sky.publib.framework.boot.ServerTypeEnum;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.netty.support.handler.net.NetConnectCenter;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnect;
import com.game2sky.publib.framework.netty.support.handler.net.server.ServerConnectManager;
import com.game2sky.publib.framework.util.JsonUtils;
import com.game2sky.publib.framework.util.TimeUtils;

/**
 * 自走棋.
 *
 * <AUTHOR> Lee
 * @version v0.1 2019年5月9日 下午5:45:27  Jet Lee
 */
public class AutoChessCenterManager {

	private static AutoChessCenterManager instance = new AutoChessCenterManager();

	private AutoChessCenterManager() {
	}

	public static AutoChessCenterManager getInstance() {
		return instance;
	}

	private AutoChessBaseInfo baseInfo;

	private AutoChessMatchSort autoChessMatchSort = new AutoChessMatchSort();

	private AutoChessRankSort autoChessRankSort = new AutoChessRankSort();

	/** 上个赛季排行榜缓存 */
	private AutoChessRankCache lastSeasonRankCache = null;

	/** 当前排行榜缓存 */
	private AutoChessRankCache rankCache = null;

	/** 排行榜信息是否有变动 */
	private volatile boolean rankChange = false;

	/** 上次刷新排行榜时间 */
	private long lastRefreshRankTime = 0;

	public void init() {
		RedisDao dao = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
		int maxRank = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_MAX_RANK);
		try {
			baseInfo = dao.valueGetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_BASE_INFO, AutoChessBaseInfo.class);
			if (baseInfo == null) {
				baseInfo = new AutoChessBaseInfo(1, 1);
				saveBaseInfo();
			}
			lastSeasonRankCache = dao.valueGetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_LAST_RANK,
				AutoChessRankCache.class);
			if (lastSeasonRankCache == null) {
				lastSeasonRankCache = new AutoChessRankCache();
			}
			if (lastSeasonRankCache.getRankList() == null) {
				lastSeasonRankCache.setRankList(new ArrayList<AutoChessRankPlayerInfo>(maxRank));
			}

			rankCache = dao.valueGetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_RANK, AutoChessRankCache.class);
			if (rankCache == null) {
				rankCache = new AutoChessRankCache();
			}
			if (rankCache.getRankList() == null) {
				rankCache.setRankList(new ArrayList<AutoChessRankPlayerInfo>(maxRank));
			} else {
				// 先把当前排行榜上的数据放入排行桶里
				addPlayers(rankCache.getRankList());
			}
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[自走棋redis异常]", e);
		}
	}

	public void tick() {
		// 根据匹配规则匹配当前在匹配池的玩家
		try {
			AutoChessMatchHandler.match();
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[AutoChessCenter.match] [error]", e);
		}
		try {
			refreshRankList(false);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("[AutoChessCenter.refreshRankList] [error]", e);
		}
	}

	public void weekReset() {
		AMLog.LOG_COMMON.info("[AutoChessCenterManager] [weekReset]");
		startNewWeek();
		SSRefreshGameAutoChessBase ss = new SSRefreshGameAutoChessBase(baseInfo, true,
			lastSeasonRankCache.getRankList(), rankCache.getRankList());
		// 通知连接本分支的所有游戏服刷新基本信息
		sendMsgToAllBenfu(ss);
	}

	private void sendMsgToAllBenfu(APSHandler msg) {
		ServerConnectManager serverCenter = NetConnectCenter.getInstance().getServerCenter();
		Set<ServerConnect> connectList = new HashSet<ServerConnect>(serverCenter.getServerIdConnects().values());
		for (ServerConnect serverConnect : connectList) {
			ServerTypeEnum clientType = serverConnect.getClientType();
			if (clientType != ServerTypeEnum.BENFU) {
				continue;
			}
			if (serverConnect.getServerIds().isEmpty()) {
				AMLog.LOG_ERROR.error("serverIds is null... Flag=" + serverConnect.getClientFlag());
				continue;
			}
			// 单进程只发送一次
			int serverId = serverConnect.getServerIds().get(0);
			CoreBoot.centerToBenfu(msg, 0, serverId);
			AMLog.LOG_COMMON.info("[AutoChess] [消息名称：{}],发送给服务器组{}", msg.getClass().getSimpleName(),
				serverConnect.getServerIds());
		}
	}

	public void sendBaseInfoToBenfu(int serverId, boolean isWeekEnd) {
		SSRefreshGameAutoChessBase ss = new SSRefreshGameAutoChessBase(baseInfo, isWeekEnd,
			lastSeasonRankCache.getRankList(), rankCache.getRankList());
		CoreBoot.centerToBenfu(ss, 0, serverId);
	}

	private void startNewWeek() {
		if (nextWeek()) {
			// 开始新的赛季，清空排行,保存当前赛季排行为上赛季排行
			List<AutoChessRankPlayerInfo> lastRank = lastSeasonRankCache.getRankList();
			lastRank.clear();
			refreshRankList(true);
			lastRank.addAll(rankCache.getRankList());
			rankCache.getRankList().clear();
			rankChange = false;
			autoChessRankSort.clear();
			saveLastRankInfo(); // 保存上赛季排名
			saveCurrentRankInfo(); // 保存当前赛季排名
		}
		saveBaseInfo();
	}

	private boolean nextWeek() {
		boolean isNewSeason = false;
		baseInfo.setWeek(baseInfo.getWeek() + 1);
		int seasonWeek = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_SEASON_WEEK);
		// 赛季结束，重新开始
		if (baseInfo.getWeek() > seasonWeek) {
			baseInfo.setSeason((baseInfo.getSeason() + 1));
			baseInfo.setWeek(1);
			isNewSeason = true;
		}
		return isNewSeason;
	}

	/**
	 * 保存基本信息
	 */
	private void saveBaseInfo() {
		try {
			RedisDao redis = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
			redis.valueSetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_BASE_INFO, baseInfo, 0);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("save AutoChessBaseInfo ERROR with baseInfo = " + JsonUtils.O2S(baseInfo), e);
		}
	}

	/**
	 * 保存上赛季排行信息
	 */
	private void saveLastRankInfo() {
		try {
			RedisDao redis = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
			redis.valueSetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_LAST_RANK, lastSeasonRankCache, 0);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("save rank ERROR with lastRank = " + JsonUtils.O2S(lastSeasonRankCache), e);
		}
	}

	/**
	 * 保存当前赛季排行信息
	 */
	private void saveCurrentRankInfo() {
		try {
			RedisDao redis = RedisManager.getRedisDao(ServerTypeEnum.QUANFU.name());
			redis.valueSetByBlob(RedisKeyConstantsPub.AUTOCHESS_CENTER_RANK, rankCache, 0);
		} catch (Exception e) {
			AMLog.LOG_ERROR.error("save rank ERROR with rankCache = " + JsonUtils.O2S(rankCache), e);
		}
	}

	/**
	 * 重新计算排行榜数据
	 * @param force 强制重新计算
	 */
	private void refreshRankList(boolean force) {
		long now = Globals.getTimeService().now();
		if (!force) {
			int refreshTime = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_RANK_REFRESH_TIME);
			if (now - lastRefreshRankTime < refreshTime * TimeUtils.SECOND) {
				return;
			}
		}
		lastRefreshRankTime = now;
		// 检测排行桶数量，超过最大值，则移除部分
		int maxPlayer = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_CENTER_MAX_PLAYER);
		if (autoChessRankSort.getTotal() > maxPlayer) {
			maxPlayer = maxPlayer * 9 / 10;
			autoChessRankSort.removeTailPlayers(autoChessRankSort.getTotal() - maxPlayer);
		}
		// 重置排行
		if (rankChange) {
			List<AutoChessRankPlayerInfo> rankList = rankCache.getRankList();
			rankList.clear();
			autoChessRankSort.initRankList(rankList);
			// 保存当前排行到redis
			saveCurrentRankInfo();
			rankChange = false;
			// 通知所有本服，排行榜发生变化
			SSRefreshAutoChessRank msg = new SSRefreshAutoChessRank(rankList);
			sendMsgToAllBenfu(msg);
		}
	}

	public AutoChessMatchSort getAutoChessMatchSort() {
		return autoChessMatchSort;
	}

	public void addPlayers(List<AutoChessRankPlayerInfo> players) {
		int maxRank = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_MAX_RANK);
		int maxPlayer = BaseService.getConfigIntByDefault(PriConfigKeyName.AUTO_CHESS_CENTER_MAX_PLAYER);
		for (AutoChessRankPlayerInfo playerInfo : players) {
			PlayerDisplayInformation displayInformation = playerInfo.getDisplayInformation();
			long playerId = displayInformation.getPlayerId();
			int oldRank = autoChessRankSort.getRank(playerId, -1);
			int newRank = autoChessRankSort.getRank(playerId, playerInfo.getScore());
			if (newRank > maxPlayer) {
				continue;
			}
			autoChessRankSort.addElement(playerInfo);
			if (oldRank <= maxRank || newRank <= maxRank) {
				setRankChange();
			}
		}
	}

	private void setRankChange() {
		rankChange = true;
	}

	public void sendPlayerRankToBenfu(SSAutoChessPlayerRankReq msg) {
		long playerId = msg.getPlayerId();
		List<Integer> serverIds = msg.getServerIds();
		List<Long> playerIds = new ArrayList<Long>();
		List<Integer> ranks = new ArrayList<Integer>();
		if (playerId > 0) {
			// 查询单个玩家的排行
			int rank = autoChessRankSort.getRank(playerId, -1);
			if (rank == Integer.MAX_VALUE) {
				rank = 0;
			}
			playerIds.add(playerId);
			ranks.add(rank);
		} else {
			TIntHashSet serverSet = new TIntHashSet();
			for (Integer serverId : serverIds) {
				serverSet.add(serverId);
			}
			int tempRank = 0;
			ArrayList<AutoChessRankBarrel> barrelList = autoChessRankSort.getBarrelList();
			for (int i = barrelList.size() - 1; i >= 0; i--) {
				AutoChessRankBarrel barrel = barrelList.get(i);
				LinkedList<Long> tmpPlayerIds = barrel.getPlayerIds();
				for (Iterator<Long> iterator = tmpPlayerIds.iterator(); iterator.hasNext();) {
					tempRank++;
					Long tmpPlayerId = (Long) iterator.next();
					AutoChessRankPlayerInfo playerInfo = autoChessRankSort.getPlayerInfo(tmpPlayerId);
					if (playerInfo != null && serverSet.contains(playerInfo.getDisplayInformation().getServerId())) {
						playerIds.add(tmpPlayerId);
						ranks.add(tempRank);
					}
				}
			}
		}
		SSAutoChessPlayerRankRes res = new SSAutoChessPlayerRankRes(playerIds, ranks);
		CoreBoot.centerToBenfu(res, 0, serverIds.get(0));
	}
}
