package com.game2sky.prilib.socket.logic.integral.match;

import java.util.Map;

import com.game2sky.prilib.communication.game.integralArena.SSIntegralDoMatch;
import com.game2sky.prilib.socket.logic.integral.IntegralCenterManager;
import com.game2sky.prilib.socket.logic.integral.data.IntegralArena;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

/**
 * 跨服积分赛定时匹配
 * <AUTHOR>
 * @version v0.1 2017年5月24日 上午11:44:01  zhoufang
 */
public class ScheduleIntegralMatch extends ScheduledMessage{

	public static final int PERIOD = 2000;
//	private List<KV<Integer,Integer>> areaTypes = new ArrayList<KV<Integer,Integer>>();
	
	public ScheduleIntegralMatch() {
		super(System.currentTimeMillis());
//		Map<Integer, IntegralArena> map = IntegralCenterManager.getInstance().getIntegralArenas();
//		for(Entry<Integer, IntegralArena> entry : map.entrySet()){
//			for(Integer key : entry.getValue().getAreas().keySet()){
//				this.areaTypes.add(new KV<Integer, Integer>(entry.getKey(), key));
//			}
//		}
	}

	@Override
	public void execute() {
		Map<Integer, IntegralArena> map = IntegralCenterManager.getInstance().getIntegralArenas();
		for(Integer key : map.keySet()){
			SSIntegralDoMatch msg = new SSIntegralDoMatch(key);
			Message build = Message.buildByZoneId(0L, 0, msg, null, null, null);
			Globals.getMsgProcessDispatcher().put(build);
		}
	}

}
