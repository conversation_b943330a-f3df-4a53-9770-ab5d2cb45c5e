package com.game2sky.prilib.benfu.logic.ladderwar;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.game2sky.prilib.communication.PrivPSEnum;
import com.game2sky.prilib.communication.game.ladderwar.CSGetLadderWarGuessInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionFightingEnter;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionSupport;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderCompetitionSupportRefresh;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarBaseInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarGetNicePointHistory;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarGuessRequest;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarInfo;
import com.game2sky.prilib.communication.game.ladderwar.CSLadderWarRewardRequest;
import com.game2sky.prilib.communication.game.ladderwar.CSOnceLadderCompetitionData;
import com.game2sky.prilib.communication.game.ladderwar.SSCenterGetFormation;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerEnterRes;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerFightStateChange;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarPlayerInfoChange;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarStartFight;
import com.game2sky.prilib.communication.game.ladderwar.SSLadderWarStartFightInScene;
import com.game2sky.prilib.communication.game.ladderwar.SSResLadderWarMatchTree;
import com.game2sky.prilib.communication.game.ladderwar.SSScheduleLadderWar;
import com.game2sky.prilib.core.socket.logic.human.Human;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.protostuf.ProtoStuffMapping;
import com.game2sky.publib.framework.web.base.BaseController;

/**
 * 本服争霸赛消息控制器.
 *
 * <AUTHOR> Lee
 * @version v0.1 2018年12月3日 下午4:41:34  Jet Lee
 */
@Controller
public class LadderWarBenfuController extends BaseController {

	@Autowired
	private LadderWarBenfuService service;

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSResLadderWarMatchTree)
	public void ssResLadderWarMatchTree(SSResLadderWarMatchTree msg, Head head) {
		service.ssResLadderWarMatchTree(msg, head);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSLadderWarPlayerInfoChange)
	public void ssLadderWarPlayerInfoChange(SSLadderWarPlayerInfoChange msg, Head head) {
		service.ssLadderWarPlayerInfoChange(msg, head);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSScheduleLadderWar)
	public void ssScheduleLadderWar(SSScheduleLadderWar msg, Head head) {
		service.ssScheduleLadderWar(msg, head);
	}


	@ProtoStuffMapping(pvalue = PrivPSEnum.SSLadderWarStartFight)
	public void ssLadderWarStartFight(SSLadderWarStartFight msg) {
		this.service.ssLadderWarStartFight(msg);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSLadderWarStartFightInScene)
	public void ssLadderWarStartFightInScene(SSLadderWarStartFightInScene msg, Human human) {
		this.service.ssLadderWarStartFightInScene(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderWarInfo)
	public void csLadderWarInfo(CSLadderWarInfo msg, Human human) {
		service.csLadderWarInfo(msg, human);
	}
	

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderWarBaseInfo)
	public void csLadderWarBaseInfo(CSLadderWarBaseInfo msg, Human human) {
		service.csLadderWarBaseInfo(msg, human);
	}


	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderCompetitionFightingEnter)
	public void csLadderCompetitionFightingEnter(CSLadderCompetitionFightingEnter msg, Human self) {
		this.service.enterFight(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSLadderWarPlayerEnterRes)
	public void ssLadderWarPlayerEnterRes(SSLadderWarPlayerEnterRes msg,Human self) {
		this.service.enterFightRes(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSOnceLadderCompetitionData)
	public void csOnceLadderCompetitionData(CSOnceLadderCompetitionData msg, Human human) {
		service.csOnceLadderCompetitionData(msg, human);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderWarRewardRequest)
	public void csLadderWarRewardRequest(CSLadderWarRewardRequest msg, Human self) {
		service.csLadderWarRewardRequest(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderCompetitionSupport)
	public void csLadderCompetitionSupport(CSLadderCompetitionSupport msg, Human self) {
			service.csLadderCompetitionSupport(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderCompetitionSupportRefresh)
	public void csLadderCompetitionSupportRefresh(CSLadderCompetitionSupportRefresh msg, Human self) {
		service.csLadderCompetitionSupportRefresh(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderWarGetNicePointHistory)
	public void csLadderWarGetNicePointHistory(CSLadderWarGetNicePointHistory msg, Human self) {
		service.csLadderWarGetNicePointHistory(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSGetLadderWarGuessInfo)
	public void csGetLadderWarGuessInfo(CSGetLadderWarGuessInfo msg, Human self) {
		service.csGetLadderWarGuessInfo(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.CSLadderWarGuessRequest)
	public void csLadderWarGuessRequest(CSLadderWarGuessRequest msg, Human self) {
		service.csLadderWarGuessRequest(msg, self);
	}

	@ProtoStuffMapping(pvalue = PrivPSEnum.SSLadderWarPlayerFightStateChange)
	public void ssLadderWarPlayerFightStateChange(SSLadderWarPlayerFightStateChange msg, Human human) {
		this.service.playerFightStateChange(msg, human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSReadyFightWindow)
	public void csReadyFightWindow(Human human) {
		this.service.readyFightWindow(human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.CSConfirmReady)
	public void csConfirmReady (Human human) {
		this.service.confirmReady(human);
	}
	
	@ProtoStuffMapping(pvalue = PrivPSEnum.SSCenterGetFormation)
	public void SSCenterGetFormation(SSCenterGetFormation msg, Head head) {
		service.SSCenterGetFormation(msg, head);
	}
}
