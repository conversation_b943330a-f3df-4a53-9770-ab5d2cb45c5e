# 场景的monitor日志输出帧率条件，低于这个帧率才输出
scene.monitor.warnFrame = 15

# 场景的monitor日志输出频率，单位毫秒
scene.monitor.frame = 10000

# 弹幕服务
#barrage.url = http://op_barrage.sail2world.com/dispatcher/proto

# watcher的输出间隔
watcher.tick.time = 5000

#一次执行的玩家消息上限数量，小于等于0，则无限制
player.msg.excute.maxNum=12
#-------
#玩家身上的消息池大小和检测阀值 (该值要小于玩家消息队列大小100)
player.msg.limit = 90
#消息队列检测阀值，超过该值时，需要清理移动和心跳消息
player.msg.exam.threshold = 36
#消息队列检测阀值的清理底线，清理后保证队列中至少剩余的移动类消息数
player.msg.exam.keep.num = 12
#-------

# 从客户端来的数据信息最长长度
client.message.max.len = 99999

# 登录线程数
thread.num.login = 16
# 场景线程数
thread.num.scene = 16
# 世界线程数
thread.num.world = 1
# 排队线程数
thread.num.queue = 1
# 聊天线程数
thread.num.chat = 1

scheduled.thread.num=4

# 异步非绑定
thread.num.async = 4
# 异步绑定
thread.num.async.bind = 8

# client的writable在给客户端推送消息时，连续超过N次为false，则服务端直接关闭客户端链接
netty.client.writable.false.times = 100
# channel的flush间隔
channel.flush.interval = 16


game.flow.init.time.out = 3000
#登陆超时和断线重连时间由15秒改成60【CBT2测试15秒不够用】
game.flow.login.time.out = 60000
game.flow.reconnect.time.out = 60000
game.flow.offing.time.out = 6000

#offingFlow时需要等待押送状态
game.flow.offing.wait.be_convoy=true

netty.tcp.so.back.log = 100
netty.boss.thread.num = 1
netty.io.thread.num = 8

# socket 超时时间，长时间接收不到客户端的消息服务器会关闭跟客户端之间的连接,单位（秒）
channel.active.timeout = 60
# 连续后多久未收到登录请求，服务端主动断开连接，单位（秒）
unbind.keep.time=5

onOff = 0
flags = 
dict.init.svn.onOff = 0

white.ip.list=
# 支付服白名单
pay.white.ip.list=**************,**************

slow.sql.time=1000

work.group.num=200

#配置服内网负载均衡IP
center.config.url.andr=http://*************
center.config.url.ios=http://*************
#主服内网负载均衡IP
main.server.url.andr=http://*************
main.server.url.ios=http://*************
#pay服内网负载均衡IP
pay.server.url.andr=http://**************:8555/pay/superSdkCallBack
pay.server.url.ios=http://**************:8555/pay/superSdkCallBack

#梦岛对外地址
social.server.url=http://127.0.0.1/social

human.saveTick.num=1200
union.saveTick.num=1200
homeland.saveTick.num=1200
union.attornTick0.num=100
union.attornTick1.num=6000
union.shopSale.num=100
union.refresh.num=6000
union.delete.num=2000
DbLandService.spaceTime=5
DbLandService.max_error_retry=-1
DbLandService.errorRetrySpaceTime=10000
DbLandService.workerThreadNumber=8
DbLandService.logSpaceTime=60000
shutdownBatchLand=1
fightReplay.deleteDay=7
exeMailCache.clearDay=1
partner.saveTick.num=1200
partner.delete.num=2000
partner.refresh.num=20
#宝藏海域tick间隔
treasuresea.tick.num=100
hidetreasure.tick.num=100

scene.client.num=1
client.server.pool=4

login.async.http=false

#begin-----排队相关-----

#最大容许的通道数量
queue.max.channel.number=7500
#针对普通用户服务器容量
queue.normal.capcity=4500
#针对vip的服务器容量，vip可以额外进入服务器
queue.vip.capcity=5000
#排队刷新时间间隔
queue.refresh.time=10
#每秒最大进入游戏数量
queue.persecond.login.number=50

#新玩家队列长度
queue.new.size = 1000
#老玩家队列长度
queue.old.size = 500
#vip队列长度
queue.vip.size = 1000

#human内存现在，超过该值，不容许登陆
human.memory.max.num=20000

#10分钟内不会重发服务器爆满状态
server.state.change.interval=600000

#end-------排队相关------


!--1 dbs 2 hibernate
DBType=2
config.dbConfigName = dbs_server_hibernate.cfg.xml,dbs_server_hibernate_query.xml,dbs_server_hibernate_query_pri.xml

#类热更目录
mmo.class.reload.path=/data/op/class.reload.path

#洪水包的参数设置(检测时间，毫秒，默认为1000）
attack.msg.checktime=5000
#洪水包的移动包在检测时间内的最大数量，超过后断线，默认为50
attack.msg.maxmove=500
#每种类型消息在检测时间内的最大数量，与MessageType.maxMsgPerSecond比较，取最大值，超过后断线，默认为10
attack.msg.maxMsgPerCheckTime=100

#重新连接不用排队
reconnect.no.queue.time=-1


#Interface日志慢监控开关
interface.slow.open=true
#通用慢监控时长 (毫秒)
interface.slow.time=10
#PLAYER_MS的慢监控时长
interface.type.slow.time.1=10
#LOGIN_MSG的慢监控时长
interface.type.slow.time.2=10
#SSPlayerLoginReq的慢监控时长
interface.code.slow.time.1062=300
#CSCreatePlayer的慢监控时长
interface.code.slow.time.1110=300


#HTTP统一超时时间 (毫秒)
http.timeout=5000
http.timeout.connection=3000
http.timeout.so=5000



#Fatal报警开关-HTTP状态，目前仅限400类
alarm.http.status=true
#Fatal报警开关-HTTP异常
alarm.http.exception=true
#HTTP慢请求监控时长(毫秒)
alarm.http.slow.time=500


#Fatal报警-报警开关
alarm.reconnect.open=true
#重连失败30次，则报警
alarm.reconnect.fail.num=30


#Fatal报警开关-DBLand
alarm.dbland.open=true
#Fatal记录周期
alarm.dbland.period=-1
#Fatal记录周期出现次数 (直接打印)
alarm.dbland.num=-1


#Fatal报警开关-支付异常
alarm.payServer.open=true
#Fatal记录周期
alarm.payServer.period=-1
#Fatal记录周期次数
alarm.payServer.num=-1

#线程存活的监控
alarm.thread.alive.open=true
alarm.thread.alive.name.scope=WorldTaskScheduler,WorldTask,SceneTaskScheduler,SceneRunner
#Fatal线程丢失监听时间，遇到直接报警，1秒
alarm.thread.alive.checkTime=1000
#周期60秒 出现10次则报警
alarm.thread.alive.period=10000
alarm.thread.alive.num=10

#聊天或主消息线程监听事件设置为60秒
alarm.thread.alive.checkTime.QueueMessageProcessor=60000
#周期10分钟 出现5次则报警
alarm.thread.alive.period.QueueMessageProcessor=600000
alarm.thread.alive.num.QueueMessageProcessor=5

#定时器线程监听事件设置为10秒
alarm.thread.alive.checkTime.ScheduleService=10000
#周期10分钟 出现5次则报警
alarm.thread.alive.period.ScheduleService=600000
alarm.thread.alive.num.ScheduleService=5

#发奖线程监听事件设置为10分钟
alarm.thread.alive.checkTime.AwardSendTask=600000
#周期1小时 出现10次则报警
alarm.thread.alive.period.AwardSendTask=3600000
alarm.thread.alive.num.AwardSendTask=10


#线程慢tick的监控
alarm.thread.slowTick.open=true
#默认配置的管控范围
alarm.thread.slowTick.name.scope=WorldTask,SceneRunner
#第1组配置，执行超过100算慢tick，10秒中有10个慢tick报警
alarm.thread.slowTick.period=10000
alarm.thread.slowTick.checkTime=100
alarm.thread.slowTick.slowNum=10
#慢到奔溃值，遇到直接报警   500毫秒
alarm.thread.slowTick.breakdownTime=500

#排队线程配置，执行超过100算慢tick，10秒中有10个慢tick报警
alarm.thread.slowTick.period.QueueService=10000
alarm.thread.slowTick.checkTime.QueueService=50
alarm.thread.slowTick.slowNum.QueueService=10





#停服消息检测开关
showdown.await.message=true
#每次sleep时长
showdown.await.message.sleepTime=1000
#最大等待sleep时长
showdown.await.message.maxSleepTime=10000
#等待最大次数
showdown.await.message.maxNum=3
#收消息等待间隔时间
showdown.await.message.putInterval=3000



#离线缓存开关
human.cache.offine=true
#离线缓存检测间隔 10分钟
human.cache.offine.check.interval=60000
#离线缓存过期时间 2个小时 
human.cache.offine.overdue=7200000
#在每次添加时检测数量清理
human.cache.offine.outNum.onAdd=true
#离线缓存数量
human.cache.offine.outNum=1000
#dbland 接受容器忙碌
dbland.busy.num=10000
#是否校验没有登陆的消息
check.client.nologin.msg.switch=true

#清理aihelp红点的时间
clean.aihelp.redpoint.time=172800000

#查询vip等级的接口地址 改成
check.vip.level.url=http://*************/opensdk-router/open/user/vipinfo
#check.vip.level.url=http://opensdk.sail2world.com/opensdk-router/open/user/vipinfo

#查询vip等级的接口使用的秘钥
vip.key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCV5QSLbFQSHWcnRPuhjI2rGms0y2F0nO+e/gd82rOvoJrIp+pcdLRbkDxRJFJtNAJSafvYxJF30yMJcdeybT/fQdxLN7T/XSUe2FQqPOenIFP+DCuM84yOf0N/qgcmh5919B9YjkI9eVw5zBxnWHhXaMznt93NOdU/NvlWj3sycwIDAQAB

#查询vip等级的接口使用的应用id
vip.v=SUPER_ADMIN_APP_ID

#查询vip等级的接口使用的游戏id
vip.gameid=KOP
#JF线程数
JF.thread.num=8
#JF经分 sdk url
sdk.jf.url=http://testssdk.sail2world.com/supersdk-web/requestPlugin/aliJFPlugin/test/AND0117
#COS config
cos.secretId=AKIDGLCoBLX6cvzoOkwjMjrlRs65oHURbo7A
cos.secretKey=uY4PWP4Wkna6MM5YkgQvA8qHW0IPKQIu
cos.bucketRegion=ap-guangzhou

#redis的key的过期时间。单位：s
redis.timeout.playerInformation=604800
redis.timeout.arenaDefendInformation=604800