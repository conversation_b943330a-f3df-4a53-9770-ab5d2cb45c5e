package com.game2sky.prilib.benfu.logic.integral.task;

import com.game2sky.prilib.communication.game.integralArena.SSStartIntegralFightReq;
import com.game2sky.publib.Globals;
import com.game2sky.publib.framework.common.log.AMLog;
import com.game2sky.publib.framework.communication.Head;
import com.game2sky.publib.framework.communication.Message;
import com.game2sky.publib.schedule.ScheduledMessage;

public class ScheduleStartIntegralFight extends ScheduledMessage {
	
	private SSStartIntegralFightReq msg;
	private Head head;

	public ScheduleStartIntegralFight(SSStartIntegralFightReq msg, Head head) {
		super(Globals.getTimeService().now());
		this.msg = msg;
		this.head = head;
	}

	@Override
	public void execute() {
		if(AMLog.LOG_COMMON.isInfoEnabled()) { 
			AMLog.LOG_COMMON.info("ScheduleStartIntegralFight. 执行消息SSStartIntegralFightReq");
		}
		Message build = Message.build(head, msg);
		Globals.getMsgProcessDispatcher().put(build);
	}

}
