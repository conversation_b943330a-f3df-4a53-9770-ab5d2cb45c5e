package mmo.battle.graph;

import com.game2sky.publib.framework.util.graph.Graph;

/**
 * TODO ADD FUNCTION_DESC.
 *
 * <AUTHOR>
 * @version v0.1 2018年6月11日 下午12:00:21  Administrator
 */
public class GraphTest {

	public static void main(String[] args) {
		int[][] tArr = {	{ 0, 6, 0, 7, 4, 8 }, { 0, 0, 0, 0, 0, 0 }, { 7, 0, 0, 1, 0, 0 }, { 0, 4, 1, 0, 0, 0 },
							{ 4, 0, 0, 0, 0, 0 }, { 0, 0, 0, 0, 0, 0 } };
		final int len = tArr.length;
		Graph<Integer> graph = new Graph<>(tArr.length);
		for (int i = 1; i <= len; i++) {
			graph.addNode(i);
		}
		for (int x = 0; x < len; x++) {
			for (int y = 0; y < len; y++) {
				if (tArr[x][y] > 0) {
					graph.addEdge(x + 1, y + 1, new GraphEdge(tArr[x][y]));
				}
			}
		}
		long begin = System.currentTimeMillis();
		for (int i = 0; i < 10000; i++) {
			graph.calcPaths(1, 6, null);
		}
		System.out.println(System.currentTimeMillis() - begin);
	}

}
