package com.game2sky.prilib.socket.logic.homeland.match;


/**
 * 家园战争值桶，用于匹配
 *
 * <AUTHOR>
 * @version v0.1 2020年6月1日 下午3:15:10  daixl
 */
@Deprecated
public class HomeLandMatchBarrel{
// extends Barrel 
//	private Set<Long> playerIds = new HashSet<Long>();
//
//	public HomeLandMatchBarrel(Long id) {
//		this.id = id;
//	}
//
//	public void addPlayerId(Long playerId) {
//		this.playerIds.add(playerId);
//		this.haveNumber = playerIds.size();
//	}
//
//	public void removePlayerId(Long playerId) {
//		this.playerIds.remove(playerId);
//		this.haveNumber = playerIds.size();
//	}
//
//	public Set<Long> getPlayerIds() {
//		return playerIds;
//	}

}
