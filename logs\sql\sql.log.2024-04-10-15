2024-04-10 15:23:46,341 [DbLandService-woker-thread-3] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.SQLGrammarException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:67) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.player.PlayerDeviceInfoManager.updateEntity(PlayerDeviceInfoManager.java:31) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:23:46,382 [DbLandService-woker-thread-3] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:23:46,387 [DbLandService-woker-thread-3] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"autoRecoverEnergyTime":1712737360826,"dBNameSuffix":"player","doneNum":0,"enitityClassName":"InstPlayerGvo","entityPriority":294,"fromServerLocalHostId":0,"lastGetPowerTime":1712422960769,"modified":false,"modifyTime":1712737426281,"playerId":800010025201,"resetTimes":0,"sceneMapId":0,"splitCount":0,"splitPlayerId":800010025201,"splitSuffix":"","tableName":"inst_player_gvo","taskData":"[{\"sceneMapId\":8008,\"taskStateList\":[{\"id\":9990050,\"state\":2},{\"id\":9990041,\"state\":2},{\"id\":9990047,\"state\":2},{\"id\":9990067,\"state\":2},{\"id\":9990059,\"state\":2}]},{\"sceneMapId\":8005,\"taskStateList\":[{\"id\":9990085,\"state\":2},{\"id\":9990010,\"state\":2},{\"id\":9990025,\"state\":2},{\"id\":9990002,\"state\":2},{\"id\":9990029,\"state\":2}]}]","toSeaShipId":0,"todayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]","uniqDBSId":800010025201,"yesterdayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]"}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-10 15:23:56,392 [DbLandService-woker-thread-5] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:23:56,393 [DbLandService-woker-thread-5] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"autoRecoverEnergyTime":1712737360826,"dBNameSuffix":"player","doneNum":0,"enitityClassName":"InstPlayerGvo","entityPriority":294,"fromServerLocalHostId":0,"lastGetPowerTime":1712422960769,"modified":false,"modifyTime":1712737426281,"playerId":800010025201,"resetTimes":0,"sceneMapId":0,"splitCount":0,"splitPlayerId":800010025201,"splitSuffix":"","tableName":"inst_player_gvo","taskData":"[{\"sceneMapId\":8008,\"taskStateList\":[{\"id\":9990050,\"state\":2},{\"id\":9990041,\"state\":2},{\"id\":9990047,\"state\":2},{\"id\":9990067,\"state\":2},{\"id\":9990059,\"state\":2}]},{\"sceneMapId\":8005,\"taskStateList\":[{\"id\":9990085,\"state\":2},{\"id\":9990010,\"state\":2},{\"id\":9990025,\"state\":2},{\"id\":9990002,\"state\":2},{\"id\":9990029,\"state\":2}]}]","toSeaShipId":0,"todayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]","uniqDBSId":800010025201,"yesterdayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]"}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-10 15:24:16,399 [DbLandService-woker-thread-8] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:24:16,400 [DbLandService-woker-thread-8] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"autoRecoverEnergyTime":1712737360826,"dBNameSuffix":"player","doneNum":0,"enitityClassName":"InstPlayerGvo","entityPriority":294,"fromServerLocalHostId":0,"lastGetPowerTime":1712422960769,"modified":false,"modifyTime":1712737426281,"playerId":800010025201,"resetTimes":0,"sceneMapId":0,"splitCount":0,"splitPlayerId":800010025201,"splitSuffix":"","tableName":"inst_player_gvo","taskData":"[{\"sceneMapId\":8008,\"taskStateList\":[{\"id\":9990050,\"state\":2},{\"id\":9990041,\"state\":2},{\"id\":9990047,\"state\":2},{\"id\":9990067,\"state\":2},{\"id\":9990059,\"state\":2}]},{\"sceneMapId\":8005,\"taskStateList\":[{\"id\":9990085,\"state\":2},{\"id\":9990010,\"state\":2},{\"id\":9990025,\"state\":2},{\"id\":9990002,\"state\":2},{\"id\":9990029,\"state\":2}]}]","toSeaShipId":0,"todayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]","uniqDBSId":800010025201,"yesterdayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]"}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-10 15:24:46,408 [DbLandService-woker-thread-4] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:24:46,408 [DbLandService-woker-thread-4] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"autoRecoverEnergyTime":1712737360826,"dBNameSuffix":"player","doneNum":0,"enitityClassName":"InstPlayerGvo","entityPriority":294,"fromServerLocalHostId":0,"lastGetPowerTime":1712422960769,"modified":false,"modifyTime":1712737426281,"playerId":800010025201,"resetTimes":0,"sceneMapId":0,"splitCount":0,"splitPlayerId":800010025201,"splitSuffix":"","tableName":"inst_player_gvo","taskData":"[{\"sceneMapId\":8008,\"taskStateList\":[{\"id\":9990050,\"state\":2},{\"id\":9990041,\"state\":2},{\"id\":9990047,\"state\":2},{\"id\":9990067,\"state\":2},{\"id\":9990059,\"state\":2}]},{\"sceneMapId\":8005,\"taskStateList\":[{\"id\":9990085,\"state\":2},{\"id\":9990010,\"state\":2},{\"id\":9990025,\"state\":2},{\"id\":9990002,\"state\":2},{\"id\":9990029,\"state\":2}]}]","toSeaShipId":0,"todayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]","uniqDBSId":800010025201,"yesterdayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]"}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-10 15:25:26,416 [DbLandService-woker-thread-8] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:25:26,416 [DbLandService-woker-thread-8] ERROR(DbEntityTask.java:228) - DbEntityTask UPDATE???? entity: {"autoRecoverEnergyTime":1712737360826,"dBNameSuffix":"player","doneNum":0,"enitityClassName":"InstPlayerGvo","entityPriority":294,"fromServerLocalHostId":0,"lastGetPowerTime":1712422960769,"modified":false,"modifyTime":1712737426281,"playerId":800010025201,"resetTimes":0,"sceneMapId":0,"splitCount":0,"splitPlayerId":800010025201,"splitSuffix":"","tableName":"inst_player_gvo","taskData":"[{\"sceneMapId\":8008,\"taskStateList\":[{\"id\":9990050,\"state\":2},{\"id\":9990041,\"state\":2},{\"id\":9990047,\"state\":2},{\"id\":9990067,\"state\":2},{\"id\":9990059,\"state\":2}]},{\"sceneMapId\":8005,\"taskStateList\":[{\"id\":9990085,\"state\":2},{\"id\":9990010,\"state\":2},{\"id\":9990025,\"state\":2},{\"id\":9990002,\"state\":2},{\"id\":9990029,\"state\":2}]}]","toSeaShipId":0,"todayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]","uniqDBSId":800010025201,"yesterdayLogInfos":"[{\"type\":0,\"value\":0.0},{\"type\":1,\"value\":0.0},{\"type\":2,\"value\":0.0},{\"type\":3,\"value\":0.0},{\"type\":4,\"value\":0.0},{\"type\":5,\"value\":0.0},{\"type\":6,\"value\":0.0}]"}
com.game2sky.publib.framework.dbs.orm.DataAccessException: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:441) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.gvo.GvoManager.updateEntity(GvoManager.java:153) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: org.hibernate.exception.DataException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:77) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: java.sql.BatchUpdateException: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'taskData' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:168) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	... 13 more
2024-04-10 15:27:29,905 [DbLandService-woker-thread-2] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.SQLGrammarException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:67) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:84) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateSingleServerDBServiceImpl.update(HibernateSingleServerDBServiceImpl.java:79) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.update(BaseDao.java:53) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.player.PlayerDeviceInfoManager.updateEntity(PlayerDeviceInfoManager.java:31) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbEntityTask.writeToDb(DbEntityTask.java:113) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.saveGroup(DbLandService.java:611) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService.access$000(DbLandService.java:40) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:245) ~[mmo.core-op.jar!/:op]
	at com.game2sky.publib.db.land.DbLandService$LandGroupTask.call(DbLandService.java:230) ~[mmo.core-op.jar!/:op]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_202]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_202]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_202]
Caused by: java.sql.BatchUpdateException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'system, useMultiThreadRendering, playerId) values ('20', '2688', '12th Gen Intel' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 21 more
2024-04-10 15:35:56,174 [main] ERROR(HibernateDBServcieImpl.java:436) - Excute sql fail , serverLocalHostId=0, dbName=player
org.hibernate.exception.SQLGrammarException: Could not execute JDBC batch update
	at org.hibernate.exception.SQLStateConverter.convert(SQLStateConverter.java:67) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.exception.JDBCExceptionHelper.convert(JDBCExceptionHelper.java:43) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:253) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:266) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.engine.ActionQueue.executeActions(ActionQueue.java:167) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:298) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.event.def.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:27) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.flush(SessionImpl.java:1000) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.impl.SessionImpl.managedFlush(SessionImpl.java:338) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.transaction.JDBCTransaction.commit(JDBCTransaction.java:106) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl$TranHibernateTemplate.doCall(HibernateDBServcieImpl.java:432) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl.save(HibernateDBServcieImpl.java:135) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.orm.HibernateDBServcieImpl.save(HibernateDBServcieImpl.java:131) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.publib.framework.dbs.dao.BaseDao.save(BaseDao.java:39) ~[mmo.communication-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.arena.ArenaRobotManager.initRobot(ArenaRobotManager.java:76) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.arena.ArenaManager.loadData(ArenaManager.java:91) ~[mmo.core-op.jar!/:op]
	at com.game2sky.prilib.core.socket.logic.arena.ArenaManager.start(ArenaManager.java:55) ~[mmo.core-op.jar!/:op]
	at com.game2sky.CoreBoot.boot(CoreBoot.java:657) ~[mmo.core-op.jar!/:op]
	at com.game2sky.MainOpBenfu.main(MainOpBenfu.java:93) ~[classes!/:op]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_202]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_202]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_202]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_202]
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:48) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:87) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:50) ~[mmo.benfu-op.jar:op]
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:58) ~[mmo.benfu-op.jar:op]
Caused by: java.sql.BatchUpdateException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'rank, playerId) values (76406, 1712738156163, 1, 1499811622913311744)' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createBatchUpdateException(SQLError.java:224) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:816) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 24 more
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'rank, playerId) values (76406, 1712738156163, 1, 1499811622913311744)' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchSerially(ClientPreparedStatement.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeBatchInternal(ClientPreparedStatement.java:418) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at com.mysql.cj.jdbc.StatementImpl.executeBatch(StatementImpl.java:795) ~[mysql-connector-j-8.0.33.jar!/:8.0.33]
	at org.hibernate.jdbc.BatchingBatcher.doExecuteBatch(BatchingBatcher.java:48) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	at org.hibernate.jdbc.AbstractBatcher.executeBatch(AbstractBatcher.java:246) ~[hibernate-3.2.6.ga.jar!/:3.2.6.ga]
	... 24 more
